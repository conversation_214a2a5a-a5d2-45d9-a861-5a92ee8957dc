{"name": "yaoku", "version": "0.1.0", "private": true, "author": "联众智慧科技股份有限公司", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "fmt": "prettier vue.config.js src -w"}, "dependencies": {"@devexpress/analytics-core": "22.1.6", "@mdfe/auth": "^0.3.3", "@mdfe/editable-table": "^0.1.0-beta.0", "@mdfe/editor": "^1.0.0-next.7", "@mdfe/lyra": "^0.6.0", "@mdfe/material.overflow-layout": "^3.0.0", "@mdfe/material.select-comma": "^3.1.0", "@mdfe/material.select-scroll": "^3.0.1", "@mdfe/material.select-table": "^3.7.2", "@mdfe/medi-hooks": "^1.0.0-alpha.8", "@mdfe/medi-layout": "^3.0.2", "@mdfe/medi-ui": "^3.19.0", "@mdfe/medi-ui-3-compact": "^1.0.0-next.5", "@mdfe/medi-ui-pro": "^3.9.1-beta.0", "@mdfe/medi-vl-table": "^0.1.0-alpha.25", "@mdfe/mediinfo.lyra.log": "^0.7.1", "@mdfe/sass-bem": "^1.0.0-alpha.5", "@mdfe/stark-app": "^2.0.0-alpha.10", "@mdfe/view-manager": "^2.5.0", "@mdfe/vue-access": "^2.0.0-beta.5", "@mdfe/vxe-table": "^1.0.0-next.2", "add": "^2.0.6", "axios": "^1.6.0", "big.js": "6.2.2", "core-js": "^3.35.1", "dayjs": "^1.11.3", "devexpress-reporting": "22.1.6", "devextreme": "22.1.6", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "sortablejs": "^1.15.0", "tinymce": "^5.5.1", "vue": "^3.4.15", "vue-clipboard3": "^2.0.0", "vue-router": "^4.2.5", "vuex": "^4.1.0", "vxe-table": "4.6.25", "word-pinyin": "^0.1.7", "yarn": "^1.22.21"}, "devDependencies": {"@mdfe/vue-cli-plugin-material.select-table": "^0.0.13", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@ttou/define-config": "^3.2.0", "@types/node": "^20.11.5", "@types/webpack-env": "^1.18.4", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.4", "sass-embedded": "^1.89.2", "sass-loader": "^13.3.2", "typescript": "~5.3.3"}, "resolutions": {"axios": "npm:axios@^1.6.5", "core-js": "http://*************:8081/repository/npm-main/core-js/-/core-js-3.35.0.tgz", "date-fns": "http://*************:8081/repository/npm-main/date-fns/-/date-fns-2.30.0.tgz", "oidc-client": "http://*************:8081/repository/npm-main/oidc-client/-/oidc-client-1.11.5.tgz", "@devexpress/analytics-core": "http://*************:8081/repository/npm-main/@devexpress/analytics-core/-/analytics-core-22.1.6.tgz", "ace-builds": "http://*************:8081/repository/npm-main/ace-builds/-/ace-builds-1.32.3.tgz", "devextreme": "http://*************:8081/repository/npm-main/devextreme/-/devextreme-22.1.6.tgz", "devexpress-reporting": "http://*************:8081/repository/npm-main/devexpress-reporting/-/devexpress-reporting-22.1.6.tgz", "jquery-ui": "http://*************:8081/repository/npm-main/jquery-ui/-/jquery-ui-1.13.2.tgz", "prettier": "http://*************:8081/repository/npm-main/prettier/-/prettier-3.1.1.tgz", "canvas": "http://*************:8081/repository/npm-main/empty-npm-package/-/empty-npm-package-1.0.0.tgz", "yorkie": "http://*************:8081/repository/npm-main/empty-npm-package/-/empty-npm-package-1.0.0.tgz", "nodejieba": "http://*************:8081/repository/npm-main/empty-npm-package/-/empty-npm-package-1.0.0.tgz", "commander": "http://*************:8081/repository/npm-main/empty-npm-package/-/empty-npm-package-1.0.0.tgz", "lodash.debounce": "http://*************:8081/repository/npm-main/lodash.debounce/-/lodash.debounce-0.0.1.tgz", "lodash.defaultsdeep": "http://*************:8081/repository/npm-main/lodash.defaultsdeep/-/lodash.defaultsdeep-0.0.1.tgz", "lodash.kebabcase": "http://*************:8081/repository/npm-main/lodash.kebabcase/-/lodash.kebabcase-0.0.1.tgz", "lodash.mapvalues": "http://*************:8081/repository/npm-main/lodash.mapvalues/-/lodash.mapvalues-0.0.1.tgz", "lodash.memoize": "http://*************:8081/repository/npm-main/lodash.memoize/-/lodash.memoize-0.0.1.tgz", "lodash.uniq": "http://*************:8081/repository/npm-main/lodash.uniq/-/lodash.uniq-0.0.1.tgz", "lodash.clonedeep": "http://*************:8081/repository/npm-main/lodash.clonedeep/-/lodash.clonedeep-0.0.1.tgz", "lodash.isequal": "http://*************:8081/repository/npm-main/lodash.isequal/-/lodash.isequal-0.0.1.tgz", "lodash.merge": "http://*************:8081/repository/npm-main/lodash.merge/-/lodash.merge-0.0.1.tgz"}, "engines": {"node": ">=16"}, "license": "UNLICENSED", "packageManager": "yarn@1.22.19"}