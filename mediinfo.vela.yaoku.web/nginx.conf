worker_processes 1;

events {
  worker_connections 1024;
}


http {

  # 发送文件
  sendfile on;

  # 将更多的字节组成一个数据包，从而提高I/O性能
  tcp_nodelay on;

  # 将较小数据包合并为一个数据包发送，减少网络报文段的数量
  tcp_nopush on;

  # 长连接超时时间，单位是秒

  keepalive_timeout 65;

    # 为了快速处理静态数据集
  # MIME类型，请求头字符串的名称，nginx使用哈希表
  types_hash_max_size 2048;

  # 文件扩展名与类型映射表
  include mime.types;

  # 默认文件类型
  default_type application/octet-stream;

  ##
  # Gzip
  ##
  # 关闭 gzip，使用 gzip_static 来处理
  gzip off;

  # 在流水线中，通过前端构建时生成 .gz 文件
  # 可以减少 nginx 配置压缩的开销
  # 需要前端构建时生成 .gz 文件

  gzip_static on;

  server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;

    location = /index.html {
      add_header Cache-Control no-cache;
      add_header Pragma no-cache;
      add_header Expires 0;
    }
    location ^~/mediinfo-vela-yaoku-web/ {
      alias /usr/share/nginx/html/;
      index index.html index.htm;
      try_files $uri $uri/ /index.html;
      add_header Cache-Control no-cache;
    }
    location / {
      index index.html index.htm;
      try_files $uri $uri/ /index.html;
      add_header Cache-Control no-cache;
    }
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|js|css)$ {      
      #缓存非html资源文件
      expires 3d;
    }
    #指定文件编码
    charset UTF-8;
    # 添加 text/css 以支持css文件
    charset_types *;

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root html;
    }
  }
}
