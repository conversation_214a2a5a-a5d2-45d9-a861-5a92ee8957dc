const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  publicPath: '/mediinfo-vela-yaoku-web/',
  pages: {
    index: {
      entry: 'src/main.ts',
      template: 'public/index.html',
      scriptLoading: 'blocking',
    },
    shuangKongYPPage: {
      title: '双控药品限量',
      entry: 'src/pages/shuangKongYPPage/main.js',
      scriptLoading: 'blocking',
    },
  },
  assetsDir: 'static',
  lintOnSave: false,
  transpileDependencies: ['@mdfe/*'],
  productionSourceMap: false,
  configureWebpack: {
    // 如果是开发环境，开启物理文件缓存，提升第二次启动速度
    // 如果修改组件命名空间，或更新第三方模块不生效
    // 可以删除 node_modules/.cache 目录后再试
    // See https://webpack.js.org/configuration/cache/#cachetype
    ...(process.env.NODE_ENV === 'development' && {
      cache: { type: 'filesystem' },
    }),
    output: {
      library: 'mediinfo-vela-yaoku-web_[name]',
      libraryTarget: 'umd',
      uniqueName: 'webpackJsonp_mediinfo-vela-yaoku-web',
    },
  },
  css: {
    loaderOptions: {
      scss: {
        additionalData: `
        // 修改 Medi UI 的 CSS 命名空间
        @forward "@mdfe/medi-ui/theme-chalk/src/mixins/config.scss" with (
          $namespace: "${process.env.VUE_APP_NAMESPACE}"
        );

        // 修改工具类的 CSS 命名空间
        @forward "@mdfe/sass-bem/config.scss" with (
          $bem-component-namespace: "${process.env.VUE_APP_NAMESPACE}"
        );

        // 导入 bem 工具类
        @use "@mdfe/sass-bem/bem" as *;

        // 兼容之前项目使用
        $md-prefix: "${process.env.VUE_APP_NAMESPACE}";`,
        sassOptions: {
          api: 'modern-compiler',
          silenceDeprecations: [
            'global-builtin',
            'import',
            'legacy-js-api',
            'color-functions',
          ],
        },
      },
    },
  },
  devServer: {
    port: 1888,
    allowedHosts: 'all',
    client: {
      progress: true,
      overlay: {
        errors: true,
        warnings: false,
        runtimeErrors: false,
      },
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': '*',
      'Access-Control-Allow-Headers': '*',
    },
    proxy: require('./config/proxy'),
  },
});
