import {
  initAuthClient,
  type Profile,
  useHistoryAPICleanupRedirectCallback,
} from '@mdfe/auth';
import { type Ref, ref } from 'vue';

export type CurrentUserRef = Ref<Profile>;

const anonymousUser = ref<Profile>({
  sub: '',
  name: 'Anonymous',
  iss: '',
  aud: '',
  exp: -1,
  iat: -1,
});

const authClient = initAuthClient({
  client_id: 'lyra.yewukzt.client',
  authority: '/mediinfo-lyra-authserver',
  redirect_uri: `${process.env.BASE_URL}#/callback`,
  silent_redirect_uri: `${process.env.BASE_URL}silent-renew-oidc.html`,
  post_logout_redirect_uri: process.env.BASE_URL,
});

const currentUserRef: CurrentUserRef = ref(anonymousUser);

export async function loginWithRedirect() {
  const { user, appState } = await authClient.prepare();

  useHistoryAPICleanupRedirectCallback(appState);

  currentUserRef.value = user.profile;

  return user;
}

export function useCurrentUser(): CurrentUserRef {
  return currentUserRef;
}
