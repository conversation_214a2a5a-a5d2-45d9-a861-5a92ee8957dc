<script lang="ts" setup>
import { MdLayout, type MenuOption } from '@mdfe/medi-layout';
import { useNamespace } from '@mdfe/medi-ui';
import { computed, type PropType } from 'vue';

import { useSiteData } from '../composables';
import NavbarUseInfo from './NavbarUseInfo.vue';

defineOptions({ name: 'DefaultLayout' });

const props = defineProps({
  logo: String,
  home: {
    type: String,
    default: '/',
  },
  views: {
    type: Array,
    default: () => [],
  },
  sidebar: {
    type: Array as PropType<MenuOption[]>,
    default: () => [],
  },
  menuConfig: {
    type: Object,
    default: () => ({}),
  },
});

const ns = useNamespace('theme-layout');

const siteData = useSiteData();

const menuProps = computed(() => {
  return { iconPrefix: process.env.VUE_APP_NAMESPACE, ...props.menuConfig };
});
</script>

<template>
  <div :class="ns.b()">
    <MdLayout
      :logo="logo"
      :title="siteData.title"
      :home="home"
      :side-menu="sidebar"
      :menu-props="menuProps"
    >
      <template #headerRight>
        <slot name="navbar-right">
          <NavbarUseInfo />
        </slot>
      </template>

      <slot></slot>
    </MdLayout>
  </div>
</template>

<style lang="scss">
@import '@mdfe/sass-bem/bem';

@include c('theme-layout') {
  height: 100%;
  width: 100%;

  @include c('main') {
    --mds-main-padding: 0px;
  }
}
</style>
