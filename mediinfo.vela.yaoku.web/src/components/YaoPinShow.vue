<template>
  <span :style="styleClass" class="bmis-yaopin-show"
    >{{ mingCheng }}<slot></slot>
  </span>
</template>
<script>
export default {
  name: 'yaopin-show',
  props: {
    styleData: {
      type: Object,
      default: () => ({
        jiaCuBZ: null,
        xieTiBZ: null,
        ziTiYS: null,
        tianJiaWZ: null,
      }),
    },
    yaoPinMC: {
      type: String,
    },
  },
  computed: {
    mingCheng({ yaoPinMC, styleData }) {
      return (styleData.tianJiaWZ || '') + (yaoPinMC || '');
    },
    styleClass({ styleData }) {
      let sty = {};
      if (styleData.jiaCuBZ) {
        sty['font-weight'] = 'bold';
      }
      if (styleData.xieTiBZ) {
        sty['font-style'] = 'oblique';
      }
      sty.color = styleData.ziTiYS;
      return sty;
    },
  },
};
</script>
<style lang="scss" scoped></style>
