<template>
  <md-dialog
    title="配置设置"
    save-text="保存"
    v-model="dialogVisible"
    :before-close="handleClose"
    :before-save="handleSave"
    :close-on-click-modal="false"
    size="large"
    :class="prefixClass('peiZhiSZDialog')"
  >
    <md-form
      :class="prefixClass('peiZhiSM-form')"
      :rules="formRules"
      :model="formData"
      ref="form"
    >
      <md-row>
        <md-col :span="12">
          <md-form-item label="配置名称" prop="peiZhiMC">
            <md-input v-model="formData.peiZhiMC" />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="关联功能" prop="guanLianGM">
            <md-select-table
              v-model="formData.guanLianGM"
              :columns="Columns"
              :fetchData="FetchXMData"
              :immediateLoad="false"
              filterable
              multiple
              labelKey="gongNengMC"
              valueKey="gongNengID"
              placeholder="请输入功能名称搜索"
            />
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="配置项" prop="peiZhiXMC">
            <md-select
              v-model="peiZhiXiangList"
              multiple
              filterable
              style="width: 100%"
            >
              <md-option
                v-for="(item, index) in peiZhiXOptions"
                :key="index"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
      </md-row>
      <div class="editorBox">
        <md-editor
          :init="editorInit"
          v-model="formData.peiZhiNR"
          style="height: 39rem"
        ></md-editor>
      </div>
    </md-form>
  </md-dialog>
</template>
<script>
import SelectTable from '@mdfe/material.select-table';
// import 'tinymce/skins/ui/oxide/skin.min.css'
import { Editor } from '@mdfe/editor';
import { request as postSavePeiZhiSMXX } from '@/service/gongYong/yewu/postSavePeiZhiSMXX';
import { request as getGuanLianGN } from '@/service/gongYong/yewu/getGuanLianGN';
import { getGongNengID } from '@/system/utils/local-cache';
import { getToken } from '@/system/utils/local-cache';
import axios from 'axios';
import { getUser } from '@mdfe/auth';
const IMG_TEMP_STR = '{zuHuID}';
const IMG_TEMP_RE = new RegExp(IMG_TEMP_STR, 'ig');
const editorContext = {
  zuHuID: '',
};
export default {
  name: 'peiZhiSM-dialog',
  data() {
    this.editorContext = editorContext;
    this.editorInit = {
      toolbar:
        'fontselect | fontsizeselect | bold | italic | strikethrough | table | link unlink | image | forecolor backcolor | bullist | numlist | alignleft | aligncenter | alignright | alignjustify | outdent | indent | removeformat | undo | redo',
      min_height: 300,
      relative_urls: false,
      images_upload_handler: this.handleImageUpload,
      file_picker_callback: (callback) => {
        cosnsole.log(callback);
      },
      setup: (editor) => {
        // 核心：覆盖粘贴事件处理
        editor.on('paste', (e) => {
          this.handlePaste(e, editor);
        });
      },
      paste_preprocess: (plugin, args) => {
        const div = document.createElement('div');
        div.innerHTML = args.content;

        // 清除所有内联样式和类
        Array.from(div.querySelectorAll('*')).forEach((el) => {
          el.removeAttribute('style');
          el.removeAttribute('class');
          el.removeAttribute('dir'); // 钉钉可能添加的方向属性
          // 移除其他自定义属性（如data-*）
          Array.from(el.attributes).forEach((attr) => {
            if (attr.name.startsWith('data-')) {
              el.removeAttribute(attr.name);
            }
          });
        });

        // 转换换行符为段落（可选）
        args.content = div.innerHTML.replace(/<br\s*[/]?>/gi, '</p><p>');
      },
      // 允许的HTML元素
      valid_elements:
        'p[style],span[style],br,strong[style]/b,strong,em/i,u,ul,ol,li,img[src|alt|width|height|style]',
      // 允许的样式（仅限用户手动添加的样式）
      valid_styles: {
        '*': 'font-weight,font-style,text-decoration', // 允许加粗、斜体、下划线
        p: 'text-align', // 允许段落对齐
        span: 'color,background-color,font-size,font-family,font-weight,font-style,text-decoration,vertical-align,line-height,text-align', // 允许字体颜色、背景色、字体大小、字体、字体粗细、字体斜体、字体下划线、垂直对齐、行高、文本对齐
      },
      // 强制使用<p>标签包裹内容
      // forced_root_block: 'p',
      // 禁用自动添加内联样式
      paste_remove_styles: true,
      // 清除WebKit特定样式
      paste_webkit_styles: 'none',
    };
    return {
      gongNengMC: '',
      Columns: [
        {
          label: '应用名称',
          prop: 'yingYongMC',
        },
        {
          label: '功能名称',
          prop: 'gongNengMC',
        },
      ],
      formData: {
        peiZhiMC: '',
        peiZhiXMC: '',
        peiZhiXDM: '',
        peiZhiNR: '',
        guanLianGM: [],
      },
      formRules: {
        peiZhiMC: [
          { required: true, message: '请输入配置名称', trigger: 'change' },
        ],
        guanLianGM: [
          { required: true, message: '请选择关联功能', trigger: 'change' },
        ],
      },
      peiZhiXOptions: [],
      dialogVisible: false,
      currentPeiZhi: {},
      peiZhiXiangList: [],
    };
  },
  beforeDestroy() {
    delete this.editorInit;
    delete this.editorContext;
  },
  methods: {
    async handleImageUpload(blobInfo, success, failure) {
      if (!blobInfo.blob) return;
      try {
        const formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());
        const res = await axios.post(
          '/mediinfo-lyra-nengli/api/v1.0/cunchu/wenjian/uploadfile',
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `${await getToken()}`,
            },
          },
        );
        let str = res.data.data[0].templateDownloadPath.replace(
          IMG_TEMP_RE,
          editorContext.zuHuID,
        );
        success(str);
      } catch (error) {
        console.error('图片上传失败:', error);
        failure('上传失败: ' + error.message);
      }
    },
    async handlePaste(e, editor) {
      const html = e.clipboardData.getData('text/html');
      if (!html) return;

      // 解析DOM并查找外链图片
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const images = [...doc.getElementsByTagName('img')];
      // 过滤出需要处理的外链图片
      const externalImages = images.filter((img) => {
        return (
          img.src.startsWith('http') &&
          !img.src.startsWith('data:image') &&
          !img.src.startsWith('blob:')
        );
      });
      if (externalImages.length === 0) return;
      e.preventDefault(); // 阻止默认粘贴行为
      // 创建处理队列
      const processingQueue = externalImages.map(async (img, index) => {
        try {
          // 下载远程图片
          const response = await fetch(img.src, { mode: 'cors' });
          const blob = await response.blob();

          // 生成临时Blob URL
          const blobUrl = URL.createObjectURL(blob);

          // 替换为临时地址
          img.src = blobUrl;

          // 触发上传流程
          return this.handleImageUpload(blob, editor, () => {});
        } catch (error) {
          console.error('图片下载失败:', error);
          return img.src; // 保留原链接
        }
      });

      // 等待所有图片处理完成
      const results = await Promise.all(processingQueue);

      // 替换最终URL
      results.forEach((newUrl, index) => {
        if (newUrl) {
          externalImages[index].src = newUrl;
          externalImages[index].removeAttribute('data-original');
        }
      });
      // 重建HTML并插入
      const newHtml = doc.documentElement.innerHTML;
      editor.insertContent(newHtml);
    },
    handleClose() {
      this.$refs.form.resetFields();
      this.formData.peiZhiNR = '';
      this.dialogVisible = false;
      this.resolve(false);
    },
    async showModel(option) {
      let res = await getUser();
      this.editorContext.zuHuID = res.profile.tenantId;
      this.gongNengMC = option.gongNengMC;
      this.currentPeiZhi = option.currentPeiZhi;
      this.formData.peiZhiXDM = option.biaoZhunDM;
      this.formData.peiZhiXMC = option.title;
      this.formData.peiZhiMC = option.title;
      this.peiZhiXiangList = option.peiZhiXiangList.map((o) => o.biaoZhunDM);
      this.peiZhiXOptions = option.peiZhiXiangList;
      if (option.detail) {
        this.formData.id = option.detail.id;
        this.formData.peiZhiMC = option.detail.peiZhiMC;
        this.peiZhiXiangList = option.detail.peiZhiXiangList.map(
          (o) => o.xiangMuZDM,
        );
        this.formData.guanLianGM = option.detail.guanLianGNList?.map((o) => {
          return {
            gongNengID: o.xiangMuZDM,
            gongNengMC: o.xiangMuZMC.split('/')[1],
            yingYongMC: o.xiangMuZMC.split('/')[0],
          };
        });
        this.formData.peiZhiNR = option.detail.peiZhiNR;
      }
      this.dialogVisible = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    async FetchXMData({ inputValue, page, pageSize }) {
      const params = {
        gongNengMC: inputValue,
        caiDanBZ: 1,
        yeMianBZ: 1,
        pageIndex: page,
        pageSize: pageSize,
      };
      let res = await getGuanLianGN(params);
      return res;
    },
    async handleSave() {
      let result = await this.$refs.form.validate();
      if (result) {
        try {
          let params = {
            id: this.formData.id,
            peiZhiMC: this.formData.peiZhiMC,
            gonGnengid: getGongNengID(),
            gongNengMC: this.gongNengMC,
            peiZhiFLDM: this.currentPeiZhi.weiZhiID,
            peiZhiFLMC: this.currentPeiZhi.weiZhiMC,
            peiZhiXDM: this.formData.peiZhiXDM,
            peiZhiXMC: this.formData.peiZhiXMC,
            peiZhiNR: this.formData.peiZhiNR
              ? this.formData.peiZhiNR.replace(
                  new RegExp(this.editorContext.zuHuID, 'g'),
                  IMG_TEMP_STR,
                )
              : '',
            guanLianGNList: this.formData.guanLianGM.map((item) => {
              return {
                xiangMuZDM: item.gongNengID,
                xiangMuZMC: item.yingYongMC + '/' + item.gongNengMC,
              };
            }),
            peiZhiXiangList: this.peiZhiXiangList.map((item) => {
              return {
                xiangMuZDM: item,
                xiangMuZMC: this.peiZhiXOptions.find(
                  (o) => o.biaoZhunDM == item,
                ).biaoZhunMC,
              };
            }),
          };
          await postSavePeiZhiSMXX(params);
          this.$message.success('保存成功');
          this.dialogVisible = false;
          this.resolve(true);
        } catch (e) {
          this.$logger.error(e);
        }
      }
    },
  },
  components: {
    'md-editor': Editor,
    'md-select-table': SelectTable,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-peiZhiSZDialog {
  .#{$md-prefix}-peiZhiSM-form {
    display: flex;
    flex-direction: column;
    .editorBox {
      flex: 1;
    }
  }
}
</style>
