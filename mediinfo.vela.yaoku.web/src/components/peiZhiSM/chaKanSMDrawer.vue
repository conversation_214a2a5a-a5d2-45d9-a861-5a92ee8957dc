<template>
  <md-drawer
    :modal="false"
    v-model="drawer"
    :append-to-body="false"
    direction="rtl"
    size="60%"
    horizontal-top="80px"
    :before-close="handleClose"
    ref="feiYongDrawer"
  >
    <template v-slot:header>
      <div :class="prefixClass('myheader')">
        <div>查看配置说明</div>
        <div class="btn" @click="peiZhiSM">
          <md-icon name="bianji"></md-icon>
          配置说明
        </div>
      </div>
    </template>
    <div v-loading="loading" :class="prefixClass('detailBox')">
      <div v-if="detail" :class="prefixClass('peiZhiSM')">
        <div class="top">
          <div :class="prefixClass('label')">{{ detail.peiZhiMC }}</div>
          <span>关联功能：</span>
          <span
            class="gongnengItem"
            v-for="(item, index) in detail.guanLianGNList"
            :key="index"
          >
            {{ item.xiangMuZMC }}
          </span>
        </div>
        <div>
          <div v-html="detail.peiZhiNR"></div>
        </div>
      </div>
      <md-empty v-else style="height: 100%; justify-content: center"></md-empty>
    </div>
    <peizhism-dialog :key="biaoZhunDM" ref="peiZhiSMDialog" />
  </md-drawer>
</template>

<script>
import peiZhiSMDialog from './peiZhiSMDialog';
import gongyongApi from '@/service/gongYong/yewu/index.js';
import { getGongNengID } from '@/system/utils/local-cache';
import { getUser } from '@mdfe/auth';
export default {
  name: 'chaKanSM-drawer',
  data() {
    return {
      zuHuID: '',
      loading: false,
      detail: '',
      title: '',
      biaoZhunDM: '',
      currentPeiZhi: '',
      gongNengMC: '',
      drawer: false,
      peiZhiXiangList: [],
    };
  },
  methods: {
    async showModel(option) {
      let res = await getUser();
      this.zuHuID = res.profile.tenantId;
      this.title = option.title;
      this.currentPeiZhi = option.currentPeiZhi;
      this.gongNengMC = option.gongNengMC;
      this.biaoZhunDM = option.biaoZhunDM;
      this.drawer = true;
      this.peiZhiXiangList = option.peiZhiXiangList;
      this.getshuoMingXQ();
    },
    handleClose() {
      this.drawer = false;
    },
    async getshuoMingXQ() {
      try {
        this.loading = true;
        this.detail = null;
        if (!this.biaoZhunDM) return;
        let res = await gongyongApi.GetPeiZhiSMXQ({
          gongNengID: getGongNengID(),
          peiZhiFLDM: this.currentPeiZhi.weiZhiID,
          peiZhiXDM: this.biaoZhunDM,
        });
        if (res.peiZhiNR) {
          res.peiZhiNR = res.peiZhiNR.replace(
            new RegExp('{zuHuID}', 'g'),
            this.zuHuID,
          );
        }
        this.detail = res;
      } finally {
        this.loading = false;
      }
    },
    async peiZhiSM() {
      let result = await this.$refs.peiZhiSMDialog.showModel({
        currentPeiZhi: this.currentPeiZhi,
        gongNengMC: this.gongNengMC,
        title: this.title,
        biaoZhunDM: this.biaoZhunDM,
        peiZhiXiangList: this.peiZhiXiangList,
        detail: this.detail,
      });
      if (result) {
        this.getshuoMingXQ();
      }
    },
  },
  components: {
    'peizhism-dialog': peiZhiSMDialog,
  },
};
</script>

<style scoped lang="scss">
/* 深度选择器穿透 */
::v-deep .#{$md-prefix}-peiZhiSM ol {
  list-style-type: decimal;
  margin-left: 20px;
}

::v-deep .#{$md-prefix}-peiZhiSM ul {
  list-style-type: disc;
  margin-left: 20px;
}
::v-deep .#{$md-prefix}-drawer__header {
  color: #333333;
  height: 37px;
  line-height: 37px;
  background-color: #000;
  padding: 0 0 0 20px;
  font-size: 16px;
  & > span {
    outline: none;
  }
}
#{$md-prefix}-drawer__close-btn {
  color: #aaaaaa;
}
.#{$md-prefix}-detailBox {
  height: 100%;
}
.#{$md-prefix}-myheader {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.top {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  margin-bottom: 20px;
}
.#{$md-prefix}-label {
  font-family: PingFang SC;
  font-weight: 600;
  color: #222222;
  font-size: 14px;
  margin: 5px 0;
}
.#{$md-prefix}-peiZhiSM {
  padding: 0 15px;
}
.btn {
  color: #1385f0;
  cursor: pointer;
}
.gongnengItem {
  color: #1385f0;
  background: #e2efff;
  margin: 0 2px;
  padding: 2px 4px;
}
</style>
