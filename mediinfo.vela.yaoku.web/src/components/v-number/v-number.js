const directive = {
  mounted(el, binding) {
    el.vNumber = new VNumber(el, binding);
  },
  unmounted(el) {
    el.vNumber.destroy();
    el.vNumber = null;
  },
};

class VNumber {
  constructor(el, binding) {
    this.el = el;
    this.binding = binding;
    this.targetInput = el.tagName === 'INPUT' ? el : el.querySelector('input');
    this.handleInput = this.handleInput.bind(this);
    this.handleFocusout = this.handleFocusout.bind(this);
    this.init();
  }
  init() {
    this.targetInput.addEventListener('input', this.handleInput);
    this.el.addEventListener('focusout', this.handleFocusout);
  }
  destroy() {
    this.targetInput.removeEventListener('input', this.handleInput);
    this.el.removeEventListener('focusout', this.handleFocusout);
    this.targetInput = null;
  }
  handleInput() {
    let value = this.targetInput.value;
    // 清除"数字"和"."以及"-"以外的字符
    value = value.replace(/[^\-\d.]/g, '');
    // 只保留第一个'-'号
    value = value.replace(/\.{2,}/g, '.').replace(/\-{2,}/g, '-');
    // 将 '-.' 替换成 '-0.'
    value = value.replace(/^\./g, '0.').replace(/^\-\./, '-0.');

    if (
      this.binding.modifiers.float &&
      typeof this.binding.value !== 'undefined'
    ) {
      //清除多余小数点
      value = clearExtraDot(value);
      // 期望保留的最大小数位数
      let pointKeep = 0;
      if (
        typeof this.binding.value === 'string' ||
        typeof this.binding.value === 'number'
      ) {
        pointKeep = parseInt(this.binding.value);
      } else if (typeof this.binding.value === 'object') {
        // 支持新的小数点保留位配置
        pointKeep = this.binding.value.decimal;
      }
      if (!isNaN(pointKeep)) {
        if (!Number.isInteger(pointKeep) || pointKeep < 0) {
          pointKeep = 0;
        }
        // 增加'-'号的支持
        const str = '^(\\-)*(\\d+)\\.(\\d{' + pointKeep + '}).*$';
        const reg = new RegExp(str);
        if (pointKeep === 0) {
          // 不需要小数点
          value = value.replace(reg, '$1$2');
        } else {
          // 通过正则保留小数点后指定的位数
          value = value.replace(reg, '$1$2.$3');
        }
      }
    } else {
      value = this.targetInput.value.replace(/[^\d]/g, '');
    }
    this.targetInput.value = value;
    if (this.el.__vue__) {
      this.el.__vue__.$emit('input', value);
    }
  }
  handleFocusout(event) {
    const target = event.target;
    if (isNaN(target.value)) {
      target.value = '';
    }
    let val = target.value;
    if (val && typeof this.binding.value === 'object') {
      let { min, max, decimal, moreThen, lessThen } = this.binding.value;
      decimal = parseFloat(decimal);
      min = parseFloat(min);
      max = parseFloat(max);
      if (!isNaN(min)) {
        if (min >= 0) {
          // 不能是负数
          val = val.replace('-', '');
        }
        if (parseFloat(val) < min) {
          val = min;
        }
      }
      if (!isNaN(max)) {
        if (parseFloat(val) > max) {
          val = max;
        }
      }
      // 大于
      if (!isNaN(moreThen)) {
        if (parseFloat(val) <= moreThen) {
          val = '';
        }
      }
      // 小于
      if (!isNaN(lessThen)) {
        if (parseFloat(val) >= lessThen) {
          val = '';
        }
      }
      // 精度判断,补0操作
      if (!isNaN(decimal) && parseFloat(target.value) > 0) {
        val = parseFloat(target.value).toFixed(decimal);
      }
    }
    if (this.el.__vue__) {
      this.el.__vue__.$emit('input', val);
    }
  }
}

function clearExtraDot(val) {
  let index = val.indexOf('.');
  let leftVal = val.substring(0, index + 1);
  let rightVal = val.substring(index + 1, val.length);
  return leftVal + rightVal.replace('.', '');
}

function install(Vue) {
  Vue.directive('number', directive);
}

export default {
  install: install,
};
