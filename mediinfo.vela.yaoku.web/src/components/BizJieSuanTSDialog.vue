<template>
  <md-dialog
    title=""
    width="430px"
    height="auto"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :show-header="false"
  >
    <!-- <div class="biz-jiesuants-content__title">诊间结算</div> -->
    <div class="biz-jiesuants-box">
      <md-icon name="tixing-s" class="biz-jiesuants-content__icon"></md-icon>
      <div>
        <div class="biz-jiesuants-content">
          <div class="biz-jiesuants-content__label">
            当前药品效期小于等于六个月，确定入库?
          </div>
        </div>
      </div>
    </div>
    <template v-slot:footer class="biz-dialog-footer">
      <md-button type="primary" plain @click="handleCaoZuo('del')">
        删除药品
      </md-button>
      <md-button type="primary" plain @click="handleCaoZuo('cancel')">
        取消
      </md-button>
      <md-button type="primary" @click="handleCaoZuo('confirm')">
        确定
      </md-button>
    </template>
  </md-dialog>
</template>

<script>
export default {
  name: 'biz-feiyongxmxz-dialog',
  data() {
    return {
      dialogVisible: false,
      cellRef: null,
    };
  },
  methods: {
    async showDialog(cellRef) {
      this.cellRef = cellRef;
      this.dialogVisible = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    // handleCancel() {
    //   this.handleClose();
    //   this.reject();
    // },
    // handleZuanZiFei() {
    //   this.handleClose();
    //   this.resolve(true);
    // },
    handleCaoZuo(type) {
      this.handleClose();
      this.$emit('handleYPXQMEthod', type, this.cellRef);
      this.resolve();
    },
    handleClose() {
      this.dialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.biz-jiesuants-content {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: var(--md-font-2);
  + .biz-jiesuants-content {
    margin-top: 8px;
  }
  &__title {
    padding: 8px;
    color: #303133;
    font-size: var(--md-font-4);
    line-height: 1;
  }
  &__icon {
    margin-right: getCssVar('spacing-2');
    color: #f90;
    font-size: 24px;
  }
}

.biz-jiesuants-box {
  padding: 8px;
  padding-top: getCssVar('spacing-3');
  padding-bottom: getCssVar('spacing-3');
  display: flex;
  align-items: center;
}

.biz-jiesuants-content-box {
  padding: 8px;
}
.biz-dialog-footer {
  padding: 0 8px;
}
</style>
