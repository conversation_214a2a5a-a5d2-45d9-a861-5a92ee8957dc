<template>
  <ul ref="tagList" :class="prefixClass('biz-tag-list')">
    <template v-for="(tag, index) in lists">
      <li
        v-if="hideStartIndex > index"
        :key="index"
        :class="prefixClass('biz-tag-list__tag')"
        @click="handleClickTag(tag)"
      >
        <!-- {{ tag._label }} -->
        <YaoPinShow
          :styleData="tag.xianShiXX ? tag.xianShiXX : {}"
          :yaoPinMC="tag.yaoPinMC"
        />
      </li>
    </template>

    <li
      v-if="showMore || haveMore"
      ref="tagMore"
      :class="prefixClass('biz-tag-list__more')"
      @click="handleClickMore($event)"
    >
      <slot v-if="$slots.more" name="more"></slot>
      <span v-else>...</span>
    </li>

    <object
      ref="resizeProxy"
      type="text/html"
      :class="prefixClass('biz-tag-list__resizeProxy')"
    ></object>
  </ul>
</template>

<script>
import YaoPinShow from '@/components/YaoPinShow.vue';
import { debounce } from 'lodash';
const getDeepValue = (data, keyString) => {
  let val = data;
  const keys = keyString.split('.');
  for (let i = 0; i < keys.length; i++) {
    val = val[keys[i]];
  }
  return val;
};
export default {
  name: 'biz-tag-list',
  props: {
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    labelKey: {
      type: String,
      default: 'label',
    },
    showMore: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      lists: [],
      hideStartIndex: null,
      haveMore: true,
      resizeEvent: null,
    };
  },
  watch: {
    list: {
      handler: function (val) {
        if (!Array.isArray(val)) return;
        this.initList(val);
        this.hideStartIndex = this.list.length;
        this.computedHideStartIndex();
      },
      deep: true,
      immediate: true,
    },
    labelKey: {
      handler: function () {
        this.initList(this.list);
        this.computedHideStartIndex();
      },
    },
  },
  mounted() {
    this.resizeEvent = debounce(() => {
      this.computedHideStartIndex();
    }, 100);
    this.$refs.resizeProxy.contentDocument.defaultView.addEventListener(
      'resize',
      this.resizeEvent,
    );
  },
  activated() {
    // this.$refs.resizeProxy.contentDocument.defaultView.addEventListener(
    //   'resize',
    //   this.resizeEvent,
    // );
  },
  methods: {
    handleClickTag(tag) {
      this.$emit('click-tag', tag);
    },
    handleClickMore(event) {
      const moreList = this.list.slice(this.hideStartIndex);
      this.$emit('clickMore', { event, moreList });
    },
    initList(list) {
      this.lists = list.map((l) => {
        let label;
        if (l !== null && typeof l === 'object') {
          label = getDeepValue(l, this.labelKey);
        } else {
          label = l;
        }
        return {
          ...l,
          _label: label,
        };
      });
    },
    computedHideStartIndex() {
      this.haveMore = true;
      this.hideStartIndex = this.list.length;
      this.$nextTick(() => {
        const tagList = this.$refs.tagList;
        const tags = tagList.querySelectorAll(
          '.' + this.prefixClass(`biz-tag-list__tag`).trim(),
        );
        const tagMore = this.$refs.tagMore;
        const tagListBound = tagList.getBoundingClientRect();
        const tagMoreBound = tagMore.getBoundingClientRect();
        let flag = false;
        for (let i = 0; i < tags.length; i++) {
          const tagBound = tags[i].getBoundingClientRect();
          const right =
            tagListBound.width -
            (tagBound.left - tagListBound.left + tagBound.width);

          if (right < tagMoreBound.width && !flag) {
            this.hideStartIndex = i;
            flag = true;
          }
        }
        this.haveMore = flag;
      });
    },
  },
  components: {
    YaoPinShow,
  },
};
</script>

<style scoped lang="scss">
.#{$md-prefix}-biz-tag-list {
  position: relative;
  white-space: nowrap;
  &__tag,
  &__more {
    display: inline-block;
    margin-right: 4px;
    padding: 0px 12px 0px 8px;
    border-radius: 2px;
    background: #f0f5fb;
    line-height: 20px;
    font-size: 14px;
    color: #222;
  }
  &__more {
    cursor: pointer;
  }
  &__resizeProxy {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 0;
  }
}
</style>
