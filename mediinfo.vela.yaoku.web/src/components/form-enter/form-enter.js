const directive = {
  mounted(el, binding, vnode) {
    const vm = vnode.ctx.proxy;
    el.keyEnter = new KeyEnter(el, binding, vm);
    el.keyEnter.init();
  },
  unmounted(el) {
    el.keyEnter.destroy();
    el.keyEnter = null;
  },
};
/**
 * 1.如果使用enter-sort 属性，请直接将enter-sort属性添加在对应的组件上
 * 2.支持修饰符auto来处理外部自动新增一行的问题
 * 3.支持传一个值进来，这个值必须是一个函数，
 */
class KeyEnter {
  constructor(el, binding, vm) {
    this.el = el;
    this.vm = vm;
    this.binding = binding;
    this.callback = binding.value;
    this.modifiers = this.binding.modifiers;
    this.selectorList = [];
    this.activeIndex = -1; //当前聚焦的节点索引值
    this.selector = '[enter-sort],[enterIndex]'; //选择器属性
    this.formSelector = 'input.' + this.cssPrefix + '-input__inner';
    this.isAttrSelect = false; //是否是通过enter-sort来获取的选择器
    this.handleEnter = this.handleEnter.bind(this);
    this.dateEnter = this.dateEnter.bind(this);
    this.cascaderEnter = this.cascaderEnter.bind(this);
    this.keepIndex = -1;
    this.cssPrefix = null;
    this.hasAutocomplete = false;
  }
  init() {
    this.selectorList = this.getSelectorList();
    const elVue = getVueInstance(this.el);
    if (elVue) {
      elVue.toNextByIndex = this.toNextByIndex.bind(this);
    } else {
      this.el.toNextByIndex = this.toNextByIndex.bind(this);
    }
    this.el.updateIndexByElement = this.updateIndexByElement.bind(this);
    this.el.addEventListener('keydown', this.handleEnter);
  }
  destroy() {
    this.selectorList = null;
    this.el.updateIndexByElement = null;
    const elVue = getVueInstance(this.el);
    if (elVue) {
      elVue.toNextByIndex = null;
    } else {
      this.el.toNextByIndex = null;
    }
    this.el.removeEventListener('keydown', this.handleEnter);
    window.removeEventListener('keydown', this.dateEnter);
    window.removeEventListener('keydown', this.cascaderEnter);
  }
  toNextByIndex(index) {
    if (index === -1) {
      this.activeIndex = -1;
    } else {
      this.activeIndex = index - 1;
    }
    this.toNext();
  }
  getCssPrefix() {
    let data = this.selectorList.find((item) => getVueInstance(item));
    if (data) {
      this.cssPrefix = getVueInstance(data).cssPrefix;
    } else {
      this.cssPrefix = null;
    }
  }
  //获取选择器列表
  getSelectorList() {
    const selectorList =
      Array.from(this.el.querySelectorAll(this.selector)) || [];
    if (!selectorList.length == 0) {
      this.isAttrSelect = true;
      //先做排序再做去重
      selectorList.sort((a, b) => {
        return this.getEnterSortValue(a) - this.getEnterSortValue(b);
      });
      //去除因为$attrs 造成的属性传递问题,去重
      for (let i = 0; i < selectorList.length; i++) {
        for (let j = i + 1; j < selectorList.length; j++) {
          if (
            this.getEnterSortValue(selectorList[i]) ==
            this.getEnterSortValue(selectorList[j])
          ) {
            selectorList.splice(j, 1);
            j--;
          }
        }
      }
      return selectorList;
    } else {
      //直接返回所有的表单元素
      return Array.from(this.el.querySelectorAll(this.formSelector));
    }
  }
  //获取当前聚焦的节点的索引
  getActiveIndex(el) {
    const activeEl = el || document.activeElement;
    let index = this.selectorList.findIndex((item) => {
      return item.contains(activeEl);
    });
    return index;
  }
  getEnterSortValue(el) {
    return el.getAttribute('enter-sort') || el.getAttribute('enterIndex');
  }
  //获取可聚焦的表单元素
  getAllowFocusEl(el) {
    //如果标签类型不是 input button textarea 找子级
    if (
      el.tagName != 'INPUT' &&
      el.tagName != 'BUTTON' &&
      el.tagName != 'TEXTAREA'
    ) {
      return el.querySelectorAll('input,textarea')[0];
    } else {
      return el;
    }
  }
  handleEnter(event) {
    if (event.key != 'Enter' || ![13, 108].includes(event.keyCode)) return;
    event.stopPropagation();
    event.preventDefault();
    //自动新增一行选择器处理
    if (this.modifiers.auto) {
      this.selectorList = this.getSelectorList();
    }
    if (!this.cssPrefix) {
      this.getCssPrefix();
      if (this.el.querySelector(`.${this.cssPrefix}-autocomplete`)) {
        this.hasAutocomplete = true;
      }
    }
    this.activeIndex = this.getActiveIndex();
    if (this.callback && typeof this.callback === 'function') {
      this.callback({
        el: document.activeElement,
        length: this.selectorList.length,
        activeIndex: this.activeIndex,
        callback: () => {
          this.toNext();
        },
      });
    } else {
      this.toNext();
    }
  }
  getNodeVue(el) {
    const targetVue = getVueInstance(el);
    if (targetVue) {
      return targetVue;
    } else {
      return getVueInstance(el.parentNode);
    }
  }
  //是否是指定的类型
  isNodeType(el, field) {
    return el.className.indexOf(field) > -1;
  }
  //是否是必填的
  isRequire(el) {
    let value;
    const elVue = getVueInstance(el);
    if (el) {
      if (elVue) {
        value = elVue.value;
      } else {
        value = el.value;
      }
    }
    return el.getAttribute('require') !== null && !value;
  }
  //下拉框是否默认选中第一行
  isNoDefaultSelected(el) {
    return el.hasAttribute('no-default-select');
  }
  //处理日期的事件
  dateEnter(e) {
    if (e.key != 'Enter' || e.code != 'Enter' || e.keyCode != 13) return;
    e.stopPropagation();
    e.preventDefault();
    const data = window.customEnter || {};
    if (this.activeIndex != data.activeIndex) return;
    data.dateVue && data.dateVue.hidePicker();
    setTimeout(() => {
      this.toNext();
    }, 100);
  }
  cascaderEnter(e) {
    if (e.key != 'Enter' || e.code != 'Enter' || e.keyCode != 13) return;
    e.stopPropagation();
    e.preventDefault();
    const data = window.customEnter || {};
    if (this.activeIndex != data.activeIndex && !data.cascaderTag) return;
    setTimeout(() => {
      this.toNext();
    }, 100);
  }
  //是否是禁用状态
  isDisabled(v) {
    if (v) {
      return v.disabled || v.$attrs.disabled; //修复日期时间选择器，disabled没有绑定在自己身上的问题处理
    }
    return false;
  }
  updateIndexByElement(el) {
    this.keepIndex = this.getActiveIndex(el);
  }
  //处理用户直接选择的日期类型的组件
  toNext() {
    //判断如果全局的日期面板关闭
    if (window.customEnter) {
      //解绑
      window.customEnter = null;
      window.removeEventListener('keydown', this.dateEnter);
      window.removeEventListener('keydown', this.cascaderEnter);
    }
    if (this.activeIndex == -1 && this.keepIndex > -1) {
      this.activeIndex = this.keepIndex;
      this.keepIndex = -1;
    }
    const selectorList = this.selectorList;
    const len = selectorList.length;
    //处理上一次聚焦点不是已有的
    if (this.activeIndex > -1) {
      const beforeEl = selectorList[this.activeIndex]; //上一个聚焦节点
      const beforeElVue = this.getNodeVue(beforeEl);
      // 首先判断是否是必填的 ,必填无值的，直接返回
      // select框默认是会选中第一个的，如果是必填的select框允许跳过必填
      if (this.isRequire(beforeEl)) return;

      //判断是否是md-select-table组件
      if (
        this.isNodeType(beforeEl, `${this.cssPrefix}-select-table`) &&
        beforeElVue
      ) {
        //如果没有visibile 的属性,则此时vue 实例指向的是
        if (beforeElVue.visible === undefined) {
          //如果没有选中值，默认选中第一个
          if (beforeElVue.currentRowIndex == -1) {
            beforeElVue.navigateOptions('next');
            const data = beforeElVue.tableData[0];
            //找到子级关闭
            beforeElVue.handleRegionNodeClick(data);
          }
        } else if (beforeElVue.visible) {
          //如果没有选值，则默认选中第一条
          if (beforeElVue.hoverIndex == -1) {
            const $parent = beforeElVue.$parent;
            const data = beforeElVue.options[0];
            $parent.navigateOptions('next');
            $parent.handleRegionNodeClick &&
              $parent.handleRegionNodeClick(data);
          }
          beforeElVue.visible = false;
        }
      }
      //判断上一次是否是select组件,如果是则需要将其关闭
      else if (this.isNodeType(beforeEl, 'select')) {
        //如果上一次select存在并且没有关闭。则关闭
        if (beforeElVue && beforeElVue.visible) {
          if (this.isNoDefaultSelected(beforeEl)) {
          } else if (beforeElVue.value == '') {
            //如果没有选值，则默认选中第一条
            beforeElVue.hoverIndex = 0;
            beforeElVue.hoverOption = beforeElVue.options[0];
            beforeElVue.selectOption();
          }
          beforeElVue.visible = false;
        }
      }
      //判断上一次是否是input-picker组件
      else if (this.isNodeType(beforeEl, 'picker') && beforeElVue) {
        beforeElVue.$children[1] &&
          beforeElVue.$children[1].hide &&
          beforeElVue.$children[1].hide();
      }
    }

    if (this.activeIndex == len - 1) {
      this.getAllowFocusEl(selectorList[0]).focus();
      return;
    }

    this.activeIndex++;

    const currentEl = selectorList[this.activeIndex]; //当前聚焦的节点
    const currentElVue = this.getNodeVue(currentEl);

    //判断当前是否是禁用状态的
    if (this.isDisabled(currentElVue)) {
      return this.toNext();
    }
    //如果是自动补全组件
    if (this.hasAutocomplete) {
      const completeList = Array.from(document.body.childNodes).filter(
        (item) => {
          if (
            item.classList &&
            item.classList.contains(`${this.cssPrefix}-autocomplete-suggestion`)
          ) {
            return item;
          }
        },
      );
      completeList.forEach((item) => {
        item.style.display = 'none';
      });
    }
    //级联选择器
    if (this.isNodeType(currentEl, 'cascader') && currentElVue) {
      currentElVue.toggleDropDownVisible &&
        currentElVue.toggleDropDownVisible(true);
      //全局绑定一个回车事件
      window.customEnter = {
        cascaderTag: true,
        activeIndex: this.activeIndex,
      };
      window.addEventListener('keydown', this.cascaderEnter);
    }

    //判断是否是md-select-table组件
    if (
      this.isNodeType(currentEl, `${this.cssPrefix}-select-table`) &&
      currentElVue
    ) {
      if (currentElVue.handleFocus) {
        currentElVue.handleFocus();
      } else {
        const children = currentElVue.$children[0];
        children.handleFocus && children.handleFocus();
      }
      this.keepIndex = this.activeIndex;
    }
    //判断当前是否是select组件，要提前打开下拉框
    else if (this.isNodeType(currentEl, 'select') && currentElVue) {
      currentElVue.visible = true;
      let value = currentElVue.value;
      let options = currentElVue.options;
      //如果 默认没有选中 并且没有值  以及没有值得select框 默认选中第一个
      if (this.isNoDefaultSelected(currentEl)) {
        currentElVue.hoverIndex = -1;
        currentElVue.hoverOption = -1;
      } else if (!value || !currentElVue.hoverOption) {
        currentElVue.hoverIndex = 0;
        currentElVue.hoverOption = options[0];
      } else {
        let index = -1;
        if (typeof value !== 'object') {
          index = options.findIndex((item) => {
            return item.value == value;
          });
        } else {
          index = options.findIndex((item) => {
            return (
              item.value[currentElVue.valueKey] == value[currentElVue.valueKey]
            );
          });
        }
        currentElVue.hoverIndex = index;
        currentElVue.hoverOption = options[index];
      }
    }

    //判断当前是否是日期组件,提前打开面板
    //处理面板选中丢失聚焦点的问题

    if (this.isNodeType(currentEl, 'date-editor')) {
      const dateVue = currentElVue.showPicker
        ? currentElVue
        : currentElVue.$parent;
      dateVue.showPicker();
      //全局绑定一个回车事件
      window.customEnter = {
        dateVue: dateVue,
        activeIndex: this.activeIndex,
      };
      window.addEventListener('keydown', this.dateEnter);
    }
    //如果是通过属性选择的，需要判断Vue实例的位置
    // if (this.isAttrSelect) {
    // } else {
    // }
    if (currentEl.tagName === 'BUTTON') {
      currentEl.click();
    } else {
      this.getAllowFocusEl(currentEl).focus();
    }
  }
}

function install(Vue) {
  Vue.directive('enter', directive);
}

export default {
  install: install,
};

function getVueInstance(el) {
  const vm = el.__vue__;
  if (vm) return vm;

  const vnode = el.__vnode;
  if (vnode) return vnode.ctx.proxy;

  return null;
}
