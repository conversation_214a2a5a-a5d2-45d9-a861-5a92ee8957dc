<template>
  <div
    :class="[classObj['wrapper'], { 'is-show': visible }]"
    :style="{ zIndex: zIndex }"
  >
    <transition name="dialog-fade" @afterLeave="afterLeave">
      <div v-show="show" :class="classObj['namespace']" :style="dialogStyle">
        <div :class="classObj['header']" class="blue-dialog-header">
          <img
            src="../../assets/images/tkbg.png"
            :class="classObj['background']"
            alt=""
          />
          <slot v-if="$slots.header" name="header"></slot>
          <div v-else :class="classObj['default']">{{ title }}</div>
        </div>
        <div :class="classObj['body']">
          <md-scrollbar style="height: 100%">
            <slot></slot>
          </md-scrollbar>
        </div>
        <div :class="classObj['footer']">
          <md-checkbox
            v-if="isShowCheckBox"
            v-model="baoHanYKKCL"
            style="float: left"
            >包含药库库存为0</md-checkbox
          >
          <md-button type="primary" class="buttonstyle" plain @click="close"
            >取消</md-button
          >
          <md-button
            type="primary"
            plain
            @click="handleAllAccept"
            class="yyBTN buttonstyle"
            v-if="isShowYY"
            >全部应用</md-button
          >
          <md-button
            :loading="saveLoading"
            class="buttonstyle"
            type="primary"
            @click="submit"
            >{{ saveText }}</md-button
          >
        </div>
      </div>
    </transition>
    <transition name="mask-fade">
      <div
        v-show="show"
        :class="classObj['mask']"
        @click="handleClickMask"
      ></div>
    </transition>
  </div>
</template>

<script>
import { MdButton, MdScrollbar, useNamespace } from '@mdfe/medi-ui';

const getPx = (target) => {
  if (typeof target === 'string') {
    let n = target.match(/\d*/);
    return `${Number(n[0])}px`;
  } else {
    return `${target}px`;
  }
};

export default {
  name: 'bmis-blue-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: [String, Number],
      default: '432px',
    },
    height: {
      type: [String, Number],
      default: '280px',
    },
    appendToBody: {
      type: Boolean,
      default: false,
    },
    closeOnClickModal: {
      type: Boolean,
      default: true,
    },
    saveLoading: {
      type: Boolean,
      default: false,
    },
    saveText: {
      type: String,
      default: '确定',
    },
    isShowYY: {
      type: Boolean,
      default: false,
    },
    isShowCheckBox: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      baoHanYKKCL: 0,
      show: true,
      zIndex: 2000,
    };
  },
  computed: {
    dialogStyle() {
      return {
        width: getPx(this.width),
        height: getPx(this.height),
        zIndex: this.zIndex,
      };
    },
    classObj() {
      const ns = useNamespace('blue-dialog');
      return {
        namespace: ns.b(),
        wrapper: ns.e('wrapper'),
        mask: ns.e('mask'),
        header: ns.e('header'),
        body: ns.e('body'),
        footer: ns.e('footer'),
        background: ns.e('header__background'),
        default: ns.e('header__default'),
      };
    },
  },
  watch: {
    visible: {
      handler: function (val) {
        this.show = val;
        this.baoHanYKKCL = false;
      },
      immediate: true,
    },
  },
  created() {
    // definePopupManager.register('bmis-dialog', this)
  },
  beforeDestroy() {
    // definePopupManager.deregister('bmis-dialog')
  },
  mounted() {
    if (this.appendToBody) {
      document.body.appendChild(this.$el);
    }
  },
  destroyed() {
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  methods: {
    submit() {
      this.$emit('submit', this.baoHanYKKCL);
    },
    handleAllAccept() {
      this.$emit('handleAllAccept');
    },
    handleClickMask() {
      if (!this.closeOnClickModal) return;
      this.close();
    },
    close() {
      this.$emit('close');
      this.show = false;
    },
    afterLeave() {
      this.$emit('close');
      this.$emit('update:visible', false);
    },
  },
  components: {
    'md-scrollbar': MdScrollbar,
    'md-button': MdButton,
  },
};
</script>
<style scoped lang="scss">
@include c('blue-dialog') {
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 2;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);

  @include element('wrapper') {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    visibility: hidden;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    &.is-show {
      visibility: visible;
    }
    @include element('mask') {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
    }
  }
  @include element('header') {
    position: relative;
    height: 64px;
    flex-shrink: 0;
    // background: rgb(var(--md-color-5));
    background: linear-gradient(
      90deg,
      rgb(var(--md-color-6)) 0%,
      rgb(var(--md-color-5)) 50%,
      rgb(var(--md-color-4)) 100%
    );
    // @include md-linear((color-6, color-5, color-4), to right);
    @include ee('background') {
      position: absolute;
      height: 100%;
      width: 100%;
    }
    @include ee('default') {
      text-align: center;
      line-height: 64px;
      font-weight: 500;
      font-size: 18px;
      letter-spacing: 2px;
      color: #fff;
    }
  }
  @include element('body') {
    padding: 8px;
    flex: 1;
    min-height: 0;
    min-width: 0;
  }
  @include element('footer') {
    padding: 0 16px 8px;
    text-align: right;
    font-size: 0;

    .buttonstyle {
      min-width: 64px;
      margin-left: 8px;
    }
  }
  // @include component('button') {
  //   min-width: 64px;
  //   margin-left: 8px;
  // }
}

.dialog-fade-enter-active {
  animation: dialog-fade-in 0.3s;
}

.dialog-fade-leave-active {
  animation: dialog-fade-out 0.3s;
}

.mask-fade-enter-active {
  animation: mask-fade-in 0.3s;
}

.mask-fade-leave-active {
  animation: mask-fade-out 0.3s;
}

@keyframes dialog-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes dialog-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}
@keyframes mask-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes mask-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.yyBTN {
  width: 92px;
}
</style>
