<template>
  <div
    ref="viewer"
    style="width: 100%; height: 100%"
    data-bind="dxReportViewer: $data"
    :class="
      prefixClass([
        {
          'show-report-toolbar': !toolbarVisible,
        },
      ])
    "
  ></div>
</template>

<script>
import ko from 'knockout';
import { debounce, pick } from 'lodash';
import qs from 'qs';
import { getPrintConfig } from './browserMethod';

import { ajaxSetup } from '@devexpress/analytics-core/analytics-utils';
import 'devexpress-reporting/dx-webdocumentviewer';
import { PreviewElements } from 'devexpress-reporting/dx-webdocumentviewer';

//打印报表插件
import { logger } from '@/service/log';
import '@devexpress/analytics-core/dist/css/dx-analytics.common.css';
import '@devexpress/analytics-core/dist/css/dx-analytics.light.css';
import lyra from '@mdfe/lyra';
import 'devexpress-reporting/dist/css/dx-webdocumentviewer.css';
import 'devextreme/dist/css/dx.light.css';
import 'jquery-ui/themes/base/all.css';
const decodeUrl = (data) => {
  if (data === decodeURIComponent(data)) {
    return encodeURIComponent(data);
  } else {
    return data;
  }
};
const getStorageValueByKey = (keys) => {
  if (typeof keys === 'string') {
    return decodeUrl(localStorage.getItem(keys));
  } else if (Array.isArray(keys)) {
    let key = keys.find((item) => {
      return localStorage.getItem(item);
    });
    return decodeUrl(localStorage.getItem(key));
  }
  return null;
};
const defaultReportHeaders = () => {
  return {
    gongNengID: getStorageValueByKey(['GongNengID', 'gongNengID']),
    shuRuMLX: getStorageValueByKey(['ShuRuMLX', 'shuRuMLX']),
    weiZhiID: getStorageValueByKey(['WeiZhiID', 'weiZhiID']),
    weiZhiMC: getStorageValueByKey(['WeiZhiMC', 'weiZhiMC']),
    keShiID: getStorageValueByKey(['KeShiID', 'keShiID']),
    keShiMC: getStorageValueByKey(['KeShiMC', 'keShiMC']),
    bingQuID: getStorageValueByKey(['BingQuID', 'bingQuID']),
    bingQuMC: getStorageValueByKey(['BingQuMC', 'bingQuMC']),
    jiGouID: getStorageValueByKey(['JiGouID', 'jiGouID']),
    jiGouMC: getStorageValueByKey(['JiGouMC', 'jiGouMC']),
    jiGouLX: getStorageValueByKey(['JiGouLX', 'jiGouLX']),
    shangJiJGID: getStorageValueByKey(['ShangJiJGID', 'shangJiJGID']),
  };
};

export default {
  name: 'print-report',
  props: {
    //是否展示工具栏
    toolbarVisible: {
      type: Boolean,
      default: false,
    },
    //预览模式翻页是否平铺
    showMultipagePreview: {
      type: Boolean,
      default: true,
    },
    //报表的打印类型ID
    id: {
      type: String,
      required: true,
      default: '',
    },
    //访问报表请求的入参
    params: {
      type: Object,
      default: () => ({}),
    },
    httpClient: {
      type: Function,
      required: true,
      default: null,
    },
    token: {
      type: String,
      required: true,
      default: '',
    },
    headers: {
      type: Object,
      default: () => {
        return lyra.getShareDataSync();
      },
    },
  },
  data() {
    return {
      previewModel: ko.observable(),
      isInit: false,
      printConfig: null,
    };
  },
  watch: {
    params: {
      handler() {
        this.init();
      },
      deep: true,
    },
    id: {
      handler() {
        this.init();
      },
      deep: true,
    },
  },
  async mounted() {
    this.printConfig = await getPrintConfig(this.httpClient);
    this.init();
  },
  beforeDestroy() {
    this.destroyNode();
  },
  methods: {
    init: debounce(async function () {
      const id = this.id;
      const params = this.params;
      if (!id || !this.httpClient) return;
      if (this.isInit) {
        this.destroyNode();
        this.isInit = false;
      }
      try {
        const printInfo = await this.getDaYinPZXXByID(id);
        const BeforeRender = await this.onBeforeRender();
        this.printInfo = printInfo;
        const callbacks = {
          BeforeRender,
          DocumentReady: this.onDocumentReady,
          CustomizeMenuActions: this.CustomizeMenuActions, //配置工具栏里面的按钮属性
          CustomizeElements: this.CustomizeElements,
        };
        const viewerOptions = {
          reportUrl: ko.observable(
            `${printInfo.moBanID}?ds=${printInfo.shuJuYID}&${qs.stringify(params)}`,
          ), // 用于指定应用程序启动时要在 Web 文档查看器中打开的报告的 URL
          viewerModel: this.previewModel,
          callbacks: callbacks,
          requestOptions: {
            //用于处理来自服务器端 Web 文档查看器的请求的选项
            host: this.printConfig.previewUrl + printInfo.fuWu,
            invokeAction: '/DXXRDV',
          },
        };
        ko.applyBindings(viewerOptions, this.$refs.viewer, () => {
          this.isInit = true;
        });
      } catch (error) {
        logger.error(error);
      }
    }, 300),
    async onBeforeRender() {
      const token = await this.getToken();
      let header = this.headers;
      if (!header.JiGouID || header.JiGouID == 'null') {
        header = pick(this.httpClient.defaults.headers, [
          'caiDanID',
          'gongNengID',
          'ShuRuMLX',
          'WeiZhiID',
          'WeiZhiMC',
          'KeShiID',
          'KeShiMC',
          'BingQuID',
          'BingQuMC',
          'JiGouID',
          'JiGouMC',
          'jiGouLX',
          'ShangJiJGID',
        ]);
      }
      ajaxSetup.ajaxSettings = {
        headers: {
          Authorization: token,
          ...header,
        },
      };
    },
    async getToken() {
      if (window.__POWERED_BY_QIANKUN__) {
        const token = await lyra.getAccessToken();
        return token.token_type + ' ' + token.access_token;
      } else {
        return this.token;
      }
    },
    onDocumentReady: function () {
      this.$emit('isReady');
    },
    destroyNode() {
      ko.cleanNode(this.$refs.viewer);
    },
    CustomizeElements(s, e) {
      if (!this.toolbarVisible) {
        //是否展示配置工具栏
        var toolbarPart = e.GetById(PreviewElements.Toolbar);
        var toolbarPartIndex = e.Elements.indexOf(toolbarPart);
        e.Elements.splice(toolbarPartIndex, 1);
      }
      //配置右侧选项卡面板
      var panelPart = e.GetById(PreviewElements.RightPanel);
      var panelPartIndex = e.Elements.indexOf(panelPart);
      e.Elements.splice(panelPartIndex, 1);
    },
    CustomizeMenuActions(s, e) {
      //HACK 目前不确定请求的回调函数放到哪里
      setTimeout(() => {
        //页面缩放比例调到1
        s.previewModel?.reportPreview?.zoom(1);
        //设置默认平铺的预览方式
        if (this.showMultipagePreview) {
          var reportPreview = s.GetPreviewModel().reportPreview;
          reportPreview.showMultipagePreview(true);
        }
      }, 500);
    },
    /**
     * 获取当前报表内的参数
     * @param {Object} params
     * @param {String} params.columnName  待查询的名称
     */
    async getCurrentReportValue(params) {
      const id = this.id;
      let printInfo = null;
      let request = null;
      if (this.printConfig.previewUrl == '/') {
        //旧版请求地址
        printInfo = await this.getDaYinPZXXByID(id);
        request = `/${printInfo.fuWu}/api/v1.0/ReportPrint/getcurrentcolumnvalue`;
      } else {
        //新版请求地址
        request = `${this.printConfig.previewUrl}api/Report/getcurrentcolumnvalue`;
      }
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const reportId = this.previewModel().reportPreview.reportId;
          if (reportId) {
            params.reportId = reportId;
            this.httpClient
              .get(request, {
                params,
              })
              .then((response) => {
                resolve(response);
              })
              .catch(() => {
                reject();
              });
          }
        }, 1000);
      });
    },
    // 打印
    async print(Copies) {
      const documentId = this.previewModel().reportPreview.documentId;
      const reportId = this.previewModel().reportPreview.reportId;
      return this.httpClient.post(this.printConfig.silentUrl, {
        ReportDocumentId: documentId,
        ReportId: reportId,
        ServiceName: this.printInfo.fuWu,
        Copies: Copies || 1,
      });
    },

    async getDaYinPZXXByID(id) {
      const daYinJiList = await this.httpClient.get(
        this.printConfig.printerUrl,
      );
      return this.httpClient.get(
        `/mediinfo-lyra-didaima/api/v1/DaYin/getPeiZhiXXByDYSX?daYinLXID=${id}&daYinJMC=${daYinJiList.join(
          ',',
        )}`,
      );
    },
  },
};
</script>
