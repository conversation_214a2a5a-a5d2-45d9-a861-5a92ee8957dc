//浏览器壳方法
import { getClientIP, getMacAddress } from '@mdfe/lyra';
/**
 * 获取浏览器壳大版本号
 * @example Vue.prototype.$getVersion = await getVersion(request)
 * @param {*} request 项目中封装的接口请求方法
 * @returns
 */
export async function getVersion(request) {
  let res = null;
  if (window.jsObj) {
    res = await request.get('http://medi/api/system');
  } else {
    res = await request.get('http://localhost:18800/api/system');
  }
  const data = JSON.parse(res);
  return data ? data.split('.')[0] : '';
}

/**
 * 获取打印配置
 * @param {*} request 项目中封装的接口请求方法
 * @returns
 */
export async function getPrintConfig(request) {
  let PrintConfig = {
    silentUrl: 'http://medi/api/Print',
    printerUrl: 'http://localhost:18800/api/print',
    previewUrl: '/',
  };
  try {
    const version = await getVersion(request);
    if (version === '2') {
      PrintConfig = {
        silentUrl: 'http://medi/api/Report/Print',
        printerUrl: 'http://localhost:18800/api/print',
        previewUrl: 'http://medi/',
      };
    } else if (version === '3') {
      PrintConfig = {
        silentUrl: 'http://localhost:18800/api/Report/Print',
        printerUrl: 'http://localhost:18800/api/print',
        previewUrl: 'http://localhost:18800/',
      };
    }
    return PrintConfig;
  } catch (e) {
    return PrintConfig;
  }
}

/**
 * 获取IP地址
 * @param {*} request 项目中封装的接口请求方法
 * @param {*} version 浏览器版本号
 * @returns
 */
export async function getIP(request) {
  const clientIP = await getClientIP();
  if (clientIP != '') return clientIP;
  let ip = null;
  if (window.jsObj && window.jsObj.getIP) {
    ip = await jsObj.getIP();
  } else {
    ip = await await getClientIP();
  }
  return ip;
}

/**
 * 获取MAC地址
 * @param {*} request 项目中封装的接口请求方法
 * @param {*} version 浏览器版本号
 * @returns
 */
export async function getMac(request) {
  const Mac = await getMacAddress();
  if (Mac != '') return Mac;
  let macaddress = null;
  if (window.jsObj && window.jsObj.getMac) {
    macaddress = await jsObj.getMac();
  } else {
    macaddress = await getMacAddress();
  }
  return macaddress;
}

/**
 * 获取计算机名称
 * @param {*} request 项目中封装的接口请求方法
 * @param {*} version 浏览器版本号
 * @returns
 */
export async function getMachineName(request) {
  let machineName = null;
  if (window.jsObj && window.jsObj.getMachineName) {
    machineName = await jsObj.getMachineName();
  } else {
    machineName = await request.get(
      'http://localhost:18800/api/system/getMachineName',
    );
  }
  return machineName;
}

/**
 * 字符串转base64
 * @param {*} str
 * @returns
 */
export function toBase64(str) {
  const encode = encodeURI(str);
  const base64 = btoa(encode);
  return base64;
}
