import qs from 'qs';
import { getPrintConfig } from './browserMethod';

class PrintReport {
  constructor(httpClient) {
    this.httpClient = httpClient;
    this.printConfig = null;
    this.setPrintConfig();
  }

  async setPrintConfig() {
    if (!this.printConfig) {
      this.printConfig = await getPrintConfig(this.httpClient);
    }
  }

  async getDaYinPZXXById(id) {
    const daYinJiList = await this.httpClient.get(this.printConfig.printerUrl);
    const url = `/mediinfo-lyra-didaima/api/v1/DaYin/getPeiZhiXXByDYSX?daYinLXID=${id}&daYinJMC=${daYinJiList.join(
      ',',
    )}`;
    return this.httpClient.get(url);
  }

  async printByUrl(id, params, others = {}) {
    const printInfo = await this.getDaYinPZXXById(id);
    const args = {
      ServiceName: printInfo.fuWu,
      ReportUrl: `${printInfo.moBanID}?ds=${printInfo.shuJuYID}&${qs.stringify(params)}`,
      Copies: others.Copies || 1,
    };
    args.FileName = others.fileName;
    return this.httpClient.post(this.printConfig.silentUrl, args);
  }

  /**
   *
   * @param {*} printList { id: [ params1, params2 ] } id-打印类型id  params-打印接口入参
   */
  async printListByUrl(printList) {
    let request = [];
    for (let id in printList) {
      if (Object.hasOwnProperty.call(printList, id)) {
        const printInfo = await this.getDaYinPZXXById(id);
        printList[id].forEach(async (params) => {
          const args = {
            ServiceName: printInfo.fuWu,
            ReportUrl: `${printInfo.moBanID}?ds=${printInfo.shuJuYID}&${qs.stringify(params)}`,
          };
          request.push(this.httpClient.post(this.printConfig.silentUrl, args));
        });
      }
    }
    return Promise.all(request);
  }

  /**
   * 导出报表文件至本地
   * @param {打印类型ID} id
   * @param {参数} params
   * @param {导出文件类型0 pdf 4 word  5 xlsx} reportPrintEnum
   * @param {文件名} fileName
   * @returns
   */
  async savePrintFile(
    id,
    params,
    fileName = Date.parse(new Date()),
    reportPrintEnum = 0,
  ) {
    //文件后缀
    const fileHZList = [
      { index: 0, houZui: '.pdf' },
      { index: 4, houZui: '.docx' },
      { index: 5, houZui: '.xlsx' },
    ];
    //1.获取配置的打印服务名
    const printInfo = await this.getDaYinPZXXById(id);

    //2.参数 将原先参数 Base64 编码
    const args = {
      url: btoa(
        `${printInfo.moBanID}?ds=${printInfo.shuJuYID}&${qs.stringify(params)}`,
      ),
    };

    //3.请求接口 设置相应类型,不设置会 导出空白pdf
    const responseType = {
      responseType: 'arraybuffer',
    };
    let url = null;
    if (this.printConfig.previewUrl == '/') {
      //旧版请求地址
      url = `/${printInfo.fuWu}/api/v1/ReportPrint/printbyurl?reportPrintEnum=${reportPrintEnum}`;
    } else {
      //新版请求地址
      url = `${this.printConfig.previewUrl}${printInfo.fuWu}/api/Report/printbyurl?reportPrintEnum=${reportPrintEnum}`;
    }

    const data = await this.httpClient.post(url, args, responseType);

    let houZui = fileHZList.find((r) => r.index == reportPrintEnum);
    //4.模拟a标签 下载接口返回的blob文件流
    const padUrl = window.URL.createObjectURL(
      new Blob([data], { type: 'application/pdf;charset=uft-8' }),
    );
    const link = document.createElement('a');
    link.href = padUrl;
    link.download = fileName;
    if (houZui) {
      link.download = link.download + houZui.houZui;
    }
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    return true;
  }
}

export default PrintReport;
