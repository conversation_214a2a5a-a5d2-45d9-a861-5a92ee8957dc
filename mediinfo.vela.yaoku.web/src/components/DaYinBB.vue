<template>
  <bmis-print-com
    v-bind="$attrs"
    :httpClient="http"
    :token="token"
    :headers="userInfoHeaders"
    ref="printCom"
  />
</template>

<script>
//TODO
// import { BmisPrintCom } from '@mdfe/bmis-print'
import { getToken } from '@/system/utils/local-cache';
import http from '@/system/utils/request';
import lyra from '@mdfe/lyra';
import { BmisPrintCom } from './print/index';
export default {
  name: 'dayinbb',
  data() {
    return {
      http,
      token: '',
      userInfoHeaders: {},
    };
  },
  async created() {
    this.token = await getToken();
    this.userInfoHeaders = lyra.getShareDataSync();

    const { shangJiYLJGID } = lyra.getShareDataSync();
    this.userInfoHeaders.ShangJiJGID = shangJiYLJGID || '';
  },
  methods: {
    print(Copies) {
      this.$refs.printCom.print(Copies);
    },
  },
  components: {
    //TODO
    'bmis-print-com': BmisPrintCom,
  },
};
</script>
<style scoped lang="scss">
.#{$md-prefix}-show-report-toolbar {
  ::v-deep .dxrd-preview.dxrd-designer-wrapper .dxrd-preview-wrapper {
    top: 8px;
    bottom: 8px;
  }
}
</style>
