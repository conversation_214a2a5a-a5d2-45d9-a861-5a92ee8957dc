export const directive = {
  mounted(el, binding, vnode) {
    const vm = vnode.ctx.proxy;
    el.keyTableEnter = new KeyTableEnter(el, binding, vm);
    el.keyTableEnter.init();
  },
  unmounted(el) {
    // el.keyTableEnter.destroy()
  },
};
/**
 * 1.如果使用enter-sort 属性，请直接将enter-sort属性添加在对应的组件上
 * 2.支持修饰符auto来处理外部自动新增一行的问题
 * 3.支持传一个值进来，这个值必须是一个函数，
 */
class KeyTableEnter {
  constructor(el, binding, vm) {
    this.el = el;
    this.vm = vm;
    this.binding = binding;
    this.isStop = false;
    this.value = binding.value;
    this.isFirstLoad = true;
    this.selectorList = [];
    this.activeIndex = -1;
    this.className =
      vm.cssPrefix + '-editable-table__cell' ||
      'mediinfo-vela-yaoku-web-editable-table__cell';
    this.currentElObj = null;
    this.nextElObj = null;
    this.query = {};
    this.handleDate = this.handleDate.bind(this);
    this.handleEnter = this.handleEnter.bind(this);
    this.resetFocusIndex = this.resetFocusIndex.bind(this);
    this.allowEditRowLength = 0;
    this.requireData = {};
  }
  init() {
    this.vm.toNext = this.toNext.bind(this);
    this.vm.stopEnter = this.stopEnter.bind(this);
    this.vm.actionEnter = this.actionEnter.bind(this);
    this.vm._next = this._next.bind(this);
    this.vm._prev = this._prev.bind(this);
    this.vm._current = this._current.bind(this);
    this.selectorList = this.getSelectorList();
    this.requireData = this.getRequireObj();
    //使用keydown来替代浏览器的tab事件
    this.el.addEventListener('keydown', this.handleEnter);
    //重置activeIndex
    this.el.addEventListener('click', this.resetFocusIndex);
  }
  destroy() {
    this.selectorList = null;
    this.currentElObj = null;
    this.nextElObj = null;
    this.vm = null;
    this.el.removeEventListener('keydown', this.handleEnter);
    this.el.removeEventListener('click', this.resetFocusIndex);
  }
  //停止回车指令
  stopEnter() {
    this.isStop = true;
  }
  //启动回车指令
  actionEnter() {
    this.isStop = false;
    this.activeIndex++;
    if (this.value && typeof this.value == 'function') {
      this.query = {
        length: this.selectorList.length,
        activeIndex: this.activeIndex,
        callback: (res) => {
          setTimeout(() => {
            if (res) {
              this.activeIndex = res.index || this.activeIndex;
              this.selectorList = this.getSelectorList();
            }
            this.toNext();
          });
        },
      };
      this.value(this.query);
    } else {
      this.toNext();
    }
  }
  handleEnter(event) {
    //是否开启指令
    if (this.isStop) return;
    if (event.code == 'Tab' || event.key == 'Tab' || event.keyCode == 9) {
      event.preventDefault();
      event.stopPropagation();
      //获取可编辑表格的可编辑节点数量
      if (this.allowEditRowLength === 0) {
        this.allowEditRowLength = this.el.querySelectorAll(
          `tbody tr:first-child .${this.className}`,
        ).length;
      }
      this.selectorList = this.getSelectorList();
      if (this.selectorList.length == 0) return;
      const currentIndex = this.getCurrentFocusIndex();
      let multiples = 0;
      let remainder = currentIndex % this.allowEditRowLength;
      if (currentIndex >= this.allowEditRowLength) {
        multiples = parseInt(currentIndex / this.allowEditRowLength);
      }
      //因为toNext会自动新增一个，所以要手动删减一个
      this.activeIndex =
        remainder + this.allowEditRowLength * (multiples + 1) - 1;
      this.toNext(null, currentIndex);
      return;
    }
    if (event.key != 'Enter' || ![13, 108].includes(event.keyCode)) return;
    event.preventDefault();
    event.stopPropagation();
    //获取可编辑表格的可编辑节点数量
    this.selectorList = this.getSelectorList();
    if (this.selectorList.length == 0) return;
    this.activeIndex = this.getCurrentFocusIndex();
    if (this.value && typeof this.value == 'function') {
      this.query = {
        length: this.selectorList.length,
        activeIndex: this.activeIndex,
        callback: (res) => {
          setTimeout(() => {
            if (res) {
              this.activeIndex = res.index || this.activeIndex;
              this.selectorList = this.getSelectorList();
            }
            this.toNext();
          });
        },
      };

      this.value(this.query);
    } else {
      this.toNext();
    }
  }
  isNoDefaultSelected(el) {
    return el.hasAttribute('no-default-select');
  }
  getRequireObj() {
    const data = {};
    const columns = this.vm.columns || [];
    columns.forEach((item) => {
      data[item.prop] = item.require === true;
    });
    return data;
  }
  isRequire(el) {
    try {
      const elVue = getVueInstance(el);
      if (elVue) {
        const trList = Array.from(
          this.el.querySelectorAll('[class *= -table__row]'),
        );
        const trIndex = trList.findIndex((item) => item.contains(el));
        const vnode = elVue.$.vnode;
        const key = vnode.key;
        const require = this.requireData[key];
        if (trIndex > -1 && require) {
          // TODO 之前从 data 获取的值未知，暂使用替代的 props
          // const data = elVue.$vnode.context.data;
          const data = this.vm.modelValue || [];
          return !(data[trIndex] && data[trIndex][key]);
        }
        return false;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
  getSelectorList() {
    return Array.from(this.el.querySelectorAll(`.${this.className}`));
  }
  getCurrentFocusIndex() {
    const currentFocusEl = document.activeElement;
    this.selectorList = this.getSelectorList();
    if (currentFocusEl.tagName === 'BODY') return this.activeIndex + 1;
    //查找父级聚焦节点
    const currentParentFocusEl = this.getParentNode(currentFocusEl);
    let index = this.selectorList.findIndex((item) => {
      return item === currentParentFocusEl;
    });
    return index;
  }
  getParentNode(el) {
    if (el && el.classList.contains(this.className)) {
      return el;
    } else {
      return this.getParentNode(el.parentElement);
    }
  }
  async toNext(index, argIndex) {
    if (this.isStop) return;
    if (index || index == 0) {
      this.activeIndex = index;
      this.selectorList = this.getSelectorList();
    }

    if (this.activeIndex >= this.selectorList.length - 1) {
      return;
    }
    let currentIndex =
      this.activeIndex + 1 > this.selectorList.length - 1
        ? 0
        : this.activeIndex + 1;
    const beforeIndex = argIndex == 0 || argIndex ? argIndex : currentIndex - 1;
    if (currentIndex > 0) {
      this.currentElObj = await this.getFormObj(this.selectorList[beforeIndex]);
    }
    if (this.currentElObj && this.currentElObj.type == 'date') {
      let dateVue = getVueInstance(this.currentElObj.el);
      if (dateVue) {
        if (!dateVue.showPicker) {
          dateVue = dateVue.$parent;
        } else {
          dateVue.hidePicker();
          dateVue.emitChange();
        }
      }
    }
    //判断上一个是否是必填
    const beforeElement = this.selectorList[beforeIndex];
    if (this.isRequire(beforeElement)) return;
    const beforeVm = getVueInstance(beforeElement);
    //切换上一个模式为预览模式
    if (beforeVm) {
      await beforeVm.endEdit();
    }

    //切换当前模式为编辑模式
    const currentElement = this.selectorList[currentIndex];
    const currentVM = currentElement && getVueInstance(currentElement);
    if (currentVM) {
      await currentVM.startEdit();
    }
    this.nextElObj = await this.getFormObj(currentElement);
    //下一个如果为时间选择器 要额外处理
    if (this.nextElObj.type == 'date') {
      document.addEventListener('keydown', this.handleDate, true);
    }
    if (this.nextElObj.type == 'select') {
      this.nextElObj.el.click();
    }
    // 如果当前为select
    if (this.currentElObj && this.currentElObj.type == 'select') {
      const currentVue = getVueInstance(this.currentElObj.el);
      if (currentVue && currentVue.visible) {
        if (this.isNoDefaultSelected(this.currentElObj.el)) {
        }
        //如果没有值，并且没有选中的，则选中第一条数据
        else if (currentVue.value == '' || currentVue.hoverIndex == -1) {
          currentVue.hoverIndex = 0;
          currentVue.hoverOption =
            currentVue.options.length > 0 ? currentVue.options[0] : null;
          if (currentVue.hoverOption) {
            currentVue.selectOption();
          }
        }
        currentVue.visible = false;
      }
    }
    if (this.nextElObj && this.nextElObj.type == 'select') {
      const nextVue = getVueInstance(this.nextElObj.el);
      nextVue.visible = true;
      if (this.isNoDefaultSelected(this.nextElObj.el) && !nextVue.value) {
        nextVue.hoverIndex = -1;
        nextVue.hoverOption = -1;
      }
      //如果 默认没有选中 并且没有值  以及没有值得select框 默认选中第一个
      else if ((nextVue.hoverIndex == -1 && !nextVue.value) || !nextVue.value) {
        nextVue.hoverIndex = 0;
        nextVue.hoverOption = nextVue.options[0];
        //有值的 找到值 添加hover
      } else {
        const value = nextVue.value;
        const options = nextVue.options || [];
        let index = options.findIndex((item) => {
          return item.value == value;
        });
        nextVue.hoverIndex = index;
        nextVue.hoverOption = nextVue.options[index];
      }
    }
    //获取当前的可输入框类型
    setTimeout(() => {
      //处理禁用状态下的新增问题，手动执行一次回车事件
      if (this.disabled(this.selectorList[currentIndex])) {
        this.activeIndex = currentIndex;
        this.toNext();
      } else {
        const focusElement = this.getEditorChild(
          this.selectorList[currentIndex],
        );
        focusElement && focusElement.focus();
      }
    });
  }
  _prev() {
    this.activeIndex = this.activeIndex - 1 < 0 ? -1 : this.activeIndex - 1;
    this.toNext();
  }
  _next() {
    // this.selectorList = this.getSelectorList()
    // this.activeIndex =
    //   this.activeIndex + 1 >= this.selectorList.length - 1
    //     ? this.selectorList.length - 1
    //     : this.activeIndex + 1
    // this.toNext()
  }
  _current() {
    this.activeIndex = this.activeIndex - 1;
    this.toNext();
  }
  disabled(el) {
    if (!el) return;
    if (el.__md__instance) {
      return el.__md__instance.disabled;
    }
    return el.getAttribute('is-disabled');
  }
  handleDate(event) {
    //是否开启指令
    if (this.isStop) return;
    if (event.key != 'Enter' || event.keyCode != 13) return;
    if (event.target.tagName !== 'BODY' && this.nextElObj.type !== 'date')
      return;
    event.preventDefault();
    event.stopPropagation();
    document.removeEventListener('keydown', this.handleDate, true);
    this.activeIndex = this.activeIndex + 1;
    if (this.value && typeof this.value == 'function') {
      this.query = {
        length: this.selectorList.length,
        activeIndex: this.activeIndex,
        callback: (res) => {
          setTimeout(() => {
            if (res) {
              this.activeIndex = res.index || this.activeIndex;
              this.selectorList = this.getSelectorList();
            }
            this.toNext();
          });
        },
      };

      this.value(this.query);
    } else {
      this.toNext();
    }
  }
  getChildNode(el) {
    return el.querySelector('.text');
  }
  getEditorChild(el) {
    if (
      el.tagName != 'INPUT' &&
      el.tagName != 'BUTTON' &&
      el.tagName != 'TEXTAREA'
    ) {
      return el.querySelectorAll('input,textarea')[0];
    } else {
      return el;
    }
  }
  getFormObj(el) {
    return new Promise((resolve) => {
      const typeArr = [
        'select-table',
        'select-trigger',
        'date',
        'radio',
        'checkbox',
        'number',
        'input',
      ];
      let obj = {
        parentEl: el,
      };
      if (el && el.children.length > 0) {
        //过滤出子级元素节点
        const elementList = Array.from(el.children).filter(
          (item) => item.nodeType == '1',
        );
        try {
          if (elementList.length > 0) {
            const ele = elementList[elementList.length - 1].children[0];
            if (ele) {
              let type = typeArr.find(
                (item) => ele.className.indexOf(item) > -1,
              );
              if (type) {
                obj.type = type;
                obj.el = ele;
              } else {
                let childEle = ele.firstChild;
                if (childEle) {
                  obj.type = typeArr.find(
                    (item) => childEle.className.indexOf(item) > -1,
                  );
                  obj.el = childEle;
                }
              }
            }
          }
        } catch (error) {}
      }
      resolve(obj);
    });
  }
  async resetFocusIndex(event) {
    const cssPrefix = this.vm.cssPrefix || 'md';
    if (
      event.target.classList.contains(
        `${cssPrefix}-editable-table__cell-text`,
      ) ||
      event.target.classList.contains(
        `${cssPrefix}-editable-table__cell-editor`,
      )
    ) {
      if (this.isFirstLoad) {
        this.isFirstLoad = false;
        this.selectorList = this.getSelectorList();
      }
      this.activeIndex = this.getCurrentFocusIndex();
      const currentElement = this.selectorList[this.activeIndex];
      this.nextElObj = await this.getFormObj(currentElement);
      const elAndType = await this.getFormObj(
        this.selectorList[this.activeIndex],
      );
      this.activeIndex--;
      if (elAndType.type == 'date') {
        document.addEventListener('keydown', this.handleDate, true);
      }
    }
  }
}
function install(Vue) {
  Vue.directive('table-enter', directive);
}

export default {
  install,
};

function getVueInstance(el) {
  if (!el) return null;
  // Vue2
  const vm = el.__vue__;
  if (vm) return vm;

  const vnode = el.__md__instance;
  if (vnode) return vnode;

  return null;
}
