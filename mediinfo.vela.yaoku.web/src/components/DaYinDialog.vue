<template>
  <md-dialog
    :title="title"
    width="90%"
    height="90%"
    :content-scrollable="false"
    :body-loading="loading"
    v-model="dialogVisible"
    :before-close="handleCancel"
  >
    <div :class="prefixClass('dialog-box')">
      <div :class="prefixClass('dialog-right')">
        <div :class="prefixClass('dialog-right-content')">
          <dayin-baobiao
            v-if="dialogVisible"
            :params="params"
            :headers="headers"
            :toolbarVisible="true"
            :id="id"
            ref="baobiao"
          />
        </div>
      </div>
    </div>
    <template v-slot:footer>
      <div class="dialog-footer">
        <md-button
          type="primary"
          plain
          :disabled="pageLoading"
          @click="handleCancel"
          >取消</md-button
        >
        <md-button
          v-show="showDaoChuBtn"
          type="primary"
          :loading="pageLoading"
          @click="handleSavePDF(0)"
          >导出pdf</md-button
        >
        <md-button
          v-show="showDaoChuBtn"
          type="primary"
          :loading="pageLoading"
          @click="handleSavePDF(5)"
          >导出excel</md-button
        >
        <md-button
          v-show="showDaoChuBtn"
          type="primary"
          :loading="pageLoading"
          @click="handleSavePDF(4)"
          >导出word</md-button
        >
        <md-button type="primary" :loading="pageLoading" @click="handleSave"
          >打印</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { MdMessageBox } from '@mdfe/medi-ui';
import DayinBaobiao from '@/components/DaYinBB';
import { printByUrl, savePrintFile } from '@/system/utils/print';
import { getUserInfo } from '@/system/utils/local-cache';

export default {
  name: '',
  props: {
    params: {
      type: Object,
      default: () => ({}), //打印参数
    },
    id: {
      type: String, //打印类型id
      require: true,
    },
    title: {
      type: String, //标题
      default: '',
    },
    showDaoChuBtn: {
      type: Boolean, //是否显示导出按钮
      default: true,
    },
  },
  data() {
    return {
      headers: {},
      pageLoading: false,
      dialogVisible: false,
      loading: false,
    };
  },
  computed: {},
  created() {
    this.headers = getUserInfo([
      'WeiZhiID',
      'WeiZhiMC',
      'KeShiID',
      'KeShiMC',
      'BingQuID',
      'BingQuMC',
      'JiGouID',
      'JiGouMC',
      'ShuRuMLX',
      'CaiDanID',
    ]);
  },
  methods: {
    //隐藏显示弹框
    async showModal() {
      this.dialogVisible = true;
    },
    //导出pdf
    async handleSavePDF(fileLX) {
      try {
        this.pageLoading = true;
        await savePrintFile(
          this.id,
          this.params,
          Date.parse(new Date()),
          fileLX,
        );
        this.$message({
          type: 'success',
          message: '导出成功！',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    async handleSave() {
      MdMessageBox.confirm('确认打印？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          this.pageLoading = true;
          await printByUrl(this.id, this.params);
          this.$message({
            type: 'success',
            message: '打印成功！',
          });
          // 快递药品回调
          if (!this.showDaoChuBtn) {
            this.$emit('savePrint');
          }
        } finally {
          this.pageLoading = false;
        }
      });
    },
    handleCancel() {
      this.dialogVisible = false;
    },
  },
  components: {
    'dayin-baobiao': DayinBaobiao,
  },
};
</script>

<style scoped lang="scss">
::v-deep .#{$md-prefix}-scrollbar__view {
  height: 100%;
  box-sizing: border-box;
}

.#{$md-prefix}-dialog-box {
  display: flex;
  height: 100%;
  padding: 0 8px;
  box-sizing: border-box;

  .#{$md-prefix}-dialog-right {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;

    &-top {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    &-content {
      flex: 1 1 auto;
      background-color: #f5f5f5;
    }
  }
}
</style>
