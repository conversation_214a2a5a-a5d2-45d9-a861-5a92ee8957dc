<template>
  <md-dialog
    v-model="visibleDialog"
    title="可编辑表格列设置"
    destroy-on-close
    size="large"
    :contentScrollable="false"
    :footer="false"
    :append-to-body="true"
    :class="prefixClass('table-set')"
    :before-close="handleClose"
    :before-save="handleSave"
  >
    <column-set
      ref="columnSet"
      show-level
      :data="columns"
      :no-recovery="level !== '0'"
      :showLevel="true"
      :column-layout="controlColumnLayout"
      :level="level"
      :loading="controlLoading"
      :extra-columns="controlExtraColumns"
      :row-key="controlRowKey"
      :class="prefixClass('column-set')"
      @handleSetConfirm="getNewColumn"
      @handleCancel="controlCancel"
      @handleRecovery="recoveryColumn"
      @level-change="levelChange"
      :close-control-on-confirm="false"
      :close-control-on-recovery="false"
      :controlLevel="controlLevel"
    >
      <template #jianPanCZSZ="{ row }">
        <md-select
          v-model="row.jianPanCZSZ"
          :disabled="row.canShuRu"
          @change="jianPanCZSZChange($event, row)"
        >
          <md-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </md-select>
      </template>
    </column-set>
  </md-dialog>
</template>

<script>
import { MdDialog, MdColumnSet, MdMessage } from '@mdfe/medi-ui';
import { cloneDeep, isEmpty } from 'lodash';
import lyra from '@mdfe/lyra';
import { logger } from '@/service/log';
import {
  getJiansuoyexxGetziduanlistByJianSuoYDM,
  postJiansuoyexxAddgerendajsyxx,
  deleteJiansuoyexxHuifucszByJiBenXXID,
} from '@/service/jianSuoYe';
export default {
  name: 'table-column-set',
  data() {
    return {
      visibleDialog: false,
      columsOld: [], //colums 初始值
      columsCSH: [], //colums 初始化的值
      controlColumnLayout: [
        'mingCheng',
        'kuanDu',
        'paiXu',
        'guDing',
        'duiQi',
        'jianPanCZSZ',
        'operate',
      ],
      controlExtraColumns: [
        {
          slot: 'jianPanCZSZ',
          label: '键盘操作设置',
          cIndex: 5,
        },
      ],
      options: [
        {
          value: 0,
          label: '默认定位',
        },
        {
          value: 1,
          label: '回车忽略',
        },
        {
          value: 2,
          label: '回车跳转',
        },
      ],
      columnsParams: null,
      columnsJiBenXX: null,
      allQuanXianOld: null,
      controlLevel: true, //显示隐藏控制级别
      controlLoading: false, //控制级别loading效果
      level: '1', //控制级别
      weiYingYCS: {
        weiYingYDM: null,
        weiYingYMC: null,
        jianSuoYDM: null,
        jianSuoYMC: null,
      },
      duiQiFSOptions: [
        { value: '1', label: '左对齐', align: 'left' },
        { value: '2', label: '右对齐', align: 'right' },
        { value: '3', label: '居中对齐', align: 'center' },
      ],
      customLevels: [
        { value: '1', label: '本用户' },
        { value: '2', label: '本机构' },
        { value: '0', label: '全部' },
      ],
    };
  },
  mounted() {
    this.customLevels = [
      { value: '1', label: '位置' },
      { value: '2', label: '本机构' },
      { value: '0', label: '全部' },
    ];
  },
  methods: {
    showModel(columns) {
      this.columns = columns;
      this.visibleDialog = true;
      const { yingYongID, yingYongMC, gongNengID, gongNengMC, WeiZhiID } =
        lyra.getShareDataSync();
      this.getColumnInit(
        { yingYongID, yingYongMC, gongNengID, gongNengMC, WeiZhiID },
        this.columns,
      );
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    handleClose(event) {
      this.visibleDialog = false;
    },
    handleSave() {
      this.resolve(this.columns);
      this.visibleDialog = false;
    },
    jianPanCZSZChange(event, row) {
      if (
        event === 0 &&
        this.columns.filter((item) => item.jianPanCZSZ === 0).length === 1
      ) {
        row.jianPanCZSZ = null;
        MdMessage.error('只能有一个默认定位');
      }
    },
    //表头初始化
    async getColumnInit(allQuanXian) {
      //获取微应用参数
      const params = {
        jianSuoYDM: allQuanXian.gongNengID,
        kongZhiJBDM: null,
      };
      this.allQuanXianOld = cloneDeep(allQuanXian);
      this.weiYingYCS = {
        weiYingYDM: allQuanXian.yingYongID,
        weiYingYMC: allQuanXian.yingYongMC,
        jianSuoYDM: allQuanXian.gongNengID + allQuanXian.WeiZhiID,
        jianSuoYMC: allQuanXian.gongNengMC,
      };
      if (isEmpty(this.columsOld)) {
        this.columsOld = cloneDeep(this.columns);
      }
      this.columnsParams = params;
      await this.getColumnData(params);
      const columnsData = this.columnsData;
      if (columnsData && !isEmpty(columnsData.peiZhiZDListDto)) {
        this.columns = this.getColumnCL(columnsData);
        this.columnsJiBenXX = columnsData.peiZhiDto;
        const kongZhiJB = this.customLevels.find(
          (item) => item.value == columnsData.peiZhiDto.kongZhiJBDM,
        );
        this.level = kongZhiJB ? kongZhiJB.value : '2';
      } else {
        this.columns = cloneDeep(this.columsOld).map((item) => {
          Object.assign(item, {
            jianPanCZSZ: null,
            canShuRu: item.type === 'text',
          });
        });
      }
    },
    //获取表头数据
    async getColumnData(params) {
      try {
        this.columnsData = await getJiansuoyexxGetziduanlistByJianSuoYDM(
          {
            jianSuoYDM: params.jianSuoYDM,
          },
          { params: params },
        );
      } catch (error) {
        logger.error(error);
      }
    },
    //根据返回表头数据处理column值
    getColumnCL(columnsData) {
      try {
        const columns = cloneDeep(this.columns);
        const peiZhiDto = columnsData.peiZhiDto;
        const kongZhiJB = this.customLevels.find(
          (item) => item.value == peiZhiDto.kongZhiJBD,
        );
        this.level = kongZhiJB ? kongZhiJB.value : '1';
        const peiZhiZDListDto = [];
        columnsData.peiZhiZDListDto.forEach((item, index) => {
          const ziduan = columns.find((col) => col.prop == item.ziDuanID);
          // 有对齐方式
          if (this.handleAlign(item.duiQiFSDM)) {
            ziduan.align = this.handleAlign(item.duiQiFSDM);
          }
          ziduan.id = item.id;
          // 有宽度赋值
          ziduan.hidden = item.xianShiKD == 0 ? true : false;
          // ziduan.xianShiPXBZ = !!item.sortable;
          ziduan.sortable = item.xianShiPXBZ ? 'custom' : false;

          if (item.xianShiKD) {
            ziduan.width = item.xianShiKD;
          }
          // 有有固定赋值
          if (peiZhiDto.zuoGuDLS > 0 && peiZhiDto.zuoGuDLS > index) {
            ziduan.fixed = 'left';
          } else if (
            peiZhiDto.youGuDLS > 0 &&
            peiZhiZDListDto.length - peiZhiDto.zuoGuDLS >= index
          ) {
            ziduan.fixed = 'right';
          }
          Object.assign(ziduan, {
            canShuRu: ziduan.type === 'text',
          });
          peiZhiZDListDto.push(ziduan);
        });
        return this.getHeaderFooter(peiZhiZDListDto);
      } catch (error) {
        console.error(error);
        logger.error(error);
        return this.columns;
      }
    },
    getHeaderFooter(peiZhiZDListDto) {
      const newColumn = peiZhiZDListDto.filter((item) => {
        return (
          item.type != 'operate' &&
          item.type != 'selection' &&
          !(item.type == 'index' || item.prop == 'xunHao')
        );
      });
      const selection = this.columns.find((item) => item.type == 'selection');
      const operate = this.columns.find((item) => item.type == 'operate');
      const index = this.columns.find(
        (item) => item.type == 'index' || item.prop == 'xunHao',
      );
      //判断列表有无多选
      if (selection) {
        newColumn.unshift(selection);
      }
      if (index) {
        newColumn.unshift(index);
      }
      if (operate) {
        newColumn.push(operate);
      }
      return newColumn;
    },
    // 设置为新的表头
    async getNewColumn(newColumn, type, done) {
      try {
        newColumn = this.getHeaderFooter(newColumn);
        const weiYingYCS = this.weiYingYCS;
        const columnsData = newColumn.filter((item) => {
          return (
            item.type != 'operate' &&
            item.type != 'selection' &&
            !(item.type == 'index' || item.prop == 'xunHao')
          );
        });

        const { yongHuMC } = lyra.getShareDataSync();
        const yongHuXM = decodeURIComponent(yongHuMC);
        // 列设置字段数据
        const peiZhiZDListDto = this.columnsData?.peiZhiZDListDto;
        const ziDianData = columnsData.map((item, index) => {
          const duiQiFS = this.handleDuiQiFS(item.align);
          const xianShiKD = item.hidden ? 0 : item.width ? item.width : null;
          const xianShiPXBZ = item.sortable ? 1 : 0;
          return {
            ziDuanID: item.prop,
            ziDuanMC: item.label,
            xianShiKD: xianShiKD,
            xianShiPXBZ: xianShiPXBZ,
            shunXuHao: index + 1,
            peiZhiID: null,
            duiQiFSDM: duiQiFS.value,
            duiQiFSMC: duiQiFS.label,
            id: item.id || null,
          };
        });

        const kongZhiJB =
          this.customLevels.find((item) => {
            return item.value == type;
          }) || this.customLevels[0];
        const zuoGuDLS = columnsData.filter(
          (item, index) => item.fixed && columnsData.length / 2 > index,
        ).length;
        const youGuDLS = columnsData.filter(
          (item, index) => item.fixed && columnsData.length / 2 < index,
        ).length;
        //判断有无基本信息,有就算编辑
        const jiBenXX = {
          id: this.columnsData?.peiZhiDto?.id ?? null,
          jiBenXXID: this.columnsData?.id ?? null,
        };
        weiYingYCS.id = this.columnsData?.id ?? null;
        const param = {
          jiBenXXByLSZCreateDto: weiYingYCS,
          peiZhiCreateDto: {
            ...jiBenXX,
            yongHuXM,
            kongZhiJBDM: Number(kongZhiJB.value),
            kongZhiJBMC: kongZhiJB.label,
            zuoGuDLS: Number(zuoGuDLS),
            youGuDLS: Number(youGuDLS),
          },
          peiZhiZDListDto: ziDianData,
        };
        await postJiansuoyexxAddgerendajsyxx(param);
        this.$message.success('保存成功');
        //关闭的时候初始化的值表头值
        // await this.controlCancel()
        done();
        this.columns = newColumn;
      } catch (error) {
        console.error(error);
        logger.error(error);
      }
    },
    //控制级别事件方法
    async levelChange(level) {
      try {
        this.controlLoading = true;
        this.columns = [...this.columns, {}];
        const kongZhiJB = this.customLevels.find((item) => item.value == level);
        this.columnsParams.kongZhiJBDM = kongZhiJB.value;
        await this.getColumnData(this.columnsParams);
        const columnsData = this.columnsData;
        if (columnsData && !isEmpty(columnsData.peiZhiZDListDto)) {
          this.columns = this.getColumnCL(columnsData);
          this.columnsJiBenXX = columnsData.peiZhiDto;
        } else {
          await this.$nextTick();
          this.columns = cloneDeep(this.columsOld);
          this.columnsJiBenXX = null;
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.controlLoading = false;
      }
    },
    // 恢复默认表头
    async recoveryColumn() {
      try {
        this.columns = [...this.columsOld, {}];
        if (this.columnsData && this.columnsData.peiZhiDto) {
          await deleteJiansuoyexxHuifucszByJiBenXXID({
            jiBenXXID: this.columnsData.peiZhiDto.jiBenXXID,
            kongZhiJBDM: this.columnsData.peiZhiDto.kongZhiJBDM,
          });
          this.$message.success('已恢复初始值！');
          await this.controlCancel();
        }
      } catch (error) {
        logger.error(error);
      }
    },
    //监听取消事件
    async controlCancel() {
      this.columnsParams.kongZhiJBDM = null;
      await this.getColumnInit(this.allQuanXianOld, cloneDeep(this.columsOld));
    },
    //对齐方式
    handleDuiQiFS(align) {
      if (align) {
        const find = this.duiQiFSOptions.find((item) => item.align == align);
        return find;
      } else {
        return {
          value: null,
          label: null,
        };
      }
    },
    handleAlign(align) {
      if (align) {
        const find = this.duiQiFSOptions.find((item) => item.value == align);
        return find.align;
      } else {
        return null;
      }
    },
  },
  components: {
    'md-dialog': MdDialog,
    'column-set': MdColumnSet,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-table-set {
  height: 100%;
  .#{$md-prefix}-column-set {
    max-height: 100%;
  }
}
</style>
