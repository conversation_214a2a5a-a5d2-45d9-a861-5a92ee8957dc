import { cloneDeep, isEmpty } from 'lodash';
import { MdMessage } from '@mdfe/medi-ui';
import lyra from '@mdfe/lyra';
import { logger } from '@/service/log';
import {
  getJiansuoyexxGetziduanlistByJianSuoYDM,
  postJiansuoyexxAddgerendajsyxx,
  deleteJiansuoyexxHuifucszByJiBenXXID,
  getYongHuLCQX,
} from '@/service/jianSuoYe';

export default {
  provide() {
    return {
      container: this,
    };
  },
  data() {
    return {
      columnsOld: [], //colums 初始值
      columsCSH: [], //colums 初始化的值
      controlColumnLayout: [
        'mingCheng',
        'kuanDu',
        'paiXu',
        'guDing',
        'duiQi',
        'operate',
      ],
      columnsParams: null,
      columnsJiBenXX: null,
      controlLevel: true, //显示隐藏控制级别
      controlLoading: false, //控制级别loading效果
      level: '3', //控制级别
      showLevel: true,
      weiYingYCS: {
        weiYingYDM: null,
        weiYingYMC: null,
        jianSuoYDM: null,
        jianSuoYMC: null,
      },
      duiQiFSOptions: [
        { value: '1', label: '左对齐', align: 'left' },
        { value: '2', label: '右对齐', align: 'right' },
        { value: '3', label: '居中对齐', align: 'center' },
      ],
      customLevels: [
        { value: '3', label: '位置' },
        { value: '2', label: '本机构' },
        { value: '0', label: '全部' },
      ],
      options: [
        {
          value: '0',
          label: '默认定位',
        },
        {
          value: '1',
          label: '回车忽略',
        },
        {
          value: '2',
          label: '回车跳转',
        },
      ],
      noRecovery: false,
      caoZuoSheZhiMap: {},
      teShuBZ: '',
      tableKey: 0,
    };
  },
  async created() {
    const { JiGouID, yongHuID } = lyra.getShareDataSync();
    const params = {
      zuZhiJGID: JiGouID,
      yongHuID: yongHuID,
    };
    const res = await getYongHuLCQX(params);
    if (res.some((q) => q.quanXianDM == '2001')) {
      this.showLevel = true;
    } else {
      this.showLevel = false;
    }
  },
  methods: {
    /**
     * 更新操作设置映射
     * 此方法用于将传入的数据更新到操作设置映射中
     * @param {Object} data - 新的操作设置数据
     */
    updateCaoZuo(data) {
      this.caoZuoSheZhiMap = data;
    },
    /**
     * 监听回车事件
     * @param {*} param0
     */
    handleTableEnter({ activeIndex, callback }) {
      const columnsWithoutText = this.columns.filter(
        (item) =>
          !item.hidden &&
          item.type !== 'check' &&
          item.type !== 'text' &&
          item.type != 'operate' &&
          item.type != 'selection' &&
          !(item.type == 'index' || item.prop == 'xunHao'),
      );
      const index = activeIndex % columnsWithoutText.length;
      const requireIndex =
        columnsWithoutText.findIndex((item) => item.caoZuoSZDM === '0') == -1
          ? 0
          : columnsWithoutText.findIndex((item) => item.caoZuoSZDM === '0');
      if (
        columnsWithoutText[index].caoZuoSZDM === '2' ||
        index === columnsWithoutText.length - 1
      ) {
        this.$refs.editTable.toNext(
          activeIndex - index + columnsWithoutText.length + requireIndex - 1,
        );
      } else {
        for (let i = 0; i < columnsWithoutText.length; i++) {
          if (i <= index) continue;
          if (columnsWithoutText[i].caoZuoSZDM === '1') {
            continue;
          } else {
            this.$refs.editTable.toNext(activeIndex - index + i - 1);
            break;
          }
        }
      }
    },
    /**
     *  键盘操作设置
     * @param {*} event
     * @param {*} row
     */
    CaoZuoSZChange(event, row) {
      this.columns.forEach((item) => {
        if (item.prop === row.prop) {
          item.caoZuoSZDM = event;
        } else {
          item.caoZuoSZDM =
            event != '1' && item.caoZuoSZDM === event ? null : item.caoZuoSZDM;
        }
      });
    },
    //表头初始化
    async getColumnInit() {
      const { yingYongID, yingYongMC, gongNengID, gongNengMC, WeiZhiID } =
        lyra.getShareDataSync();
      this.weiYingYCS = {
        weiYingYDM: yingYongID,
        weiYingYMC: yingYongMC,
        jianSuoYDM: `${gongNengID}${WeiZhiID}${this.teShuBZ}`,
        jianSuoYMC: gongNengMC,
      };
      this.columnsParams = {
        jianSuoYDM: `${gongNengID}${WeiZhiID}${this.teShuBZ}`,
        kongZhiJBDM: this.level,
      };
      if (isEmpty(this.columnsOld)) {
        this.columnsOld = cloneDeep(this.columns);
      }
      if (isEmpty(this.columnsParams)) {
        const { gongNengID, WeiZhiID } = lyra.getShareDataSync();
        this.columnsParams = {
          jianSuoYDM: gongNengID + '' + WeiZhiID,
          kongZhiJBDM: this.level,
        };
      }
      await this.getColumnData(this.columnsParams);
      const columnsData = this.columnsData;
      if (columnsData && !isEmpty(columnsData.peiZhiZDListDto)) {
        this.columns = this.getColumnCL(columnsData);
        this.columnsJiBenXX = columnsData.peiZhiDto;
        const kongZhiJB = this.customLevels.find(
          (item) => item.value == columnsData.peiZhiDto.kongZhiJBDM,
        );
        this.level = kongZhiJB ? kongZhiJB.value : '3';
      } else {
        this.columns = this.columnsOld.map((item) => {
          Object.assign(item, {
            caoZuoSZDM: null,
            caoZuoSZMC: null,
            bieMing: item.label,
            lieMingCheng: item.label,
            canShuRu: item.type === 'text' || item.type === 'check',
          });
          return item;
        });
      }
      this.tableKey++;
    },
    //获取表头数据
    async getColumnData(params) {
      try {
        this.columnsData =
          await getJiansuoyexxGetziduanlistByJianSuoYDM(params);
      } catch (error) {
        logger.error(error);
      }
    },
    //根据返回表头数据处理column值
    getColumnCL(columnsData) {
      try {
        const columns = cloneDeep(this.columnsOld);
        const peiZhiDto = columnsData.peiZhiDto;
        const kongZhiJB = this.customLevels.find(
          (item) => item.value == peiZhiDto.kongZhiJBD,
        );
        this.level = kongZhiJB ? kongZhiJB.value : '3';
        const peiZhiZDListDto = [];
        columnsData.peiZhiZDListDto.forEach((item, index) => {
          // todo:{type: 'selection',width: 60,},带勾选列的表格，第一列selection是没有prop属性的，ziduan获取是uundefined
          const ziduan = columns.find((col) => col.prop == item.ziDuanID);
          // 有对齐方式
          const align = this.handleAlign(item.duiQiFSDM);
          Object.assign(ziduan, {
            align: align ?? '',
          });
          ziduan.id = item.id;
          // 有宽度赋值
          ziduan.hidden = item.xianShiKD == 0 ? true : false;
          // ziduan.xianShiPXBZ = !!item.sortable;
          ziduan.sortable = item.xianShiPXBZ ? 'custom' : false;

          // 最小宽度没有值时，才去设置宽度，不然控制某列隐藏后，元素吃到width属性，无法自动填充表格
          if (item.xianShiKD && !ziduan.minWidth) {
            ziduan.width = item.xianShiKD;
          }
          // 有有固定赋值
          if (peiZhiDto.zuoGuDLS > 0 && peiZhiDto.zuoGuDLS > index) {
            ziduan.fixed = 'left';
          } else if (
            peiZhiDto.youGuDLS > 0 &&
            columnsData.peiZhiZDListDto.length - peiZhiDto.youGuDLS < index
          ) {
            ziduan.fixed = 'right';
          } else {
            ziduan.fixed = undefined;
          }
          Object.assign(ziduan, {
            canShuRu: ziduan.type === 'text' || ziduan.type === 'check',
            bieMing: item.xianShiMC || item.ziDuanMC,
            lieMingCheng: ziduan.label,
            label: item.xianShiMC || item.ziDuanMC || ziduan.label,
          });
          ziduan.caoZuoSZDM = item.caoZuoSZDM;
          peiZhiZDListDto.push(ziduan);
        });
        return this.getHeaderFooter(peiZhiZDListDto);
      } catch (error) {
        console.error(error);
        logger.error(error);
        return this.columns;
      }
    },
    getHeaderFooter(peiZhiZDListDto) {
      const newColumn = peiZhiZDListDto.filter((item) => {
        return (
          item.type != 'operate' &&
          item.type != 'selection' &&
          !(item.type == 'index' || item.prop == 'xunHao')
        );
      });
      const selection = this.columns.find((item) => item.type == 'selection');
      const operate = this.columns.find((item) => item.type == 'operate');
      const index = this.columns.find(
        (item) => item.type == 'index' || item.prop == 'xunHao',
      );
      //判断列表有无多选
      if (selection) {
        newColumn.unshift(selection);
      }
      if (index) {
        newColumn.unshift(index);
      }
      if (operate) {
        newColumn.push(operate);
      }
      return newColumn;
    },
    // 设置为新的表头
    async getNewColumn(newColumn, type) {
      try {
        console.log(newColumn, 'newColumn');
        const weiYingYCS = this.weiYingYCS;
        const columnsData = newColumn.filter((item) => {
          return (
            item.type != 'operate' &&
            item.type != 'selection' &&
            !(item.type == 'index' || item.prop == 'xunHao')
          );
        });
        const { yongHuMC } = lyra.getShareDataSync();
        const yongHuXM = decodeURIComponent(yongHuMC);
        const ziDianData = columnsData.map((item, index) => {
          const duiQiFS = this.handleDuiQiFS(item.align);
          const chuShiZiDuan =
            this.columnsOld.find((ot) => ot.prop === item.prop) || {};
          const xianShiKD = item.hidden
            ? 0
            : item.width
              ? item.width
              : chuShiZiDuan.width || chuShiZiDuan.minWidth;
          const xianShiPXBZ = item.sortable ? 1 : 0;
          return {
            ziDuanID: item.prop,
            ziDuanMC: item.label,
            xianShiMC: item.bieMing,
            xianShiKD: xianShiKD,
            xianShiPXBZ: xianShiPXBZ,
            shunXuHao: index + 1,
            peiZhiID: null,
            duiQiFSDM: duiQiFS.value,
            duiQiFSMC: duiQiFS.label,
            id: item.id || null,
            caoZuoSZDM: item.caoZuoSZDM,
          };
        });

        const kongZhiJB =
          this.customLevels.find((item) => {
            return item.value == type;
          }) || this.customLevels[0];
        const zuoGuDLS = columnsData.filter(
          (item, index) => item.fixed && columnsData.length / 2 > index,
        ).length;
        const youGuDLS = columnsData.filter((item, index) => {
          return item.fixed && columnsData.length / 2 < index;
        }).length;
        //判断有无基本信息,有就算编辑
        const jiBenXX = {
          id: this.columnsData?.peiZhiDto?.id ?? null,
          jiBenXXID: this.columnsData?.id ?? null,
        };
        weiYingYCS.id = this.columnsData?.id ?? null;
        const param = {
          jiBenXXByLSZCreateDto: weiYingYCS,
          peiZhiCreateDto: {
            ...jiBenXX,
            yongHuXM,
            kongZhiJBDM: Number(kongZhiJB.value),
            kongZhiJBMC: kongZhiJB.label,
            zuoGuDLS: Number(zuoGuDLS),
            youGuDLS: Number(youGuDLS),
          },
          peiZhiZDListDto: ziDianData,
        };
        await postJiansuoyexxAddgerendajsyxx(param);
        MdMessage.success('保存成功');
        await this.controlCancel();
      } catch (error) {
        console.error(error);
        logger.error(error);
      } finally {
        this.tableKey++;
      }
    },
    //控制级别事件方法
    async levelChange(level) {
      try {
        this.noRecovery = level === '0';
        this.controlLoading = true;
        this.columns = [...this.columns, {}];
        const kongZhiJB = this.customLevels.find((item) => item.value == level);
        this.columnsParams.kongZhiJBDM = kongZhiJB.value;
        await this.getColumnData(this.columnsParams);
        const columnsData = this.columnsData;
        if (columnsData && !isEmpty(columnsData.peiZhiZDListDto)) {
          this.columns = this.getColumnCL(columnsData);
          this.columnsJiBenXX = columnsData.peiZhiDto;
        } else {
          await this.$nextTick();
          this.columns = this.columnsOld.map((item) => {
            Object.assign(item, {
              caoZuoSZDM: null,
              caoZuoSZMC: null,
              bieMing: item.label,
              lieMingCheng: item.label,
              canShuRu: item.type === 'text' || item.type === 'check',
            });
            return item;
          });
          this.columnsJiBenXX = null;
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.controlLoading = false;
      }
    },
    // 恢复默认表头
    async recoveryColumn() {
      try {
        this.columns = [...this.columnsOld, {}];
        if (this.columnsData && this.columnsData.peiZhiDto) {
          await deleteJiansuoyexxHuifucszByJiBenXXID({
            jiBenXXID: this.columnsData.peiZhiDto.jiBenXXID,
            kongZhiJBDM: this.columnsData.peiZhiDto.kongZhiJBDM,
          });
          MdMessage.success('已恢复初始值！');
          await this.controlCancel();
        }
      } catch (error) {
        logger.error(error);
      } finally {
        this.tableKey++;
      }
    },
    //监听取消事件
    async controlCancel() {
      this.columnsParams.kongZhiJBDM = null;
      await this.getColumnInit();
      this.tableKey++;
    },
    //对齐方式
    handleDuiQiFS(align) {
      if (align) {
        const find = this.duiQiFSOptions.find((item) => item.align == align);
        return find;
      } else {
        return {
          value: null,
          label: null,
        };
      }
    },
    handleAlign(align) {
      if (align) {
        const find = this.duiQiFSOptions.find((item) => item.value == align);
        return find.align || '';
      } else {
        return null;
      }
    },
  },
  components: {},
};
