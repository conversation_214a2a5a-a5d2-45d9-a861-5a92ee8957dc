<script lang="ts" setup>
import auth from '@mdfe/auth';
import { MdIcon, MdPopover, useNamespace } from '@mdfe/medi-ui';

import { useCurrentUser } from '../composables';

const user = useCurrentUser();

const ns = useNamespace('navbar-userinfo');

function handleLogout() {
  auth.logout();
}
</script>

<template>
  <MdPopover
    placement="bottom-end"
    trigger="click"
    width="100"
    :show-arrow="false"
    :popper-class="ns.b('popover')"
  >
    <template #reference>
      <div :class="ns.b()">
        <MdIcon name="yonghuming" style="margin-right: 8px" />
        <span>{{ user.name }}</span>
        <MdIcon name="xiajiantou-s" style="margin-left: 8px" />
      </div>
    </template>

    <div :class="ns.e('menu')">
      <div :class="ns.e('menu-item')" @click="handleLogout">
        <MdIcon name="tuichu1" style="margin-right: 8px" />
        <span>退出登录</span>
      </div>
    </div>
  </MdPopover>
</template>

<style lang="scss">
@import '@mdfe/sass-bem/bem';

@include c('navbar-userinfo-popover') {
  margin-top: -10px !important;
  min-width: 100px !important;
}
</style>
