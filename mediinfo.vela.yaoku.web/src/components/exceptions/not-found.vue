<template>
  <md-page>
    <div class="msg">
      <h2 class="msg-title">404</h2>
      <p class="msg-text">Page not found!</p>
    </div>
  </md-page>
</template>

<script>
import { Page } from '@mdfe/medi-ui';

export default {
  name: 'page-not-found-page',
  mounted() {
    document.dispatchEvent(new Event('custom-render-ready'));
  },
  components: {
    'md-page': Page,
  },
};
</script>

<style lang="scss">
.msg {
  height: 100%;
}

.msg-title {
  margin-top: 200px;
  font-size: 140px;
  text-align: center;
}

.msg-text {
  margin-top: 30px;
  font-size: 20px;
  text-align: center;
}
</style>
