<template>
  <md-select-table
    v-bind="$attrs"
    :value="yiShengOBJ"
    :columns="columns"
    :fetchData="fetchData"
    :labelKey="labelKey"
    :valueKey="valueKey"
    remote
    filterable
    isScrollLoad
    ref="selectTable"
    @clear="handleClear"
    @change="selectChange"
  />
</template>
<script>
import SelectTable from '@mdfe/material.select-table';

import { getYongHuXXByYHLB } from '@/service/gongYong/yongHuXX';

export default {
  name: 'biz-yishengss-select',
  props: {
    value: {
      type: [Array, String],
      default: () => {
        return [];
      },
    },
    leiBie: {
      type: String,
      default: 'ys',
    },
    yiShengXM: {
      //责任医生姓名 可以通过sync传参给父组件
      type: String,
      default: null,
    },
    yiShengID: {
      //责任医生ID 可以通过sync传参给父组件
      type: String,
      default: null,
    },
    labelKey: {
      type: String,
      default: 'yongHuXM',
    },
    valueKey: {
      type: String,
      default: 'yongHuID',
    },
    keShiID: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isBingQv: {
      type: Boolean,
      default: true,
    },
    //请求的service
    api: {
      type: Function,
      default: getYongHuXXByYHLB,
    },
    yiShengObj: {
      type: Object,
    },
    columns: {
      type: Array,
      default: () => {
        return [
          {
            prop: 'yongHuID',
            label: '工号',
            minWidth: 100,
          },
          {
            prop: 'yongHuXM',
            label: '姓名',
          },
        ];
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    yiShengOBJ: {
      get() {
        if (this.yiShengXM) {
          const obj = {};
          obj[this.labelKey] = this.yiShengXM;
          obj[this.valueKey] = this.yiShengID;
          return obj;
        } else {
          return {};
        }
      },
    },
  },
  // watch: {
  //   yiShengOBJ: {
  //     immediate: true,
  //     handler(newVal) {
  //       if (newVal && this.$refs.selectTable) {
  //         this.$nextTick(() => {
  //           this.fetchData(1, 10, this.yiShengOBJ.yiShengXM);
  //           this.selectChange(this.yiShengOBJ);
  //         });
  //       }
  //     },
  //   },
  // },
  methods: {
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      const params = {
        likeQuery: inputValue,
        yongHuLB: this.leiBie === 'ys' ? '1' : '2',
        bingQuID: null,
        pageIndex: page,
        pageSize,
        keShiID: null,
      };
      const res = await this.api(params);
      return res;
    },
    handleClear() {
      this.$emit('clear');
    },
    selectChange(data) {
      if (typeof data !== 'object') {
        return;
      }
      this.$emit('update:yiShengXM', data.yongHuXM);
      this.$emit('update:yiShengID', data.yongHuID);
      if (this.labelKey != 'yongHuXM' && this.multiple) {
        const multipleObj = {};
        const multipleAry = data.map((item) => {
          multipleObj[this.labelKey] = item.yongHuXM;
          multipleObj[this.valueKey] = item.yongHuID;
          return multipleObj;
        });
        this.$emit('update:duoKeYQYSCreateDtos', multipleAry);
        return;
      }
      this.$emit('change', data);
    },
  },
  components: {
    'md-select-table': SelectTable,
  },
};
</script>
<style scoped lang="scss"></style>
