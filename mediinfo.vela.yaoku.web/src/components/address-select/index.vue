<template>
  <div ref="reference" :class="classObj['namespace']">
    <md-popover
      :popper-class="classObj['popover']"
      placement="bottom-start"
      :show-arrow="false"
      :width="popverWidth"
      v-bind="$attrs"
      ref="popover"
      trigger="click"
      popper-class="addressList-yaoku"
    >
      <template #reference>
        <md-input
          v-model="inputValue"
          v-bind="$attrs"
          slot="reference"
          :placeholder="placeholder"
          :validate-event="false"
          :clearable="true"
          ref="input"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
          @clear="handleClear"
          @keydown.down.stop.prevent="handleSelectDown"
          @keydown.up.stop.prevent="handleSelectUp"
          @keydown.enter.prevent="handleSelectEnter"
        >
          <template #suffix>
            <i
              key="arrow-down"
              :class="[classObj['icon'], { 'is-reverse': dropDownVisible }]"
              @click.stop="toggleDropDownVisible()"
            ></i>
          </template> </md-input
      ></template>
      <bmis-address-list
        v-loading="loading"
        v-model="addressValue"
        :change-value="changeValue"
        :data="data"
        :show="isShow"
        :hover-index="hoverIndex"
        :select-enter="selectEnter"
        @hoverIndex="handleHoverIndex"
        @change="onChange"
        @mousedown="preventBlur($event)"
      >
      </bmis-address-list>
    </md-popover>
  </div>
</template>
<script>
import { MdInput, useNamespace } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';

import AddressList from './address-list';

export default {
  name: 'bmis-address-select',
  props: {
    allowCreate: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    modelValue: {
      type: Array,
      default: () => {
        return [];
      },
    },
    placeholder: {
      type: String,
      default: '请选择地址',
    },
  },
  data() {
    return {
      hoverIndex: -1,
      selectEnter: null,
      loading: false,
      dropDownVisible: false,
      isShow: false,
      popverWidth: '100',
      addressValue: [],
      inputValue: null,
      changeValue: null,
      hcValue: null,
    };
  },
  computed: {
    classObj() {
      const ns = useNamespace('address-select');
      const nsIcon = useNamespace('icon');
      const nsInput = useNamespace('input');
      return {
        namespace: ns.b(),
        popover: ns.e('popover'),
        icon: `${nsInput.e('icon')} ${nsIcon.b('xiajiantou-k')}`,
      };
    },
  },
  watch: {
    inputValue(val) {
      let input = val ? val.split('/') : [];
      // this.$emit('input', input);
      this.$emit('update:modelValue', input);
    },
    visible(value) {
      this.dropDownVisible = value;
      this.loading = value;
      if (value) this.initData();
    },
    modelValue: {
      handler(val) {
        if (val) {
          this.inputValue = val.join('/');
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.popverWidth = this.$refs.reference.offsetWidth + 40;
  },
  methods: {
    initData() {
      if (this.data.length === 0) {
        this.isShow = false;
        const unwatch = this.$watch('data', function (val) {
          if (val.length > 0) {
            // 处理业务逻辑
            this.isShow = true;
            if (this.hcValue) this.addressValue = this.hcValue.split('/');
            this.loading = false;
            // 之后调用unwatch, 取消监听
            unwatch();
          }
        });
      } else {
        this.loading = false;
      }
    },
    handleHoverIndex(val) {
      this.hoverIndex = val;
    },
    handleSelectDown() {
      this.hoverIndex++;
    },
    handleSelectUp() {
      this.hoverIndex--;
    },
    handleSelectEnter() {
      const hoverIndex = cloneDeep(this.hoverIndex);
      this.selectEnter = hoverIndex >= 0 ? hoverIndex : null;
    },
    onChange(...val) {
      this.hcValue = val[0].join('/');
      this.inputValue = val[0].join('/');
      this.dropDownVisible = false;
      this.$emit('change', val);
      this.$refs.popover.hide();
    },
    toggleDropDownVisible() {
      this.dropDownVisible = !this.dropDownVisible;
    },
    handleFocus(e) {
      this.isShow = true;
      this.hcValue = this.inputValue;
      if (this.hcValue) this.addressValue = this.hcValue.split('/');
      this.$emit('focus', e);
    },
    handleBlur(e) {
      if (!this.allowCreate) {
        this.inputValue = this.hcValue;
      }
      this.isShow = false;
      this.hoverIndex = -1;
    },
    preventBlur(event) {
      event.stopPropagation();
    },
    handleInput(val, event) {
      this.changeValue = this.inputValue;
      if (this.allowCreate) {
        this.hcValue = null;
        this.addressValue = [];
      }
    },
    handleClear() {
      this.inputValue = null;
      this.changeValue = null;
      this.hcValue = null;
      this.addressValue = [];
      this.$emit('change');
    },
  },
  components: {
    'md-input': MdInput,
    'bmis-address-list': AddressList,
  },
};
</script>
<style>
.addressList-yaoku {
  /* overflow: hidden; */
}
</style>
