<template>
  <div :class="classObj['namespace']">
    <md-tabs v-show="cityChange" v-model="activeName">
      <md-tab-pane label="省份" name="shengFen">
        <md-scrollbar :native="false">
          <div :class="classObj['content']">
            <span
              v-for="(provinces, provinceKey) in data"
              :key="provinceKey"
              :class="[
                classObj['item'],
                { 'is-active': provinceCode == provinces.biaoZhunDM },
              ]"
              @click="onProvince(provinces)"
            >
              {{ provinces.biaoZhunMC }}
            </span>
          </div>
        </md-scrollbar>
      </md-tab-pane>
      <md-tab-pane label="城市" name="chengShi">
        <md-scrollbar :native="false">
          <div :class="classObj['content']">
            <span
              v-for="(citys, cityKey) in city"
              :key="cityKey"
              :class="[
                classObj['item'],
                { 'is-active': cityCode == citys.biaoZhunDM },
              ]"
              @click="onCity(citys)"
            >
              {{ citys.biaoZhunMC }}
            </span>
          </div>
        </md-scrollbar>
      </md-tab-pane>
      <md-tab-pane label="区县" name="quXian">
        <md-scrollbar :native="false">
          <div :class="classObj['content']">
            <span
              v-for="(countys, countyKey) in county"
              :key="countyKey"
              :class="[
                classObj['item'],
                { 'is-active': countyCode == countys.biaoZhunDM },
              ]"
              @click="onCounty(countys)"
            >
              {{ countys.biaoZhunMC }}
            </span>
          </div>
        </md-scrollbar>
      </md-tab-pane>
    </md-tabs>
    <div v-show="!cityChange" :class="classObj['suggestion']">
      <md-scrollbar ref="scrollbar" :native="false" style="height: 100%">
        <ul>
          <li
            v-for="(item, index) in cityList"
            :key="index"
            :class="[
              { 'is-hover': index == hoverIndex },
              { 'is-active': item.name == address },
            ]"
            @click="onCityChange(item)"
          >
            <span>{{ item.name }}</span>
            <md-icon v-if="item.name == address" name="gou"></md-icon>
          </li>
        </ul>
      </md-scrollbar>
    </div>
  </div>
</template>

<script>
import {
  MdTabs,
  MdTabPane,
  MdScrollbar,
  useNamespace,
  MdIcon,
} from '@mdfe/medi-ui';
import { isEmpty, cloneDeep, debounce } from 'lodash';
import { flat } from './address.city';

export default {
  name: 'bmis-address-list',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    hoverIndex: {
      type: Number,
      default: 0,
    },
    selectEnter: {
      type: Number,
      default: 0,
    },
    changeValue: {
      type: String,
      default: '',
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: 'shengFen',
      cityChange: true,
      province: [],
      address: '',
      cityList: [],
      city: [],
      county: [],
      provinceObj: null,
      provinceCode: '',
      provinceValue: '',
      cityObj: null,
      cityCode: '',
      cityValue: '',
      countyObj: null,
      countyCode: '',
      countyValue: '',
    };
  },
  computed: {
    classObj() {
      const nsList = useNamespace('address-list');
      const nsSuggestion = useNamespace('address-suggestion');
      return {
        namespace: nsList.b(),
        content: nsList.e('content'),
        item: nsList.e('item'),
        suggestion: `${nsSuggestion.b()}`,
      };
    },
  },
  watch: {
    hoverIndex(val) {
      let index = val;
      if (val < -1) {
        index = this.cityList.length + val + 1;
        this.$emit('hoverIndex', index);
        return;
      }
      let height = null;
      let scrollbarEl = this.$refs.scrollbar.wrap;
      const numbers = parseInt(scrollbarEl.offsetHeight / 36);
      if (index > numbers) {
        scrollbarEl.scrollTop = 32 * (index - numbers);
      } else {
        scrollbarEl.scrollTop = 0;
      }

      if (index >= this.cityList.length) {
        this.$emit('hoverIndex', 0);
      }
    },
    selectEnter(val) {
      if (val > -1) {
        this.onCityChange(this.cityList[val]);
      }
    },
    show(val) {
      if (val) this.cityChange = val;
    },
    data: {
      //深度监听，可监听到对象、数组的变化
      handler(val) {
        if (isEmpty(this.cityLists)) {
          this.cityLists = cloneDeep(flat(val));
        }
      },
      deep: true,
      immediate: true,
    },
    value(val) {
      if (!isEmpty(val)) {
        this.data.forEach((item) => {
          if (val[0] == item.biaoZhunMC) {
            this.provinceObj = item;
            this.provinceCode = item.biaoZhunDM;
            this.provinceValue = item.biaoZhunMC;
            this.city = item.childList;
          }
        });
      } else {
        this.activeName = 'shengFen';
        this.provinceObj = null;
        this.provinceCode = '';
        this.provinceValue = '';
        this.cityObj = null;
        this.cityCode = '';
        this.cityValue = '';
        this.countyObj = null;
        this.countyCode = '';
        this.countyValue = '';
        this.city = [];
        this.county = [];
      }
      if (!isEmpty(this.city)) {
        this.city.forEach((item) => {
          if (val[1] == item.biaoZhunMC) {
            this.cityObj = item;
            this.cityCode = item.biaoZhunDM;
            this.cityValue = item.biaoZhunMC;
            this.county = item.childList;
            this.activeName = 'chengShi';
          }
        });
      }
      if (!isEmpty(this.county)) {
        this.county.forEach((item) => {
          if (val[2] == item.biaoZhunMC) {
            this.countyObj = item;
            this.countyCode = item.biaoZhunDM;
            this.countyValue = item.biaoZhunMC;
            this.activeName = 'quXian';
          }
        });
      }
    },
    changeValue: {
      //深度监听，可监听到对象、数组的变化
      handler(newV) {
        this.getSelectData(newV);
      },
      deep: true,
    },
  },
  methods: {
    onCityChange(val) {
      this.address = val.name;
      let arr = val.name.split('/');
      let arr1 = val.code.split('/');
      let arr2 = val.youBian.split('/');
      this.cityChange = true;
      this.$emit('change', arr, arr1, arr2);
      this.$emit('hide', false);
    },
    onProvince(val) {
      this.activeName = 'chengShi';
      this.provinceObj = val;
      this.provinceCode = val.biaoZhunDM;
      this.provinceValue = val.biaoZhunMC;
      this.city = val.childList;
    },
    onCity(val) {
      this.county = val.childList;
      this.cityObj = val;
      this.cityCode = val.biaoZhunDM;
      this.cityValue = val.biaoZhunMC;
      if (isEmpty(this.county)) {
        const arr = [this.provinceValue, this.cityValue];
        const arrCode = [this.provinceCode, this.cityCode];
        const arrYouBian = [
          this.provinceObj.youBian == null ? '' : this.provinceObj.youBian,
          this.cityObj.youBian == null ? '' : this.cityObj.youBian,
        ];
        this.$emit('change', arr, arrCode, arrYouBian);
        this.$emit('hide', false);
        return false;
      }
      this.activeName = 'quXian';
    },
    onCounty(val) {
      this.countyObj = val;
      this.countyCode = val.biaoZhunDM;
      this.countyValue = val.biaoZhunMC;
      const arr = [this.provinceValue, this.cityValue, this.countyValue];
      const arrCode = [this.provinceCode, this.cityCode, this.countyCode];
      const arrYouBian = [
        this.provinceObj.youBian == null ? '' : this.provinceObj.youBian,
        this.cityObj.youBian == null ? '' : this.cityObj.youBian,
        this.countyObj.youBian == null ? '' : this.countyObj.youBian,
      ];
      this.$emit('change', arr, arrCode, arrYouBian);
      this.$emit('hide', false);
    },
    //输入框搜索方法
    getSelectData: debounce(function (val) {
      this.cityList = this.cityLists.filter(
        (data) =>
          !val ||
          (data.name && data.name.toLowerCase().includes(val.toLowerCase())) ||
          (data.py && data.py.toLowerCase().includes(val.toLowerCase())) ||
          (data.wb && data.wb.toLowerCase().includes(val.toLowerCase())),
      );
      this.cityChange = false;
    }, 400),
  },
  components: {
    'md-tabs': MdTabs,
    'md-icon': MdIcon,
    'md-tab-pane': MdTabPane,
    'md-scrollbar': MdScrollbar,
  },
};
</script>
