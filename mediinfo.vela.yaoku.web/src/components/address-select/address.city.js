export function flat(data) {
  let tem = [];
  data.forEach((item) => {
    let code = '';
    let code1 = '';
    let code2 = '';
    let name = '';
    let name1 = '';
    let name2 = '';
    let py = '';
    let py1 = '';
    let py2 = '';
    let wb = '';
    let wb1 = '';
    let wb2 = '';
    let youBian = '';
    let youBian1 = '';
    let youBian2 = '';
    code = item.biaoZhunDM;
    name = item.biaoZhunMC;
    youBian = item.youBian;
    py = item.shuRuMa1;
    wb = item.shuRuMa2;
    if (item.childList.length > 0) {
      item.childList.forEach((items) => {
        code1 = list(code, items.biaoZhunDM);
        name1 = list(name, items.biaoZhunMC);
        youBian1 = list(youBian, items.youBian);
        py1 = list(py, items.shuRuMa1);
        wb1 = list(wb, items.shuRuMa2);
        if (items.childList.length > 0) {
          items.childList.forEach((itemss) => {
            code2 = list(code1, itemss.biaoZhunDM);
            name2 = list(name1, itemss.biaoZhunMC);
            youBian2 = list(youBian1, itemss.youBian);
            py2 = list(py1, itemss.shuRuMa1);
            wb2 = list(wb1, itemss.shuRuMa2);
            tem.push({
              code: code2,
              name: name2,
              youBian: youBian2,
              py: py2,
              wb: wb2,
            });
          });
        } else {
          tem.push({
            code: code1,
            name: name1,
            youBian: youBian1,
            py: py1,
            wb: wb1,
          });
        }
      });
    } else {
      tem.push({
        code: code,
        name: name,
        youBian: youBian,
        py: py,
        wb: wb,
      });
    }
  });
  return tem;
}
export function list(str, str1) {
  const strs = str == null ? '' : str;
  const str1s = str1 == null ? '' : str1;
  return strs.concat('/' + str1s);
}
