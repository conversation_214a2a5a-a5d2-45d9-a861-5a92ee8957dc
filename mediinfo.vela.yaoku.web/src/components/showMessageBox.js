// import { MdMdMessageBox } from '@mdfe/medi-ui';

// export const showMdMessageBox = ({ message, exception, code }) => {
//   if (code > -1) return;
//   if (code === -100) {
//     MdMdMessageBox({
//       title: '业务错误',
//       type: 'error',
//       message,
//       confirmButtonText: '我知道了',
//     });
//   } else {
//     const systemError = code > -100;
//     MdMdMessageBox({
//       center: !systemError,
//       showMascot: !systemError,
//       showClose: false,
//       systemError,
//       content:
//         String(message).length > 57
//           ? String(message).substring(0, 54) + '...'
//           : String(message),
//       details: exception,
//       cancelButtonText: '提交工单',
//     });
//   }
// };

import { reportError } from '@mdfe/lyra';
import { MdMessageBox } from '@mdfe/medi-ui';

//传多个参数后面弃用
export const showMessageBox = (
  { message, exception, code },
  config = {
    headers: '',
    url: '',
    method: '',
  },
  headers,
) => {
  if (code > -1) return;
  if (code === -100) {
    MdMessageBox({
      title: '业务错误',
      type: 'error',
      message,
      confirmButtonText: '我知道了',
    });
  } else {
    const systemError = code > -100;
    if (process.env.NODE_ENV === 'development' || !config.url) {
      MdMessageBox({
        center: !systemError,
        showMascot: !systemError,
        showClose: false,
        systemError,
        content:
          String(message).length > 57
            ? String(message).substring(0, 54) + '...'
            : String(message),
        details: exception,
        cancelButtonText: '提交工单',
      });
    } else {
      const headersParams = JSON.stringify(config.headers);
      const headersQQT = JSON.stringify(headers);
      let content = '';
      content = content + `接口地址：${config.url}` + '\n';
      content = content + `接口类型：${config.method}\n`;
      content = content + `错误代码：${code}\n`;
      content = content + `请求参数：${headersParams}\n`;
      content = content + `请求体：${headersQQT}\n`;
      content = content + `错误内容：${message}`;
      reportError({
        center: !systemError,
        showMascot: !systemError,
        systemError,
        type: 'business',
        title: '接口错误',
        message: message,
        content: content,
      });
    }
  }
};
export const errorMdMessageBox = (response) => {
  const result = response.data || {};
  if (result.code > -1) return;
  if (result.code === -100) {
    MdMessageBox({
      title: '业务错误',
      type: 'error',
      message: result.message,
      confirmButtonText: '我知道了',
    });
  } else {
    const config = response.config;
    const systemError = result.code > -100;
    if (process.env.NODE_ENV === 'development' || !config.url) {
      MdMessageBox({
        center: !systemError,
        showMascot: !systemError,
        showClose: false,
        systemError,
        content:
          String(result.message).length > 57
            ? String(result.message).substring(0, 54) + '...'
            : String(result.message),
        details: result.exception,
        cancelButtonText: '提交工单',
      });
    } else {
      const xiangYingBT = JSON.stringify(response.headers);
      const qingQiuBT = JSON.stringify(config.headers);
      const params =
        JSON.stringify(config.params) + JSON.stringify(config.data);

      let content = '';
      content = content + `接口地址：${config.url}` + '\n';
      content = content + `接口类型：${config.method}\n`;
      content = content + `错误代码：${result.code}\n`;
      content = content + `请求参数：${params}\n`;
      content = content + `响应标头：${xiangYingBT}\n`;
      content = content + `请求标头：${qingQiuBT}\n`;
      content = content + `错误内容：${result.message}`;
      reportError({
        center: !systemError,
        showMascot: !systemError,
        systemError,
        type: 'business',
        title: '接口错误',
        message: result.message,
        content: content,
      });
    }
  }
};
