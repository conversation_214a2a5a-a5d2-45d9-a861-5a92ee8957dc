<template>
  <div :class="prefixClass('jigou-container')">
    <md-tree-select
      v-model="checkedData"
      :data="weiZhiList"
      clearable
      trigger="click"
      check-strictly
      :render-after-expand="false"
      @change="onchang"
      v-bind="$attrs"
      v-on="$listeners"
      :chang-on-close="changOnClose"
    />
  </div>
</template>

<script>
import { request as getGetHisZuZhiJGTree } from '@/service/gongYong/getGetHisZuZhiJGTree';
import { getWeiZhiPLList } from '@/service/gongYong/weiZhi';
import {
  getJiGouID,
  getWeiZhiID,
  getWeiZhiMC,
} from '@/system/utils/local-cache';
import { MdTreeSelect } from '@mdfe/medi-ui';

/**
 * 位置选择组件
 *
 * 参数：（其他参数参考md-tree-select组件）
 *
 */
export default {
  name: 'bizJiGouTree',
  components: {
    'md-tree-select': MdTreeSelect,
  },
  data() {
    return {
      weiZhiTreeData: [],
      checkedData: [],
      seachInputValue: '',
      isShowInput: false,
      defaultProps: {
        value: 'id',
        label: 'label',
      },
      weiZhiList: [],
      hasTongYong: false, //是否有通用权限
    };
  },
  props: {
    changOnClose: {
      type: Boolean,
      default: true,
    },
    url: {
      type: String,
      default: '/api/getJiGouOption',
    },
    shiFouQB: {
      type: Boolean,
      default: false,
    },
    youWuTY: {
      type: Boolean,
      default: false,
    },
    /**默认位置id*/
    moRenWZID: {
      type: String,
      default: '',
    },
    //自定义
    ziDingY: {
      type: Boolean,
      default: false,
    },
    //位置类型id
    weiZhiLXDMs: {
      type: Array,
      default: [],
    },
  },
  watch: {
    moRenJGID(value) {
      this.checkedData = [value];
    },
  },
  created() {
    if (!this.ziDingY) this.getOptions();
  },
  methods: {
    onchang(e) {
      let name;
      if (e !== '0') {
        name = this.weiZhiList[0].children.find((fl) => fl.value === e).label;
      } else {
        name = '通用';
      }
      this.$emit('change', { label: name, value: e });
    },
    async getOptions() {
      //取权限
      const quanXianResult = await getGetHisZuZhiJGTree({
        shiFouQB: false,
        youWuTY: true,
      });
      //判断如果权限如果包括0  则显示通用的
      if (quanXianResult && quanXianResult.findIndex((r) => r.id == '0') > -1) {
        this.hasTongYong = true;
      }

      this.$emit('isTongYong', this.hasTongYong);
      const params = {
        zuZhiJGIDs: getJiGouID(),
        weiZhiLXDMs: this.weiZhiLXDMs,
        likeQuery: '',
      };
      const weiZhiList = await getWeiZhiPLList(params);

      if (this.hasTongYong) {
        //如果有通用则可以编辑全部位置
        this.weiZhiList = [];
        const weiZhiArr = weiZhiList.map((r) => {
          return {
            label: r.weiZhiMC,
            value: r.weiZhiID,
          };
        });
        this.weiZhiList.push({
          label: '通用',
          value: '0',
          children: weiZhiArr,
        });
      } else {
        //没有权限则默认当前自己的位置
        this.weiZhiList = [
          {
            label: decodeURIComponent(getWeiZhiMC()),
            value: decodeURIComponent(getWeiZhiID()),
          },
        ];
      }
      //默认赋值id
      if (this.weiZhiList && this.weiZhiList.length > 0) {
        if (
          !this.moRenWZID ||
          this.moRenWZID == '' ||
          this.moRenWZID == undefined
        ) {
          this.checkedData.push(this.weiZhiList[0].value);
        } else {
          this.checkedData.push(this.moRenWZID);
        }
      }
    },
  },
};
</script>
