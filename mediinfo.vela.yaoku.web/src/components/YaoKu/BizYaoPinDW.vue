<template>
  <md-select-table
    v-model="current"
    :fetchData="fetchData"
    :columns="columns"
    :labelKey="labelKey"
    :valueKey="valueKey"
    :immediateLoad="false"
    :placeholder="placeholder"
    filterable
    :tableProps="{
      style: { width: '1100px' },
    }"
    @change="change"
    @blur="handleBlur"
    ref="tableSelect"
  />
</template>

<script>
import {
  GetGongYongYPForYFList,
  GetYaoPinList,
  GetYaoPinTSSX,
} from '@/service/yaoPinYF/common';
import { GetGongYongYPForYKList } from '@/service/yaoPinYK/common';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import MdSelectTable from '@mdfe/material.select-table';
import '@mdfe/material.select-table/es/index.scss';
const isEmptyObject = (value) => {
  if (!value) return true;
  if (!Array.isArray(value) && typeof value === 'object') {
    return Object.keys(value).length === 0;
  } else {
    return true;
  }
};
export default {
  name: 'biz-yaopindw',
  emits: ['change', 'blur'],
  props: {
    modelValue: {
      required: true,
    },
    valueKey: {
      type: String,
      default: 'jiaGeID',
    },
    labelKey: {
      type: String,
      default: 'yaoPinMC',
    },
    placement: {
      type: String,
      default: 'bottom',
    },
    //目标位置管理类型
    muBiaoWZGLLX: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '输入搜索',
    },
    showSuffix: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'yk',
      validator: function (v) {
        return ['yk', 'yf', 'yh', 'kcsxx', 'bsd', 'zkzy'].includes(v);
      },
    },
    guiGeLX: {
      //  0->全部， 1-> 只查大规格， 2->只查小规格
      type: String,
      default: '0',
    },
    quanYuanSJ: {
      //是否查全院数据
      type: Boolean,
      default: false,
    },
    //库存是否为空
    kuCunSFWK: {
      type: Boolean,
      default: null,
    },
    //是否获取药品分布数量
    fenBuSL: {
      type: Boolean,
      default: false,
    },
    weiZhiID: {
      type: String,
      default: '',
    },
    yaoPinLXStr: {
      type: String,
      default: '',
    },
    columnsList: {
      type: Array,
      default: () => [],
    },
    jiCaiYPBZ: {
      type: String,
      default: '',
    },
    paiChuJGIDList: {
      type: String,
      default: '',
    },
    //如果是入库单改变接口GetYaoPinTSSX参数
    isRuKuPage: {
      type: Boolean,
      default: false,
    },
    zhangBuLBID: {
      type: String,
      default: '',
    },
    liangDingYPBZ: {
      type: String,
      default: '',
    },
    // 查询小规格
    showXiaoGuiGeYP: {
      type: Boolean,
      default: false,
    },
    // 显示描述
    showChanDiJC: {
      type: Boolean,
      default: false,
    },
    lingKCBZ: {
      type: Boolean,
      default: false,
    },
    linShiYYBZ: {
      type: Boolean,
      default: false,
    },
    yinPianKLBZ: {
      type: [String, Number], // '':全部,1:颗粒,2:饮片
      default: '',
    },
  },
  data() {
    return {
      query: {
        yaoPinMC: '',
        yaoPinLX: getKuCunGLLX(),
        pageSize: 10,
        pageIndex: 1,
        guiGeLX:
          this.type == 'yf' ||
          this.type == 'yh' ||
          this.type == 'ksql' ||
          this.type == 'bsd'
            ? this.guiGeLX
            : null,
        quanYuanSJ: this.quanYuanSJ,
      },
      queryDsiable: false,
      current: {},
      selectOption: [],
      xianShiXX: [],
    };
  },
  computed: {
    columns() {
      if (this.columnsList.length > 0) {
        return this.columnsList;
      }
      if (this.type === 'yk' || this.type === 'bsd') {
        return [
          {
            label: '',
            prop: 'yaoPinLXMC',
            width: 50,
            align: 'center',
            formatter(v) {
              return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
            },
            showOverflowTooltip: true,
          },
          {
            label: '药品名称',
            prop: 'yaoPinMCYGG',
            width: 400,
            // formatter(v) {
            //   if (v.yaoPinMC) {
            //     return v.yaoPinMC;
            //   } else {
            //     return '';
            //   }
            // },
            render: (h, { row, $index }) => {
              const tagStyles = (styleData) => {
                let sty = {};
                if (styleData && styleData.jiaCuBZ) {
                  sty['font-weight'] = 'bold';
                }
                if (styleData && styleData.xieTiBZ) {
                  sty['font-style'] = 'oblique';
                }
                sty.color = styleData ? styleData.ziTiYS : 'unset';
                return sty;
              };
              const label =
                row.xianShiXX && row.xianShiXX.tianJiaWZ
                  ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
                  : row.yaoPinMC;

              const label2 =
                this.showChanDiJC && row.chanDiJC
                  ? `(${row.chanDiJC || ''})`
                  : '';

              return h(
                'span',
                { style: tagStyles(row.xianShiXX) },
                label + label2,
              );
            },
            showOverflowTooltip: true,
          },
          {
            label: '规格',
            prop: 'yaoPinGG',
            width: 200,
            formatter(v) {
              if (v.jiaGeID) {
                return v.yaoPinGG;
              } else {
                return '';
              }
            },
            render: (h, { row, $index }) => {
              const tagStyles = (styleData) => {
                let sty = {};
                if (styleData && styleData.jiaCuBZ) {
                  sty['font-weight'] = 'bold';
                }
                if (styleData && styleData.xieTiBZ) {
                  sty['font-style'] = 'oblique';
                }
                sty.color = styleData ? styleData.ziTiYS : 'unset';
                return sty;
              };
              const label = row.yaoPinGG;
              return h('span', { style: tagStyles(row.xianShiXX) }, label);
            },
            showOverflowTooltip: true,
          },
          {
            label: '产地名称',
            prop: 'chanDiMC',
            width: 200,
            showOverflowTooltip: true,
          },
          {
            label: '单位',
            prop: 'baoZhuangDW',
            width: 50,
          },
          {
            label: '库存量',
            prop: 'kuCunSL',
            align: 'right',
            width: 100,
            formatter: (v) => {
              return Number(v.kuCunSL || 0).toFixed(3);
            },
          },
          {
            label: '单价',
            prop: 'danJia',
            align: 'right',
            width: 100,
            formatter: (v) => {
              return formatJiaGe(v.danJia);
            },
          },
        ];
      } else {
        return [
          {
            label: '',
            prop: 'yaoPinLXMC',
            width: 50,
            align: 'center',
            formatter(v) {
              return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
            },
            showOverflowTooltip: true,
          },
          {
            render: (h, { row, $index }) => {
              const tagStyles = (styleData) => {
                let sty = {};
                if (styleData && styleData.jiaCuBZ) {
                  sty['font-weight'] = 'bold';
                }
                if (styleData && styleData.xieTiBZ) {
                  sty['font-style'] = 'oblique';
                }
                sty.color = styleData ? styleData.ziTiYS : 'unset';
                return sty;
              };
              const label =
                row.xianShiXX && row.xianShiXX.tianJiaWZ
                  ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
                  : row.yaoPinMC;

              const label2 =
                this.showChanDiJC && row.chanDiJC
                  ? `(${row.chanDiJC || ''})`
                  : '';

              return h(
                'span',
                { style: tagStyles(row.xianShiXX) },
                label + label2,
              );
            },
            label: '药品名称',
            prop: 'yaoPinMCYGG',
            width: 400,
            showOverflowTooltip: true,
            // formatter(v) {
            //   if (v.yaoPinMC) {
            //     return v.yaoPinMC + ' ' + v.yaoPinGG;
            //   } else {
            //     return '';
            //   }
            // },
          },
          {
            label: '规格',
            prop: 'yaoPinGG',
            width: 200,
            formatter(v) {
              if (v.jiaGeID) {
                return v.yaoPinGG;
              } else {
                return '';
              }
            },
            render: (h, { row, $index }) => {
              const tagStyles = (styleData) => {
                let sty = {};
                if (styleData && styleData.jiaCuBZ) {
                  sty['font-weight'] = 'bold';
                }
                if (styleData && styleData.xieTiBZ) {
                  sty['font-style'] = 'oblique';
                }
                sty.color = styleData ? styleData.ziTiYS : 'unset';
                return sty;
              };
              const label = row.yaoPinGG;
              return h('span', { style: tagStyles(row.xianShiXX) }, label);
            },
            showOverflowTooltip: true,
          },
          {
            label: '产地名称',
            prop: 'chanDiMC',
            width: 140,
            showOverflowTooltip: true,
          },
          {
            label: '单位',
            prop: 'baoZhuangDW',
            width: 50,
          },
          {
            label: '库存量',
            prop: 'kuCunSL',
            align: 'right',
            width: 100,
            formatter: (v) => {
              return Number(v.kuCunSL).toFixed(3);
            },
          },
          {
            label: '单价',
            prop: 'danJia',
            width: 70,
            align: 'right',
            formatter: (v) => {
              return formatJiaGe(v.danJia);
            },
          },
        ];
      }
    },
  },
  watch: {
    modelValue: {
      handler: function (val) {
        this.$nextTick(() => {
          if (val !== '' && !isEmptyObject(val)) {
            val.yaoPinZC =
              val.xianShiXX &&
              val.xianShiXX.tianJiaWZ !== undefined &&
              val.xianShiXX.tianJiaWZ
                ? val.xianShiXX.tianJiaWZ + val.yaoPinMC + ' ' + val.yaoPinGG
                : val.yaoPinMC + ' ' + val.yaoPinGG;
            this.selectOption = [val];
          } else {
            this.selectOption = [];
          }
          this.current = val;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    if (this.fenBuSL) {
      this.$emit('getFenBuSL');
    }
  },
  methods: {
    focus() {
      this.$refs.tableSelect.focus();
    },
    handleBlur() {
      this.$emit('blur');
    },
    change(data) {
      // console.log(data);
      this.$emit('change', data);
      // this.$emit('input', data)
    },
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      this.query.yaoPinMC = inputValue;
      this.query.pageSize = pageSize;
      this.query.pageIndex = page;
      this.queryDsiable = false;
      this.query.kuCunSFWK = this.kuCunSFWK;
      let yaoPinLX = '';
      //取目标位置与当前位置管理类型的交集
      if (this.muBiaoWZGLLX) {
        for (var i = 0; i < 4; i++) {
          if (this.muBiaoWZGLLX[i] == '1') {
            if (this.query.yaoPinLX.indexOf(i + 1) > -1) {
              yaoPinLX +=
                this.query.yaoPinLX[this.query.yaoPinLX.indexOf(i + 1)] + '|';
            }
          }
        }

        if (!yaoPinLX) {
          //目标位置与当前位置 库存管理类型无交集
          this.$message({
            type: 'warning',
            message: `【单位/部门】与当前位置无相同库存管理类型！`,
          });
          return;
        }
      }
      if (this.yaoPinLXStr) {
        this.query.yaoPinLX = this.yaoPinLXStr;
      } else {
        this.query.yaoPinLX = yaoPinLX;
      }
      if (this.linShiYYBZ) {
        this.query.linShiYYBZ = '1';
      }

      this.query.yinPianKLBZ = this.yinPianKLBZ;

      if (this.type === 'bsd') this.query.kuCunSFWK = false; //新增报损单过滤掉没有库存的药品
      let data;
      if (this.type === 'yk' || this.type === 'bsd') {
        if (this.lingKCBZ) {
          this.query.lingKCBZ = this.lingKCBZ;
        }
        this.query.zhangBuLBID = this.type === 'yk' ? this.zhangBuLBID : '';
        // 允许查小规格
        if (this.showXiaoGuiGeYP) this.query.baoKuoXGG = true;
        if (this.liangDingYPBZ) this.query.liangDingYPBZ = this.liangDingYPBZ;
        data = await GetGongYongYPForYKList(this.query);
      }
      if (this.type === 'yh') {
        this.query.kuCunSFWK = false;
        data = await GetGongYongYPForYKList(this.query);
      }
      if (this.type === 'yf') {
        data = await GetGongYongYPForYFList(this.query);
      }
      if (this.type === 'kcsxx') {
        this.query.muBiaoWZID = this.weiZhiID;
        data = await GetYaoPinList(this.query);
      }
      if (this.type === 'ypxh') {
        this.query.yaoPinLX = '1|2|4';
        this.query.shiFouCXSYFW = 0;
        this.query.guiGeLX = 1;
        data = await GetGongYongYPForYFList(this.query);
      }
      if (this.type === 'gtypsqks') {
        this.query.yaoPinLX = '1|2|3|4';
        this.query.guoTanYP = false;
        data = await GetGongYongYPForYKList(this.query);
      }
      if (this.type === 'zkzy') {
        // this.query.yaoPinLX = '1';
        data = await GetGongYongYPForYKList(this.query);
      }
      if (this.type === 'jc') {
        this.query.jiCaiYPBZ = this.jiCaiYPBZ;
        data = await GetGongYongYPForYKList(this.query);
      }
      if (this.type == 'jcjp') {
        this.query.jiCaiYPBZ = this.jiCaiYPBZ;
        this.query.paiChuJGIDList = this.paiChuJGIDList;
        data = await GetGongYongYPForYKList(this.query);
      }
      await this.getXianShiXX(data);
      const xianShiXX = this.xianShiXX;
      data.forEach((item) => {
        if (this.labelKey != 'yaoPinMC') {
          item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
        }
        item.label = item.yaoPinMC;
        item.xianShiXX = xianShiXX[item.jiaGeID] || {};
      });
      return data;
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        const params = {
          jiaGeIDList,
          xianShiLXDM: '1',
        };
        if (this.isRuKuPage) {
          params.shiFouXSJC = true;
        }
        res = await GetYaoPinTSSX(params);
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      this.xianShiXX = { ...this.xianShiXX, ...xianShiXX };
    },
  },
  components: {
    'md-select-table': MdSelectTable,
  },
};
</script>

<style scoped lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$namespace}-table-select {
  width: 300px;
  // background-color: rgba($color: #fff, $alpha: 0.6);
  background-color: #fff;
  border-radius: 4px;

  ::v-deep .biz-input.focus {
    border-color: getCssVar('color-6');
  }
}
</style>
<style lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.biz-select__options {
  padding: 0 !important;

  .bmis-table__header {
    background-color: getCssVar('color-1');
  }
}
</style>
