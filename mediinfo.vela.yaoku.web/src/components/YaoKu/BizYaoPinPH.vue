<template>
  <md-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    title="药品批号"
    class="ypph-dialog"
  >
    <div class="ypph-dialog__header">
      {{ currentYaoPin && currentYaoPin.yaoPinMC }}
      <input class="fix-input" ref="fixInput" type="text" />
    </div>
    <div class="ypph-dialog__table">
      <md-table
        :data="tableData"
        :columns="columns"
        highlight-current-row
        height="100%"
        ref="mdTablePro"
        @row-dblclick="handleDoubleClick"
        @current-change="currentChange"
      >
      </md-table>
    </div>
    <template v-slot:footer>
      <md-button type="primary" plain @click="handleClose"> 取消 </md-button>
      <md-button type="primary" @click="handleSave"> 确定 </md-button>
    </template>
  </md-dialog>
</template>

<script>
import { logger } from '@/service/log';
import { GetChuKuYPPCListYF } from '@/service/yaoPinYF/common';
import { GetPanCunYPPCListYF } from '@/service/yaoPinYF/yaoFangPC';
import { GetBaoSunYPPCListYF } from '@/service/yaoPinYF/yaoKuBS';
import { GetChuKuYPPCList } from '@/service/yaoPinYK/common';
import { GetBaoSunYPPCList } from '@/service/yaoPinYK/yaoKuBS';
import { GetPanCunYPPCListYK } from '@/service/yaoPinYK/yaoKuPC';
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
export default {
  name: 'biz-yaopinph',
  props: {
    model: {
      type: String,
      default: 'ck',
      validator: function (v) {
        return ['ck', 'bs', 'yfck', 'yfbs', 'yfpc', 'ykpc', 'rk'].includes(v);
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      currentYaoPin: null,
      selectedRow: null,
      columns: [
        {
          label: '生产批号',
          prop: 'shengChanPH',
          hidden: false,
        },
        {
          label: '药品效期',
          prop: 'yaoPinXQ',
          formatter: (row) => {
            return row.yaoPinXQ ? dayjs(row.yaoPinXQ).format('YYYY-MM-DD') : '';
          },
          hidden: false,
        },
        {
          label: '进价',
          prop: 'jinJia',
          // align:'right'
        },
        {
          label: '零售价',
          prop: 'lingShouJia',
          // align:'right'
        },
        {
          label: '库存量',
          prop: 'kuCunSL',
        },
        {
          label: '最近出库',
          prop: 'zuiJinYCCKBZ',
          align: 'center',
          render: (h, { row }) => {
            if (row.zuiJinYCCKBZ) {
              return h('md-icon', {
                class: 'iconfont icongou',
                style: {
                  color: '#1e88e5',
                },
              });
            } else {
              return '';
            }
          },
        },
      ],
      piHaoList: [],
      tableData: [],
      currentIndex: -1,
      refTable: null, //tableRef
      tableBody: null, //tableBody对象
      bodyClientHeight: 249,
      rowHeight: 33, //一行的高度
      currentHeight: 33,
    };
  },
  watch: {
    dialogVisible: {
      handler: function (val) {
        if (val) {
          this.$nextTick(() => {
            if (Array.isArray(this.tableData) && this.tableData.length > 0) {
              this.currentIndex = 0;
              this.currentHeight = this.rowHeight;
              this.refTable = this.$refs.mdTablePro;
              this.tableBody = this.refTable.$el.querySelector(
                `.mediinfo-vela-yaoku-web-base-table__body-wrapper`,
              );
              // console.log(this.$refs, 'this.refTable');
              // this.tableBody = this.refTable.$el
              // const input = this.$refs.fixInput;
              // input.focus();
              this.refTable.setCurrentRow(this.tableData[0]);
            } else {
              this.currentIndex = -1;
            }
          });
        }
      },
    },
  },
  methods: {
    handleClear() {
      this.tableData = [];
      document.removeEventListener('keyup', this.handleKeyUp);
    },
    show(item) {
      this.handleClear();
      this.currentYaoPin = item;
      this.columns[0].hidden = this.model === 'ykpc' ? true : false;
      this.columns[1].hidden = this.model === 'ykpc' ? true : false;
      let time = setTimeout(() => {
        document.addEventListener('keyup', this.handleKeyUp);
        clearTimeout(time);
      }, 500);
      this.handleFetch();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    hide() {
      this.dialogVisible = false;
    },
    async handleRefresh() {
      await this.$refs.mdTablePro.refresh();
    },
    handleKeyUp(event) {
      event.stopPropagation();
      if (!this.dialogVisible) return;
      const len = this.tableData.length;
      if (len == 0) return;
      //下键
      if (event.keyCode == 40 && this.currentIndex < len - 1) {
        this.currentIndex++;
        this.currentHeight += this.rowHeight;
        //上键
      } else if (event.keyCode == 38 && this.currentIndex > 0) {
        this.currentIndex--;
        this.currentHeight -= this.rowHeight;
      }
      if (this.bodyClientHeight < this.currentHeight) {
        this.tableBody.scrollTop = this.currentHeight - this.bodyClientHeight;
      } else {
        this.tableBody.scrollTop = 0;
      }
      const row = this.tableData.find((item, index) => {
        return this.currentIndex == index;
      });
      this.refTable.setCurrentRow(row);
      if (event.keyCode == 13) {
        this.handleDoubleClick();
      }
    },
    async handleFetch() {
      let items;
      try {
        const params = {
          jiaGeID: this.currentYaoPin.jiaGeID,
          chuRuKFSID: this.currentYaoPin.chuRuKFSID,
          danWeiBMID: this.currentYaoPin.danWeiBMID,
          zuZhiJGID: this.currentYaoPin.zuZhiJGID,
          kaiShiSJ: this.currentYaoPin.kaiShiSJ,
          jieShuSJ: this.currentYaoPin.jieShuSJ,
          pageIndex: 1,
          pageSize: 999,
        };
        this.dialogVisible = false;
        if (this.model === 'ck') {
          items = await GetChuKuYPPCList(params);
        } else if (this.model === 'bs') {
          items = await GetBaoSunYPPCList(params);
        } else if (this.model === 'yfck') {
          items = await GetChuKuYPPCListYF(params);
        } else if (this.model === 'yfbs') {
          items = await GetBaoSunYPPCListYF(params);
        } else if (this.model === 'ykpc') {
          items = await GetPanCunYPPCListYK(params);
        } else if (this.model === 'yfpc') {
          items = await GetPanCunYPPCListYF(params);
        } else if (this.model === 'rk') {
          items = await GetChuKuYPPCList(params);
        }
        this.tableData = items;
        const total = items.length;
        if (total > 1) {
          this.dialogVisible = true;
          this.$emit('showDialog', true);
        } else {
          this.resolve(items[0]);
        }
      } catch (e) {
        logger.error(e);
        if (e.message !== 'Duplicate request.') this.reject(e);
        throw new Error(e);
      } finally {
        this.$emit('loading');
      }
    },
    currentChange(currentRow) {
      this.selectedRow = currentRow;
    },
    handleDoubleClick() {
      this.handleSave();
    },
    async handleSave() {
      if (this.selectedRow) {
        this.selectedRow.isDuoTiao = true;
        this.resolve(this.selectedRow);
        this.$emit('select', this.selectedRow);
        this.$emit('showDialog', false);
        this.dialogVisible = false;
      } else {
        MdMessage.warning('请选择药品');
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.reject();
    },
  },
};
</script>

<style scoped lang="scss">
.ypph-dialog {
  ::v-deep .#{$md-prefix}-scrollbar__view {
    padding: 0 !important;
  }
  .ypph-dialog__header {
    position: relative;
    padding: 6px 8px;
    background: #f5f5f5;
    font-size: 14px;
    line-height: 20px;
    color: #222;
    input {
      opacity: 0;
    }
  }
  .ypph-dialog__table {
    height: 298px;
    box-sizing: border-box;
    padding: 8px;
  }
}
</style>
