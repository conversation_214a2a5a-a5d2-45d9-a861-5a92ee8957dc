<template>
  <md-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    title="历次进价"
    class="ypjj-dialog"
  >
    <div class="ypjj-dialog__table">
      <md-table
        :data="tableData"
        :columns="columns"
        highlight-current-row
        height="100%"
        ref="mdTablePro"
        @row-dblclick="handleDoubleClick"
        @current-change="currentChange"
        class="lishijjtable"
      >
        <template #xuhao="{ row, $index }">
          <span>{{ $index + 1 }}</span>
        </template>
        <template #liCiJJ="{ row, $index }">
          <span v-if="$index !== this.tableData.length - 1">{{
            row.liCiJJ
          }}</span>
          <md-input
            v-else
            v-number.float="{}"
            v-model="row.liCiJJ"
            placeholder="请填写"
          />
        </template>
      </md-table>
    </div>
    <template v-slot:footer>
      <md-button type="primary" @click="handleSave"> 确定 </md-button>
    </template>
  </md-dialog>
</template>

<script>
import { logger } from '@/service/log';
import { GetChuKuYPLCJJList } from '@/service/yaoPinYK/common';
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
export default {
  name: 'biz-yaopinph',
  props: {
    model: {
      type: String,
      default: 'ck',
      validator: function (v) {
        return ['ck', 'bs', 'yfck', 'yfbs', 'yfpc', 'ykpc', 'rk'].includes(v);
      },
    },
    chuRuKFSID: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialogVisible: false,
      currentYaoPin: null,
      selectedRow: null,
      tableData: [],
      currentIndex: -1,
      refTable: null, //tableRef
      tableBody: null, //tableBody对象
      bodyClientHeight: 249,
      rowHeight: 33, //一行的高度
      currentHeight: 33,
    };
  },
  computed: {
    columns() {
      if (this.chuRuKFSID === '1001') {
        return [
          {
            label: '序号',
            slot: 'xuhao',
            width: 60,
          },
          {
            label: '入库日期',
            prop: 'jiZhangSJ',
            formatter: (row, column, cellValue, index) => {
              return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
            },
          },
          {
            label: '进价',
            prop: 'liCiJJ',
            align: 'right',
          },
          {
            label: '扣率',
            prop: 'kouLv',
            align: 'right',
            formatter: (row, column, cellValue, index) => {
              return cellValue ? cellValue : 0;
            },
          },
        ];
      } else {
        return [
          {
            label: '序号',
            slot: 'xuhao',
            width: 60,
          },
          {
            slot: 'liCiJJ',
            label: '历史进价',
            prop: 'liCiJJ',
            align: 'right',
            startMode: 'click',
            endMode: 'custom',
            formatter: (row, column, cellValue, index) => {
              return cellValue;
            },
          },
          {
            label: '库存量',
            prop: 'kuCunSL',
            align: 'right',
          },
        ];
      }
    },
  },
  watch: {
    dialogVisible: {
      handler: function (val) {
        if (val) {
          this.$nextTick(() => {
            if (Array.isArray(this.tableData) && this.tableData.length > 0) {
              this.currentIndex = 0;
              this.currentHeight = this.rowHeight;
              this.refTable = this.$refs.mdTablePro;
              this.tableBody = this.refTable.$el.querySelector(
                `.mediinfo-vela-yaoku-web-base-table__body-wrapper`,
              );
              //退库
              if (this.chuRuKFSID === '9003') {
                this.tableData.push({
                  jiaGeID: '',
                  liCiJJ: '',
                  lingShouJia: '',
                  kuCunSL: 0,
                  kuSunZL: this.tableData[0].kuCunZL,
                });
              }
              // console.log(this.$refs, 'this.refTable');
              // this.tableBody = this.refTable.$el
              // const input = this.$refs.fixInput;
              // input.focus();
              this.refTable.setCurrentRow(this.tableData[0]);
            } else {
              this.currentIndex = -1;
            }
          });
        }
      },
    },
  },
  methods: {
    handleClear() {
      this.tableData = [];
      document.removeEventListener('keyup', this.handleKeyUp);
    },
    show(item) {
      this.handleClear();
      this.currentYaoPin = item;
      let time = setTimeout(() => {
        document.addEventListener('keyup', this.handleKeyUp);
        clearTimeout(time);
      }, 500);
      this.handleFetch();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    handleKeyUp(event) {
      event.stopPropagation();
      if (!this.dialogVisible) return;
      const len = this.tableData.length;
      if (len == 0) return;
      //下键
      if (event.keyCode == 40 && this.currentIndex < len - 1) {
        this.currentIndex++;
        this.currentHeight += this.rowHeight;
        //上键
      } else if (event.keyCode == 38 && this.currentIndex > 0) {
        this.currentIndex--;
        this.currentHeight -= this.rowHeight;
      }
      if (this.bodyClientHeight < this.currentHeight) {
        this.tableBody.scrollTop = this.currentHeight - this.bodyClientHeight;
      } else {
        this.tableBody.scrollTop = 0;
      }
      const row = this.tableData.find((item, index) => {
        return this.currentIndex == index;
      });
      this.refTable.setCurrentRow(row);
      if (event.keyCode == 13) {
        this.handleDoubleClick();
      }
    },
    async handleFetch() {
      let items;
      try {
        const params = {
          jiaGeID: this.currentYaoPin.jiaGeID,
        };
        this.dialogVisible = false;
        items = await GetChuKuYPLCJJList(params);
        this.tableData = items;
        const total = items.length;
        if (total > 0) {
          this.dialogVisible = true;
          this.$emit('showDialog', true);
        } else {
          if (this.chuRuKFSID === '1001') {
            this.resolve({});
            return;
          }
          MdMessage.warning('当前药品没有历史进价信息');
          this.reject('当前药品没有历史进价信息');
        }
      } catch (e) {
        logger.error(e);
        if (e.message !== 'Duplicate request.') this.reject(e);
        throw new Error(e);
      } finally {
        this.$emit('loading');
      }
    },
    currentChange(currentRow) {
      this.currentIndex = this.tableData.findIndex((item, index) => {
        return currentRow.liCiJJ == item.liCiJJ;
      });
      this.selectedRow = currentRow;
    },
    handleDoubleClick() {
      this.handleSave();
    },
    async handleSave() {
      if (this.selectedRow) {
        //退库保存逻辑
        if (this.chuRuKFSID === '9003') {
          if (!this.selectedRow.liCiJJ && this.selectedRow.liCiJJ !== 0) {
            MdMessage.warning('请填写历史进价！');
            return;
          }
          //如果是手动填写的数据，将零售价和库存总量赋值
          if (!this.selectedRow.jiaGeID) {
            this.selectedRow.jiaGeID = this.tableData[0].jiaGeID;
            this.selectedRow.kuCunZL = this.tableData[0].kuCunZL;
            this.selectedRow.lingShouJia = this.selectedRow.liCiJJ;
          }
          this.selectedRow.isDuoTiao = true;
          this.resolve(this.selectedRow);
          this.$emit('select', this.selectedRow);
          this.dialogVisible = false;
        } else {
          this.selectedRow.isDuoTiao = true;
          this.resolve(this.selectedRow);
          this.$emit('select', this.selectedRow);
          this.dialogVisible = false;
        }
      } else {
        MdMessage.warning('请选择一条历史进价');
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.reject();
    },
  },
};
</script>

<style scoped lang="scss">
.ypjj-dialog {
  ::v-deep .#{$md-prefix}-scrollbar__view {
    padding: 0 !important;
  }

  .ypjj-dialog__header {
    position: relative;
    padding: 6px 8px;
    background: #f5f5f5;
    font-size: 14px;
    line-height: 20px;
    color: #222;

    input {
      opacity: 0;
    }
  }

  .ypjj-dialog__table {
    height: 298px;
    box-sizing: border-box;
    padding: 8px;

    span {
      display: block;
      height: 30px;
      line-height: 30px;
      padding: 0 8px;
    }

    ::v-deep .lishijjtable {
      .mediinfo-vela-yaoku-web-base-table__body-wrapper {
        .mediinfo-vela-yaoku-web-base-table__row {
          background-color: #f5f5f5;

          .mediinfo-vela-yaoku-web-base-table__cell {
            padding: 0px;

            .cell {
              padding: 0px;

              .mediinfo-vela-yaoku-web-input__wrapper {
                box-shadow: unset;
                border-radius: 0px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
