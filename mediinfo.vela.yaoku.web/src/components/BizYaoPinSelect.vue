<template>
  <md-select-table
    v-model="yaoPinOBJ"
    :fetchData="fetchData"
    :columns="columns"
    :labelKey="labelKey"
    :valueKey="valueKey"
    :placeholder="placeholder"
    filterable
    @change="seleceChange"
    @blur="handleBlur"
    @clear="seleceChange"
    ref="tableSelect"
  />
</template>

<script>
import { GetPeiWuJJYPList } from '@/service/yaoPin/yaoPinZD';
import MdSelectTable from '@mdfe/material.select-table';
const isEmptyObject = (value) => {
  if (!value) return true;
  if (!Array.isArray(value) && typeof value === 'object') {
    return Object.keys(value).length === 0;
  } else {
    return true;
  }
};
export default {
  name: 'biz-yaopin',
  props: {
    yaoPinMC: {
      type: String,
      default: null,
    },
    yaoPinID: {
      type: String,
      default: null,
    },
    valueKey: {
      type: String,
      default: 'yaoPinID',
    },
    labelKey: {
      type: String,
      default: 'yaoPinMC',
    },
    placement: {
      type: String,
      default: 'bottom',
    },
    placeholder: {
      type: String,
      default: '请输入药品名称选择',
    },
    showSuffix: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      query: {
        yaoPinMC: '',
        pageSize: 10,
        pageIndex: 1,
        yaoPinLXDMs: '3',
      },
      queryDsiable: false,
      columns: [
        {
          label: '药品名称',
          prop: 'yaoPinMC',
        },
      ],
      yaoPinOBJ: {},
    };
  },
  computed: {
    // yaoPinOBJ: {
    //   get() {
    //     if (this.yaoPinID) {
    //       const obj = {};
    //       obj[this.labelKey] = this.yaoPinMC;
    //       obj[this.valueKey] = this.yaoPinID;
    //       return obj;
    //     } else {
    //       console.log({}, 2);
    //       return {};
    //     }
    //   },
    // },
  },
  watch: {
    yaoPinID: {
      handler(val) {
        this.$nextTick(() => {
          if (val) {
            this.yaoPinOBJ = {
              yaoPinID: this.yaoPinID,
              yaoPinMC: this.yaoPinMC,
            };
          } else {
            this.yaoPinOBJ = {};
          }
        });
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    handleBlur() {
      // console.log('blur');
    },
    seleceChange(data) {
      if (typeof data !== 'object') {
        this.$emit('change', {});
        return;
      }
      let obj = JSON.parse(JSON.stringify(data));
      this.$emit('update:yaoPinID', obj.yaoPinID);
      this.$emit('update:yaoPinMC', obj.yaoPinMC);
      this.$emit('change', data);
    },
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      this.query.yaoPinMC = inputValue;
      this.query.pageSize = pageSize;
      this.query.pageIndex = page;
      this.queryDsiable = false;
      return await GetPeiWuJJYPList(this.query);
    },
  },
  components: {
    'md-select-table': MdSelectTable,
  },
};
</script>

<style scoped lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$namespace}-table-select {
  width: 300px;
  // background-color: rgba($color: #fff, $alpha: 0.6);
  background-color: #fff;
  border-radius: 4px;
  ::v-deep .biz-input.focus {
    border-color: getCssVar('color-6');
  }
}
</style>
<style lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.biz-select__options {
  padding: 0 !important;
  .bmis-table__header {
    background-color: getCssVar('color-1');
  }
}
</style>
