// 注意：为了在微前端中资源加载正常，此行代码必须在顶部
import '@mdfe/stark-app/public-path.js';
import MediUI from '@mdfe/medi-ui';
import MediUIPro from '@mdfe/medi-ui-pro';
import '@/system/bootstrap';
import { setHttpHeaders } from '@/system/utils/request';
import lyra, { installDefaultCanShuService } from '@mdfe/lyra';
import boot from '../../system/bootstrap';
import App from './App.vue';
import { createStore } from '@/store/index.js';
import MdSelectComma from '@mdfe/material.select-comma';
import routes from '../../routes';
import { createRouter, createWebHashHistory } from 'vue-router';
import { createApp } from 'vue';
import { logger, filterError } from '@/service/log';
/**
 * 渲染应用
 *
 * @param {HTMLDivElement} container
 */
function render(container) {
  const store = createStore();
  const router = createRouter({
    history: createWebHashHistory(),
    routes,
  });
  const app = createApp(App);
  app.config.errorHandler = function (
    err,
    instance,
    // `info` 是一个 Vue 特定的错误信息
    // 例如：错误是在哪个生命周期的钩子上抛出的
    info,
  ) {
    // 你需要通过特殊 reportVueError 方法上报此错误
    logger.reportVueError(err, instance, info);
  };
  app.config.globalProperties.$logger = {
    error: (error) => {
      if (
        filterError.some(
          (item) => error.message && error.message.includes(item),
        )
      )
        return;
      logger.error(error);
    },
  };
  app.use(boot);
  app.use(MediUI, { namespace: 'HISHZGL' });
  app.use(MediUIPro);
  app.use(router);
  app.use(store);
  app.use(MdSelectComma);
  app.mount(container || '#app');
}

/**
 * 生命周期 - 微应用启动前
 *
 * @param {Object} options
 */
export async function bootstrap() {}

/**
 * 生命周期 - 微应用挂载
 *
 * @param {Object} options
 * @param {HTMLDivElement} options.container
 */
export async function mount({ container, headers }) {
  const { JiGouID, WeiZhiID, yingYongID } = lyra.getShareDataSync();
  if (yingYongID) {
    installDefaultCanShuService({
      zuZhiJGID: JiGouID,
      yingYongID: yingYongID,
      weiZhiID: WeiZhiID,
    });
  }
  // 设置 HTTP 请求头
  setHttpHeaders({
    ...headers,
  });

  // 渲染 vue 应用
  render(container.querySelector('#app'));
}

/**
 * 生命周期 - 微应用卸载
 *
 * @param {Object} options
 * @param {HTMLDivElement} options.container
 */
export async function unmount({ container }) {
  const rootElement = container.querySelector('#app');
  const app = rootElement ? rootElement.__vue__ : null;
  if (app) {
    app.$destroy();
    app.$el.innerHTML = '';
  }
}
