<template>
  <div id="app">
    <md-config-provider
      :namespace="prefixCls"
      :theme="theme"
      :size="size"
      :spacing="spacing"
      :border="border"
    >
      <md-theme-provider>
        <ShuangKongYPXL v-bind="$attrs" />
      </md-theme-provider>
    </md-config-provider>
  </div>
</template>

<script>
import ShuangKongYPXL from '@/views/yaoKuGL/shuangKongYPXL/ShuangKongYPXL.vue';
export default {
  name: 'ShuangKongYPXLpage',
  data() {
    return {
      prefixCls: process.env.VUE_APP_NAMESPACE,
      theme: 'default',
      size: 'default',
      spacing: 'default',
      border: 'default',
    };
  },
  components: {
    ShuangKongYPXL,
  },
};
</script>
