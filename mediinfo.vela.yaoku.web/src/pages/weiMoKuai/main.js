import '@mdfe/stark-app/public-path';
import '@/system/bootstrap';
import Vue from 'vue';

import App from './App.vue';
import { createStore } from '@/store';
import { setUserInfo } from '@/system/utils/local-cache';

let store = null;

export async function bootstrap() {}

export async function mount({ container, ...data }) {
  if (data.header) {
    setUserInfo(data.header);
  }
  store = createStore();
  const app = new Vue({
    // 为了保持数据的整体响应，必须讲数据传递给 data
    data: data,
    store,
    render(h) {
      return h(App, { props: this.$data });
    },
  });

  app.$mount(container.querySelector('#app'));
}

export async function unmount({ container }) {
  const mountElement = container.querySelector('#app');
  const app = mountElement ? mountElement['__vue__'] : null;

  if (app) {
    app.$destroy();
    app.$el.innerHTML = '';
  }
}
