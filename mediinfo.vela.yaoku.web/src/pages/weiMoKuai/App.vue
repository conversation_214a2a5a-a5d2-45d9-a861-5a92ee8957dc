<template>
  <div id="app">
    <md-config-provider
      prefixCls="HISYK"
      :theme="theme"
      :size="size"
      :spacing="spacing"
      :border="border"
    >
      <md-theme-provider>
        <component
          v-if="component"
          :is="component"
          :weiMoKuaiData="formData"
          :opener="opener"
        ></component>
      </md-theme-provider>
    </md-config-provider>
  </div>
</template>

<script>
import { mapState } from 'vuex';
const tabs = (moKuai) => {
  const path = {
    caoYaoXDF: 'caoYaoXDF/index.vue',
  };
  return () => import(`@/views/${path[moKuai]}`);
};

export default {
  name: 'App',
  props: {
    opener: Object,
    formData: Object,
  },
  data() {
    return {
      component: null,
      theme: 'default',
      size: 'default',
      spacing: 'default',
      border: 'default',
    };
  },
  // computed: {
  //   ...mapState('app', ['configSetting'])
  // },
  // watch: {
  //   configSetting: {
  //     handler(val) {
  //       this.theme = val.theme
  //       this.size = val.size
  //       this.spacing = val.spacing
  //       this.border = val.border
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // },
  mounted() {
    this.component = tabs(this.formData.moKuai);
  },
};
</script>
<style lang="scss" scoped>
.HISWJZ-message-body {
  padding: var(--md-spacing-3);
}
</style>
