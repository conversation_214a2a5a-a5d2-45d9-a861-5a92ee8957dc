<template>
  <div :class="prefixClass('peiwujj-views')">
    <div :class="prefixClass('peiwujj-views-left')">
      <div
        v-for="item in yaoPinJJLXList"
        :class="[
          prefixClass('jinjilx-item'),
          { active: jinJiLXactive === item.biaoZhunDM },
        ]"
        @click="changeJinJiLX(item)"
      >
        <md-title
          :label="item.biaoZhunMC"
          style="font-size: 14px"
          type="grace"
        />
        <md-icon
          name="youjiantou-s"
          v-if="jinJiLXactive === item.biaoZhunDM"
        ></md-icon>
      </div>
    </div>
    <div :class="prefixClass('peiwujj-views-right')">
      <div :class="prefixClass('peiwujj-contain')">
        <div :class="prefixClass('peiwujj-contain-caozuo')">
          <md-input
            placeholder="输入药品名称搜索"
            v-model="yaoPinMC"
            style="width: 320px"
            @change="handleSearch"
            @clear="handleSearch"
          >
            <template #suffix>
              <md-icon slot="suffix" name="seach" @click="handleSearch()" />
            </template>
          </md-input>
          <md-button
            type="primary"
            :icon="prefixClass('icon-jia')"
            @click="handlePeiWuJJ('new')"
            >配伍禁忌</md-button
          >
        </div>
        <div :class="prefixClass('peiwujj-contain-table')">
          <md-table-pro
            ref="tablePro"
            :columns="columns"
            :onFetch="handleFetch"
            :autoLoad="false"
            height="100%"
          >
            <template #shiYongFW="{ $index, row, cellRef }">
              <div :class="prefixClass('table-shiyongfw')">
                <span :class="[{ active: row.zhuYuanSYBZ }]">住</span>
                <span :class="[{ active: row.menZhenSYBZ }]">门</span>
              </div>
            </template>
          </md-table-pro>
        </div>
      </div>
    </div>
    <peiwujj-dialog
      ref="peiWuJJDialog"
      @refresh="handleSearch"
    ></peiwujj-dialog>
  </div>
</template>
<script>
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import peiWuJJDialog from './PeiWuJJDialog.vue';
import { GetPeiWuJJCount, GetPeiWuJJList } from '@/service/yaoPinYK/yaoPinGZ';
export default {
  data() {
    return {
      yaoPinJJLXList: [],
      jinJiLXactive: '',
      jinJiLXMC: '',
      yaoPinMC: '',
      columns: [
        {
          label: '药品名称',
          prop: 'yaoPinMC',
        },
        {
          label: '排斥药品名称',
          prop: 'paiChiYPMC',
        },
        {
          label: '提示内容',
          prop: 'tiShiNR',
        },
        {
          label: '使用范围',
          prop: 'shiYongFW',
          width: 74,
          slot: 'shiYongFW',
        },
        {
          type: 'operate',
          label: '操作',
          width: 46,
          actions: [
            {
              text: '编辑',
              onPressed: ({ row, column, $index }) => {
                this.handlePeiWuJJ('edit', row);
              },
            },
          ],
        },
      ],
    };
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      getYaoPinShuJuYZYList([
        'YP0087', //配伍禁忌类型
      ]).then((res) => {
        this.yaoPinJJLXList = res[0].zhiYuList;
        this.jinJiLXactive = this.yaoPinJJLXList[0].biaoZhunDM;
        this.jinJiLXMC = this.yaoPinJJLXList[0].biaoZhunMC;
        this.$refs.tablePro.search();
      });
    },
    changeJinJiLX(item) {
      this.jinJiLXactive = item.biaoZhunDM;
      this.jinJiLXMC = item.biaoZhunMC;
      this.$refs.tablePro.search();
    },
    // 查询条件变更搜索
    handleSearch() {
      this.$refs.tablePro.search();
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        leiXingDM: this.jinJiLXactive,
        pageSize: pageSize,
        pageIndex: page,
        yaoPinMC: this.yaoPinMC,
      };
      if (!this.yaoPinJJLXList.length) {
        return {
          items: [],
          total: 0,
        };
      }
      const [items, total] = await Promise.all([
        GetPeiWuJJList(params, config),
        GetPeiWuJJCount(params, config),
      ]);
      return {
        items,
        total,
      };
    },
    handlePeiWuJJ(type, row) {
      this.$refs.peiWuJJDialog.showModal({
        mode: type,
        row: {
          ...row,
          leiXingDM: this.jinJiLXactive,
          leiXingMC: this.jinJiLXMC,
        },
      });
    },
  },
  components: {
    'peiwujj-dialog': peiWuJJDialog,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-peiwujj-views {
  padding: var(--md-spacing-3);
  display: flex;
  height: 100%;
  &-left {
    width: 160px;
    min-width: 160px;
    height: 100%;
    margin-right: var(--md-spacing-3);
    background-color: #fff;
    padding: var(--md-spacing-3);
    .#{$md-prefix}-jinjilx-item {
      background: #f5f5f5;
      border-radius: 4px;
      padding: var(--md-spacing-1x) var(--md-spacing-3);
      margin-bottom: var(--md-spacing-3);
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      &.active {
        background-color: var(--md-color-primary-light-7);
      }
      ::v-deep .#{$md-prefix}-title__content {
        font-size: var(--md-font-2);
        font-weight: normal;
      }
    }
  }
  &-right {
    flex: 1;
    background-color: #fff;
    padding: var(--md-spacing-3);
  }
  .#{$md-prefix}-peiwujj-contain {
    // flex: 1;
    // overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    &-caozuo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--md-spacing-3);
    }
    &-table {
      flex: 1;
      overflow: hidden;
    }
  }
  .#{$md-prefix}-table-shiyongfw {
    span {
      padding: 3px;
      background-color: #ccc;
      color: #fff;
      &:first-child {
        margin-right: 5px;
      }
      &.active {
        background-color: var(--md-color-primary);
      }
    }
  }
}
</style>
