<template>
  <md-dialog
    :title="title"
    v-model="dialogVisible"
    :before-save="handleSave"
    :before-close="handleClose"
    width="560px"
    height="340px"
    :content-scrollable="false"
    lock-scroll
    :close-on-click-modal="false"
  >
    <div class="dialogPage">
      <md-form
        :model="formData"
        label-width="110px"
        use-status-icon
        ref="formValidata"
        :rules="rules"
      >
        <md-form-item label="类型" prop="leiXingDM">
          <md-select v-model="formData.leiXingDM" disabled>
            <md-option
              v-for="item in yaoPinJJLXList"
              :label="item.biaoZhunMC"
              :value="item.biaoZhunDM"
              :key="item.biaoZhunDM"
            />
          </md-select>
        </md-form-item>

        <md-form-item label="药品名称" prop="yaoPinMC">
          <biz-yaopin-select
            v-model:yaoPinID="formData.yaoPinID"
            v-model:yaoPinMC="formData.yaoPinMC"
            @change="(data) => changeYaoPin(data, '1')"
          />
        </md-form-item>
        <md-form-item label="排斥药品名称" prop="paiChiYPMC">
          <biz-yaopin-select
            v-model:yaoPinID="formData.paiChiYPID"
            v-model:yaoPinMC="formData.paiChiYPMC"
            @change="(data) => changeYaoPin(data, '2')"
          />
        </md-form-item>
        <md-form-item label="提示内容" prop="tiShiNR">
          <md-input
            :rows="2"
            type="textarea"
            v-model="formData.tiShiNR"
            placeholder="请输入"
          ></md-input>
        </md-form-item>
        <md-form-item label="">
          <div :class="prefixClass('shiyongbz')">
            <md-checkbox
              label="住院使用"
              v-model="formData.zhuYuanSYBZ"
              :false-label="0"
              :true-label="1"
            />
            <md-checkbox
              label="门诊使用"
              v-model="formData.menZhenSYBZ"
              :false-label="0"
              :true-label="1"
            />
          </div>
        </md-form-item>
        <md-form-item label="顺序号" prop="shunXuHao">
          <md-input
            v-model="formData.shunXuHao"
            placeholder="请输入"
            v-number="{}"
          ></md-input>
        </md-form-item>
      </md-form>
    </div>

    <template #footer class="dialog-footer">
      <md-button
        :disabled="saveLoading"
        type="danger"
        plain
        style="float: left"
        @click="handleCancel"
        v-if="mode !== 'new'"
      >
        作废
      </md-button>
      <md-button
        type="primary"
        plain
        :disabled="saveLoading"
        @click="handleClose"
        >取消</md-button
      >
      <md-button type="primary" :loading="saveLoading" @click="handleSave">
        保存
      </md-button>
    </template>
  </md-dialog>
</template>

<script>
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  SavePeiWuJJList,
  DeletePeiWuJJList,
} from '@/service/yaoPinYK/yaoPinGZ';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import cloneDeep from 'lodash/cloneDeep';
import BizYaoPinSelect from '@/components/BizYaoPinSelect.vue';
const initFormData = () => {
  return {
    id: '',
    leiXingDM: '',
    yaoPinID: '',
    yaoPinMC: '',
    paiChiYPMC: '',
    paiChiYPID: '',
    menZhenSYBZ: 0,
    zhuYuanSYBZ: 0,
    tiShiNR: '',
    shunXuHao: null,
  };
};
export default {
  data() {
    return {
      dialogVisible: false,
      saveLoading: false,
      formData: {
        id: '',
        leiXingDM: '',
        yaoPinID: '',
        yaoPinMC: '',
        paiChiYPMC: '',
        paiChiYPID: '',
        menZhenSYBZ: 0,
        zhuYuanSYBZ: 0,
        tiShiNR: '',
        shunXuHao: null,
      },
      yaoPinJJLXList: [],
      peiWuJJYPList: [],
      peiZhiFSList: [],
      yongYaoFLList: [],
      yiZhuXZList: [],
      onCheck: false,
      mode: '',
      rules: {
        leiXingDM: [
          { required: true, trigger: 'change', message: '请选择类型' },
        ],
        yaoPinMC: [
          {
            required: true,
            trigger: ['change', 'blur'],
            message: '请选择药品',
          },
        ],
        paiChiYPMC: [
          {
            required: true,
            trigger: ['change', 'blur'],
            message: '请选择药品',
          },
        ],
      },
    };
  },
  computed: {
    title() {
      return this.mode == 'new' ? '新增中药配伍禁忌' : '编辑中药配伍禁忌';
    },
  },
  watch: {
    // 'formData.yaoPinID': {
    //   handler(val) {
    //     console.log(val, 'val');
    //   },
    // },
  },
  methods: {
    async showModal(options) {
      try {
        this.dialogVisible = true;
        this.mode = options.mode;
        if (this.mode === 'edit') {
          this.formData = cloneDeep(options.row);
        } else {
          this.formData = {
            id: '',
            leiXingDM: options.row.leiXingDM,
            leiXingMC: options.row.leiXingMC,
            yaoPinID: '',
            yaoPinMC: '',
            paiChiYPMC: '',
            paiChiYPID: '',
            menZhenSYBZ: 0,
            zhuYuanSYBZ: 0,
            tiShiNR: '',
            shunXuHao: null,
          };
          this.$nextTick(() => {
            this.$refs.formValidata.clearValidate();
          });
        }

        // 获取配伍禁忌类型
        const res = await getYaoPinShuJuYZYList([
          'YP0087', //配伍禁忌类型
        ]);
        this.yaoPinJJLXList = res[0].zhiYuList;
      } catch (error) {
        MdMessage.error(error);
      }
    },
    //关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      // setTimeout(() => {
      // this.$nextTick(() => {
      this.formData = {
        id: '',
        leiXingDM: '',
        leiXingMC: '',
        yaoPinID: '',
        yaoPinMC: '',
        paiChiYPMC: '',
        paiChiYPID: '',
        menZhenSYBZ: 0,
        zhuYuanSYBZ: 0,
        tiShiNR: '',
        shunXuHao: null,
      };
      // });
      // }, 500);
    },
    //保存
    async handleSave() {
      const ref = await this.$refs.formValidata.validate();
      if (!ref) return;
      try {
        this.saveLoading = true;
        const form = cloneDeep(this.formData);
        await SavePeiWuJJList(form);
        MdMessage.success('保存成功！');
        this.handleClose();
        this.$emit('refresh');
      } catch (error) {
        // MdMessage.warning(error);
      } finally {
        this.saveLoading = false;
      }
    },
    async handleCancel() {
      try {
        await MdMessageBox.confirm(`确定作废该配伍禁忌？`, '操作提醒！', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        await DeletePeiWuJJList(this.formData.id).then(() => {
          MdMessage.success('作废成功!');
          this.handleClose();
          this.$emit('refresh');
        });
      } catch (error) {
        if (error === 'cancel') return;
        MdMessage.error(error.message);
      }
    },
    changeYaoPin(data, type) {
      if (type == '1') {
        if (JSON.stringify(data) == '{}') {
          this.formData.yaoPinID = '';
          this.formData.yaoPinMC = '';
        } else {
          this.formData.yaoPinID = data.yaoPinID;
          this.formData.yaoPinMC = data.yaoPinMC;
        }
      } else {
        if (JSON.stringify(data) == '{}') {
          this.formData.paiChiYPID = '';
          this.formData.paiChiYPMC = '';
        } else {
          this.formData.paiChiYPID = data.yaoPinID;
          this.formData.paiChiYPMC = data.yaoPinMC;
        }
      }
    },
  },
  components: {
    'biz-yaopin-select': BizYaoPinSelect,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-shiyongbz {
  width: 100%;
  border: 1px dashed;
  border-color: #dddddd;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}
.dialogPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 8px;
  box-sizing: border-box;
}

.formStyle {
  padding: 12px 8px 8px;
  box-sizing: border-box;
  background-color: #f5f5f5;
}
.shuyel:hover {
  border-color: #3da0fc !important;
}
::v-deep .ywgl-scrollbar__view {
  padding: 0 !important;
  height: 100% !important;
}

.ywgl-dialog__wrapper .ywgl-dialog .ywgl-dialog__footer {
  border: 1px solid;
  border-color: #dddddd;
}
</style>
