<template>
  <md-tabs v-model="activeName" :class="prefixClass('tabs-views')">
    <md-tab-pane
      label="配伍禁忌"
      name="first"
      :class="prefixClass('content-bg')"
      @go-to-tab="handleGoToTab"
      lazy
    >
      <peiwujj-page ref="peiwujj" />
    </md-tab-pane>
  </md-tabs>
</template>

<script>
import DairukuPage from '@/views/yaoKuGL/yaoPinRK/components/DaiRuKuPage';
import WeijizhangPage from '@/views/yaoKuGL/yaoPinRK/components/WeiJiZhangPage';
import YijizhangPage from '@/views/yaoKuGL/yaoPinRK/components/YiJiZhangPage';
import PeiWuJJPage from './components/PeiWuJJPage';
export default {
  name: 'yaopinrk',
  data() {
    return {
      activeName: 'first', // 当前选中导航
      openId: '', // 进入页面时，路由处传入的id （跳转到该页，并显示该id的详情信息。）
      isChongHong: '0', // 是否是其他页面进入
    };
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val.path === '/yaoPinGZ') {
          let query = val.query;
          // 判断是否跳转到某一个导航（second未记账、third已记账）
          if (query && query.showType) {
            if (this.activeName === query.showType) {
              switch (query.showType) {
                case 'second':
                  if (this.$refs.weijizhang)
                    this.$refs.weijizhang.handleSearch();
                  break;
                case 'third':
                  if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch();
                  break;
              }
            }
            this.activeName = query.showType;
          }
          if (query.isChongHong) {
            this.isChongHong = query.isChongHong;
            this.openId = query.id;
          }
        }
      },
    },
    // tab切换时请求数据
    activeName: {
      handler: function (val) {
        switch (val) {
          case 'first':
            if (this.$refs.peiwujj) this.$refs.peiwujj.handleSearch();
            break;
          case 'second':
            // if (this.$refs.weijizhang) this.$refs.weijizhang.handleSearch()
            break;
          case 'third':
            // if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch()
            break;
        }
      },
    },
  },
  methods: {
    //未记账跳转记账页签 yy
    handleGoToTab() {
      this.activeName = 'third';
    },
  },
  components: {
    'peiwujj-page': PeiWuJJPage,
    'weijizhang-page': WeijizhangPage,
    'dairuku-page': DairukuPage,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
  padding: 0 8px;
}
::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  min-height: 0;
  background: #f0f2f5;
}
</style>
