<template>
  <div :class="prefixClass('caoyaoxdf')">
    <div
      :class="[prefixClass('caoyaoxdf-pane'), prefixClass('caoyaoxdf-left')]"
    >
      <div :class="prefixClass('caoyaoxdf-pane__inner')">
        <div :class="prefixClass('caoyaoxdf-pane__header')">
          <div :class="prefixClass('caoyaoxdf-pane__header__title')">
            <md-radio-group
              v-model="query.shiYongFWLB"
              border
              @change="handleSearch"
            >
              <md-radio label="0" :disabled="isMenZhenOrZhuYuan">全院</md-radio>
              <md-radio label="1" :disabled="isMenZhenOrZhuYuan">个人</md-radio>
            </md-radio-group>
          </div>
          <div :class="prefixClass('caoyaoxdf-pane__header__extra')">
            <md-button
              type="primary"
              :disabled="zhuYuanShow"
              :icon="prefixClass('icon-xinzeng')"
              noneBg
              @click="handleAdd"
              >新增</md-button
            >
          </div>
        </div>
        <div :class="prefixClass('caoyaoxdf-pane__search')">
          <md-search-input
            v-model="query.queryLike"
            placeholder="输入协定方名称搜索"
            @search="handleSearch"
          ></md-search-input>
        </div>
        <div :class="prefixClass('caoyaoxdf-pane__main')">
          <md-table
            :columns="columns"
            :data="tableData"
            highlight-current-row
            highlight-arrow-row
            height="100%"
            ref="table"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <div
      :class="[prefixClass('caoyaoxdf-pane'), prefixClass('caoyaoxdf-right')]"
    >
      <div :class="prefixClass('caoyaoxdf-pane__inner')">
        <div :class="prefixClass('caoyaoxdf-pane__main')">
          <md-form
            :model="formData"
            label-width="90px"
            :disabled="zhuYuanShow"
            :class="prefixClass('caoyaoxdf-form')"
            ref="form"
          >
            <md-row>
              <md-col :span="12">
                <md-form-item label="协定方名称">
                  <md-input v-model="formData.zuTaoMC" enter-sort="1" />
                </md-form-item>
              </md-col>
              <md-col :span="6">
              <!-- 协定方类型可修改  :disabled="formData.zuTaoID" -->
                <md-form-item label="类型">
                  <md-select
                    v-model="zhongYaoKFLX"
                    value-key="biaoZhunDM"
                    :clearable="false"
                    enter-sort="2"
                    :automatic-dropdown="true"
                    :before-select="handleLeiXingChange"
                    @change="handleVisibleChange"
                  >
                    <md-option
                      v-for="item in leiXingOptions"
                      :key="item.biaoZhunDM"
                      :label="item.biaoZhunMC"
                      :value="item"
                    >
                    </md-option>
                  </md-select>
                </md-form-item>
              </md-col>
              <md-col :span="6">
                <md-form-item label="频次">
                  <md-input v-model="pingCi" disabled />
                </md-form-item>
              </md-col>
              <md-col :span="6">
                <md-form-item label="用法">
                  <md-select
                    v-model="teShuFF"
                    value-key="biaoZhunDM"
                    enter-sort="4"
                    :automatic-dropdown="true"
                    @change="handleVisibleChange"
                  >
                    <md-option
                      v-for="item in yongFaOptions"
                      :key="item.biaoZhunDM"
                      :label="item.biaoZhunMC"
                      :value="item"
                    >
                    </md-option>
                  </md-select>
                </md-form-item>
              </md-col>
              <md-col :span="6">
                <md-form-item label="顺序号">
                  <md-input v-model="formData.shunXuHao" enter-sort="5" />
                </md-form-item>
              </md-col>
              <md-col :span="6">
                <md-form-item label="保密设置">
                  <md-radio-group
                    v-model="formData.baoMiBZ"
                    enter-sort="6"
                    border
                  >
                    <md-radio :label="0">公开</md-radio>
                    <md-radio :label="1">保密</md-radio>
                  </md-radio-group>
                </md-form-item>
              </md-col>
              <md-col v-if="query.shiYongFWLB == '1'" :span="6">
                <md-form-item label="使用范围">
                  <biz-yisheng-select
                    enter-sort="7"
                    :yiShengXM.sync="formData.yiShengKSMC"
                    :yiShengID.sync="formData.yiShengKSID"
                  />
                </md-form-item>
              </md-col>
              <md-col :span="6">
                <md-form-item label="自费状态">
                  <md-select v-model="formData.ziFeiZT" placeholder="请选择">
                    <md-option
                      v-for="item in ziFeiZTOptions"
                      :key="item.biaoZhunDM"
                      :label="item.biaoZhunMC"
                      :value="item.biaoZhunDM"
                    >
                    </md-option>
                  </md-select>
                </md-form-item>
              </md-col>
            </md-row>
          </md-form>
          <div :class="prefixClass('caoyaoxdf-table')">
            <biz-caoyao-table
              :disabled="zhuYuanShow"
              :list="caoYaoList"
              :zhongYaoKFLX="zhongYaoKFLX"
              :geiYaoFSOptions="geiYaoFSOptions"
              @add="handleAddYiZhu"
              @del="handleDelYiZhu"
            />
          </div>
        </div>
        <div v-if="!zhuYuanShow" :class="prefixClass('caoyaoxdf-pane__footer')">
          <md-button
            v-if="formData.id"
            type="danger"
            plain
            class="floatleft"
            @click="handleZuoFei"
            >作废</md-button
          >
          <span>单剂金额：</span><span class="danJiJE">{{ danJiJE }}</span>
          <md-button type="primary" plain @click="handleQuXiao">取消</md-button>
          <md-button type="primary" @click="handleSave">保存</md-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getXieDingFList,
  getXieDingFXX,
  saveZuTao,
  zuoFeiZuTao,
  getWeiZhiData,
} from '@/service/yiZhu/yiZhuZDZT';
import { zuZhuangJLDWOptions as bmis_zuZhuangJLDWOptions } from '@/system/utils/zuZhuangJLDWOptions';
import SelectTable from '@mdfe/material.select-table';
import { MdMessageBox, useKeyboard } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';

import { getYaoPinShuJuYZYList } from '@/service/yaoPin/geiYaoFS';
import { getYiZhuSJYZYListByLBID } from '@/service/yiZhu/yiZhuYWZD';

import BizYiShengSelect from '@/components/BizYiShengSelect';
import { getGeiYaoFSSel } from '@/service/yaoPin/geiYaoFS';
import { getShuJuYZYList } from '@/service/feiYong/feiYongZD'
import {
  getJiGouID,
  getJiGouMC,
  getYongHuID,
  getYongHuXM,
  getWeiZhiID,
} from '@/system/utils/local-cache';
import { ref } from 'vue';
import CaoYaoTable from './components/CaoYaoTable.vue';
const initFromData = (zhuYuanBZ) => {
  return {
    id: null,
    zuTaoID: null,
    zuZhiJGID: getJiGouID(),
    zuZhiJGMC: getJiGouMC(),
    zuTaoMC: null,
    shunXuHao: null,
    shiYongFWLB: null,
    yiShengKSID: null,
    yiShengKSMC: null,
    menZhenSYBZ: zhuYuanBZ ? null : 1,
    zhuYuanSYBZ: zhuYuanBZ ? 1 : null,
    jiZhenSYBZ: null,
    baoMiBZ: 1,
    xieDingFBZ: 1,
    ziFeiZT: '',
    addZhuYuanYZXXList: [],
    updateZhuYuanYZXXList: [],
    deleteZhuYuanYZXXList: [],
  };
};

const initYiZhuData = (zhuYuanBZ) => {
  return {
    id: null,
    zuZhiJGID: null,
    zuZhiJGMC: null,
    zuTaoID: null,
    zuTaoMC: null,
    fuYiZID: null,
    shunXuHao: null,
    yiZhuDH: null,
    yiZhuMC: null,
    yiZhuXMID: null,
    yiZhuXMMC: null,
    yiShengZT: null,
    yiZhuMS: null,
    yiZhuFLDM: null,
    yiZhuFLMC: null,
    shouRiCS: null,
    changQiYZBZ: null,
    jiFeiJG: null,
    jiFeiFSDM: null,
    jiFeiFSMC: null,
    yiZhuLXDM: null,
    yiZhuLXMC: null,
    geiYaoFSID: null,
    geiYaoFSMC: null,
    geiYaoFSLXDM: null,
    geiYaoFSLXMC: null,
    pinCiID: 'ONCE',
    pinCiMC: 'ONCE',
    jiaGeID: null,
    guiGeID: null,
    daGuiGID: null,
    yaoPinID: null,
    yaoPinMC: null,
    yaoPinGG: null,
    yaoPinBMID: null,
    chanDiID: null,
    chanDiMC: null,
    chanDiLBDM: null,
    chanDiLBMC: null,
    yiCiJL: null,
    yiCiJLDW: null,
    daYinJL: null,
    yiCiYL: null,
    tianShu: null,
    shuLiang: null,
    zuiXiaoDWYL: null,
    zuiXiaoDW: null,
    baoZhuangLiang: null,
    baoZhuangDW: null,
    jiLiang: null,
    jiLiangDW: null,
    tiJi: null,
    tiJiDW: null,
    jiXingID: null,
    jiXingMC: null,
    jiXingLBDM: null,
    jiXingLBMC: null,
    ziFeiZT: null,
    jiZhenBZ: null,
    diSu: null,
    diSuDW: null,
    biGuangBZ: null,
    fuYaoSXDM: null,
    fuYaoSXMC: null,
    lingYaoFSDM: null,
    lingYaoFSMC: null,
    quZhengLXDM: null,
    quZhengLXMC: null,
    yaoFangWZID: null,
    yaoFangWZMC: null,
    chuYuanDYBZ: null,
    jingMaiPBZ: null,
    jiaJiBZ: null,
    shuangQianMBZ: null,
    shiJiDM: null,
    shiJiMC: null,
    caoYaoBZ: null,
    chuFangTS: null,
    fuFangBZ: null,
    zhongYaoKFLXDM: null,
    zhongYaoKFLXMC: null,
    daiJianTS: null,
    daiJianDW: null,
    teShuFFDM: null,
    teShuFFMC: null,
    duLiFLDM: null,
    duLiFLMC: null,
    zhiXingKSID: null,
    zhiXingKSMC: null,
    yangBenLXID: null,
    yangBenLXMC: null,
    jianChaBWID: null,
    jianChaBWMC: null,
    menZhenSYBZ: zhuYuanBZ ? null : 1,
    zhuYuanSYBZ: zhuYuanBZ ? 1 : null,
    jiZhenSYBZ: null,
    caoYaoTGYFSID: null,
    caoYaoTGYFSMC: null,
    benYuanZSCS: null,
    waiPeiBZ: null,
    ziBeiYBZ: null,
    linShiID: null,
    dangRiLJBZ: null,
    zhuRiLJBZ: null,
    zongTianS: null,
  };
};

export default {
  setup() {
    const form = ref(null);
    const { focusNext } = useKeyboard(form);
    return {
      form,
      focusNext,
    };
  },
  name: 'caoyaoxdf',
  props: {
    weiMoKuaiData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      query: {
        shiYongFWLB: '0',
        queryLike: '',
        menZhenZYBZ: null,
        yiShengBZ: null,
      },
      ziFeiZTOptions: [],
      weiZhiData: {},
      radio1: '全院',
      isMenZhenOrZhuYuan: false,
      tableData: [],
      columns: [
        {
          prop: 'shunXuHao',
          label: '顺序号',
          width: 70,
          showOverflowTooltip: false,
        },
        {
          prop: 'zuTaoMC',
          label: '协定方名称',
        },
      ],
      formData: initFromData(),
      yongFaOptions: [],
      leiXingOptions: [],
      geiYaoFSOptions: [],
      zhongYaoKFLX: {
        biaoZhunDM: '3',
        biaoZhunMC: '小包装饮片',
      },
      zhongYaoKFLXOld: null,
      pingCi: 'ONCE',
      teShuFF: null,
      caoYaoList: [],
      oldCaoYaoIds: [],
      danJiJE: 0,
    };
  },
  computed: {
    zhuYuanBZ({ weiMoKuaiData }) {
      return weiMoKuaiData.zhuYuanBZ;
    },
    zhuYuanShow({ zhuYuanBZ, query }) {
      return zhuYuanBZ && query.shiYongFWLB === '0';
    },
  },
  created() {
    this.getZiDian();
    this.getWeiZhiIDData();
    this.initFeiYongData();
  },
  watch: {
    caoYaoList: {
      handler() {
        const list = this.caoYaoList;
        let danJiJE = 0;
        if (list && list.length > 0) {
          list?.map((item) => {
            if (item.danJia && item.yiCiJL)
              danJiJE += Number(item.danJia) * Number(item.yiCiJL);
          });
        }
        if (danJiJE != 0) danJiJE = danJiJE.toFixed(2);
        this.danJiJE = danJiJE;
      },
      deep: true,
    },
  },
  methods: {
    async initFeiYongData() {
      const res = await getShuJuYZYList({
        shuJuYLBID: 'FY0043',
        pageSize: 100,
        pageIndex: 1
      })
      this.ziFeiZTOptions = res || []
    },
    handleVisibleChange() {
      setTimeout(() => {
        // this.focusNext();
      }, 100);
    },
    async getZiDian() {
      const params = {
        likeQuery: '',
        caoYaoSYBZ: '1',
        menZhenZYBZ: this.zhuYuanBZ ? '2' : '1',
      };
      const [yaoPinZD, yiZhuZD, geiYaoFS] = await Promise.all([
        getYaoPinShuJuYZYList(['YP0057']),
        getYiZhuSJYZYListByLBID('YZ0005'),
        getGeiYaoFSSel(params),
      ]);
      this.yongFaOptions =
        yaoPinZD.find((item) => item.shuJuYLBID === 'YP0057').zhiYuList || [];
      this.leiXingOptions = yiZhuZD.zhiYuList || [];
      this.geiYaoFSOptions = geiYaoFS;
    },
    async getZuTaoData() {
      this.resetData();
      const res = await getXieDingFList(this.query);
      this.tableData = res;
      if (res.length > 0) {
        this.setCurrent(res[0]);
      }
    },
    async getWeiZhiIDData() {
      const WeiZhiID = getWeiZhiID();
      const res = await getWeiZhiData(WeiZhiID);
      this.weiZhiData = res;
      if (res.weiZhiDLDM == '1' || res.weiZhiDLDM == '2') {
        this.query.shiYongFWLB = '1';
        this.formData.shiYongFWLB = '1';
        this.formData.yiShengKSID = getYongHuID() || null;
        this.formData.yiShengKSMC = getYongHuXM() || null;
        this.isMenZhenOrZhuYuan = true;
      } else {
        this.isMenZhenOrZhuYuan = false;
      }
      this.getZuTaoData();
    },
    async handleXieDingFXX(data) {
      const res = await getXieDingFXX({ zuTaoID: data.zuTaoID });
      this.formData = this.serialize(
        res,
        Object.keys(initFromData(this.zhuYuanBZ)),
      );
      this.formData.ziFeiZT = res.caoYaoYZXXList[0].ziFeiZT || ''
      if (
        this.weiZhiData.weiZhiDLDM == '1' ||
        this.weiZhiData.weiZhiDLDM == '2'
      ) {
        this.query.shiYongFWLB = '1';
        this.formData.shiYongFWLB = '1';
        this.formData.yiShengKSID = getYongHuID() || null;
        this.formData.yiShengKSMC = getYongHuXM() || null;
        this.isMenZhenOrZhuYuan = true;
      } else {
        this.isMenZhenOrZhuYuan = false;
      }
      if (res.caoYaoYZXXList[0]) {
        const caoYaoXX = res.caoYaoYZXXList[0];
        this.zhongYaoKFLX = caoYaoXX.zhongYaoKFLXDM
          ? {
              biaoZhunDM: caoYaoXX.zhongYaoKFLXDM,
              biaoZhunMC: caoYaoXX.zhongYaoKFLXMC,
            }
          : null;
        this.zhongYaoKFLXOld = cloneDeep(this.zhongYaoKFLX);
        this.teShuFF = caoYaoXX.teShuFFDM
          ? {
              biaoZhunDM: caoYaoXX.teShuFFDM,
              biaoZhunMC: caoYaoXX.teShuFFMC,
            }
          : null;
      }
      this.caoYaoList = res.caoYaoYZXXList.map((item) => {
        this.oldCaoYaoIds.push({ id: item.id });
        item.danWeiOptions = bmis_zuZhuangJLDWOptions(item);
        item.yaoPin = {
          yaoPinMC: item.yaoPinMC,
          yaoPinID: item.yaoPinID,
        };
        return item;
      });
      this.handleAddYiZhu();
    },
    handleCurrentChange(row) {
      this.resetData();
      if (!row) return;
      this.handleXieDingFXX(row);
      this.setCurrent(row);
    },
    setCurrent(row) {
      this.$refs.table.setCurrentRow(row);
    },
    handleSearch() {
      this.getZuTaoData();
    },
    handleAdd() {
      this.setCurrent();
      this.resetData();
    },
    handlePinCiChange() {},
    handleAddYiZhu() {
      this.caoYaoList.push(initYiZhuData(this.zhuYuanBZ));
    },
    handleDelYiZhu(index) {
      this.caoYaoList.splice(index, 1);
    },
    async handleSave() {
      let caoYaoList = [];
      const formData = this.formData;
      if (!formData.zuTaoMC) {
        this.$message.warning('请填写协定方名称！');
        return;
      }
      if (this.caoYaoList.length === 0) {
        this.$message.warning('请填写草药！');
        return;
      }
      formData.zuZhiJGID = formData.zuZhiJGID || getJiGouID();
      formData.zuZhiJGMC = formData.zuZhiJGMC || getJiGouMC();
      let addZhuYuanYZXXList = [];
      let updateZhuYuanYZXXList = [];
      let caoYaoIds = [];
      const teShuFFDM = this.getFuZhiCZ(this.teShuFF, 'biaoZhunDM');
      const teShuFFMC = this.getFuZhiCZ(this.teShuFF, 'biaoZhunMC');
      const zhongYaoKFLXDM = this.getFuZhiCZ(this.zhongYaoKFLX, 'biaoZhunDM');
      const zhongYaoKFLXMC = this.getFuZhiCZ(this.zhongYaoKFLX, 'biaoZhunMC');

      this.caoYaoList.forEach((item, index) => {
        const obj = this.serialize(
          item,
          Object.keys(initYiZhuData(this.zhuYuanBZ)),
        );
        if (obj.yaoPinMC || obj.yaoPinID) {
          const fuzhi = {
            shunXuHao: index + 1,
            yiZhuXMID: obj.jiaGeID,
            yiZhuMC: obj.yaoPinMC + obj.yaoPinGG,
            yiCiYL: obj.yiCiJL,
            teShuFFDM,
            teShuFFMC,
            pinCiID: 'ONCE',
            pinCiMC: 'ONCE',
            zhongYaoKFLXDM,
            zhongYaoKFLXMC,
            zuTaoID: formData.zuTaoID,
            zuTaoMC: formData.zuTaoMC,
            zuZhiJGID: formData.zuZhiJGID,
            zuZhiJGMC: formData.zuZhiJGMC,
            ziFeiZT: formData.ziFeiZT
          };
          Object.assign(obj, fuzhi);
          if (obj.id) {
            caoYaoIds.push(obj.id);
            updateZhuYuanYZXXList.push(obj);
          } else {
            addZhuYuanYZXXList.push(obj);
          }
          caoYaoList.push(obj);
        }
      });
      let deleteZhuYuanYZXXList = this.oldCaoYaoIds.filter(
        (item) => !caoYaoIds.includes(item.id),
      );
      formData.addZhuYuanYZXXList = addZhuYuanYZXXList;
      formData.updateZhuYuanYZXXList = updateZhuYuanYZXXList;
      formData.deleteZhuYuanYZXXList = deleteZhuYuanYZXXList;
      await saveZuTao(formData);
      this.$message.success('保存成功！');
      this.handleSearch();
    },
    handleZuoFei() {
      MdMessageBox.confirm('操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => zuoFeiZuTao({ id: this.formData.id }))
        .then(() => {
          this.$message.success('作废成功！');
          this.handleSearch();
        })
        .catch((error) => {
          if (error === 'cancel') return;
        });
    },
    getFuZhiCZ(data, key) {
      return data ? data[key] || '' : null;
    },
    serialize(data, keys) {
      let a = {};
      keys.forEach((item) => {
        a[item] = data[item] !== undefined ? data[item] : null;
      });
      return a;
    },
    async handleLeiXingChange({ value }) {
      if (value.biaoZhunDM == this.zhongYaoKFLX.biaoZhunDM) return
      let result = true;
      const zhongYaoKFLXOld = cloneDeep(this.zhongYaoKFLX);
      // 0 饮片 1 散配颗粒剂 2整包颗粒剂 3小包装饮片
      const sun = Number(zhongYaoKFLXOld.biaoZhunDM) + Number(value.biaoZhunDM);
      if (this.caoYaoList.length > 1 && sun !== 3) {
        let flag = await MdMessageBox.confirm(
          '修改处方性质将删除已经输入的内容, 确定修改吗?',
          '提示',
          {
            confirmButtonText: '是',
            cancelButtonText: '否',
            customClass: 'HIS-caoYaoCFXZ-confirm',
            type: 'warning',
          },
        )
          .then(() => {
            return true;
          })
          .catch(() => {
            return false;
          });
        if (flag) this.caoYaoList = [initYiZhuData(this.zhuYuanBZ)];
        result = flag;
      }
      return result;
    },
    resetData() {
      this.zhongYaoKFLX = {
        biaoZhunDM: '3',
        biaoZhunMC: '小包装饮片',
      };
      this.zhongYaoKFLXOld = cloneDeep(this.zhongYaoKFLX);
      this.teShuFF = {
        biaoZhunDM: '1',
        biaoZhunMC: '常规',
      };
      this.caoYaoList = [];
      this.oldCaoYaoIds = [];
      this.formData = initFromData(this.zhuYuanBZ);
      const shiYongFWLB = this.query.shiYongFWLB === '1';
      const params = {
        yiShengKSID: shiYongFWLB ? getYongHuID() : null,
        yiShengKSMC: shiYongFWLB ? getYongHuXM() : null,
        shiYongFWLB: this.query.shiYongFWLB,
      };
      Object.assign(this.formData, params);
      //切换个人草药方时，需要yiShengBZ区分全院与个人
      if (!Object.keys(this.weiMoKuaiData).length) {
        this.query.yiShengBZ = shiYongFWLB ? 1 : null;
      } else {
        this.query.yiShengBZ = this.zhuYuanBZ && shiYongFWLB ? 1 : null;
      }
      // this.query.yiShengBZ = this.zhuYuanBZ && shiYongFWLB ? 1 : null;
      this.handleAddYiZhu();
    },
    handleQuXiao() {
      if (this.formData.zuTaoID) {
        this.handleXieDingFXX(this.formData);
      } else {
        this.handleAdd();
      }
    },
  },
  components: {
    'md-select-table': SelectTable,
    'biz-caoyao-table': CaoYaoTable,
    'biz-yisheng-select': BizYiShengSelect,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-caoyaoxdf {
  display: flex;
  width: 100%;
  height: 100%;
  padding: getCssVar('spacing-3');
  box-sizing: border-box;
  background-color: #d8d8d8;
  &-left {
    width: 264px;
    margin-right: getCssVar('spacing-3');
  }
  &-right {
    flex: 1;
    min-width: 0;
    min-height: 0;
  }
  &-pane {
    background-color: #fff;
    &__inner {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    &__header {
      display: flex;
      width: 100%;
      align-items: center;
      padding: getCssVar('spacing-3');
      box-sizing: border-box;
      &__title {
        flex: 1;
        min-width: 0;
      }
      &__extra {
        padding-left: getCssVar('spacing-3');
      }
    }
    &__search {
      padding: getCssVar('spacing-3');
      padding-top: 0;
    }
    &__main {
      flex: 1;
      min-height: 0;
      padding: getCssVar('spacing-3');
      padding-top: 0;
      overflow: auto;
    }
    &__footer {
      padding: getCssVar('spacing-3') getCssVar('spacing-4');
      background-color: getCssVar('color-1');
      text-align: right;
      .#{$md-prefix}-button {
        min-width: 64px;
      }
      &:last-child {
        .#{$md-prefix}-button {
          margin-right: getCssVar('spacing-3');
        }
      }
      .floatleft {
        margin: 0;
        float: left;
      }
      .danJiJE {
        margin-right: getCssVar('spacing-3');
        color: getCssVar('color-6');
      }
    }
  }
  &-form {
    padding-top: getCssVar('spacing-3');
    padding-bottom: getCssVar('spacing-3');
  }
}
.#{$md-prefix}-radio-group.is-border {
  width: 100%;
}
.#{$md-prefix}-caoyaoxdf-pane__header__title {
  margin-right: getCssVar('spacing-6');
}
</style>
