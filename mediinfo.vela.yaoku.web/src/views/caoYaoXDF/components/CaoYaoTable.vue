<template>
  <div :class="prefixClass('caoyao-table')">
    <md-editable-table-pro
      v-table-enter="(data) => handleTableEnter(data, 'table1')"
      v-model="caoYaoList1"
      :columns="columns1"
      :hideAddButton="true"
      :stripe="false"
      :edit="false"
      :showDefaultOperate="false"
      ref="tableRef1"
    >
      <!-- :border="false" -->
      <template v-slot:yaoPinMC="{ row, $index, cellRef }">
        <md-select-table
          v-model="row.yaoPin"
          :fetchData="fetchData"
          :columns="yaoPinColums"
          labelKey="yaoPinMC"
          valueKey="jiaGeID"
          :immediateLoad="false"
          require
          hover-only
          placeholder="输入搜索"
          filterable
          :ref="`yiZhuJGID${$index * 2}`"
          @change="handleYaoPinChange(row, $index * 2, $event)"
        />
      </template>
      <template v-slot:yiCiJL="{ row, $index }">
        <md-input
          v-model="row.yiCiJL"
          v-number.float="{ min: 0 }"
          :clearable="false"
          require
          :enter-sort="$index * 8 + 1"
        ></md-input>
      </template>
      <template v-slot:yiCiJLDW="{ row, $index, cellRef }">
        <md-select
          v-model="row.yiCiJLDW"
          require
          hover-only
          @visible-change="handleVisibleChange($event, cellRef)"
          @change="handleSelectChange('danWei', row)"
        >
          <!-- 因为这里danWeiOptions是[{value: '3', label: 'g'}],这种格式，为了做最小改动所以label和value都显示label -->
          <md-option
            v-for="item in row.danWeiOptions"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          ></md-option>
        </md-select>
      </template>
      <template v-slot:geiYaoFS="{ row, $index, cellRef }">
        <md-select
          v-model="row.geiYaoFSID"
          filterable
          hover-only
          @visible-change="handleVisibleChange($event, cellRef)"
          @change="handleSelectChange('geiYaoFS', row)"
        >
          <md-option
            v-for="item in geiYaoFSOptions"
            :key="item.geiYaoFSID"
            :label="item.geiYaoFSMC"
            :value="item.geiYaoFSID"
          ></md-option>
        </md-select>
      </template>
      <template v-if="!disabled" v-slot:operate="{ row, $index }">
        <md-button
          v-if="row.id || row.guid"
          type="danger"
          :icon="prefixClass('icon-shanchu')"
          noneBg
          @click="handleDelRow($index * 2)"
        ></md-button>
      </template>
    </md-editable-table-pro>
    <md-editable-table-pro
      v-table-enter="(data) => handleTableEnter(data, 'table2')"
      v-model="caoYaoList2"
      :columns="columns2"
      :hideAddButton="true"
      :stripe="false"
      :showDefaultOperate="false"
      ref="tableRef2"
    >
      <!-- :border="false" -->
      <template v-slot:yaoPinMC="{ row, $index }">
        <md-select-table
          v-model="row.yaoPin"
          :fetchData="fetchData"
          :columns="yaoPinColums"
          labelKey="yaoPinMC"
          valueKey="jiaGeID"
          :immediateLoad="false"
          require
          hover-only
          placeholder="输入搜索"
          filterable
          :ref="`yiZhuJGID${$index * 2}`"
          @change="handleYaoPinChange(row, $index * 2, $event)"
        />
      </template>
      <template v-slot:yiCiJL="{ row, $index }">
        <md-input
          v-model="row.yiCiJL"
          v-number.float="{ min: 0 }"
          :clearable="false"
          require
          :enter-sort="$index * 8 + 1"
        ></md-input>
      </template>
      <template v-slot:yiCiJLDW="{ row, $index, cellRef }">
        <md-select
          v-model="row.yiCiJLDW"
          require
          hover-only
          :enter-sort="$index * 8 + 2"
          @visible-change="handleVisibleChange($event, cellRef)"
          @change="handleSelectChange('danWei', row)"
        >
          <!-- 因为这里danWeiOptions是[{value: '3', label: 'g'}],这种格式，为了做最小改动所以label和value都显示label -->
          <md-option
            v-for="item in row.danWeiOptions"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          ></md-option>
        </md-select>
      </template>
      <template v-slot:geiYaoFS="{ row, $index, cellRef }">
        <md-select
          v-model="row.geiYaoFSID"
          filterable
          hover-only
          :enter-sort="$index * 8 + 3"
          @visible-change="handleVisibleChange($event, cellRef)"
          @change="handleSelectChange('geiYaoFS', row)"
        >
          <md-option
            v-for="item in geiYaoFSOptions"
            :key="item.geiYaoFSID"
            :label="item.geiYaoFSMC"
            :value="item.geiYaoFSID"
          ></md-option>
        </md-select>
      </template>
      <template v-if="!disabled" v-slot:operate="{ row, $index }">
        <md-button
          v-if="row.id || row.guid"
          type="danger"
          :icon="prefixClass('icon-shanchu')"
          noneBg
          @click="handleDelRow($index * 2 + 1)"
        ></md-button>
      </template>
    </md-editable-table-pro>
  </div>
</template>

<script>
import { getGuid } from '@/system/utils/getGuid';
import { zuZhuangJLDWOptions as bmis_zuZhuangJLDWOptions } from '@/system/utils/zuZhuangJLDWOptions';
import SelectTable from '@mdfe/material.select-table';
import { logger } from '@/service/log';
import {
  getYiZhuYPList,
  getYiZhuYPXXByJGID,
} from '@/service/yaoPinYK/yaoPinBFWZ';
import { GetGongYongYPForYFList } from '@/service/yaoPinYF/common';

const initColumns = (type, disabled) => {
  return [
    {
      label: '',
      type: 'index',
      align: 'center',
      width: 30,
      index: function (val) {
        if (type === 1) {
          return 2 * val + 1;
        } else {
          return (val + 1) * 2;
        }
      },
    },
    {
      prop: 'yaoPinMC',
      slot: 'yaoPinMC',
      label: '药品名称',
      endMode: 'custom',
      minWidth: 120,
      placeholder: '请输入医嘱关键字',
      showOverflowTooltip: false,
      require: disabled ? false : true,
      // require: ({ row }) => {
      //   return !row.id && !disabled;
      // },
      edit: true,
      formatter: (row, column, cellValue, index) => {
        return (cellValue || '') + (row.yaoPinGG || '');
      },
      disabled: ({ row }) => {
        return !!row.id || disabled;
      },
    },
    {
      prop: 'yiCiJL',
      slot: 'yiCiJL',
      label: '剂量',
      width: 60,
      placeholder: '请输入',
      showOverflowTooltip: false,
      require: true,
      edit: true,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
      disabled: () => {
        return disabled;
      },
    },
    {
      prop: 'yiCiJLDW',
      slot: 'yiCiJLDW',
      label: '单位',
      width: 66,
      endMode: 'custom',
      align: 'center',
      placeholder: '请选择',
      showOverflowTooltip: false,
      require: true,
      edit: true,
      formatter: (row, column, cellValue, index) => {
        return row.yiCiJLDWMC || cellValue;
      },
      disabled: () => {
        return disabled;
      },
    },
    {
      prop: 'geiYaoFS',
      slot: 'geiYaoFS',
      label: '煎法',
      minWidth: 60,
      placeholder: '请选择',
      showOverflowTooltip: false,
      endMode: 'custom',
      edit: true,
      formatter: (row) => {
        return row.geiYaoFSMC || row.geiYaoFSID;
      },
      disabled: () => {
        return disabled;
      },
    },
    {
      prop: 'danJia',
      label: '零售金额',
      width: 76,
      align: 'right',
      type: 'text',
      showOverflowTooltip: false,
      formatter: (row, column, cellValue, index) => {
        return row.danJia && row.yiCiJL
          ? (Number(row.danJia) * Number(row.yiCiJL)).toFixed(2)
          : 0;
      },
    },
    {
      slot: 'operate',
      label: '',
      width: 50,
      align: 'center',
    },
  ];
};
const initYiZhuData = () => {
  return {
    id: null,
    zuZhiJGID: null,
    zuZhiJGMC: null,
    zuTaoID: null,
    zuTaoMC: null,
    fuYiZID: null,
    shunXuHao: null,
    yiZhuDH: null,
    yiZhuMC: null,
    yiZhuXMID: null,
    yiZhuXMMC: null,
    yiShengZT: null,
    yiZhuMS: null,
    yiZhuFLDM: null,
    yiZhuFLMC: null,
    shouRiCS: null,
    changQiYZBZ: null,
    jiFeiJG: null,
    jiFeiFSDM: null,
    jiFeiFSMC: null,
    yiZhuLXDM: null,
    yiZhuLXMC: null,
    geiYaoFSID: null,
    geiYaoFSMC: null,
    geiYaoFSLXDM: null,
    geiYaoFSLXMC: null,
    pinCiID: null,
    pinCiMC: null,
    jiaGeID: null,
    guiGeID: null,
    daGuiGID: null,
    yaoPinID: null,
    yaoPinMC: null,
    yaoPinGG: null,
    yaoPinBMID: null,
    chanDiID: null,
    chanDiMC: null,
    chanDiLBDM: null,
    chanDiLBMC: null,
    yiCiJL: null,
    yiCiJLDW: null,
    daYinJL: null,
    yiCiYL: null,
    tianShu: null,
    shuLiang: null,
    zuiXiaoDWYL: null,
    zuiXiaoDW: null,
    baoZhuangLiang: null,
    baoZhuangDW: null,
    jiLiang: null,
    jiLiangDW: null,
    tiJi: null,
    tiJiDW: null,
    jiXingID: null,
    jiXingMC: null,
    jiXingLBDM: null,
    jiXingLBMC: null,
    ziFeiZT: null,
    jiZhenBZ: null,
    diSu: null,
    diSuDW: null,
    biGuangBZ: null,
    fuYaoSXDM: null,
    fuYaoSXMC: null,
    lingYaoFSDM: null,
    lingYaoFSMC: null,
    quZhengLXDM: null,
    quZhengLXMC: null,
    yaoFangWZID: null,
    yaoFangWZMC: null,
    chuYuanDYBZ: null,
    jingMaiPBZ: null,
    jiaJiBZ: null,
    shuangQianMBZ: null,
    shiJiDM: null,
    shiJiMC: null,
    caoYaoBZ: null,
    chuFangTS: null,
    fuFangBZ: null,
    zhongYaoKFLXDM: null,
    zhongYaoKFLXMC: null,
    daiJianTS: null,
    daiJianDW: null,
    teShuFFDM: null,
    teShuFFMC: null,
    duLiFLDM: null,
    duLiFLMC: null,
    zhiXingKSID: null,
    zhiXingKSMC: null,
    yangBenLXID: null,
    yangBenLXMC: null,
    jianChaBWID: null,
    jianChaBWMC: null,
    menZhenSYBZ: null,
    zhuYuanSYBZ: null,
    jiZhenSYBZ: null,
    caoYaoTGYFSID: null,
    caoYaoTGYFSMC: null,
    benYuanZSCS: null,
    waiPeiBZ: null,
    ziBeiYBZ: null,
    linShiID: null,
    dangRiLJBZ: null,
    zhuRiLJBZ: null,
    zongTianS: null,
  };
};

export default {
  name: 'caoyao-table',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    geiYaoFSOptions: {
      type: Array,
      default: () => [],
    },
    zhongYaoKFLX: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      yaoPinColums: [
        {
          prop: 'yaoPinMC',
          label: '名称',
          width: '140px',
          formatter: (row, column, cellValue, index) => {
            return (cellValue || '') + (row.yaoPinGG || '');
          },
        },
        {
          prop: 'yiBaoDJMC',
          label: '医保等级',
        },
        {
          prop: 'chanDiMC',
          label: '产地',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
        },
        {
          prop: 'kuCunSL',
          label: '库存',
        },
        {
          prop: 'danJia',
          label: '单价',
        },
        {
          prop: 'yaoFangWZMC',
          label: '位置',
        },
      ],
      columns1: [],
      columns2: [],
    };
  },
  computed: {
    chuFangYF({ list }) {
      let yaoFangXX = {
        yaoFangWZID: '',
        yaoFangWZMC: '',
      };
      const find = list.find((item) => item.yaoFangWZID);
      if (find) {
        yaoFangXX.yaoFangWZID = find.yaoFangWZID;
        yaoFangXX.yaoFangWZMC = find.yaoFangWZMC;
      }
      return yaoFangXX;
    },
    caoYaoList1({ list }) {
      const arr = list.filter((item, index) => index % 2 === 0);
      return arr;
    },
    caoYaoList2({ list }) {
      const arr = list.filter((item, index) => index % 2 !== 0);
      return arr;
    },
  },
  watch: {
    disabled: {
      handler(val) {
        this.columns1 = initColumns(1, val);
        this.columns2 = initColumns(2, val);
      },
      immediate: true,
    },
  },
  created() {},
  methods: {
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      const params = {
        yaoPinMC: inputValue,
        yaoPinLX: '3',
        guiGeLX: '0',
        shiFouCXSYFW: 0,
        pageSize: pageSize,
        pageIndex: page,
      };
      return await GetGongYongYPForYFList(params);
      // return await getYiZhuYPList(params);
    },
    async handleYaoPinChange(row, index, data) {
      if (!this.canUseYaoPin(data, index)) {
        await this.$nextTick();
        row.yaoPin = null;
        this.resetRowData(row, index);
        return;
      }
      try {
        const yaoPinXX = await this.getYaoPinXX(data);
        yaoPinXX.danWeiOptions = bmis_zuZhuangJLDWOptions(yaoPinXX);
        Object.assign(yaoPinXX, {
          yaoFangWZID: data.yaoFangWZID,
          yaoFangWZMC: data.yaoFangWZMC,
          guid: getGuid,
        });
        Object.assign(row, yaoPinXX);
        await this.$nextTick();
        const last = this.list[this.list.length - 1];
        if (last.id || last.guid) {
          this.$emit('add');
        }
      } catch (error) {
        console.error(error);
        logger.error(error);
        this.resetRowData(row, index);
      }
    },
    handleSelectChange(type, row) {
      let configType = {
        danWei: () => {
          return {
            options: row.danWeiOptions,
            valueKey: 'value',
            labelkey: 'label',
            dataValueKey: 'yiCiJLDW',
            dataLabelKey: 'yiCiJLDWMC',
          };
        },
        geiYaoFS: () => {
          return {
            options: this.geiYaoFSOptions,
            valueKey: 'geiYaoFSID',
            labelkey: 'geiYaoFSMC',
            dataValueKey: 'geiYaoFSID',
            dataLabelKey: 'geiYaoFSMC',
          };
        },
      };
      let configData = configType[type]();
      let findData = configData.options.find(
        (item) => item[configData.valueKey] === row[configData.dataValueKey],
      );
      if (configData) {
        row[configData.dataLabelKey] = findData[configData.labelkey];
      } else {
        row[configData.dataLabelKey] = '';
      }
    },
    getYaoPinXX(result) {
      const params = {
        jiaGeID: result.jiaGeID,
        fanHuiYFWZBZ: result.yaoFangWZID ? 1 : 0,
        menZhenZYBZ: 1,
        jiaoYanMZQXBZ: 0,
        yaoFangWZID: this.chuFangYF.yaoFangWZID || '',
      };
      return getYiZhuYPXXByJGID(params);
    },
    /**
     * 能否使用该药品
     * @param {Object} data 选中的药品数据
     * @param {Number} index 当前数据在tableDataList中的index
     * @returns {Boolean} 能否使用该药品
     */
    canUseYaoPin(data, index) {
      // 未选择或清空时。 直接返回
      if (!data.jiaGeID) return false;
      const caoYaoLength = this.list.length - 1;
      const findCaoYao = this.list.find(
        (t, i) => t.jiaGeID == data.jiaGeID && i < caoYaoLength,
      );
      // 草药处方中不能重复开立同一个药品
      if (findCaoYao) {
        this.$message({
          type: 'warning',
          message: '该处方以存在相同药品，不能重复添加',
        });
        this.$refs['yiZhuJGID' + index].focus();
        return false;
      }
      return true;
    },
    isDelShow() {
      return (row) => {
        const table = this.list;
        const len = table.length;
        if (len == 1) return;
        return row.guid !== table[len - 1].guid;
      };
    },
    handleTableEnter({ activeIndex, callback }, type = 'table1') {
      let configData = {
        table1: {
          baseIndex: -1,
          refName: 'tableRef2',
        },
        table2: {
          baseIndex: 0,
          refName: 'tableRef1',
        },
      };
      let index = activeIndex + 1;
      let typeData = configData[type];
      let baseIndex = typeData.baseIndex;
      let refName = typeData.refName;

      if (activeIndex !== 0 && index % 4 === 4) {
        //因为索引从-1开始，所以减2
        // 1 => -1
        // 2 => 3
        // 3 => 7
        // 4 => 11
        let yuShu = (index + 1) / 4;
        this.$refs[refName].toNext((yuShu + baseIndex) * 4 - 1);
      } else if (activeIndex !== 0 && index % 4 === 0) {
        let yuShu = index / 4; //判断回车在第几行
        const count =
          (yuShu + baseIndex) * 4 - 1 == -1 ? 0 : (yuShu + baseIndex) * 4;
        this.$refs[refName].toNext(count);
      } else {
        callback();
      }
    },
    resetRowData(row, index) {
      Object.assign(row, initYiZhuData());
      this.$refs['yiZhuJGID' + index]?.focus();
    },
    handleDelRow(index) {
      this.$emit('del', index);
    },
    async handleVisibleChange(val, cellRef) {
      if (!val) {
        await this.$nextTick();
        // cellRef.endEdit()
        // let timer = setTimeout(() => {
        //   clearTimeout(timer)
        //   timer = null
        // }, 500)
      }
    },
  },
  components: {
    'md-select-table': SelectTable,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
@use '@mdfe/medi-ui/theme-chalk/src/common/var.scss' as *;

.#{$md-prefix}-caoyao-table {
  display: flex;
  width: 100%;
  .#{$md-prefix}-editable-table {
    flex: 1;
    min-width: 0;
    ::v-deep .#{$md-prefix}-base-table {
      th,
      td {
        border-right: none;
      }
    }
  }
  .#{$md-prefix}-editable-table:last-child {
    margin-left: -1px;
  }
  ::v-deep .#{$md-prefix}-input__wrapper {
    box-shadow: 0 0 0 1px transparent inset;
  }
}
</style>
