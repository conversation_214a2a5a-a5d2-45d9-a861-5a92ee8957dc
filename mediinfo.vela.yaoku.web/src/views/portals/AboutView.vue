<script lang="ts" setup>
import { MdButton } from '@mdfe/medi-ui';
import { useOwnPage } from '@mdfe/view-manager';
import { ref } from 'vue';

defineOptions({ name: 'AboutView' });

const { data, setTitle, closeSelf } = useOwnPage();

const description = process.env.VUE_APP_SITE_DESCRIPTION;

const pageTitle = ref(data.title);
</script>

<template>
  <h1>关于我们</h1>
  <p>{{ description }}</p>
  <br />
  <br />
  <div>
    <input v-model="pageTitle" />
    <MdButton @click="setTitle(pageTitle)">修改页面标题</MdButton>
  </div>
  <br />
  <br />
  <div>
    <MdButton @click="closeSelf()">关闭自己</MdButton>
  </div>
  <router-link to="/users/001?title=张三"> 去章三 </router-link>
</template>
