<script lang="ts" setup>
import { useSiteData } from '../../composables';

defineOptions({ name: 'WelcomeView' });

const siteData = useSiteData();
</script>

<template>
  <div class="welcome-view">
    <h1 class="title">{{ siteData.title }}</h1>
    <p class="desc">{{ siteData.description }}</p>
    <div class="mascot">
      <img src="./images/home-mascot.png" alt="LYRA吉祥物" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.welcome-view {
  position: relative;
  width: 800px;
  height: 780px;
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}

.title {
  margin-top: 76px;
  color: #666;
  font-size: 42px;
  text-align: center;
}

.desc {
  margin-top: 8px;
  color: #aaa;
  font-size: 24px;
  text-align: center;
}

.mascot {
  margin: 80px auto 0;
  width: 606px;
  text-align: center;
  background: url('./images/home-mascot-bg.png');
}
</style>
