<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('qiyongjldrawer')"
    :modal-class="prefixClass('qiyongjldrawermodal')"
    ref="rukudandrawer"
  >
    <div :class="prefixClass('drawer-1')" v-loading="pageLoading">
      <div :class="prefixClass('rkd-title')">
        <div :class="prefixClass('title-left')">
          <span>启用记录</span>
        </div>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <md-table-pro
          ref="tablePro"
          :columns="columns"
          :onFetch="handleFetch"
          height="100%"
          :autoFill="true"
        >
        </md-table-pro>
      </div>
    </div>
  </md-drawer>
</template>

<script>
import {
  GetXiaoHaoZLXZCZJLCount,
  getXiaoHaoZLXZCZJLList,
} from '@/service/yaoPinYK/yaoPinXHLXZPZ';
export default {
  name: 'rukudan-detail-drawer',
  props: {
    size: { type: [String, Number], default: '75%' },
  },
  data() {
    return {
      columns: [
        {
          prop: 'caoZuoNR',
          label: '操作内容',
        },
        {
          prop: 'caoZuoRXM',
          label: '操作人',
          width: 100,
        },
        {
          prop: 'caoZuoSJ',
          label: '操作时间',
          width: 160,
          // formatter: (row, column, cellValue, index) => {
          //   if (!cellValue) return '';
          //   return dayjs(cellValue).format('YYYY-MM-DD');
          // },
        },
      ],
      data: [],
      pageLoading: false,
      drawer: false,
      id: '',
    };
  },
  methods: {
    formatDate(date) {
      return yaoKuZDJZTimeShow(date);
    },
    //打开
    async openDrawer(row) {
      this.drawer = true;
      this.pageLoading = true;
      try {
        this.id = row.id;
        this.$nextTick(() => {
          this.$refs.tablePro.search();
        });
      } finally {
        this.pageLoading = false;
      }
      return new Promise((resolve, reject) => {
        this.finish = resolve;
        this.reject = reject;
      });
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        pageSize: pageSize,
        pageIndex: page,
        id: this.id,
      };

      const [items, total] = await Promise.all([
        getXiaoHaoZLXZCZJLList(params, config),
        GetXiaoHaoZLXZCZJLCount(params, config),
      ]);
      return {
        items,
        total,
      };
    },
    closeDrawer() {
      this.drawer = false;
    },
  },
  components: {},
};
</script>

<style lang="scss">
.#{$md-prefix}-qiyongjldrawermodal {
  // position: initial !important;
  top: 74px !important;
}
</style>

<style lang="scss" scoped>
.#{$md-prefix}-qiyongjldrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;
  ::v-deep .#{$md-prefix}-drawer__body {
    height: 100%;
  }

  .#{$md-prefix}-drawer-1 {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .#{$md-prefix}-rkd-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }

        .#{$md-prefix}-chonghongBZ {
          display: inline-block;
          margin-left: 4px;
          width: 20px;
          height: 20px;
          background-color: #f12933;
          border-radius: 2px;
          color: #ffffff;
          font-size: 14px;
          line-height: 20px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-toolbar {
        margin-right: 18px;
      }

      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }

    .#{$md-prefix}-content {
      flex: 1;
      padding: 8px 8px 8px 8px;
      box-sizing: border-box;
      overflow: hidden;
    }
  }
}
</style>
