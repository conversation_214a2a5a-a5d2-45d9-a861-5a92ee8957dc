<template>
  <md-dialog
    title="药品总量限制"
    v-model="visible"
    :v-loading="formLoading"
    class="HISYK-XiaoHaoZLXZ-addDialog"
    :before-close="handleClose"
  >
    <md-row :gutter="16" v-if="visible">
      <md-form
        :model="formData"
        :rules="formRules"
        label-width="150px"
        use-status-icon
        ref="form"
        style="width: 100%"
      >
        <md-row>
          <md-col :span="12">
            <md-form-item label="药品名称与规格" prop="yaoPinMCYGG">
              <biz-yaopindw
                v-model="formData.yaoPinMCYGG"
                :type="type"
                :placeholder="placeholder"
                :class="prefixClass('yaopin-search')"
                showSuffix
                @change="handleYaoPinDWChange($event)"
              />
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="产地名称" prop="chanDiMC" labelWidth="100px">
              <md-input v-model="formData.chanDiMC" :disabled="true" />
            </md-form-item>
          </md-col>
        </md-row>
        <md-row>
          <md-col :span="12">
            <md-form-item label="院内编码" prop="yuanNeiBM">
              <md-input v-model="formData.yuanNeiBM" :disabled="true" />
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item
              label="省平台ID"
              prop="shengPingTBM"
              labelWidth="100px"
            >
              <md-input v-model="formData.shengPingTBM" :disabled="true" />
            </md-form-item>
          </md-col>
        </md-row>
        <md-row>
          <md-col :span="12">
            <md-form-item label="限制总量" prop="xianZhiZL">
              <md-input
                v-model="formData.xianZhiZL"
                v-number="{}"
                @change="handleTiShiLiang"
              />
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="提示比例" prop="tiShiBL" labelWidth="100px">
              <md-input
                v-model="formData.tiShiBL"
                v-number.float="{ decimal: 2 }"
                @change="handleTiShiLiang"
              />
            </md-form-item>
          </md-col>
        </md-row>
        <md-row>
          <md-col :span="12">
            <md-form-item label="提示量" prop="tiShiLiang">
              <md-input
                v-model="formData.tiShiLiang"
                v-number="{}"
                :disabled="true"
              />
            </md-form-item>
          </md-col>
        </md-row>
      </md-form>
    </md-row>
    <template #footer>
      <span class="dialog-footer">
        <div style="text-align: right">
          <md-button
            v-if="isEdit"
            type="danger"
            plain
            :disabled="formLoading"
            style="float: left"
            @click="handleZuoFei(formData.id)"
          >
            作废
          </md-button>
          <div>
            <md-button
              type="primary"
              plain
              :disabled="formLoading"
              @click="handleClose"
            >
              取消
            </md-button>

            <md-button type="primary" :loading="formLoading" @click="handleSave"
              >确定</md-button
            >
          </div>
        </div>
      </span>
    </template>
  </md-dialog>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  DeleteYaoPinXHLXZPZ,
  SaveYaoPinXHLXZPZ,
} from '@/service/yaoPinYK/yaoPinXHLXZPZ';
import { MdMessageBox } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
function formData() {
  return {
    id: '',
    guiGeID: '',
    jiaGeID: '',
    yuanNeiBM: '',
    shengPingTBM: '',
    yaoPinMC: '',
    yaoPinMCYGG: {},
    yaoPinGG: '',
    chanDiID: '',
    chanDiMC: '',
    baoZhuangDW: '',
    baoZhuangLiang: '',
    xianZhiZL: '',
    tiShiBL: '',
    tiShiLiang: '',
    value: null,
    yaoPinLXDM: '',
    yaoPinLXMC: '',
    yaoPinID: '',
  };
}
export default {
  name: 'XiaoHaoZLXZ-addDialog',
  data() {
    return {
      isEdit: false,
      saveLoading: false,
      type: 'ypxh',
      placeholder: '药品名称与规格',
      visible: false,
      activeFW: null,
      formData: formData(),
      formRules: {
        yaoPinMCYGG: [
          { required: true, message: '请选择药品', trigger: 'change' },
        ],
        xianZhiZL: [
          { required: true, message: '请输入限制总量', trigger: 'change' },
        ],
        tiShiBL: [
          { required: true, message: '请输入提示比例', trigger: 'change' },
        ],
      },
      formLoading: false,
      yaoPinDWList: [],
      canKaoFWList: [],
    };
  },
  computed: {
    resFanWeiList() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('大于'));
    },
    resFanWeiList2() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('小于'));
    },
  },
  methods: {
    async showModel({ mode, data }) {
      this.visible = true;
      await this.$nextTick();
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.formData = formData();
      this.isEdit = false;
      if (mode == 'edit') {
        this.isEdit = true;
        this.formLoading = true;
        data.yaoPinMCYGG = {
          guiGeID: data.guiGeID,
          jiaGeID: data.jiaGeID,
          yuanNeiBM: data.yuanNeiBM,
          shengPingTBM: data.shengPingTBM,
          yaoPinGG: data.yaoPinGG,
          chanDiID: data.chanDiID,
          chanDiMC: data.chanDiMC,
          kuCunSL: 0,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          yaoPinMC: data.yaoPinMC,
          yaoPinID: data.yaoPinID,
        };
        this.formData = cloneDeep(data);
        this.formLoading = false;
      }
    },
    /**
     * 选择药品change事件
     */
    async handleYaoPinDWChange(data) {
      if (data) {
        const params = {
          guiGeID: data.guiGeID,
          jiaGeID: data.jiaGeID,
          yuanNeiBM: data.yuanNeiBM,
          shengPingTBM: data.shengPingTBM,
          yaoPinGG: data.yaoPinGG,
          chanDiID: data.chanDiID,
          chanDiMC: data.chanDiMC,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          yaoPinMC: data.yaoPinMC,
          yaoPinID: data.yaoPinID,
        };
        Object.assign(this.formData, params);
      } else {
        this.formData.yaoPinMCYGG = '';
        this.formData.yaoPinMC = '';
        this.formData.yaoPinGG = '';
      }
    },
    //提示量=限制总量*提示比例
    handleTiShiLiang() {
      let data = this.formData;
      if (data.tiShiBL) {
        if (Number(data.tiShiBL) > 1) {
          data.tiShiBL = 1.0;
        }
        if (data.xianZhiZL) {
          data.tiShiLiang =
            parseFloat(data.xianZhiZL) * parseFloat(data.tiShiBL);
        }
      }
    },
    //作废，停用才能作废
    async handleZuoFei(id) {
      MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(async () => {
          const result = await DeleteYaoPinXHLXZPZ(id);
          if (result) {
            this.$message({
              type: 'success',
              message: '作废成功',
            });
            this.$emit('handleSearch');
            this.visible = false;
          } else {
            this.$message({
              type: 'error',
              message: '作废失败',
            });
          }
        })
        .catch((action) => {
          this.$message({
            type: 'info',
            message: '已取消作废',
          });
        });
    },
    /**
     * 提交操作
     * @param {Object} data form.data内容
     */
    async handleSave() {
      const result = await this.$refs.form.validate();
      if (!result) return;

      let params = this.formData;
      this.saveLoading = true;
      await SaveYaoPinXHLXZPZ(params);
      this.$message({
        type: 'success',
        message: '保存成功！',
      });
      this.$emit('handleSearch');
      this.visible = false;
    },
    handleClose() {
      this.visible = false;
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.formData = formData();
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-XiaoHaoZLXZ-addDialog {
  .padleft-8 {
    padding-left: getCssVar('spacing-3');
  }

  .HISYK-XiaoHaoZLXZ-checkbox {
    width: 100%;
    border: 1px dashed #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 0 getCssVar('spacing-3');
  }

  .HISYK-XiaoHaoZLXZ-canKaoFW {
    ::v-deep .#{$namespace}-select {
      &:hover {
        border-color: unset;
      }
    }

    .select-wrap {
      width: 100px;
    }
  }
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select:hover
  .#{$namespace}-input__wrapper {
  box-shadow: unset !important;
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select
  .#{$namespace}-input
  .#{$namespace}-input__wrapper {
  box-shadow: unset;
}
</style>
