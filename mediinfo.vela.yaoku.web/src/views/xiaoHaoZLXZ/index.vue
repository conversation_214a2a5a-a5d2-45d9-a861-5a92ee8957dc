<template>
  <div class="HISYK-xiaoHaoZLXZ">
    <div class="HISYK-xiaoHaoZLXZ-container" v-loading="loading">
      <div class="HISYK-xiaoHaoZLXZ-content">
        <div class="HISYK-xiaoHaoZLXZ-search">
          <div class="HISYK-xiaoHaoZLXZ-search-left">
            <biz-yaopindw
              v-model="query.jiaGeMC"
              :type="type"
              :columnsList="columnsList"
              :placeholder="placeholder"
              :class="prefixClass('yaopin-search')"
              showSuffix
              @change="handleYaoPinDWChange($event)"
            />
            <md-checkbox
              v-show="weiZhiLXDM == '3'"
              v-model="query.qiYongBZ"
              :true-label="0"
              :false-label="1"
              @change="handleSearch"
              >只显示停用</md-checkbox
            >
            <md-checkbox
              v-model="query.yiDaoLBZ"
              :true-label="1"
              :false-label="0"
              @change="handleSearch"
              >只显示已到量</md-checkbox
            >
          </div>
          <div class="HISYK-xiaoHaoZLXZ-search-right">
            <md-button
              type="primary"
              :icon="prefixClass('icon-shuaxin')"
              noneBg
              @click="handleSearch"
              >刷新</md-button
            >
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleDaYin"
              >预览</md-button
            >
            <md-button
              v-show="weiZhiLXDM == '3'"
              :disabled="selection.length == 0"
              type="primary"
              :icon="prefixClass('icon-shezhi')"
              noneBg
              @click="handletiChengBL"
              >提示比例</md-button
            >

            <md-button
              v-show="weiZhiLXDM == '3'"
              type="primary"
              :icon="prefixClass('icon-jia')"
              @click="handleAdd"
              >限制</md-button
            >
          </div>
        </div>
        <div class="HISYK-xiaoHaoZLXZ-content-table">
          <md-table-pro
            :columns="columns"
            :onFetch="handleFetch"
            @selection-change="handleSelect"
            height="100%"
            ref="table"
            :pagination="{
              pageSizes: [10, 20, 30, 40, 50, 100, 200, 300, 400],
            }"
            :class="prefixClass('xiaohaozl-table')"
            :cell-class-name="tableCellClassName"
          >
            <template #yaoPinMCGG="{ row }">
              <YaoPinShow
                :styleData="row.xianShiXX ? row.xianShiXX : {}"
                :yaoPinMC="row.yaoPinMCGG"
              />
              <!-- {{ row.yaoPinMC }} {{ row.yaoPinGG }} -->
            </template>
            <template #qiYongBZ="{ row }">
              <!-- 0停用，1启用 -->
              <md-switch
                v-model="row.qiYongBZ"
                :activeValue="1"
                :inactiveValue="0"
                @change="handleQiYong(row, 1)"
              />
            </template>
            <template #xianShiQYBZ="{ row }">
              <!-- 0停用，1启用 -->
              <md-switch
                :disabled="!row.qiYongBZ && weiZhiLXDM == '3'"
                v-model="row.xianShiQYBZ"
                :activeValue="1"
                :inactiveValue="0"
                @change="handleQiYong(row, 2)"
              />
            </template>
            <template #operate="{ row, $index }">
              <md-button
                type="primary"
                v-show="weiZhiLXDM == '3'"
                plain
                noneBg
                @click="handleEdit(row)"
              >
                编辑
              </md-button>
              <md-button type="primary" plain noneBg @click="handleJilu(row)">
                记录
              </md-button>
            </template>
          </md-table-pro>
        </div>
        <!-- 新增/编辑药品总量限制 -->
        <addDialog ref="addDialog" @handleSearch="handleSearch"></addDialog>

        <!-- 提成比例 -->
        <md-dialog
          v-model="dialogVisible"
          :show-header="false"
          width="240px"
          height="92px"
        >
          <span>提示比例</span>
          <md-input
            v-model="tiShiBL"
            width="100px"
            v-number.float="{ decimal: 2 }"
            style="margin-left: 8px"
          />
          <template #footer>
            <md-button type="primary" noneBg @click="handleYingYong"
              >应用</md-button
            >
          </template>
        </md-dialog>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YFXT0120'"
      :fileName="'限量单'"
      :title="'限量单打印预览'"
    />
    <JiLuDrawer
      ref="JiLuDrawer"
      @handleSearch="handleSearch"
      size="45%"
    ></JiLuDrawer>
  </div>
</template>
<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import YaoPinShow from '@/components/YaoPinShow.vue';
import { GetYaoPinTSSX } from '@/service/yaoPinYF/common';
import {
  GetYaoPinXHLXZPZCount,
  GetYaoPinXHLXZPZList,
  QiTingYXHLXZPZ,
  UpdateTiShiBL,
} from '@/service/yaoPinYK/yaoPinXHLXZPZ';
import formatJiaGe from '@/system/utils/formatJiaGe';
import lyra from '@mdfe/lyra';
import { MdMessageBox, MdTooltip } from '@mdfe/medi-ui';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import { cloneDeep } from 'lodash';
import { h } from 'vue';
import JiLuDrawer from './components/JiLuDrawer.vue';
import addDialog from './components/addDialog.vue';

const initQuery = () => {
  return {
    jiaGeID: '',
    jiaGeMC: '',
    qiYongBZ: 1,
    yiDaoLBZ: 0,
  };
};
export default {
  name: 'xiaoHaoZLXZ',
  data() {
    return {
      params: {},
      loading: false,
      query: initQuery(),
      inline: false,
      placeholder: '药品名称与规格',
      type: 'ypxh',
      showDialog: false,
      hasChange: false,
      canDaYin: false,
      dialogVisible: false,
      tiShiBL: '',
      tableData: [],
      selection: [],
      xianShiXX: [],
      columnsList: [
        {
          label: '',
          prop: 'yaoPinLXMC',
          width: 50,
          align: 'center',
          formatter(v) {
            return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
          },
          showOverflowTooltip: true,
        },
        {
          render: (h, { row, $index }) => {
            const tagStyles = (styleData) => {
              let sty = {};
              if (styleData && styleData.jiaCuBZ) {
                sty['font-weight'] = 'bold';
              }
              if (styleData && styleData.xieTiBZ) {
                sty['font-style'] = 'oblique';
              }
              sty.color = styleData ? styleData.ziTiYS : 'unset';
              return sty;
            };
            const label =
              row.xianShiXX && row.xianShiXX.tianJiaWZ
                ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
                : row.yaoPinMC;
            return h('span', { style: tagStyles(row.xianShiXX) }, label);
          },
          label: '药品名称',
          prop: 'yaoPinMCYGG',
          width: 400,
          showOverflowTooltip: true,
          // formatter(v) {
          //   if (v.yaoPinMC) {
          //     return v.yaoPinMC + ' ' + v.yaoPinGG;
          //   } else {
          //     return '';
          //   }
          // },
        },
        {
          label: '规格',
          prop: 'yaoPinGG',
          width: 200,
          formatter(v) {
            if (v.jiaGeID) {
              return v.yaoPinGG;
            } else {
              return '';
            }
          },
          render: (h, { row, $index }) => {
            const tagStyles = (styleData) => {
              let sty = {};
              if (styleData && styleData.jiaCuBZ) {
                sty['font-weight'] = 'bold';
              }
              if (styleData && styleData.xieTiBZ) {
                sty['font-style'] = 'oblique';
              }
              sty.color = styleData ? styleData.ziTiYS : 'unset';
              return sty;
            };
            const label = row.yaoPinGG;
            return h('span', { style: tagStyles(row.xianShiXX) }, label);
          },
          showOverflowTooltip: true,
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          width: 140,
          showOverflowTooltip: true,
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 50,
        },
        // {
        //   label: '库存量',
        //   prop: 'kuCunSL',
        //   align: 'right',
        //   width: 100,
        //   formatter: (v) => {
        //     return Number(v.kuCunSL).toFixed(3);
        //   },
        // },
        {
          label: '单价',
          prop: 'danJia',
          width: 70,
          align: 'right',
          formatter: (v) => {
            return formatJiaGe(v.danJia);
          },
        },
      ],
      weiZhiLXDM: null,
      columns: [],
    };
  },
  mounted() {
    const { WeiZhiLXDM } = lyra.getShareDataSync();
    this.weiZhiLXDM = WeiZhiLXDM;
    this.columnsComputed();
    this.handleSearch();
  },
  methods: {
    columnsComputed() {
      if (this.weiZhiLXDM == 3) {
        return (this.columns = [
          {
            type: 'selection',
          },
          {
            prop: 'leiXing',
            label: '',
            width: 40,
            showOverflowTooltip: false,
          },
          {
            slot: 'yaoPinMCGG',
            label: '药品名称与规格',
            minWidth: 240,
          },
          {
            prop: 'chanDiMC',
            label: '产地名称',
            width: 280,
            showOverflowTooltip: false,
          },

          {
            prop: 'baoZhuangDW',
            label: '单位',
            width: 60,
            showOverflowTooltip: false,
          },
          {
            prop: 'xianZhiZL',
            label: '限制总量',
            width: 100,
            align: 'right',
          },
          {
            prop: 'tiShiBL',
            label: '提示比例',
            width: 96,
            align: 'right',
          },
          {
            prop: 'tiShiLiang',
            label: '提示量',
            width: 96,
            align: 'right',
          },
          {
            prop: 'dangYueXHL',
            label: '每月消耗量',
            width: 96,
            align: 'right',
          },
          {
            prop: 'yuanNeiBM',
            label: '院内编码',
            width: 90,
          },
          {
            prop: 'shengPingTBM',
            label: '省平台ID',
            width: 120,
          },
          {
            label: '启用',
            slot: 'qiYongBZ',
            width: 50,
            align: 'center',
            fixed: 'right',
          },
          {
            slot: 'xianShiQYBZ',
            width: 94,
            align: 'center',
            fixed: 'right',
            renderHeader: ({ column }) => {
              return h('div', [
                h('span', '限时启用'),
                h(MdTooltip, null, {
                  content: () =>
                    h(
                      'span',
                      { style: 'color: #fff;font-size:12px;' },
                      '启用5-10分钟后自动关闭',
                    ),
                  default: () =>
                    h('md-icon', {
                      class: 'mediinfo-vela-yaoku-web-icon-wenhao',
                      style: 'color:#222;cursor:pointer;margin-left:4px',
                    }),
                }),
              ]);
            },
          },
          {
            slot: 'operate',
            label: '操作',
            width: 90,
            fixed: 'right',
          },
        ]);
      } else {
        return (this.columns = [
          {
            prop: 'leiXing',
            label: '',
            width: 40,
            showOverflowTooltip: false,
          },
          {
            slot: 'yaoPinMCGG',
            label: '药品名称与规格',
            minWidth: 240,
          },
          {
            prop: 'chanDiMC',
            label: '产地名称',
            'min-width': 240,
            showOverflowTooltip: false,
          },

          {
            prop: 'baoZhuangDW',
            label: '单位',
            width: 60,
            showOverflowTooltip: false,
          },
          {
            prop: 'yuanNeiBM',
            label: '院内编码',
            'min-width': 90,
          },
          {
            prop: 'shengPingTBM',
            label: '省平台ID',
            'min-width': 120,
          },
          {
            label: '限时启用',
            slot: 'xianShiQYBZ',
            width: 94,
            align: 'center',
            renderHeader: ({ column }) => {
              return h('div', [
                h('span', '限时启用'),
                h(MdTooltip, null, {
                  content: () =>
                    h(
                      'span',
                      { style: 'color: #fff;font-size:12px;' },
                      '启用5-10分钟后自动关闭',
                    ),
                  default: () =>
                    h('md-icon', {
                      class: 'mediinfo-vela-yaoku-web-icon-wenhao',
                      style: 'color:#222;cursor:pointer;margin-left:4px',
                    }),
                }),
              ]);
            },
          },
          {
            slot: 'operate',
            label: '操作',
            width: 60,
            fixed: 'right',
          },
        ]);
      }
    },
    /**
     * 选择药品change事件
     */
    async handleYaoPinDWChange(data) {
      this.query.jiaGeID = '';
      if (data) {
        if (data.jiaGeID) this.query.jiaGeID = cloneDeep(data.jiaGeID);
      }
      await this.handleSearch();
    },
    handleSearch() {
      this.$refs.table.search({ pageSize: 200 });
    },
    async handleFetch({ page, pageSize }, config) {
      const query = cloneDeep(this.query);
      let params = null;
      //只显示停用
      if (query.qiYongBZ === 0) {
        params = query;
      } else {
        params = { jiaGeID: query.jiaGeID };
      }
      params = {
        ...params,
        yiDaoLBZ: query.yiDaoLBZ,
        pageSize: pageSize,
        pageIndex: page,
      };
      let [items, total] = await Promise.all([
        GetYaoPinXHLXZPZList(params, config),
        GetYaoPinXHLXZPZCount(params, config),
      ]);
      if (items.length > 0) {
        await this.getXianShiXX(items);
        const xianShiXX = this.xianShiXX;
        items.forEach((item) => {
          item.leiXing = item.yaoPinLXMC ? item.yaoPinLXMC.charAt(0) : '';
          item.xianShiXX = xianShiXX[item.jiaGeID] || {};
          item.yaoPinMCGG = item.yaoPinMC + ' ' + item.yaoPinGG;
        });
      }
      this.tableData = cloneDeep(items);
      return { items, total };
    },
    //列表选中
    handleSelect(selection) {
      this.selection = selection;
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 8 && Number(row.dangYueXHL > Number(row.xianZhiZL))) {
        return this.prefixClass('qinglingdan-row');
      }
    },
    //启用
    async handleQiYong(row, type) {
      await QiTingYXHLXZPZ({ id: row.id, qiYongLX: type });
      this.handleSearch();
    },
    //打印
    async handleDaYin() {
      const query = cloneDeep(this.query);
      let params = { jiaGeID: query.jiaGeID };
      //只显示停用
      if (query.qiYongBZ === 0) {
        params = cloneDeep(query);
      }
      this.params = params;
      this.$refs.daYinDialog.showModal();

      // printByUrl('YFXT0120', params);
    },
    //提成比例
    async handletiChengBL() {
      this.dialogVisible = true;
    },
    // 新增 打开弹框
    handleAdd() {
      this.$refs.addDialog.showModel({
        mode: 'add',
        data: '',
      });
    },
    // 编辑 打开弹框并传入id
    handleEdit(row) {
      this.$refs.addDialog.showModel({
        mode: 'edit',
        data: row,
      });
    },
    //记录 打开详情
    handleJilu(row) {
      this.$refs.JiLuDrawer.openDrawer(row);
    },
    /**
     * 通过传入的id 从后端接口查询表单数据
     *
     */
    async handleFormFetch(options) {
      const data = cloneDeep(options.data);
      return data;
    },

    /**
     * 弹框关闭前判断提示数据变化
     */
    async handleBeforeClose(done, change) {
      try {
        this.hasChange = change;
        if (this.hasChange) {
          MdMessageBox.confirm(
            '弹框数据已被修改,关闭后修改的数据将复原 是否继续?',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          ).then(async () => {
            try {
              done();
            } finally {
              done();
            }
          });
        }
      } catch (error) {}
    },
    async handleYingYong() {
      if (!this.tiShiBL) {
        this.$message({
          type: 'warning',
          message: '提示比例不能为空！',
        });
        return;
      }
      if (Number(this.tiShiBL) > 1) {
        this.$message({
          type: 'warning',
          message: '提示比例不能大于1！',
        });
        return;
      }

      const ids = this.selection.map((item) => {
        return item.id;
      });
      const params = { idList: ids, tiShiBL: this.tiShiBL };
      await UpdateTiShiBL(params);
      this.dialogVisible = false;
      this.tiShiBL = '';
      this.handleSearch();
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        res = await GetYaoPinTSSX({
          jiaGeIDList,
          xianShiLXDM: '1',
        });
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      this.xianShiXX = { ...this.xianShiXX, ...xianShiXX };
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
    'biz-yaopindw': BizYaoPinDW,
    'md-table-pro': MdTablePro,
    addDialog,
    YaoPinShow,
    JiLuDrawer,
    // 'biz-yaopin-select': BizYaoPinSelect,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-xiaohaozl-table.#{$md-prefix}-data-table
  .#{$md-prefix}-qinglingdan-row {
  background-color: #ffd2cc !important;
  color: red;
}
</style>
<style lang="scss" scoped>
.HISYK-xiaoHaoZLXZ {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  &-container {
    flex: 1;
    min-height: 0;
  }

  &-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: white;

    &-table {
      padding-left: 8px;
      padding-right: 8px;
      // @include md-def('padding-left', 'spacing-3');
      // @include md-def('padding-right', 'spacing-3');
      flex: 1;
      min-height: 0;

      ::v-deep .table-wrapper {
        min-height: 0;
      }

      .HISYK-state {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        font-family: PingFang SC;
        text-align: center;
        color: white;
        background-color: #46a9a8;
        border-radius: 2px;
        font-size: 14px;
      }

      .HISYK-zhuangTai {
        display: inline-block;
        height: 20px;
        width: 20px;
        color: #ff8600;
        background-color: #ffeedb;
        border-radius: 2px;
        text-align: center;
      }

      .HISYK-zhuangTai0 {
        display: inline-block;
        width: 20px;
        height: 20px;
        color: white;
        background-color: #5ad8a6;
        border-radius: 2px;
        text-align: center;
      }

      .HISYK-zhuangTai1 {
        display: inline-block;
        width: 20px;
        height: 20px;
        color: white;
        background-color: #1385f0;
        border-radius: 2px;
        text-align: center;
      }
    }

    &-dialog {
      // ::v-deep .#{$--css-prefix}-input {
      //   width: 266px !important;
      // }

      ::v-deep .HISYK-form-item .HISYK-base-form-item__label {
        width: 120px !important;
      }

      ::v-deep .HISYK-form-item .HISYK-base-form-item__content {
        margin-left: 120px !important;
      }
    }
  }

  &-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #ffffff;

    &-left {
      display: flex;

      ::v-deep .HISYK-input__inner {
        width: 305px !important;
      }

      ::v-deep .mediinfo-vela-yaoku-web-checkbox {
        margin-left: 8px;
      }

      ::v-deep .HISYK-checkbox__label {
        white-space: nowrap !important;
      }
    }

    &-right {
      display: flex;

      button {
        padding-right: 8px !important;
        // @include md-def('padding-right', 'spacing-3', !important);
      }
    }
  }
}

::v-deep .HISYK-form-item .HISYK-base-form-item__label {
  width: 120px !important;
}

::v-deep .HISYK-form-item .HISYK-base-form-item__content {
  margin-left: 120px !important;
}

::v-deep .HISYK-form-item--show-status-icon {
  padding-right: 0;
}

::v-deep .HISYK-dialog__wrapper .HISYK-dialog .HISYK-dialog__footer {
  padding-top: 0;
}
</style>
