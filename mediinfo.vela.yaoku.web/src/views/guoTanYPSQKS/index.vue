<template>
  <div :class="prefixClass('guotanypsqks-views')">
    <div :class="prefixClass('guotanypsqks-contain')">
      <div :class="prefixClass('guotanypsqks-contain-caozuo')">
        <div>
          <biz-yaopindw
            v-model="yaoPinMC"
            placeholder="院内编码/药品规格"
            style="width: 305px; margin-right: 8px"
            type="gtypsqks"
            @change="handleSearch"
          >
          </biz-yaopindw>
          <md-checkbox-group
            v-model="guoTanChecked"
            @change="handleSearchChecked"
          >
            <md-checkbox
              v-for="item in checkOptions"
              :label="item.biaoZhunDM"
              :key="item.biaoZhunDM"
            >
              {{ item.biaoZhunMC }}
            </md-checkbox>
          </md-checkbox-group>
          <md-checkbox
            v-model="xianDingQBZ"
            class="checkoutClass"
            :true-label="1"
            :false-label="0"
            style="margin-left: 20px"
            @change="handleChange"
            >仅展示协议期内药品</md-checkbox
          >
        </div>
        <md-button
          type="primary"
          :icon="prefixClass('icon-dayinji')"
          noneBg
          @click="handleYuLan"
          >预览</md-button
        >
      </div>
      <div :class="prefixClass('guotanypsqks-contain-table')">
        <md-table-pro
          ref="tablePro"
          :columns="columns"
          :onFetch="handleFetch"
          height="100%"
          @sort-change="handleSortChange"
        >
          <template #mingXiList="{ row }">
            <div :class="prefixClass('table-shoucisqks')">
              <span v-for="item in row.mingXiList">{{
                item.shouCiSQKSMC
              }}</span>
            </div>
          </template>
        </md-table-pro>
      </div>
    </div>
    <shenqingks-dialog
      ref="shenQingKSDialog"
      @refresh="handleSearch"
    ></shenqingks-dialog>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT024'"
      :fileName="'国谈药品'"
      :title="'国谈药品打印预览'"
    />
  </div>
</template>
<script>
import dayjs from 'dayjs';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  GetGuoTanYPSQKSCount,
  GetGuoTanYPSQKSList,
} from '@/service/yaoPin/yaoPinZD';
import shenQingKSDialog from './components/shenQingKSDialog.vue';
import DaYinDialog from '@/components/DaYinDialog.vue';
export default {
  name: 'guotanypsqks',
  data() {
    return {
      checked: false,
      guoTanChecked: ['14'],
      checkOptions: [
        {
          biaoZhunDM: '14',
          biaoZhunMC: '国家医保谈判药品',
        },
        {
          biaoZhunDM: '999',
          biaoZhunMC: '双通道',
        },
        {
          biaoZhunDM: '998',
          biaoZhunMC: '竞价性国谈',
        },
      ],
      params: {},
      paiXuBZ: 1,
      kaiShiPXBZ: 1,
      jieShuPXBZ: 1,
      yaoPinJJLXList: [],
      jinJiLXactive: '',
      jinJiLXMC: '',
      yaoPinMC: '',
      columns: [
        {
          label: '院内编码',
          prop: 'yuanNeiBM',
        },
        {
          label: '省平台ID',
          prop: 'shengPingTBM',
        },
        {
          label: '药品名称与规格',
          prop: 'yaoPinMC',
          formatter(v) {
            return `${v.yaoPinMC}${v.yaoPinGG}`;
          },
          sortable: 'custom',
        },
        {
          label: '产地',
          prop: 'chanDiMC',
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 74,
        },
        {
          label: '首次申请科室',
          prop: 'mingXiList',
          slot: 'mingXiList',
        },
        {
          prop: 'kaiShiSJ',
          label: '开始日期',
          width: 180,
          sortable: 'custom',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'jieShuSJ',
          label: '结束日期',
          width: 180,
          sortable: 'custom',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          label: '备注',
          prop: 'beiZhu',
          width: 154,
        },
        {
          type: 'operate',
          label: '操作',
          width: 46,
          actions: [
            {
              text: '编辑',
              onPressed: ({ row, column, $index }) => {
                this.handleShenQingKS(row);
              },
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.handleSearch();
  },
  methods: {
    handleChange(val) {
      // this.xianDingQBZ = val == true ? 1 : '';
      this.$forceUpdate();
      this.$refs.tablePro.search({ pageSize: 100 });
    },
    handleSearchChecked() {
      this.$refs.tablePro.search({ pageSize: 100 });
    },
    // 查询条件变更搜索
    handleSearch(data) {
      if (data) {
        this.yaoPinMC = data;
        this.jiaGeID = data.jiaGeID;
      } else {
        this.yaoPinMC = '';
        this.jiaGeID = '';
      }
      this.$refs.tablePro.search({ pageSize: 100 });
    },
    handleYuLan() {
      this.params = { jiaGeID: this.jiaGeID, paiXuBZ: this.paiXuBZ };
      this.$refs.daYinDialog.showModal();
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        pageSize: pageSize,
        pageIndex: page,
        jiaGeID: this.jiaGeID,
        paiXuBZ: this.paiXuBZ,
        kaiShiPXBZ: this.kaiShiPXBZ,
        jieShuPXBZ: this.jieShuPXBZ,
        xianDingQBZ: this.xianDingQBZ,
        guoTanSXBZ: this.guoTanChecked.join(','),
      };

      const [items, total] = await Promise.all([
        GetGuoTanYPSQKSList(params, config),
        GetGuoTanYPSQKSCount(params, config),
      ]);
      return {
        items,
        total,
      };
    },
    handleShenQingKS(row) {
      this.$refs.shenQingKSDialog.showModal({
        row: row,
      });
    },

    //升降序
    handleSortChange({ column, prop, order }) {
      if (!order) return;
      if (column.label == '药品名称与规格') {
        this.paiXuBZ = order == 'ascending' ? 1 : 2; //正序1，倒序2
      } else if (column.label == '开始日期') {
        this.kaiShiPXBZ = order == 'ascending' ? 1 : 2;
      } else if (column.label == '结束日期') {
        this.jieShuPXBZ = order == 'ascending' ? 1 : 2;
      }
      this.$refs.tablePro.search({ pageSize: 100 });
    },
  },
  components: {
    'shenqingks-dialog': shenQingKSDialog,
    'biz-yaopindw': BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-guotanypsqks-views {
  height: 100%;
  padding: var(--md-spacing-3);
  box-sizing: border-box;
  overflow: hidden;

  .#{$md-prefix}-guotanypsqks-contain {
    // flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &-caozuo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--md-spacing-3);
      box-sizing: border-box;
      & > div:nth-child(1) {
        display: flex;
      }
      .checkoutClass {
        margin-left: var(--md-spacing-3);
      }
    }

    &-table {
      flex: 1;
      overflow: hidden;
    }
  }

  .#{$md-prefix}-table-shoucisqks {
    span {
      padding: 3px;
      margin-right: 8px;
      background-color: getCssVar('color-1');
    }
  }
}
</style>
