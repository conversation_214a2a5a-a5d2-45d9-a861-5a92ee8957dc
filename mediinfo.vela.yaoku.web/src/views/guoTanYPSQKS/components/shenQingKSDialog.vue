<template>
  <md-dialog
    title="编辑申请科室"
    v-model="dialogVisible"
    :before-save="handleSave"
    :before-close="handleClose"
    size="middle"
    :content-scrollable="false"
    lock-scroll
    :close-on-click-modal="false"
  >
    <div :class="prefixClass('shenqingks-dialog')">
      <div :class="prefixClass('shenqingks-dialog-title')">
        {{ formData.yaoPinMC }}{{ formData.yaoPinGG }}
        <div :class="prefixClass('shenqingks-dialog-subtitle')">
          <span style="margin-right: 8px"
            >产地{{ formData.chanDiMC || '-' }}</span
          >院内编码：{{ formData.yuanNeiBM || '-' }}
        </div>
      </div>
      <div :class="prefixClass('shenqingks-dialog-time')">
        <md-form
          :model="timeformData"
          :rules="formRules"
          label-width="80px"
          use-status-tooltip-icon
          ref="form"
        >
          <md-row>
            <md-col :span="12">
              <md-form-item label="开始日期" prop="kaiShiSJ">
                <md-date-picker
                  v-model="timeformData.kaiShiSJ"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleKaiShiSJ"
                ></md-date-picker>
              </md-form-item>
            </md-col>
            <md-col :span="12">
              <md-form-item label="结束日期" prop="jieShuSJ">
                <md-date-picker
                  v-model="timeformData.jieShuSJ"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleJieShuSJ"
                ></md-date-picker>
              </md-form-item>
            </md-col>
          </md-row>
        </md-form>
      </div>
      <div class="table">
        <md-editable-table-pro
          v-enter
          v-model="formData.mingXiList"
          :columns="columns"
          :new-row="addRow"
          :autoFill="true"
          :hide-add-button="true"
          auto-focus
        >
          <template v-slot:shouCiSQKSMC="{ row, $index }">
            <md-select
              v-model="row.shouCiSQKSMC"
              @change="handleKeShi($event, row, $index)"
              value-key="keShiID"
              filterable
              :remote-method="filterMethod"
              remote
            >
              <md-option
                v-for="item in keShiOptions"
                :key="item.keShiID"
                :value="item"
                :label="item.keShiMC"
              ></md-option>
            </md-select>
          </template>
        </md-editable-table-pro>
      </div>

      <md-input
        v-model="formData.beiZhu"
        type="textarea"
        placeholder="请输入备注内容"
        :rows="3"
      />
    </div>
    <!-- :add-button-disabled="addButtonDisabled" -->
    <!-- :onBeforeRemove="delRow" -->

    <template #footer class="dialog-footer">
      <md-button
        type="primary"
        plain
        :disabled="saveLoading"
        @click="handleClose"
        >取消</md-button
      >
      <md-button type="primary" :loading="saveLoading" @click="handleSave">
        保存
      </md-button>
    </template>
  </md-dialog>
</template>

<script>
import BizYaoPinSelect from '@/components/BizYaoPinSelect.vue';
import { MoJiKSList, SaveGuoTanYPSQKS } from '@/service/yaoPin/yaoPinZD';
import { DeletePeiWuJJList } from '@/service/yaoPinYK/yaoPinGZ';
import { getJiGouID } from '@/system/utils/local-cache';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import cloneDeep from 'lodash/cloneDeep';

const initFormData = () => {
  return {
    yuanNeiBM: '',
    yaoPinID: '',
    jiaGeID: '',
    yaoPinMC: '',
    yaoPinGG: '',
    baoZhuangDW: '',
    chanDiID: '',
    chanDiMC: '',
    mingXiList: [],
    keShiOptions: [],
    keShiOptionsFilter: [],
  };
};
export default {
  name: 'shenqingks-dialog',
  data() {
    return {
      timeformData: {},
      dialogVisible: false,
      saveLoading: false,
      formData: initFormData(),
      yaoPinJJLXList: [],
      peiWuJJYPList: [],
      peiZhiFSList: [],
      yongYaoFLList: [],
      yiZhuXZList: [],
      onCheck: false,
      columns: [
        {
          slot: 'shouCiSQKSMC',
          label: '科室',
        },
      ],
      addDisabled: false,
      formRules: {
        kaiShiSJ: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
        ],
      },
    };
  },

  watch: {
    // 'formData.yaoPinID': {
    //   handler(val) {
    //     console.log(val, 'val');
    //   },
    // },
  },
  methods: {
    async showModal(options) {
      const dataRow = cloneDeep(options.row);
      try {
        this.dialogVisible = true;
        this.timeformData.kaiShiSJ = dataRow.kaiShiSJ;
        this.timeformData.jieShuSJ = dataRow.jieShuSJ;
        this.formData = cloneDeep(options.row);
        // 获取配伍禁忌类型
        const params = {
          keShiXZDM: '1',
          zuZhiJGID: getJiGouID(),
          pageSize: 10000,
          menZhenSYBZ: null,
          zhuYuanSYBZ: null,
          jiZhenSYBZ: null,
        };
        this.keShiOptions = await MoJiKSList(params);
        this.keShiOptionsFilter = cloneDeep(this.keShiOptions);
        this.addRow();
      } catch (error) {
        MdMessage.error(error);
      }
    },
    //开始时间事件
    handleKaiShiSJ(val) {
      if (val) {
        let kaiShiSJ = val.replace(/-/g, '');
        let jieShu = this.timeformData.jieShuSJ.split(' ')[0];
        let jieShuSJ = jieShu.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '开始时间不能大于结束时间！',
            type: 'warning',
          });
          this.timeformData.kaiShiSJ = '';
          return;
        }
      } else {
        this.timeformData.kaiShiSJ = '';
      }
    },
    // 结束时间事件
    handleJieShuSJ(val) {
      if (val) {
        let kaiShi = this.timeformData.kaiShiSJ.split(' ')[0];
        let kaiShiSJ = kaiShi.replace(/-/g, '');
        let jieShuSJ = val.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '结束时间不能小于开始时间！',
            type: 'warning',
          });
          this.timeformData.jieShuSJ = '';
          return;
        }
      } else {
        this.timeformData.jieShuSJ = '';
      }
    },

    filterMethod(query) {
      if (query !== '') {
        this.keShiOptions = this.keShiOptionsFilter.filter((item) => {
          return (
            item.keShiMC.indexOf(query) > -1 ||
            (item.shuRuMa1 &&
              item.shuRuMa1.toLowerCase().indexOf(query.toLowerCase()) > -1) ||
            (item.shuRuMa2 &&
              item.shuRuMa2.toLowerCase().indexOf(query.toLowerCase()) > -1)
          );
        });
      } else {
        this.keShiOptions = this.keShiOptionsFilter;
      }
    },
    handleKeShi(data, row, index) {
      if (data) {
        row.shouCiSQKSID = data.keShiID;
        row.shouCiSQKSMC = data.keShiMC;
        row.shunXuHao = '';
        row.jiaGeID = this.formData.jiaGeID;
      } else {
        row.shouCiSQKSID = '';
        row.shouCiSQKSMC = '';
        row.shunXuHao = '';
        row.jiaGeID = '';
      }
      if (index == this.formData.mingXiList.length - 1) {
        this.addRow();
      }
    },
    addRow() {
      if (this.addDisabled) return;
      this.formData.mingXiList.push({
        jiaGeID: '',
        shouCiSQKSID: '',
        shouCiSQKSMC: '',
        shunXuHao: '',
      });
    },
    // delRow(index) {
    //   if (this.formData.mingXiList[index].shouCiSQKSID) {
    //     this.zuoFeiYaoPinBFWZList.push(this.formData.mingXiList[index].shouCiSQKSID);
    //   }
    //   // this.list.splice(index, 1);
    // },
    //改变顺序号
    onChangeSXH(val) {
      if (val) {
        this.formData.shunXuHao = val.replace(/[^\d]+/g, '');
      }
    },
    //关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      // setTimeout(() => {
      // this.$nextTick(() => {
      this.formData = initFormData();
      // });
      // }, 500);
    },
    //保存
    async handleSave() {
      try {
        const result = await this.$refs.form.validate();
        if (!result) return;

        this.saveLoading = true;
        const form = this.formData;

        form.mingXiList.splice(form.mingXiList.length - 1, 1);

        form.kaiShiSJ = this.timeformData.kaiShiSJ;
        form.jieShuSJ = this.timeformData.jieShuSJ;

        await SaveGuoTanYPSQKS(form);
        MdMessage.success('保存成功！');
        this.handleClose();
        this.$emit('refresh');
      } catch (error) {
        // MdMessage.warning(error);
      } finally {
        this.saveLoading = false;
      }
    },

    async handleCancel() {
      try {
        await MdMessageBox.confirm(`确定作废该配伍禁忌？`, '操作提醒！', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        await DeletePeiWuJJList(this.formData.id).then(() => {
          MdMessage.success('作废成功!');
          this.handleClose();
          this.$emit('refresh');
        });
      } catch (error) {
        if (error === 'cancel') return;
        MdMessage.error(error.message);
      }
    },
    changeYaoPin(data, type) {
      if (type == '1') {
        if (JSON.stringify(data) == '{}') {
          this.formData.yaoPinID = '';
          this.formData.yaoPinMC = '';
        } else {
          this.formData.yaoPinID = data.yaoPinID;
          this.formData.yaoPinMC = data.yaoPinMC;
        }
      } else {
        if (JSON.stringify(data) == '{}') {
          this.formData.paiChiYPID = '';
          this.formData.paiChiYPMC = '';
        } else {
          this.formData.paiChiYPID = data.yaoPinID;
          this.formData.paiChiYPMC = data.yaoPinMC;
        }
      }
    },
  },
  components: {
    'biz-yaopin-select': BizYaoPinSelect,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-shiyongbz {
  width: 100%;
  border: 1px dashed;
  border-color: #dddddd;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.#{$md-prefix}-shenqingks-dialog {
  height: 100%;
  background: #fff;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;

  &-title {
    padding: 8px;
    font-weight: bold;
    color: #222222;
    font-size: 14px;
    background-color: getCssVar('color-1');

    div {
      margin-top: 8px;
      color: #666666;
      font-weight: normal;

      span {
        margin-right: 8px;
      }
    }
  }
  &-time {
    padding: getCssVar('spacing-3');
    padding-bottom: 0px;
  }

  .table {
    flex: 1;
    overflow: hidden;
    margin-bottom: 8px;
  }
}

.formStyle {
  padding: 12px 8px 8px;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

// .shuyel ::v-deep .HISYF-input__inner {
//   border-right: 0 !important;
// }
// .shuyel ::v-deep .HISYF-input-group__append {
//   color: #aaa;
// }
.shuyel:hover {
  border-color: #3da0fc !important;
}

::v-deep .ywgl-scrollbar__view {
  padding: 0 !important;
  height: 100% !important;
}

.ywgl-dialog__wrapper .ywgl-dialog .ywgl-dialog__footer {
  border: 1px solid;
  border-color: #dddddd;
}

// .focus ::v-deep .HISYF-input-group__append {
//   @include md-def('border-color', 'color-6');
// }
</style>
