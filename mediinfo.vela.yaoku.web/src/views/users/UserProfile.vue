<script lang="ts" setup>
defineOptions({ name: 'UserProfile', inheritAttrs: false });

defineProps({
  name: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  viewName: {
    type: String,
    required: true,
  },
  viewActive: {
    type: Boolean,
    required: true,
  },
});
</script>

<template>
  <div class="user-profile-view">
    <div>{{ name }} {{ title }} {{ viewName }}</div>
  </div>
</template>
