<template>
  <md-dialog
    title="参考项"
    v-model="visible"
    :v-loading="formLoading"
    class="HISYK-CanKaoXWH-addDialog"
    :before-close="handleClose"
  >
    <md-row :gutter="16">
      <md-form
        :model="formData"
        :rules="formRules"
        label-width="100px"
        use-status-icon
        ref="form"
        style="width: 100%"
      >
        <md-row>
          <md-col :span="12">
            <md-form-item label="参考项名称" prop="canKaoXMC">
              <md-input v-model="formData.canKaoXMC" :disabled="isEdit" />
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="单位">
              <md-select
                v-model="formData.danWeiID"
                :disabled="isEdit"
                placeholder="请选择"
              >
                <md-option
                  v-for="item in danWeiList"
                  :key="item.biaoZhunDM"
                  :label="item.biaoZhunMC"
                  :value="item.biaoZhunDM"
                >
                </md-option>
              </md-select>
            </md-form-item>
          </md-col>
        </md-row>

        <md-row>
          <md-col :sapn="24">
            <md-form-item label="参考范围">
              <md-col :span="4">
                <md-select
                  v-model="formData.canKaoFW"
                  placeholder="请选择"
                  @change="handleSelect"
                >
                  <md-option
                    v-for="item in fanWeiList"
                    :key="item.biaoZhunDM"
                    :label="item.biaoZhunMC"
                    :value="item.biaoZhunDM"
                  >
                  </md-option>
                </md-select>
              </md-col>
              <md-col :span="20">
                <md-input
                  class="padleft-8"
                  v-if="formData.canKaoFW != 1"
                  v-model="formData[activeFW]"
                />
                <div class="padleft-8 HISYK-CanKaoXWH-canKaoFW" v-else>
                  <md-row>
                    <md-col :span="12">
                      <md-input
                        v-model="formData.quZhiFWXX"
                        class="fanWei1"
                        type="number"
                      >
                        <template #prepend>
                          <div class="select-wrap">
                            <md-select
                              v-model="formData.xiaXianYSFHDM"
                              placeholder="请选择"
                              style="width: 100px"
                              @change="
                                ($event) =>
                                  handleFanWeiChange($event, 'xiaXianYSFHMC')
                              "
                            >
                              <md-option
                                v-for="item in resFanWeiList"
                                :key="item.biaoZhunDM"
                                :label="item.biaoZhunMC"
                                :value="item.biaoZhunDM"
                              >
                              </md-option>
                            </md-select>
                          </div>
                        </template>
                      </md-input>
                    </md-col>
                    <md-col :span="12">
                      <md-input
                        v-model="formData.quZhiFWSX"
                        class="fanWei2"
                        type="number"
                      >
                        <template #prepend>
                          <div class="select-wrap">
                            <md-select
                              v-model="formData.shangXianYSFHDM"
                              placeholder="请选择"
                              style="width: 100px"
                              @change="
                                ($event) =>
                                  handleFanWeiChange($event, 'shangXianYSFHMC')
                              "
                            >
                              <md-option
                                v-for="item in resFanWeiList2"
                                :key="item.biaoZhunDM"
                                :label="item.biaoZhunMC"
                                :value="item.biaoZhunDM"
                              >
                              </md-option>
                            </md-select>
                          </div>
                        </template>
                      </md-input>
                    </md-col>
                  </md-row>
                </div>
              </md-col>
            </md-form-item>
          </md-col>
        </md-row>

        <md-row>
          <md-col :span="12">
            <md-form-item label="">
              <div class="HISYK-CanKaoXWH-checkbox">
                <md-checkbox
                  :label="true"
                  v-model="formData.xianZhiKL"
                  style="width: 100%"
                  >限制开立</md-checkbox
                >
              </div>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="顺序号">
              <md-input v-model="formData.shunXuHao" v-number="{}" />
            </md-form-item>
          </md-col>
        </md-row>
        <md-row>
          <md-col :span="24">
            <md-form-item label="公式代码" prop="jiSuanGS">
              <md-input
                v-model="formData.jiSuanGS"
                :disabled="isEdit"
                type="textarea"
                :rows="6"
              />
            </md-form-item>
          </md-col>
        </md-row>
        <md-row>
          <md-col :span="24">
            <md-form-item label="公式说明">
              <md-input
                v-model="formData.gongShiSM"
                :disabled="isEdit"
                type="textarea"
                :rows="2"
              />
            </md-form-item>
          </md-col>
        </md-row>
      </md-form>
    </md-row>
    <template #footer>
      <span class="dialog-footer">
        <md-button
          v-if="isEdit"
          :disabled="saveLoading"
          type="danger"
          plain
          style="float: left"
          @click="handleCancel"
        >
          作废
        </md-button>
        <md-button
          type="primary"
          plain
          :disabled="saveLoading"
          @click="handleClose"
          >取消</md-button
        >
        <md-button type="primary" :loading="saveLoading" @click="handleSave">
          确定
        </md-button>
      </span>
    </template>
  </md-dialog>
</template>

<script>
import {
  GetCanKaoXXX,
  SaveCanKaoXiang,
  ZuoFeiCanKaoXiang,
} from '@/service/yaoPin/yaoPinTPN.js';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { logger } from '@/service/log';
function formData() {
  return {
    id: null,
    canKaoXMC: '',
    danWeiID: '',
    danWeiMC: null,
    canKaoFW: null,
    canKaoFWMC: null,
    xianZhiKL: null,
    shangXianYSFHDM: null,
    shangXianYSFHMC: null,
    quZhiFWSX: null,
    xiaXianYSFHDM: null,
    xiaXianYSFHMC: null,
    quZhiFWXX: null,
    jiSuanGS: null,
    gongShiSM: null,
    shunXuHao: 0,
    zuZhiJGID: null,
  };
}
export default {
  name: 'CanKaoXWH-addDialog',
  data() {
    return {
      isEdit: false,
      saveLoading: false,
      visible: false,
      danWeiList: [],

      fanWeiList: [
        { label: '区间', value: '1' },
        { label: '大于等于', value: '2' },
        { label: '小于等于', value: '3' },
        { label: '大于', value: '4' },
        { label: '小于', value: '5' },
      ],
      activeFW: null,
      formData: formData(),
      formRules: {
        canKaoXMC: [
          { required: true, message: '请输入参考项名称', trigger: 'change' },
        ],
        jiSuanGS: [
          { required: true, message: '请输入公式代码', trigger: 'change' },
        ],
      },
      formLoading: false,
      yaoPinDWList: [],
      canKaoFWList: [],
      zuZhiJGID: null,
    };
  },
  created() {
    this.initData();
  },
  computed: {
    resFanWeiList() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('大于'));
    },
    resFanWeiList2() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('小于'));
    },
  },
  methods: {
    async showModel({ mode, data }) {
      this.visible = true;
      await this.$nextTick();
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.formData = formData();
      this.isEdit = false;
      this.zuZhiJGID = data.zuZhiJGID;
      if (mode == 'edit') {
        if (this.zuZhiJGID == 0) {
          this.isEdit = true;
        }
        this.formLoading = true;
        const res = await GetCanKaoXXX({ id: data.id });
        Object.assign(this.formData, res);
        this.formData.xianZhiKL = res.xianZhiKLBZ ? true : false;
        this.formLoading = false;
        if (this.formData.canKaoFW.includes('<')) {
          this.activeFW = 'quZhiFWSX';
        }
        if (this.formData.canKaoFW.includes('>')) {
          this.activeFW = 'quZhiFWXX';
        }
      }

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    handleSelect() {
      Object.assign(this.formData, {
        shangXianYSFHDM: null,
        shangXianYSFHMC: null,
        quZhiFWSX: null,
        xiaXianYSFHDM: null,
        xiaXianYSFHMC: null,
        quZhiFWXX: null,
      });
      const { canKaoFW } = this.formData;
      const curData = this.fanWeiList.find(
        (item) => item.biaoZhunDM == canKaoFW,
      );
      if (canKaoFW.includes('<')) {
        this.activeFW = 'quZhiFWSX';
        this.formData.shangXianYSFHDM = canKaoFW;
        this.formData.shangXianYSFHMC = curData.biaoZhunMC;
      }
      if (canKaoFW.includes('>')) {
        this.activeFW = 'quZhiFWXX';
        this.formData.xiaXianYSFHDM = canKaoFW;
        this.formData.xiaXianYSFHMC = curData.biaoZhunMC;
      }
    },
    handleFanWeiChange(value, str) {
      const curData = this.fanWeiList.find((item) => item.biaoZhunDM == value);
      this.formData[str] = curData.biaoZhunMC;
    },
    initData() {
      getYaoPinShuJuYZYList([
        'YP0022', //药品单位
        'YP0067', //参考范围
      ]).then((res) => {
        this.danWeiList = res[0].zhiYuList;
        this.fanWeiList = res[1].zhiYuList;
      });
    },
    handleCancel() {
      this.saveLoading = true;
      ZuoFeiCanKaoXiang({ id: this.formData.id })
        .then((res) => {
          this.resolve(true);
          this.visible = false;
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },
    handleClose() {
      this.visible = false;
    },
    async handleSave() {
      try {
        const result = await this.$refs.form.validate();
        if (!result) return;

        if (this.formData.danWeiID) {
          this.formData.danWeiMC = this.danWeiList.find(
            (item) => item.biaoZhunDM == this.formData.danWeiID,
          ).biaoZhunMC;
        }
        if (this.formData.canKaoFW) {
          let key = '';
          if (this.formData.canKaoFW.includes('>')) {
            key = 'quZhiFWXX';
          } else {
            key = 'quZhiFWSX';
          }
          if (
            (this.formData.quZhiFWXX == null && key == 'quZhiFWXX') ||
            (this.formData.quZhiFWSX == null && key == 'quZhiFWSX')
          ) {
            this.$message({
              type: 'warning',
              message: '请填写参考范围数值',
            });
            return;
          }
        }
        const { quZhiFWXX, quZhiFWSX } = this.formData;
        if (
          parseFloat(quZhiFWXX) &&
          parseFloat(quZhiFWSX) &&
          parseFloat(quZhiFWXX) >= parseFloat(quZhiFWSX)
        ) {
          this.$message({
            type: 'warning',
            message: '取值上限需高于取值下限',
          });
          return;
        }
        let params = {
          ...this.formData,
          zuZhiJGID: this.zuZhiJGID,
          xianZhiKLBZ: this.formData.xianZhiKL ? 1 : 0,
        };
        this.saveLoading = true;
        await SaveCanKaoXiang(params);
        this.resolve(true);
        this.$message({
          type: 'success',
          message: '保存成功！',
        });
        this.visible = false;
      } catch (error) {
        console.error(error);
        logger.error(error);
      } finally {
        this.saveLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-CanKaoXWH-addDialog {
  .padleft-8 {
    padding-left: getCssVar('spacing-3');
  }

  .HISYK-CanKaoXWH-checkbox {
    width: 100%;
    border: 1px dashed #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 0 getCssVar('spacing-3');
  }

  .HISYK-CanKaoXWH-canKaoFW {
    ::v-deep .#{$namespace}-select {
      &:hover {
        border-color: unset;
      }
    }

    .select-wrap {
      width: 100px;
    }
  }
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select:hover
  .#{$namespace}-input__wrapper {
  box-shadow: unset !important;
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select
  .#{$namespace}-input
  .#{$namespace}-input__wrapper {
  box-shadow: unset;
}
</style>
