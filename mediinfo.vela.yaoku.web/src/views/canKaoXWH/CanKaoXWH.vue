<template>
  <div class="HISYK-CanKaoXWH">
    <div class="HISYK-CanKaoXWH-header">
      <md-input
        v-model="name"
        placeholder="参考项名称"
        width="300px"
        @enter="handleSearch"
      >
        <template #suffix>
          <i :class="[nsIcon.b(), nsIcon.b('seach')]" @click="handleSearch"></i>
        </template>
      </md-input>
      <div class="HISYK-CanKaoXWH-header-btns">
        <md-button
          v-if="treeSelect[0] == 0"
          type="primary"
          :icon="nsIcon.b('xinzeng')"
          noneBg
          @click="handleAdd"
          >新增</md-button
        >
        <template v-else>
          <md-button
            type="primary"
            :icon="nsIcon.b('banbengengxin')"
            class="HISYK-mr8"
            noneBg
            @click="handleUpDate"
            >更新</md-button
          >
          <md-button type="primary" plain @click="handleInit">初始化</md-button>
        </template>

        <md-tree-select
          v-model="treeSelect"
          class="CanKaoXWH-tree"
          :data="treeData"
          trigger="click"
          checkStrictly
          @node-click="handleSelectchange"
        />
      </div>
    </div>
    <div class="HISYK-CanKaoXWH-content">
      <md-table-pro
        element-loading-text="正在加载中..."
        height="100%"
        :columns="columns"
        ref="table"
        :onFetch="handleFetch"
      >
        <template v-slot:xianZhiKLBZ="{ row }">
          <i
            v-show="row.xianZhiKLBZ"
            :class="[nsIcon.b('gou'), 'iconfont', 'icongou']"
          />
        </template>
        <template v-slot:qiYongBZ="{ row }">
          <md-switch
            v-model="row.qiYongBZ"
            :activeValue="1"
            :inactiveValue="0"
            @change="handleSwitchChange(row)"
          ></md-switch>
        </template>
        <template v-slot:opera="{ row }">
          <md-button type="primary" noneBg @click="handleEdit(row)"
            >编辑</md-button
          >
        </template>
      </md-table-pro>
    </div>
    <addDialog ref="addDialog"></addDialog>
  </div>
</template>

<script>
import { useNamespace } from '@mdfe/medi-ui';
import addDialog from './components/addDialog.vue';
import { request as getGetHisZuZhiJGTree } from '@/service/gongYong/getGetHisZuZhiJGTree';
import {
  GetCanKaoXiangList,
  GetCanKaoXiangCount,
  QiTingYCKX,
  GengXinCKX,
  ChuShiHCKX,
} from '@/service/yaoPin/yaoPinTPN.js';

export default {
  name: 'CanKaoXWH',
  data() {
    return {
      name: null,
      treeSelect: [],
      zuZhiJGID: null,
      zuZhiJGMC: null,
      treeData: [],
      columns: [
        {
          label: '参考项名称',
          prop: 'canKaoXMC',
        },
        {
          label: '单位',
          prop: 'danWeiMC',
          width: 200,
        },
        {
          label: '参考范围',
          prop: 'canKaoFW',
          width: 200,
        },
        {
          label: '限制开立',
          prop: 'xianZhiKLBZ',
          slot: 'xianZhiKLBZ',
          width: 80,
          align: 'center',
        },
        {
          label: '启用',
          prop: 'qiYongBZ',
          slot: 'qiYongBZ',
          width: 80,
          align: 'center',
        },
        {
          label: '操作',
          prop: 'opera',
          slot: 'opera',
          width: 60,
          align: 'center',
        },
      ],
      tableLoading: false,
    };
  },
  setup() {
    const nsIcon = useNamespace('icon');
    return {
      nsIcon,
    };
  },
  created() {
    this.initData();
  },
  methods: {
    async initData() {
      this.treeData = await getGetHisZuZhiJGTree({
        shiFouQB: false,
        youWuTY: true,
      });
      this.treeSelect = [this.treeData[0].value];
      this.zuZhiJGMC = this.treeData[0].label;
      this.zuZhiJGID = this.treeData[0].value;
      this.handleSearch();
    },
    async handleAdd() {
      await this.$refs.addDialog.showModel({
        mode: 'add',
        data: {
          zuZhiJGID: this.treeSelect[0],
        },
      });
      this.handleSearch();
    },
    async handleEdit(row) {
      await this.$refs.addDialog.showModel({
        mode: 'edit',
        data: {
          zuZhiJGID: this.treeSelect[0],
          ...row,
        },
      });
      this.handleSearch();
    },
    handleSearch() {
      this.$refs.table.search({ pageSize: 100 });
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        canKaoXMC: this.name,
        pageIndex: page,
        zuZhiJGID: this.zuZhiJGID,
        pageSize,
      };
      let items = [],
        total = 0;
      try {
        this.tableLoading = true;
        items = await GetCanKaoXiangList(params, config);
        total = await GetCanKaoXiangCount(params, config);
        this.tableLoading = false;
      } catch (error) {
        this.tableLoading = false;
      }
      return { items, total: total };
    },
    handleSelectchange(node, obj) {
      this.zuZhiJGMC = node.label;
      this.zuZhiJGID = node.value;
      this.handleSearch();
    },
    handleUpDate() {
      GengXinCKX({
        zuZhiJGID: this.zuZhiJGID,
        zuZhiJGMC: this.zuZhiJGMC,
      }).then((res) => {
        this.handleSearch();
      });
    },
    handleInit() {
      ChuShiHCKX({
        zuZhiJGID: this.zuZhiJGID,
        zuZhiJGMC: this.zuZhiJGMC,
      }).then((res) => {
        this.handleSearch();
      });
    },
    async handleSwitchChange(row) {
      await QiTingYCKX({ id: row.id });
      if (row.qiYongBZ == 0) {
        this.$message({
          type: 'success',
          message: '停用成功！',
        });
      } else {
        this.$message({
          type: 'success',
          message: '启用成功！',
        });
      }
    },
  },
  components: {
    addDialog,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-CanKaoXWH {
  display: flex;
  flex-direction: column;
  height: 100%;

  padding: getCssVar('spacing-3');

  .HISYK-mr8 {
    margin-right: getCssVar('spacing-3');
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: getCssVar('spacing-3');

    &-btns {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .CanKaoXWH-tree {
        width: 280px;
        margin-left: getCssVar('spacing-3');
      }
    }
  }

  &-content {
    flex: 1;
    min-height: 0;

    .#{$namespace}-icon-gou {
      color: getCssVar('color-6');
    }
  }

  .#{$namespace}-icon-seach {
    width: 24px;
  }
  .#{$namespace}-switch {
    height: unset;
  }
}
</style>
