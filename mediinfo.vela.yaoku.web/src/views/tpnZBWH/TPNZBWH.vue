<template>
  <div class="HISYK-TPNZBWH">
    <div class="HISYK-TPNZBWH-header">
      <md-input
        placeholder="指标名称"
        v-model="zhiBiaoMC"
        width="305px"
        @enter="initData"
      >
        <template #suffix>
          <i :class="[nsIcon.b(), nsIcon.b('seach')]" @click="initData"></i>
        </template>
      </md-input>
    </div>
    <div class="HISYK-TPNZBWH-table">
      <md-editable-table-pro
        v-loading="loading"
        v-model="tableData"
        :columns="columns"
        :new-row="newRow"
        auto-fill
        autoNew
        :showDefaultOperate="false"
        hideAddButton
        row-key="id"
        ref="editTable"
        :row-class-name="rowClassName"
      >
        <template #zhiBiaoMC="{ $index }">
          <md-input
            v-model="tableData[$index].zhiBiaoMC"
            placeholder="请输入"
          ></md-input>
        </template>
        <template #danWei="{ $index }">
          <md-input
            v-model="tableData[$index].danWei"
            placeholder="请输入"
          ></md-input>
        </template>
        <template #xianShiFSDM="{ $index, row }">
          <md-radio
            v-model="tableData[$index].xianShiFSDM"
            v-for="item in xianShiFSOption"
            :key="item.value"
            :label="item.value"
            @change="handleRidaoChange(row)"
            >{{ item.label }}</md-radio
          >
        </template>
        <template #qiYongBZ="{ $index }">
          <md-switch v-model="tableData[$index].qiYongBZ"></md-switch>
        </template>
        <template #opera="{ $index }">
          <div v-if="tableData.length - 1 != $index">
            <md-button
              :class="nsIcon.b('tuozhuai')"
              type="primary"
              noneBg
            ></md-button>
            <md-button
              type="primary"
              :icon="nsIcon.b('shanchu')"
              noneBg
              @click="handleDelete($index)"
            ></md-button>
          </div>
        </template>
      </md-editable-table-pro>
    </div>
    <div class="HISYK-TPNZBWH-foot">
      <md-button type="primary" plain style="width: 64px" @click="handleCancel"
        >取消</md-button
      >
      <md-button
        type="primary"
        class="mr-l-8"
        style="width: 64px"
        @click="handleSure"
        >确认</md-button
      >
    </div>
  </div>
</template>

<script>
import { GetZhiBiaoList, SaveZhiBiao } from '@/service/yaoPin/yaoPinTPN.js';
import { useParams } from '@/system/utils/useParams';
import { MdMessage, useNamespace } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
import Sortable from 'sortablejs';
const xianShiFSOption = [
  { label: '输入框', value: '1' },
  { label: '复选框', value: '2' },
];
export default {
  name: 'TPNZBWH',
  data() {
    return {
      zhiBiaoMC: null,
      key: 1,
      xianShiFSOption: xianShiFSOption,
      loading: false,
      columns: [
        {
          label: '指标名称',
          prop: 'zhiBiaoMC',
          slot: 'zhiBiaoMC',
          formatter: (row) => row.zhiBiaoMC,
          autoNewRequired: true,
        },
        {
          label: '单位',
          prop: 'danWei',
          slot: 'danWei',
          formatter: (row) => row.danWei,
          width: 200,
        },
        {
          label: '显示方式',
          prop: 'xianShiFSDM',
          slot: 'xianShiFSDM',
          width: 240,
        },

        {
          label: '顺序号',
          prop: 'shunXuHao',
          type: 'text',
          width: 100,
        },
        {
          label: '启用',
          prop: 'qiYongBZ',
          slot: 'qiYongBZ',
          width: 60,
          align: 'center',
        },
        {
          label: '',
          prop: 'opera',
          slot: 'opera',
          width: 60,
          align: 'center',
        },
      ],
      originData: [],
      tableData: [],
    };
  },
  setup() {
    const nsIcon = useNamespace('icon');
    return {
      nsIcon,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initData();
      this.initSortable();
    });
  },
  methods: {
    initData() {
      GetZhiBiaoList({ zhiBiaoMC: this.zhiBiaoMC }).then((res) => {
        this.originData = cloneDeep(res);
        this.handleCancel();
      });
    },
    handleRidaoChange(row) {
      row.xianShiFSMC = xianShiFSOption.find(
        (item) => item.value == row.xianShiFSDM,
      ).label;
    },
    handleCancel() {
      this.tableData = cloneDeep(this.originData);
      this.tableData.forEach((res) => {
        res.qiYongBZ = Boolean(res.qiYongBZ);
      });
    },
    handleDelete(index) {
      this.tableData.splice(index, 1);
      this.tableData.forEach((item, index) => {
        item.shunXuHao = index + 1;
      });
    },
    handleSure() {
      const { contrast } = useParams(this.originData);
      this.tableData.splice(this.tableData.length - 1, 1);

      let newTable = this.tableData.map((item) => {
        let obj = { ...item };
        obj.qiYongBZ = obj.qiYongBZ ? 1 : 0;
        return obj;
      });
      const { addArr, delArr, putArr } = contrast(newTable);
      const params = {
        addListDto: addArr,
        updateListDto: putArr,
        delListDto: delArr,
      };
      this.loading = true;
      SaveZhiBiao(params)
        .then((res) => {
          this.initData();
          MdMessage.success('保存成功！');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    newRow() {
      return {
        id: null,
        zhiBiaoID: null,
        zhiBiaoMC: null,
        danWei: null,
        xianShiFSDM: this.xianShiFSOption[0].value,
        xianShiFSMC: this.xianShiFSOption[0].label,
        shunXuHao: this.tableData.length + 1,
        qiYongBZ: false,
      };
    },
    /**
     * 拖拽样式
     * @param {行} param0
     */
    rowClassName({ row, rowIndex }) {
      if (rowIndex !== this.tableData.length - 1) return 'drag-row';
    },
    //拖拽排序
    async initSortable() {
      const el = this.$refs.editTable.$el.querySelectorAll(
        `.${process.env.VUE_APP_NAMESPACE}-base-table__body-wrapper table tbody`,
      )[0];
      this.sortable = Sortable.create(el, {
        sort: true, //是否可进行拖拽排序
        animation: 150,
        handle: `.${this.nsIcon.b('tuozhuai')}`.trim(),
        draggable: '.drag-row', // 允许拖拽的项目类名
        onEnd: (evt) => {
          const targetRow = this.tableData.splice(evt.oldIndex, 1)[0];
          this.tableData.splice(evt.newIndex, 0, targetRow);
          this.$nextTick(() => {
            this.tableData.forEach((item, index) => {
              item.shunXuHao = index + 1;
            });
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.HISYK-TPNZBWH {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-header {
    padding: getCssVar('spacing-3');
  }
  &-table {
    flex: 1;
    min-height: 0;
    padding: 0 getCssVar('spacing-3');
  }
  &-foot {
    display: flex;
    justify-content: flex-end;
    padding: getCssVar('spacing-3');
    background-color: getCssVar('color-1');
    .mr-l-8 {
      margin-left: getCssVar('spacing-3');
    }
  }
}
.#{$namespace}-icon-seach {
  width: 24px;
}
</style>
