<template>
  <md-dialog
    title="临时用药申请"
    v-model="visibleDialog"
    :before-save="handleSave"
    :body-loading="loading"
    width="1000px"
    height="540px"
    @close="closeModal"
  >
    <md-form
      v-loading="loading"
      :model="formModel"
      :rules="formRules"
      label-width="80px"
      use-status-icon
      ref="form"
    >
      <md-row>
        <md-col :span="12">
          <md-form-item label="申请单">
            <md-input v-model="formModel.shenQingDH" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="审批日期" prop="shenPiRQ">
            <md-date-picker
              v-model="formModel.shenPiRQ"
              placeholder="请选择日期"
              :class="prefixClass('margin-right-8')"
              :editable="false"
              :clearable="false"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="审核科室" prop="shenQingKSID">
            <md-select
              v-model="formModel.shenQingKSID"
              filterable
              placeholder="请选择"
              @change="handleKeshi"
            >
              <md-option
                v-for="item in moJiKSOptions"
                :key="item.keShiID"
                :label="item.keShiMC"
                :value="item.keShiID"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="申请人" prop="shenQingRID">
            <md-select-table
              v-model="shenQingRen"
              :fetchData="getShenQingRenList"
              :columns="shenQingRenColumns"
              labelKey="yongHuXM"
              valueKey="yongHuID"
              filterable
              placeholder="输入搜索"
              :immediateLoad="false"
              @change="handleShenQingRenChange"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="选择药品" prop="yaoPinMC">
            <div style="display: flex; width: 100%">
              <biz-yaopindw
                v-model="yaoPinMCYGG"
                :class="prefixClass('yaopin-select')"
                :linShiYYBZ="true"
                @change="handleTableYaoPinDWChange"
              >
              </biz-yaopindw>
              <md-checkbox
                v-model="formModel.jinJiCGBZ"
                :true-label="1"
                :false-label="0"
                style="margin-left: 8px; flex-shrink: 0"
                >紧急采购</md-checkbox
              >
            </div>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="产地">
            <md-input v-model="formModel.chanDiMC" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="单位">
            <md-input v-model="formModel.baoZhuangDW" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="进价">
            <md-input v-model="formModel.jinJia" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="审批数量" prop="shenPiSL">
            <md-input
              v-number
              v-model="formModel.shenPiSL"
              @change="handleShuLiang"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="进价金额">
            <md-input v-model="formModel.jinJiaJE" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="入库数量" prop="ruKuSL">
            <md-input
              v-number
              v-model="formModel.ruKuSL"
              @change="handleShuLiang"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="剩余数量">
            <md-input v-model="formModel.shengYuSL" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="24">
          <md-form-item label="备注">
            <md-input
              v-model="formModel.beiZhu"
              type="textarea"
              maxlength="200"
              show-word-limit
              rows="3"
            />
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="mode == 'edit'"
          :class="prefixClass('normal__button')"
          plain
          type="danger"
          style="float: left"
          @click="handleDelete"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="closeModal"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import dayjs from 'dayjs';

import { getMoJiKSList } from '@/service/gongYong/weiZhi.js';
import { getYongHuXX } from '@/service/gongYong/yongHuXX.js';
import {
  addLinShiYY,
  getLinShiYYXQ,
  updateLinShiYY,
  zuoFeiLinShiYY,
} from '@/service/yaoPin/LinShiYYGL.js';
import { getJiGouID } from '@/system/utils/local-cache';
import { MdMessageBox } from '@mdfe/medi-ui';
const formModelInit = () => {
  return {
    id: null,
    shenQingDH: null,
    shenQingKSID: '',
    shenQingKSMC: '',
    shenQingSJ: '',
    shenQingRID: '',
    shenQingRXM: '',
    jiaGeID: '',
    guiGeID: '',
    yaoPinMC: '',
    yaoPinGG: '',
    chanDiID: '',
    chanDiMC: '',
    baoZhuangDW: '',
    jinJia: '',
    fenLeiID: '',
    fenLeiMC: '',
    jinJiCGBZ: 0,
    shenPiRQ: '',
    shenPiSL: '',
    ruKuSL: 0,
    shengYuSL: '',
    jinJiaJE: '',
    beiZhu: '',
  };
};
export default {
  name: 'linshiyysqdialog',
  data() {
    return {
      formRules: {
        shenPiRQ: [
          { required: true, message: '请选择审批日期', trigger: 'change' },
        ],
        shenQingKSID: [
          { required: true, message: '请选择申请科室', trigger: 'change' },
        ],
        shenQingRID: [
          { required: true, message: '请选择申请人', trigger: 'change' },
        ],
        yaoPinMC: [
          { required: true, message: '请选择药品', trigger: 'change' },
        ],
        shenPiSL: [
          { required: true, message: '请输入审批数量', trigger: 'change' },
        ],
        ruKuSL: [
          { required: true, message: '请输入入库数量', trigger: 'change' },
        ],
      },
      mode: 'add',
      visibleDialog: false,
      loading: false,
      zhangBuLBOptions: [],
      duLiFLOptions: [],
      shenQingRenColumns: [
        {
          prop: 'yongHuDLM',
          label: '工号',
          minWidth: 100,
        },
        {
          prop: 'yongHuXM',
          label: '姓名',
          width: 100,
        },
      ],
      shenQingRen: {},
      yaoPinMCYGG: {},
      formModel: formModelInit(),
      resolve: null,
      reject: null,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      this.moJiKSOptions = await getMoJiKSList({
        qiYongBz: 1,
        likeQuery: '',
        zuZhiJGID: getJiGouID(),
        pageSize: 999,
        pageIndex: 1,
      });
    },
    async getShenQingRenList({ page, pageSize, inputValue }) {
      try {
        const res = await getYongHuXX({
          zuZhiJGID: getJiGouID(),
          pageSize,
          pageIndex: page,
          isSearchSub: false,
          qiYongBz: 1,
          name: inputValue,
        });
        return res;
      } catch (e) {}
    },
    /**
     * 选择药品change事件
     */
    async handleTableYaoPinDWChange(data) {
      const params = {
        jiaGeID: data.jiaGeID,
        yaoPinMC: data.yaoPinMC,
        yaoPinGG: data.yaoPinGG,
        chanDiMC: data.chanDiMC,
        chanDiID: data.chanDiID,
        baoZhuangDW: data.baoZhuangDW,
        guiGeID: data.guiGeID,
        fenLeiID: data.fenLeiID,
        fenLeiMC: data.fenLeiMC,
        jinJia: data.jinJia,
      };
      Object.assign(this.formModel, params);
      this.formModel.shengYuSL = 0;
      this.formModel.shenPiSL = 0;
      this.formModel.ruKuSL = 0;
      this.formModel.jinJiaJE = 0;
    },
    async showModal(val) {
      this.visibleDialog = true;
      await this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
      this.mode = val.mode;
      if (val.mode == 'edit') {
        const res = await getLinShiYYXQ({ id: val.data.id });
        if (res) {
          this.shenQingRen = {
            yongHuID: res.shenQingRID,
            yongHuDLM: res.shenQingRID,
            yongHuXM: res.shenQingRXM,
          };
          this.yaoPinMCYGG = {
            jiaGeID: res.jiaGeID,
            yaoPinMC: res.yaoPinMC,
            yaoPinGG: res.yaoPinGG,
            chanDiMC: res.chanDiMC,
            chanDiID: res.chanDiID,
            baoZhuangDW: res.baoZhuangDW,
            fenLeiID: res.fenLeiID,
            fenLeiMC: res.fenLeiMC,
            jinJia: res.jinJia,
          };
          Object.assign(this.formModel, res);
        }
      }
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeModal() {
      this.$refs.form.resetFields();
      this.visibleDialog = false;
      this.formModel = formModelInit();
      this.shenQingRen = {};
      this.yaoPinMCYGG = {};
    },
    handleKeshi(val) {
      this.formModel.shenQingKSMC =
        this.moJiKSOptions.find((item) => item.keShiID === val)?.keShiMC || '';
    },
    handleShenQingRenChange(val) {
      this.formModel.shenQingRID = val.yongHuID;
      this.formModel.shenQingRXM = val.yongHuXM;
      this.handleShuLiang();
    },
    handleShuLiang() {
      if (
        this.formModel.jinJiCGBZ !== 1 &&
        Number(this.formModel.ruKuSL) > Number(this.formModel.shenPiSL)
      ) {
        this.$message({
          type: 'warning',
          message: '入库数量大于申批数量',
        });
        this.formModel.ruKuSL = 0;
        this.formModel.shengYuSL =
          (this.formModel.shenPiSL || 0) - (this.formModel.ruKuSL || 0);
      } else {
        const calculatedSL =
          (this.formModel.shenPiSL || 0) - (this.formModel.ruKuSL || 0);
        this.formModel.shengYuSL = calculatedSL < 0 ? 0 : calculatedSL;
      }

      this.formModel.jinJiaJE = (
        (this.formModel.jinJia || 0) * (this.formModel.shenPiSL || 0)
      ).toFixed(2);
    },
    async handleDelete() {
      await MdMessageBox.confirm('确定作废此单据？', '操作提醒！', {
        cancelButtonText: '否',
        confirmButtonText: '是',
        type: 'warning',
      });
      if (this.formModel.ruKuSL > 0) {
        this.$message({
          type: 'error',
          message: '入库数量大于0，无法作废',
        });
        return;
      }
      await zuoFeiLinShiYY({ id: this.formModel.id });
      this.resolve('del');
      this.closeModal();
    },
    async handleSave() {
      const result = await this.$refs.form.validate();
      if (!result) return;
      this.loading = true;
      if (this.mode == 'edit') {
        this.formModel.shenQingSJ = dayjs().format('YYYY-MM-DD HH:mm:ss');
        updateLinShiYY(this.formModel)
          .then((res) => {
            this.resolve('edit');
            this.closeModal();
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.formModel.shenQingSJ = dayjs().format('YYYY-MM-DD HH:mm:ss');
        addLinShiYY(this.formModel)
          .then((res) => {
            this.resolve(res);
            this.closeModal();
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-yaopin-select {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
</style>
