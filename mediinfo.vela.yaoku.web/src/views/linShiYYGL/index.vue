<template>
  <div :class="prefixClass('linShiYYGL-layout')">
    <div :class="prefixClass('linShiYYGL-container')">
      <div :class="prefixClass('linShiYYGL-top')">
        <div :class="prefixClass('linShiYYGL-top-search')">
          <label style="margin-right: 8px"> 审批日期</label>
          <md-date-picker-range-pro
            v-model="shenPiRQ"
            style="margin-right: 8px; width: 250px"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          >
          </md-date-picker-range-pro>
          <label style="margin-right: 8px"> 申请科室</label>

          <md-select
            v-model="shenQingKSID"
            filterable
            placeholder="请选择"
            :class="prefixClass('linShiYYGL__select')"
            @change="handleSearch"
          >
            <md-option
              v-for="item in moJiKSOptions"
              :key="item.keShiID"
              :label="item.keShiMC"
              :value="item.keShiID"
            >
            </md-option>
          </md-select>
          <label style="margin-right: 8px"> 申请人</label>

          <md-select-table
            v-model="shenQingRID"
            :fetchData="getShenQingRenList"
            :columns="shenQingRenColumns"
            labelKey="yongHuXM"
            valueKey="yongHuID"
            filterable
            placeholder="输入搜索"
            :class="prefixClass('linShiYYGL__select')"
            @change="handleSearch"
          />
          <biz-yaopindw
            v-model="yaoPinMC"
            :class="prefixClass(['yaopin-select', 'linShiYYGL__input'])"
            placeholder="药品名称搜索"
            @change="handleTableYaoPinDWChange"
          >
          </biz-yaopindw>
          <md-checkbox
            v-model="jinJiCGBZ"
            style="margin-left: 8px; flex-shrink: 0"
            @change="handleSearch"
            >仅显示紧急采购</md-checkbox
          >
        </div>
        <div class="linShiYYGL-top-button">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            style="margin-right: 8px"
            @click="handleSearch"
            >刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            style="margin-right: 8px"
            @click="handleYuLan"
            >预览</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-jia')"
            @click="handleAdd"
            >新增</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('linShiYYGL-body')">
        <div :class="prefixClass('linShiYYGL__table')">
          <md-table-pro
            :columns="columns"
            :onFetch="handleFetch"
            height="100%"
            autoLoad
            resize
            stripe
            ref="table"
          >
            <template v-slot:jinJiCGBZ="{ row }">
              <div style="text-align: center">
                <i
                  v-if="row.jinJiCGBZ"
                  class="iconfont icongou"
                  style="color: #1e88e5"
                />
              </div>
            </template>

            <template #operate="{ row }">
              <md-button type="text-bg" @click="handleEdit(row)"
                >编辑</md-button
              >
            </template>
          </md-table-pro>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT023'"
        :fileName="'采购计划单'"
        :title="'打印预览'"
      />
      <LinShiYYSQDialog ref="LinShiYYSQDialog" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import DaYinDialog from '@/components/DaYinDialog.vue';
import { getJiGouID } from '@/system/utils/local-cache';
import { getYongHuXX } from '@/service/gongYong/yongHuXX.js';
import { getMoJiKSList } from '@/service/gongYong/weiZhi.js';
import {
  getLinShiYYList,
  getLinShiYYCount,
} from '@/service/yaoPin/LinShiYYGL.js';
import LinShiYYSQDialog from './components/LinShiYYSQDialog';

export default {
  name: 'linShiYYGL',
  data() {
    return {
      shenPiRQ: [
        dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      yaoPinMC: {}, //搜索内容
      jiaGeID: '',
      total: 0,
      shunXuHao: '',
      jinJiCGBZ: false,
      shenQingKSID: '',
      shenQingRID: '',
      moJiKSOptions: [],
      params: {},
      shenQingRenColumns: [
        {
          prop: 'yongHuDLM',
          label: '工号',
          minWidth: 100,
        },
        {
          prop: 'yongHuXM',
          label: '姓名',
          width: 100,
        },
      ],
      //表格属性
      columns: [
        {
          prop: 'shenQingDH',
          label: '申请单',
          width: 140,
        },
        {
          prop: 'shenPiRQ',
          label: '审批日期',
          width: 120,
          formatter(row) {
            return row.shenPiRQ ? dayjs(row.shenPiRQ).format('YYYY-MM-DD') : '';
          },
        },
        {
          slot: 'jinJiCGBZ',
          prop: 'jinJiCGBZ',
          label: '紧急采购',
          width: 100,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 300,
          formatter: (row) => {
            if (row.yaoPinMC && row.yaoPinGG) {
              return row.yaoPinMC + ' ' + row.yaoPinGG;
            }
            return '';
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地',
          minWidth: 240,
          type: 'text',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          minWidth: 120,
        },
        {
          prop: 'shenPiSL',
          label: '审批数量',
          width: 120,
          align: 'right',
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          width: 120,
          align: 'right',
        },
        {
          prop: 'shengYuSL',
          label: '剩余数量',
          width: 120,
          align: 'right',
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 120,
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 140,
          align: 'right',
        },
        {
          prop: 'shenQingKSMC',
          label: '申请科室',
          width: 140,
        },
        {
          prop: 'shenQingRXM',
          label: '申请人',
          width: 140,
        },
        {
          prop: 'fenLeiMC',
          label: '药理分类',
          width: 140,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 80,
          fixed: 'right',
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    /**
     * 选择药品change事件
     */
    async handleTableYaoPinDWChange(data) {
      this.jiaGeID = data.jiaGeID;
      this.handleSearch();
    },
    async getData() {
      this.moJiKSOptions = await getMoJiKSList({
        qiYongBz: 1,
        likeQuery: '',
        zuZhiJGID: getJiGouID(),
        pageSize: 999,
        pageIndex: 1,
      });
    },
    async getShenQingRenList({ page, pageSize, inputValue }) {
      try {
        const res = await getYongHuXX({
          zuZhiJGID: getJiGouID(),
          pageSize,
          pageIndex: page,
          isSearchSub: false,
          qiYongBz: 1,
          name: inputValue,
        });
        return res;
      } catch (e) {}
    },

    //获取医疗组列表
    async handleFetch({ page, pageSize }, config) {
      let kaiShiSJ = this.shenPiRQ[0]
        ? dayjs(this.shenPiRQ[0]).format('YYYY-MM-DD') + ' 00:00:00'
        : '';
      let jieShuSJ = this.shenPiRQ[1]
        ? dayjs(this.shenPiRQ[1]).format('YYYY-MM-DD') + ' 23:59:59'
        : '';
      let shenQingRID = this.shenQingRID ? this.shenQingRID.yongHuID : '';
      const params = {
        kaiShiSJ,
        jieShuSJ,
        shenQingKSID: this.shenQingKSID,
        shenQingRID,
        jiaGeID: this.jiaGeID,
        jinJiCGBZ: this.jinJiCGBZ ? 1 : null,
        pageSize,
        pageIndex: page,
      };
      this.params = params;
      const [items, total] = await Promise.all([
        getLinShiYYList(params, config),
        !this.total && getLinShiYYCount(params, config),
      ]);

      this.total = Number(total) || this.total;

      // this.shunXuHao = items[0]?.shunXuHao ?? 0
      return { items, total: this.total };
    },

    handleSearch() {
      this.total = 0;
      this.$refs.table.search();
    },
    //预览
    async handleYuLan() {
      // const params = {
      //   id: ’,
      // };
      this.$refs.daYinDialog.showModal();
    },
    // 显示新增弹窗
    async handleAdd() {
      await this.$refs.LinShiYYSQDialog.showModal({
        mode: 'new',
        shunXuHao: this.shunXuHao,
      });
      this.$message.success('新增成功！');
      this.handleSearch('new');
    },

    // 显示编辑弹窗
    async handleEdit(row) {
      let res = await this.$refs.LinShiYYSQDialog.showModal({
        mode: 'edit',
        data: row,
      });
      if (res == 'edit') {
        this.$message.success('编辑成功！');
      } else {
        this.$message.success('作废成功！');
      }
      this.handleSearch('edit');
    },

    // 作废一行数据
    handleDel(row) {
      MdMessageBox.confirm(`确定作废${row.yiLiaoZMC}?`, '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            // await getYiLiaoZuDel(row.id)
            this.handleSearch('del');
            MdMessage({
              type: 'success',
              message: '作废成功',
            });
          } catch (error) {
            MdMessage.error(error.message);
          }
        })
        .catch(() => {
          MdMessage({
            type: 'info',
            message: '已取消作废',
          });
        });
    },
  },

  components: {
    LinShiYYSQDialog,
    'biz-yaopindw': BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-linShiYYGL-layout {
  display: flex;
  flex: 1;
  padding: 8px;
  min-height: 0;
  background: #f0f2f5;
}

.#{$md-prefix}-linShiYYGL-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 8px;
  background-color: #fff;

  .#{$md-prefix}-linShiYYGL-top {
    display: flex;
    height: 30px;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-right: 8px;
    &-search {
      display: flex;
      align-items: center;
    }
    .#{$md-prefix}-linShiYYGL__select {
      width: 160px;
      height: 30px;
      margin-right: 8px;
    }
    .#{$md-prefix}-linShiYYGL__input {
      width: 180px;
      height: 30px;
    }

    ::v-deep .#{$md-prefix}-input__inner {
      height: 30px;
      line-height: 30px;
    }
  }
  .#{$md-prefix}-linShiYYGL-body {
    display: flex;
    flex: 1;
    min-height: 0;
    padding-right: 8px;
    .#{$md-prefix}-linShiYYGL__table {
      flex: 1;
      width: 100%;
      min-height: 0;

      ::v-deep .table-wrapper {
        min-height: 0;
      }
      ::v-deep .#{$md-prefix}-tooltip {
        max-height: unset;
      }
      .#{$md-prefix}-linShiYYGL__tagsDiv {
        display: flex;
        justify-content: flex-start;
        align-content: space-around;
        flex-wrap: wrap;
        height: 100%;
        margin-bottom: -4px;
        box-sizing: border-box;

        .#{$md-prefix}-linShiYYGL__tableTags {
          height: 22px;
          line-height: 22px;
          min-width: 76px;
          padding-left: 8px;
          padding-right: 12px;
          margin-right: 4px;
          margin-bottom: 4px;
          // background-color: #e6f3ff;
          background-color: rgb(var(--md-color-1));
          border-radius: 2px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
