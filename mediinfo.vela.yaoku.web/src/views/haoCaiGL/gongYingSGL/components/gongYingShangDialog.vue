<template>
  <md-dialog
    title="供应商"
    v-model="dialogVisible"
    width="800px"
    height="450px"
  >
    <div :class="prefixClass('dialog-header')">
      <md-form
        :model="formData"
        :rules="formRules"
        label-width="100px"
        :class="prefixClass('dialog-header__form')"
        ref="form"
      >
        <md-form-item label="单位ID" prop="danWeiID">
          <md-input v-model="formData.danWeiID" placeholder="" disabled />
        </md-form-item>
        <md-form-item label="单位名称" prop="danWeiMC">
          <md-select v-model="formData.danWeiLB" placeholder="请输入选择">
          </md-select>
        </md-form-item>
        <md-form-item label="简称" prop="jianCheng">
          <md-input v-model="formData.jianCheng" placeholder="" />
        </md-form-item>
        <md-form-item label="单位类别" prop="danWeiLB">
          <md-select v-model="formData.danWeiLB" placeholder="请选择">
            <md-option
              v-for="item in danWeiLBFSOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
        </md-form-item>
        <md-form-item label="单位类型" prop="danWeiLX" style="width: 100%">
          <md-checkbox-group
            v-model="danWeiLX"
            @change="handleCheckedCitiesChange"
          >
            <md-checkbox
              v-for="item in danWeiLXData"
              :label="item.label"
              :key="item.label"
            >
              {{ item.value }}
            </md-checkbox>
            <!-- <md-checkbox label="0">
              供应商
            </md-checkbox>
            <md-checkbox label="1">

            </md-checkbox> -->
          </md-checkbox-group>
        </md-form-item>
        <md-form-item label="税号" prop="shuiHao" style="width: 100%">
          <md-input v-model="formData.shuiHao" placeholder="请输入税号" />
        </md-form-item>
        <md-form-item label="开户银行" prop="kaiHuYH">
          <md-input v-model="formData.kaiHuYH" placeholder="请输入开户银行" />
        </md-form-item>
        <md-form-item label="银行账号" prop="yinHangZH">
          <md-input v-model="formData.yinHangZH" placeholder="请输入银行账号" />
        </md-form-item>
        <md-form-item label="联系人" prop="lianXiRen">
          <md-input v-model="formData.lianXiRen" placeholder="请输入联系人" />
        </md-form-item>
        <md-form-item label="联系方式" prop="lianXiFS">
          <md-input v-model="formData.lianXiFS" placeholder="请输入联系方式" />
        </md-form-item>
        <md-form-item label="单位地址" prop="danWeiDZ">
          <!--          //TODO-->
          <bmis-address-select
            v-model="formData.danWeiDZ"
            :data="setData"
            ref="addressSelect"
          />
        </md-form-item>
        <md-form-item prop="xiangXiDZ" label-width="8px">
          <md-input v-model="formData.xiangXiDZ" placeholder="请输入详细地址" />
        </md-form-item>
        <md-form-item label="拼音码" prop="shuRuMa1">
          <md-input v-model="formData.shuRuMa1" placeholder="请输入" />
        </md-form-item>
        <md-form-item label="五笔码" prop="shuRuMa2">
          <md-input v-model="formData.shuRuMa2" placeholder="请输入" />
        </md-form-item>
        <md-form-item label="自定义码" prop="shuRuMa3">
          <md-input v-model="formData.shuRuMa3" placeholder="请输入" />
        </md-form-item>
      </md-form>
    </div>
    <template slot="footer">
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="isEdit"
          :loading="saveLoading"
          type="danger"
          plain
          style="float: left"
          @click="handleDelete"
        >
          作废
        </md-button>
        <md-button type="primary" plain @click="handleClose">取消</md-button>
        <md-button type="primary" :loading="saveLoading" @click="handleSave">
          保存
        </md-button>
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { MdMessageBox, MdMessage } from '@mdfe/medi-ui';
import { cloneDeep, isEqual } from 'lodash';
//TODO
import AddressSelect from '@/components/address-select/index.vue';
import { getShengShiQList } from '@/service/yaoPinYK/gongHuoDW';
import { makePY, makeWb } from '@/system/utils/wubi-pinyin.js';
import { logger } from '@/service/log';
export default {
  name: 'gongyingshang-dialog',
  data() {
    return {
      danWeiLX: [],
      isEdit: false,
      resolve: null,
      reject: null,
      dialogVisible: false, //控制弹框隐藏显示
      dialogMode: '', //弹框类型，new和edit
      saveLoading: false,
      setData: [],
      isGongYingShang: false,
      isShengChanCS: false,
      danWeiLXData: [
        { label: 0, value: '供应商' },
        { label: 1, value: '生产厂商' },
      ],
      //供应商数据
      formData: {
        danWeiID: '',
        shuiHao: '',
        danWeiLB: '',
        danWeiLX: [],
        yinHangZH: '',
        lianXiRen: '',
        lianXiFS: '',
        xiangXiDZ: '',
        danWeiDZ: [],
        danWeiMC: '', //单位名称
        shunXuHao: '', //顺序号
        shuRuMa1: '', //拼音码
        shuRuMa2: '', //五笔码
        shuRuMa3: '', //自定义码
      },
      danWeiLBFSOptions: [
        {
          label: '全部单位类型',
          value: '',
        },
        {
          label: '国产',
          value: '1',
        },
        {
          label: '进口',
          value: '2',
        },
      ],
      oldFormData: {},

      //医疗组表单校验
      formRules: {
        danWeiID: [
          { required: true, message: '请输入单位ID', trigger: 'blur' },
        ],
        danWeiMC: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
        ],
        kaiHuYH: [
          { required: true, message: '请输入开户银行', trigger: 'blur' },
        ],
        yinHangZH: [
          { required: true, message: '请输入银行账号', trigger: 'blur' },
        ],
        danWeiDZ: [
          { required: true, message: '请选择单位地址', trigger: 'blur' },
        ],

        // shunXuHao: [
        //   { required: true, message: '请输入顺序号', trigger: 'blur' },
        //   { type: 'number', message: '请输入数字', trigger: 'blur' }
        // ]
      },
    };
  },
  computed: {
    // ...mapGetters('userStore', {
    //   ZuZhiJGID: 'getJiGouID'
    // })
  },
  mounted() {
    this.getShengShiQList();
  },

  methods: {
    //获取省市区List
    async getShengShiQList() {
      this.setData = await getShengShiQList();
    },
    //单位名称转拼音码和五笔码
    handleToPyWb() {
      this.formData.shuRuMa1 = makePY(this.formData.danWeiMC);
      this.formData.shuRuMa2 = makeWb(this.formData.danWeiMC);
    },
    handleShunXuHao(val) {
      if (Number(val) || Number(val) === 0) {
        this.formData.shunXuHao = Number(val);
      }
    },

    // 显示隐藏弹窗
    async showModal(options = {}) {
      this.dialogVisible = true;

      this.danWeiID = null;
      this.dialogMode = options.mode;

      //编辑弹窗，获取所有数据
      if (options.mode == 'edit') {
        this.isEdit = true;
        const data = options.data;
        try {
          // const res = await getYiLiaoZuById(data.id)
          // this.$set(this.formData, 'danWeiID', data.id)
          this.formData = {
            danWeiID: data.danWeiID,
            danWeiMC: data.danWeiMC,
            shuiHao: data.shuiHao,
            kaiHuYH: data.kaiHuYH,
            yinHangZH: data.yinHangZH,
            lianXiRen: data.lianXiRen,
            lianXiFS: data.lianXiFS,
            xiangXiDZ: data.danWeiDZ,
            // shunXuHao: data.shunXuHao
            // shuRuMa1: res.shuRuMa1,
            // shuRuMa2: res.shuRuMa2,
            // shuRuMa3: res.shuRuMa3
          };
          this.handleToPyWb();

          this.oldFormData = cloneDeep(this.formData);
        } catch (error) {
          logger.error(error);
        }
      } else if (options.mode == 'new') {
        //添加弹窗，初始化数据
        this.formData = {
          danWeiID: null,
          danWeiMC: null,
          // shunXuHao: options.shunXuHao + 1,
          shuRuMa1: null,
          shuRuMa2: null,
          shuRuMa3: null,
        };
        this.oldFormData = cloneDeep(this.formData);
      }

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    handleCheckedCitiesChange() {},
    //保存关闭弹窗
    async handleSave() {
      try {
        const result = await this.$refs.form.validate();
        if (!result) return;
      } catch (error) {
        logger.error(error);
      }

      //新增弹窗，获取数据并调用新增接口
      if (this.dialogMode == 'new') {
        const confirmRes = await MdMessageBox.confirm('确认保存？', '提示', {
          confirmButtonText: '保存',
          cancelButtonText: '取消',
          type: 'warning',
        }).catch(() => {
          this.reject();
        });
        if (confirmRes === 'confirm') {
          const data = {
            danWeiMC: this.formData.danWeiMC,
            // shunXuHao: this.formData.shunXuHao,
            // shuRuMa1: this.formData.shuRuMa1,
            // shuRuMa2: this.formData.shuRuMa2,
            // shuRuMa3: this.formData.shuRuMa3
          };

          try {
            this.saveLoading = true;
            this.resolve();
            this.dialogVisible = false;
            this.saveLoading = false;
            let messageTitle = this.dialogMode === 'new' ? '新增' : '修改';
            MdMessage.success(`${messageTitle}成功!`);
          } catch (error) {
            // Message.error(error.message)
            this.reject();
            this.saveLoading = false;
          }
        }
      }

      //编辑弹窗，获取数据并调用更新接口
      if (this.dialogMode == 'edit') {
        //判断是够修改过数据
        let hasChange = false;
        if (!isEqual(this.oldFormData, this.formData)) {
          hasChange = true;
        }

        if (hasChange) {
          const confirmRes = await MdMessageBox.confirm('确认保存？', '提示', {
            confirmButtonText: '保存',
            cancelButtonText: '取消',
            type: 'warning',
          }).catch(() => {
            this.reject();
          });
          if (confirmRes === 'confirm') {
            const data = {
              // id: this.danWeiID,
              // danWeiMC: this.formData.danWeiMC,
              // shunXuHao: this.formData.shunXuHao,
              // shuRuMa1: this.formData.shuRuMa1,
              // shuRuMa2: this.formData.shuRuMa2,
              // shuRuMa3: this.formData.shuRuMa3
            };
            try {
              this.saveLoading = true;
              // await getYiLiaoZuUpdate(data)
              this.resolve();
              this.dialogVisible = false;
              this.saveLoading = false;
              let messageTitle = this.dialogMode === 'new' ? '新增' : '修改';
              MdMessage.success(`${messageTitle}成功!`);
            } catch (error) {
              // Message.error(error.message)
              this.saveLoading = false;
            }
          }
        } else {
          this.dialogVisible = false;
        }
      }
    },
    //作废
    async handleDelete() {
      MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(async () => {
          // const result = await zuoFeiYWPCList(this.formData.data.daiMa)
          if (result) {
            MdMessage.success('作废成功');
            this.dialogVisible = false;
            // this.refresh()
            this.resolve({ mode: 'delete' });
          } else {
            MdMessage.error('作废失败');
          }
        })
        .catch((action) => {
          MdMessage.warning('已取消作废');
        });
    },
    //取消关闭弹窗
    handleClose() {
      //是否修改过数据
      let hasChange = false;
      if (!isEqual(this.oldFormData, this.formData)) {
        hasChange = true;
      }

      if (hasChange) {
        MdMessageBox.confirm('数据有修改，确认取消？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.dialogVisible = false;
            this.$refs.form.resetFields();
          })
          .catch((e) => {
            if (e) return;
          });
      } else {
        this.dialogVisible = false;
        this.$refs.form.resetFields();
      }
    },
  },
  components: {
    //TODO
    'bmis-address-select': AddressSelect,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-dialog-header {
  margin-top: 8px;
  &__form {
    display: flex;
    flex-wrap: wrap;
    margin-right: 40px;
    .#{$md-prefix}-form-item {
      width: 50%;
      .#{$md-prefix}-form-row {
        display: flex;
      }
    }
  }
}
.#{$md-prefix}-address-select {
  width: 100%;
}
</style>
