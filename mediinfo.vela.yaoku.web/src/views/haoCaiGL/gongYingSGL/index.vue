<template>
  <div :class="prefixClass('gongyingshang-layout')">
    <div :class="prefixClass('gongyingshang-container')">
      <div :class="prefixClass('gongyingshang-top')">
        <div :class="prefixClass('gongyingshang-top-search')">
          <md-select
            v-model="danWeiLX"
            placeholder="请选择"
            :class="prefixClass('gongyingshang__select')"
          >
            <md-option
              v-for="item in danWeiLXFSOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <md-input
            v-model="searchInput"
            placeholder="请输入单位编码或单位名称检索"
            :class="prefixClass('gongyingshang__input')"
            @keyup.enter.native="handleSearch"
          >
            <i
              slot="suffix"
              :class="prefixClass(['input__icon', 'icon-seach'])"
              @click="handleSearch"
            ></i>
          </md-input>
          <md-checkbox
            v-model="isTingYong"
            style="margin-left: 8px; flex-shrink: 0"
            @change="handleSearch"
            >显示停用</md-checkbox
          >
        </div>
        <div class="gongyingshang-top-button">
          <md-button
            style="margin-right: 8px; padding: 8px"
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
            >刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-jia')"
            @click="handleAdd"
            >新增</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('gongyingshang-body')">
        <div :class="prefixClass('gongyingshang__table')">
          <md-table-pro
            :columns="columns"
            :onFetch="handleFetch"
            height="100%"
            autoLoad
            resize
            stripe
            ref="table"
          >
            <template #danWeiLX="{ row }">
              <md-tag style="margin-right: 8px">供应商</md-tag>
              <md-tag type="warning">生成厂商</md-tag>
            </template>
            <!-- 启用 -->
            <template #qiYong="{ row }">
              <md-switch
                v-model="row.qiYongBZ"
                :true-value="1"
                :false-value="0"
                size="small"
                @change="handleQiYong(row)"
              ></md-switch>
            </template>
            <template #operate="{ row }">
              <md-button type="text-bg" @click="handleEdit(row)"
                >编辑</md-button
              >
            </template>
          </md-table-pro>
        </div>
      </div>

      <gongyingshang-dialog ref="gongYingShangDialog" />
    </div>
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import gongYingShangDialog from './components/gongYingShangDialog';

// import { getYiLiaoZuList, getYiLiaoZuCount, getYiLiaoZuDel } from '@/service/zuZhiJG/yiLiaoZu'

export default {
  name: 'gongyingshang',
  data() {
    return {
      searchInput: '', //搜索内容
      total: 0,
      shunXuHao: '',
      isTingYong: true,
      danWeiLX: '',
      danWeiLXFSOptions: [
        {
          label: '全部单位类型',
          value: '',
        },
        {
          label: '国产',
          value: '1',
        },
        {
          label: '进口',
          value: '2',
        },
      ],
      //表格属性
      columns: [
        {
          prop: 'danWeiID',
          label: '单位ID',
          width: 88,
        },
        {
          prop: 'danWeiMC',
          label: '单位名称',
          minWidth: 236,
        },
        {
          prop: 'jianCheng',
          label: '简称',
          width: 120,
        },
        {
          prop: 'danWeiLB',
          label: '单位类别',
          width: 120,
        },
        {
          slot: 'danWeiLX',
          label: '单位类型',
          width: 186,
        },
        {
          prop: 'sheHuiTYXYDM',
          label: '社会统一信用代码',
          width: 236,
        },
        {
          prop: 'lianXiRen',
          label: '联系人',
          width: 140,
        },
        {
          prop: 'lianXiFS',
          label: '联系方式',
          width: 188,
        },
        {
          label: '启用',
          slot: 'qiYong',
          width: 80,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 80,
        },
      ],
    };
  },

  methods: {
    async tableRefresh(type) {
      if (type == 'new') {
        this.total = 0;
        await this.$refs.table.search();
      } else if (type == 'edit') {
        await this.$refs.table.refresh();
      } else {
        this.total = 0;
        await this.$refs.table.refresh();
      }
    },

    //获取医疗组列表
    async handleFetch({ page, pageSize }, config) {
      // const params = {
      //   yiLiaoZMC: this.searchInput,
      //   pageIndex: page,
      //   pageSize: pageSize
      // }
      // const params2 = {
      //   yiLiaoZMC: this.searchInput
      // }

      // const [items, total] = await Promise.all([
      //   getYiLiaoZuList(params, config),
      //   !this.total && getYiLiaoZuCount(params2, config)
      // ])
      let items = [
        {
          danWeiID: '000920',
          danWeiMC: '吉林省日成医用电子器材有限公司',
          sheHuiTYXYDM: '354738293738291910',
          jianCheng: '日成医用',
          danWeiLB: '国产',
          lianXiRen: '李书俊',
          lianXiFS: '18288888888',
          qiYongBZ: 1,
        },
        {
          danWeiID: '000921',
          danWeiMC: '浙江省振德医疗股份有限公司',
          sheHuiTYXYDM: '354738293738291912',
          jianCheng: '振德医疗',
          danWeiLB: '进口',
          lianXiRen: '欧阳昊昊',
          lianXiFS: '18888886666',
          qiYongBZ: 0,
        },
      ];
      // this.total = Number(total) || this.total
      this.total = 2;
      // this.shunXuHao = items[0]?.shunXuHao ?? 0
      return { items, total: this.total };
    },

    handleSearch() {
      this.total = 0;
      this.$refs.table.search();
    },

    handleQiYong(row) {},

    // 显示新增弹窗
    async handleAdd() {
      await this.$refs.gongYingShangDialog.showModal({
        mode: 'new',
        shunXuHao: this.shunXuHao,
      });
      this.tableRefresh('new');
    },

    // 显示编辑弹窗
    async handleEdit(row) {
      await this.$refs.gongYingShangDialog.showModal({
        mode: 'edit',
        data: row,
      });
      this.tableRefresh('edit');
    },

    // 作废一行数据
    handleDel(row) {
      MdMessageBox.confirm(`确定作废${row.yiLiaoZMC}?`, '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            // await getYiLiaoZuDel(row.id)
            this.tableRefresh('del');
            MdMessage({
              type: 'success',
              message: '作废成功',
            });
          } catch (error) {
            MdMessage.error(error.message);
          }
        })
        .catch(() => {
          MdMessage({
            type: 'info',
            message: '已取消作废',
          });
        });
    },
  },

  components: {
    'gongyingshang-dialog': gongYingShangDialog,
    'md-table-pro': MdTablePro,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-gongyingshang-layout {
  display: flex;
  flex: 1;
  padding: 8px;
  min-height: 0;
  background: #f0f2f5;
}

.#{$md-prefix}-gongyingshang-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 8px;
  background-color: #fff;

  .#{$md-prefix}-gongyingshang-top {
    display: flex;
    height: 30px;
    justify-content: space-between;
    margin-bottom: 8px;
    &-search {
      display: flex;
    }
    .#{$md-prefix}-gongyingshang__select {
      width: 160px;
      height: 30px;
      margin-right: 8px;
    }
    .#{$md-prefix}-gongyingshang__input {
      width: 300px;
      height: 30px;
    }

    ::v-deep .#{$md-prefix}-input__inner {
      height: 30px;
      line-height: 30px;
    }
  }
  .#{$md-prefix}-gongyingshang-body {
    display: flex;
    flex: 1;
    width: 100%;
    min-height: 0;

    .#{$md-prefix}-gongyingshang__table {
      flex: 1;
      width: 100%;
      min-height: 0;

      ::v-deep .table-wrapper {
        min-height: 0;
      }
      ::v-deep .#{$md-prefix}-tooltip {
        max-height: unset;
      }
      .#{$md-prefix}-gongyingshang__tagsDiv {
        display: flex;
        justify-content: flex-start;
        align-content: space-around;
        flex-wrap: wrap;
        height: 100%;
        margin-bottom: -4px;
        box-sizing: border-box;

        .#{$md-prefix}-gongyingshang__tableTags {
          height: 22px;
          line-height: 22px;
          min-width: 76px;
          padding-left: 8px;
          padding-right: 12px;
          margin-right: 4px;
          margin-bottom: 4px;
          // background-color: #e6f3ff;
          background-color: rgb(var(--md-color-1));
          border-radius: 2px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
