<template>
  <md-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    title="新增处方控制"
    width="1200"
    height="600"
    :custom-class="prefixClass('zhongyaocf-dialog')"
  >
    <div
      v-loading="dialogLoading"
      :class="prefixClass('zhongyaocf-dialog-content')"
    >
      <div :class="prefixClass('zhongyaocf-dialog-content-left')">
        <md-form
          :model="formModel"
          :rules="formRules"
          label-width="76px"
          class="demo-ruleForm"
          :size="formSize"
          use-status-tooltip-icon
          ref="form"
        >
          <md-form-item label="控制名称" prop="kongZhiMC">
            <md-input v-model="formModel.kongZhiMC" />
          </md-form-item>
          <md-form-item label="限制等级" prop="xianZhiDJDM">
            <md-select
              v-model="formModel.xianZhiDJDM"
              placeholder="请选择"
              @change="
                handeSelect($event, formModel, 'xianZhiDJMC', xianZhiDJOptions)
              "
            >
              <md-option
                v-for="item in xianZhiDJOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-form>
        <div>
          <md-title label="控制条件" type="grace" />
          <md-button
            type="primary"
            :icon="prefixClass('icon-xinzeng')"
            noneBg
            @click="handleAddTiaoJianZu"
            >条件组</md-button
          >
        </div>
        <div>
          <div
            v-for="(item1, index1) in tiaoJianZuList"
            :key="index1"
            :class="prefixClass('zhongyaocf-tiaojianzu')"
          >
            <div
              v-for="(item2, index2) in item1.tiaoJianList"
              :key="index2"
              :class="[
                prefixClass('zhongyaocf-tiaojian-item'),
                index2 === item1.tiaoJianList.length - 1
                  ? prefixClass('zhongyaocf-tiaojian-item-last')
                  : '',
              ]"
            >
              <div>
                <md-select
                  v-model="item2.tiaoJianDM"
                  placeholder="请选择"
                  style="width: 168px; margin-right: 8px"
                  @change="
                    handeSelect($event, item2, 'tiaoJianMC', zhongCaoYaoOptions)
                  "
                >
                  <md-option
                    v-for="item in zhongCaoYaoOptions"
                    :key="item.biaoZhunDM"
                    :label="item.biaoZhunMC"
                    :value="item.biaoZhunDM"
                  >
                  </md-option>
                </md-select>
                <md-select
                  v-model="item2.kongZhiLXDM"
                  placeholder="请选择"
                  style="width: 100px; margin-right: 8px"
                  @change="
                    handeSelect($event, item2, 'kongZhiLXMC', kongZhiLXOptions)
                  "
                >
                  <md-option
                    v-for="item in kongZhiLXOptions"
                    :key="item.biaoZhunDM"
                    :label="item.biaoZhunMC"
                    :value="item.biaoZhunDM"
                  >
                  </md-option>
                </md-select>
                <md-select
                  v-model="item2.yunSuanFDM"
                  placeholder="请选择"
                  style="width: 140px; margin-right: 8px"
                  @change="
                    handeSelect($event, item2, 'yunSuanFMC', yunSuanFOptions)
                  "
                >
                  <md-option
                    v-for="item in yunSuanFOptions"
                    :key="item.biaoZhunDM"
                    :label="item.biaoZhunMC"
                    :value="item.biaoZhunDM"
                  >
                  </md-option>
                </md-select>
                <md-input
                  v-model="item2.xiangMuZMC"
                  placeholder="请输入"
                  style="width: 168px"
                >
                  <template #append>
                    <md-select
                      v-model="item2.danWeiDM"
                      placeholder="请选择"
                      style="width: 80px"
                      @change="
                        handeSelect($event, item2, 'danWeiMC', danWeiOptions)
                      "
                    >
                      <md-option
                        v-for="item in danWeiOptions"
                        :key="item.biaoZhunDM"
                        :label="item.biaoZhunMC"
                        :value="item.biaoZhunDM"
                      >
                      </md-option>
                    </md-select>
                  </template>
                </md-input>
              </div>
              <div>
                <md-button
                  type="primary"
                  :icon="prefixClass('icon-xinzeng')"
                  noneBg
                  @click="handleAddTiaoJian(item1.tiaoJianList)"
                ></md-button>
                <md-button
                  type="danger"
                  :icon="prefixClass('icon-shanchu')"
                  noneBg
                  @click="handleDelTiaoJian(item1.tiaoJianList, index2, index1)"
                ></md-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div :class="prefixClass('zhongyaocf-dialog-content-right')">
        <div>
          <md-title label="提醒内容" type="grace" />
          <div>
            <md-button type="primary" noneBg @click="handleShengChengTXNR"
              >生成</md-button
            >
            <md-tooltip
              popper-class="md-tooltip"
              effect="dark"
              content="根据规则自动生成提醒内容；若当前已填写提醒内容，点击后将覆盖内容。"
              placement="bottom"
            >
              <md-icon
                name="shuoming-s"
                :class="prefixClass('xianliangkz-dialog-icon')"
              ></md-icon>
            </md-tooltip>
          </div>
        </div>
        <div>
          <md-input
            v-model="formModel.tiXingNR"
            type="textarea"
            show-word-limit
            :maxlength="500"
            placeholder="请输入内容"
          >
          </md-input>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <md-button
          v-if="mode === 'edit'"
          :disabled="dialogLoading"
          type="danger"
          plain
          style="float: left"
          @click="handleDelete"
        >
          作废
        </md-button>
        <md-button
          type="primary"
          plain
          :disabled="dialogLoading"
          @click="handleCancle"
          >取消</md-button
        >
        <md-button type="primary" :loading="dialogLoading" @click="handleSave">
          确定
        </md-button>
      </span>
    </template>
  </md-dialog>
</template>

<script>
import { cloneDeep } from 'lodash';

import { MdMessageBox } from '@mdfe/medi-ui';

import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { getYiZhuSJYZYListByLBList } from '@/service/yiZhu/yiZhuYWZD';
import {
  saveZhongYaoCFKZ,
  zuoFeiZhongYaoCFKZ,
} from '@/service/yaoPin/yaoPinYK';

const yaoPinZiDian = {
  YP0094: 'xianZhiDJOptions',
};
const yiZhuZiDian = {
  YZ0005: 'zhongCaoYaoOptions',
};
const initFormModel = () => {
  return {
    kongZhiID: null,
    kongZhiMC: null,
    xianZhiDJDM: null,
    xianZhiDJMC: null,
    tiaoJianGZ: null,
    tiXingNR: null,
  };
};
const getTiaoJianObj = () => {
  return {
    tiaoJianDM: null,
    tiaoJianMC: null,
    yunSuanFDM: null,
    yunSuanFMC: null,
    xiangMuZMC: null,
    danWeiDM: null,
    danWeiMC: null,
  };
};
export default {
  name: 'zhongyaocfkz-dialog',
  data() {
    return {
      mode: 'new',
      dialogVisible: false,
      dialogLoading: false,
      formModel: initFormModel(),
      formRules: {
        kongZhiMC: [
          { required: true, message: '请输入控制名称', trigger: 'change' },
        ],
        xianZhiDJDM: [
          { required: true, message: '请选择限制等级', trigger: 'change' },
        ],
      },
      xianZhiDJOptions: [],
      zhongCaoYaoOptions: [],
      yunSuanFOptions: [
        {
          biaoZhunDM: '>',
          biaoZhunMC: '大于',
        },
        {
          biaoZhunDM: '<',
          biaoZhunMC: '小于',
        },
        {
          biaoZhunDM: '=',
          biaoZhunMC: '等于',
        },
        {
          biaoZhunDM: '>=',
          biaoZhunMC: '大于等于',
        },
        {
          biaoZhunDM: '<=',
          biaoZhunMC: '小于等于',
        },
      ],
      danWeiOptions: [
        {
          biaoZhunDM: '1',
          biaoZhunMC: '味',
        },
        {
          biaoZhunDM: '2',
          biaoZhunMC: '元',
        },
        {
          biaoZhunDM: '3',
          biaoZhunMC: '剂',
        },
      ],
      kongZhiLXOptions: [
        {
          biaoZhunDM: '0',
          biaoZhunMC: '味数',
        },
        {
          biaoZhunDM: '1',
          biaoZhunMC: '金额',
        },
        {
          biaoZhunDM: '2',
          biaoZhunMC: '剂数',
        },
      ],
      tiaoJianZuList: [],
    };
  },
  methods: {
    async showDialog(option) {
      this.dialogVisible = true;
      this.mode = option.mode;
      this.formModel = initFormModel();
      this.tiaoJianZuList = [
        {
          tiaoJianList: [getTiaoJianObj()],
        },
      ];
      this.getZiDian();
      Object.assign(this.formModel, option);
      if (option.tiaoJianGZ) {
        this.tiaoJianZuList = JSON.parse(option.tiaoJianGZ).tiaoJianZuList;
      }
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
      });
    },
    //字典初始化
    getZiDian() {
      getYaoPinShuJuYZYList(Object.keys(yaoPinZiDian)).then((res) => {
        res.forEach((item) => {
          const name = yaoPinZiDian[item.shuJuYLBID];
          this[name] = item.zhiYuList;
        });
      });
      getYiZhuSJYZYListByLBList(Object.keys(yiZhuZiDian)).then((res) => {
        res.forEach((item) => {
          const name = yiZhuZiDian[item.shuJuYLBID];
          this[name] = item.zhiYuList;
        });
      });
    },
    // 选择下拉框
    handeSelect(e, data, ziDuan, options) {
      const find = options.find((item) => item.biaoZhunDM === e);
      data[ziDuan] = find ? find.biaoZhunMC : null;
      if (ziDuan === 'kongZhiLXMC') {
        if (e === '0') {
          data.danWeiDM = '1';
          data.danWeiMC = '味';
        } else if (e === '1') {
          data.danWeiDM = '2';
          data.danWeiMC = '元';
        } else if (e === '2') {
          data.danWeiDM = '3';
          data.danWeiMC = '剂';
        }
      } else if (ziDuan === 'danWeiMC') {
        if (e === '1') {
          data.kongZhiLXDM = '0';
          data.kongZhiLXMC = '味数';
        } else if (e === '2') {
          data.kongZhiLXDM = '1';
          data.kongZhiLXMC = '金额';
        } else if (e === '3') {
          data.kongZhiLXDM = '2';
          data.kongZhiLXMC = '剂数';
        }
      }
    },
    // 新增条件组
    handleAddTiaoJianZu() {
      this.tiaoJianZuList.push({
        tiaoJianList: [{}],
      });
    },
    // 新增条件
    handleAddTiaoJian(data) {
      data.push(getTiaoJianObj());
    },
    // 删除条件
    handleDelTiaoJian(data, tiaoJianIndex, tiaoJianZuIndex) {
      data.splice(tiaoJianIndex, 1);
      if (data.length === 0) {
        this.tiaoJianZuList.splice(tiaoJianZuIndex, 1);
      }
      if (this.tiaoJianZuList.length === 0) {
        this.tiaoJianZuList.push({
          tiaoJianList: [getTiaoJianObj()],
        });
      }
    },
    // 生成提醒内容
    handleShengChengTXNR() {
      const tiaoJianZuTextList = [];
      this.tiaoJianZuList.forEach((item) => {
        const tiaoJianList = item.tiaoJianList.filter(
          (item) => item.tiaoJianDM,
        );
        const tiJianTextList = tiaoJianList.reduce((pre, cur) => {
          if (['0', '1'].includes(cur.tiaoJianDM)) {
            let text = '';
            text =
              (cur.tiaoJianMC || '') +
              (cur.kongZhiLXMC || '') +
              (cur.yunSuanFMC || '') +
              (cur.xiangMuZMC || '') +
              (cur.danWeiMC || '');
            pre.push(text);
          }
          // else if (cur.tiaoJianDM === '2') {
          //   let text = '性别='
          //   if (cur.xiangMuZMC1) {
          //     text = text + cur.xiangMuZMC1
          //   }
          //   if (text !== '性别=') pre.push(text)
          // } else if (cur.tiaoJianDM === '3') {
          //   let text = '诊断='
          //   if (!isEmpty(cur.xiangMuZMC1)) {
          //     const zhenDuanMCList = cur.xiangMuZMC1?.map(ot => ot.jiBingMC) || []
          //     text = text + zhenDuanMCList.join('、')
          //   }
          //   if (text !== '诊断=') pre.push(text)
          // }
          return pre;
        }, []);
        const tiaoJianZuText = tiJianTextList.join('，');
        tiaoJianZuTextList.push(tiaoJianZuText);
      });
      this.formModel.tiXingNR = tiaoJianZuTextList.join('或');
    },
    // 校验条件
    validateTiaoJian() {
      return new Promise((resolve, reject) => {
        let errorText = '';
        const find = this.tiaoJianZuList.find((item) => {
          return item.tiaoJianList.some((ot) => ot.tiaoJianDM);
        });
        if (!find) {
          errorText = '处方控制至少有一个条件';
        }
        this.tiaoJianZuList.forEach((item) => {
          item.tiaoJianList.forEach((ot) => {
            if (['0', '1'].includes(ot.tiaoJianDM)) {
              if (
                !(
                  ot.yunSuanFMC &&
                  ot.kongZhiLXMC &&
                  ot.xiangMuZMC &&
                  ot.danWeiMC
                )
              ) {
                errorText = '值必填！';
              }
            }
          });
        });
        if (errorText) {
          this.$message.warning(errorText);
          reject(false);
        } else {
          resolve(true);
        }
      });
    },
    // 作废
    async handleDelete() {
      await MdMessageBox.confirm(`确定作废？`, '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      try {
        this.dialogLoading = true;
        await zuoFeiZhongYaoCFKZ({ id: this.formModel.id });
        this.$message.success('作废成功');
        this.dialogVisible = false;
        this.resolve(true);
      } catch (error) {
      } finally {
        this.dialogLoading = false;
      }
    },
    // 取消
    handleCancle() {
      this.dialogVisible = false;
      this.resolve(false);
    },
    // 保存
    async handleSave() {
      await Promise.all([this.$refs.form.validate(), this.validateTiaoJian()]);
      try {
        this.dialogLoading = true;
        const params = {
          ...this.formModel,
        };
        delete params.mode;
        const tiaoJianZuList = cloneDeep(this.tiaoJianZuList);
        tiaoJianZuList.forEach((item) => {
          item.tiaoJianList = item.tiaoJianList.filter((ot) => ot.tiaoJianDM);
        });
        params.tiaoJianGZ = JSON.stringify({ tiaoJianZuList });
        await saveZhongYaoCFKZ(params);
        this.$message.success('保存成功');
        this.dialogVisible = false;
        this.resolve(true);
      } catch (error) {
      } finally {
        this.dialogLoading = false;
      }
    },
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-zhongyaocf-dialog {
  .#{$md-prefix}-dialog__scroll-view {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-zhongyaocf-dialog-content {
  display: flex;
  height: 510px;
  border-bottom: 1px solid #ddd;
  padding: var(--md-spacing-3);
  padding-top: 0;
  padding-bottom: 0;
  &-left {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 0;
    border-right: 1px solid #ddd;
    padding-top: var(--md-spacing-3);
    padding-right: var(--md-spacing-3);
    ::v-deep .#{$md-prefix}-form-item {
      padding-right: 0;
    }
    & > div:nth-child(2) {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    & > div:nth-child(3) {
      flex: 1;
      height: 0;
      overflow: auto;
      .#{$md-prefix}-zhongyaocf-tiaojianzu {
        margin-bottom: var(--md-spacing-3);
        padding: var(--md-spacing-3);
        background: #f5f5f5;
        border-radius: 4px;
        .#{$md-prefix}-zhongyaocf-tiaojian-item {
          display: flex;
          align-items: center;
          margin-bottom: var(--md-spacing-3);
          & > div:nth-child(1) {
            display: flex;
          }
          & > div:nth-child(2) {
            ::v-deep .#{$md-prefix}-button {
              margin-left: var(--md-spacing-2);
            }
          }
        }
        .#{$md-prefix}-zhongyaocf-tiaojian-item-last {
          margin-bottom: 0;
        }
      }
    }
  }
  &-right {
    display: flex;
    flex-direction: column;
    width: 376px;
    padding: var(--md-spacing-3);
    & > div:nth-child(1) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgb(var(--md-color-6));
      margin-bottom: var(--md-spacing-2);
      & > div:nth-child(2) {
        display: flex;
        align-items: center;
        ::v-deep .#{$md-prefix}-button {
          margin-right: var(--md-spacing-2);
        }
      }
    }
    & > div:nth-child(2) {
      flex: 1;
      height: 0;
      ::v-deep .#{$md-prefix}-textarea {
        height: 100% !important;
        textarea {
          height: 100%;
        }
      }
    }
  }
}
</style>
