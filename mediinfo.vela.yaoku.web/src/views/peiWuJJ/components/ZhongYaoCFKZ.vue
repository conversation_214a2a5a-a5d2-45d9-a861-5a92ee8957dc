<template>
  <div :class="prefixClass('zhongyaocf')">
    <div :class="prefixClass('zhongyaocf-header')">
      <div>
        <!-- <div :class="prefixClass('zhongyaocf-header-label')">项目类型</div>
        <md-select
          v-model="query.danWeiID"
          style="width: 120px;margin-right:8px"
          placeholder="请选择"
        >
          <md-option
            v-for="item in Options"
            :key="item.biaoZhunDM"
            :label="item.biaoZhunMC"
            :value="item.biaoZhunDM"
          >
          </md-option>
        </md-select> -->
        <md-input
          placeholder="输入控制名称搜索"
          v-model="query.likeQuery"
          style="width: 320px"
          @change="handleSearch"
          @clear="handleSearch"
        >
          <template #suffix>
            <md-icon slot="suffix" name="seach" @click="handleSearch" />
          </template>
        </md-input>
      </div>
      <div>
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          @click="handleSearch"
          >刷新</md-button
        >
        <md-button
          type="primary"
          :icon="prefixClass('icon-jia')"
          @click="handleZhongYaoCF"
          >新增</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('zhongyaocf-table')">
      <md-table-pro
        :columns="columns"
        :onFetch="handleFetch"
        :autoLoad="false"
        height="100%"
        ref="tablePro"
      >
      </md-table-pro>
    </div>
    <ZhongYaoCFKZDialog ref="ZhongYaoCFKZDialog"></ZhongYaoCFKZDialog>
  </div>
</template>

<script>
import ZhongYaoCFKZDialog from './ZhongYaoCFKZDialog.vue';

import {
  getZhongYaoCFKZCount,
  getZhongYaoCFKZList,
} from '@/service/yaoPin/yaoPinYK';

export default {
  name: 'zhongYaoCFKZ',
  data() {
    return {
      query: {},
      Options: [],
      columns: [
        {
          label: '控制名称',
          prop: 'kongZhiMC',
        },
        {
          label: '限制等级',
          prop: 'xianZhiDJMC',
          width: 130,
        },
        {
          label: '提醒信息',
          prop: 'tiXingNR',
        },
        {
          type: 'operate',
          label: '操作',
          width: 46,
          actions: [
            {
              text: '编辑',
              onPressed: ({ row, column, $index }) => {
                this.handleEdit(row);
              },
            },
          ],
        },
      ],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.$nextTick();
      this.handleSearch();
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        likeQuery: this.query.likeQuery,
        pageSize: pageSize,
        pageIndex: page,
      };

      const [items, total] = await Promise.all([
        getZhongYaoCFKZList(params, config),
        getZhongYaoCFKZCount(params, config),
      ]);
      return {
        items,
        total,
      };
    },
    // 查询条件变更搜索
    handleSearch() {
      this.$refs.tablePro.search({ pageSize: 100 });
    },
    async handleZhongYaoCF() {
      const result = await this.$refs.ZhongYaoCFKZDialog.showDialog({
        mode: 'new',
      });
      result && this.handleSearch();
    },
    // 编辑
    async handleEdit(row) {
      const result = await this.$refs.ZhongYaoCFKZDialog.showDialog({
        mode: 'edit',
        ...row,
      });
      result && this.handleSearch();
    },
  },
  components: {
    ZhongYaoCFKZDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-zhongyaocf {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: var(--md-spacing-3);
    // & > div:nth-child(1) {
    //   display: flex;
    //   align-items: center;
    //   .#{$md-prefix}-zhongyaocf-header-label {
    //     white-space: nowrap;
    //     padding-right: var(--md-spacing-3);
    //   }
    // }
    & > div:nth-child(1) {
      ::v-deep .#{$md-prefix}-button {
        margin-left: var(--md-spacing-3);
      }
    }
  }
  &-table {
    flex: 1;
    height: 0;
  }
}
</style>
