<template>
  <div :class="prefixClass('keshiql')">
    <div :class="prefixClass('keshiql-header')">
      <div>
        <div>
          <md-select
            v-model="query.keQingLKSID"
            filterable
            placeholder="请选择科室"
            style="width: 162px; margin-right: 8px"
            @change="handleSearch"
          >
            <md-option
              v-for="item in keShiOptions"
              :key="item.keShiID"
              :label="item.keShiMC"
              :value="item.keShiID"
            >
            </md-option>
          </md-select>
        </div>
        <BizYaoPinDW
          v-model="query.yaoPinMCObj"
          :columnsList="queryYaoPinMCColumns"
          type="ypxh"
          labelKey="yaoPinMC"
          append-to-body
          :class="prefixClass('yaopin-search')"
          @change="handleQueryYaoPinMC"
        />
      </div>
      <md-tooltip
        effect="dark"
        content="维护了数据，表示启用该功能，只有维护了的科室才能看到维护的药品；没有维护数据，代表科室能看到所有药品"
        placement="bottom"
        :popper-class="prefixClass('tooltip-box')"
      >
        <div>
          <md-icon name="shuoming" />
          <span>规则说明</span>
        </div>
      </md-tooltip>
    </div>
    <div v-loading="loading" :class="prefixClass('keshiql-table')">
      <md-editable-table-pro
        v-model="tableData"
        :columns="columns"
        :new-row="newRow"
        :showDefaultOperate="false"
        autoNew
        hideAddButton
        autoFill
        autoFocus
        :row-class-name="getRowClassName"
        ref="table"
      >
        <template #yaoPinMC="{ row, $index, cellRef }">
          <BizYaoPinDW
            v-model="row.yaoPinMCObj"
            :columnsList="yaoPinMCColumns"
            type="ypxh"
            labelKey="yaoPinMC"
            append-to-body
            :class="prefixClass('yaopin-search')"
            :key="yaoPinMCObjKey"
            @change="handleYaoPinMC($event, row, $index, cellRef)"
          />
        </template>
        <template #keQingLKSID="{ row, cellRef }">
          <md-select
            v-model="row.keQingLKSID"
            multiple
            inline
            filterable
            placeholder="请选择"
            @change="handleRowKeShi(row)"
          >
            <md-option
              v-for="item in keShiOptions"
              :key="item.keShiID"
              :label="item.keShiMC"
              :value="item.keShiID"
            >
            </md-option>
          </md-select>
        </template>
        <template #operate="{ $index }">
          <md-button type="danger" noneBg @click="handleDel($index)">
            <md-icon name="shanchu" />
          </md-button>
        </template>
      </md-editable-table-pro>
    </div>
    <div :class="prefixClass('keshiql-bottom')">
      <md-button type="primary" plain @click="handleCancle">取消</md-button>
      <md-button type="primary" @click="handleSave"> 保存 </md-button>
    </div>
    <SheZhiDialog ref="SheZhiDialog" />
  </div>
</template>

<script>
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import { getJiGouID } from '@/system/utils/local-cache';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { logger } from '@/service/log';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import SheZhiDialog from './components/SheZhiDialog';
import { MoJiKSList } from '@/service/yaoPin/yaoPinZD';
import { saveKeShiQLYPXX, getKeShiQLYPXXList } from '@/service/yaoPin/yaoPinZD';
export default {
  name: 'keshiqlypkz',
  data() {
    return {
      loading: false,
      query: {
        keQingLKSID: '',
        yaoPinMCObj: null,
      },
      keShiOptions: [],
      tableData: [], // 展示的数据
      tableDataOld: [], // 合到一起没有编辑过的数据
      keShiQLYPListOld: [], // 初始接口列表返回的数据
      columns: [
        {
          label: '药品名称',
          prop: 'yaoPinMC',
          slot: 'yaoPinMC',
          endMode: 'custom',
          minWidth: 180,
          // formatter: row => row.yaoPinMC,
          autoNewRequired: true,
        },
        {
          label: '规格',
          prop: 'yaoPinGG',
          width: 140,
          type: 'text',
          formatter: (row) => row.yaoPinGG,
        },
        {
          label: '院内编码',
          prop: 'yuanNeiBM',
          width: 122,
          type: 'text',
          formatter: (row) => row.yuanNeiBM,
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          minWidth: 180,
          type: 'text',
          formatter: (row) => row.chanDiMC,
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 48,
          type: 'text',
          formatter: (row) => row.baoZhuangDW,
        },
        {
          label: '剂型',
          prop: 'jiXingMC',
          width: 105,
          type: 'text',
          formatter: (row) => row.jiXingMC,
        },
        {
          label: '可请领科室',
          prop: 'keQingLKSID',
          slot: 'keQingLKSID',
          endMode: 'custom',
          minWidth: 350,
          // formatter: row => row.keQingLKSMC?.join('、') || ''
        },
        {
          label: '',
          slot: 'operate',
          width: 50,
          align: 'center',
        },
      ],
      queryYaoPinMCColumns: [
        {
          label: '',
          prop: 'yaoPinLXMC',
          width: 50,
          align: 'center',
          formatter(v) {
            return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
          },
        },
        {
          label: '药品名称',
          prop: 'yaoPinMCYGG',
          width: 400,
          formatter(v) {
            if (v.yaoPinMC) {
              return v.yaoPinMC;
            } else {
              return '';
            }
          },
        },
        {
          label: '规格',
          prop: 'yaoPinGG',
          width: 200,
          formatter(v) {
            if (v.jiaGeID) {
              return v.yaoPinGG;
            } else {
              return '';
            }
          },
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          width: 200,
          showOverflowTooltip: true,
        },
      ],
      yaoPinMCColumns: [
        {
          label: '',
          prop: 'yaoPinLXMC',
          width: 50,
          align: 'center',
          formatter(v) {
            return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
          },
          showOverflowTooltip: true,
        },
        {
          label: '药品名称',
          prop: 'yaoPinMCYGG',
          width: 400,
          formatter(v) {
            if (v.yaoPinMC) {
              return v.yaoPinMC;
            } else {
              return '';
            }
          },
        },
        {
          label: '规格',
          prop: 'yaoPinGG',
          width: 200,
          formatter(v) {
            if (v.jiaGeID) {
              return v.yaoPinGG;
            } else {
              return '';
            }
          },
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          width: 200,
          showOverflowTooltip: true,
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 50,
        },
        {
          label: '单价(元)',
          prop: 'danJia',
          align: 'right',
          width: 100,
          formatter: (v) => {
            return formatJiaGe(v.danJia);
          },
        },
      ],
      yaoPinMCObjKey: 0,
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      try {
        this.loading = true;
        const params = {
          keShiXZDM: '1',
          zuZhiJGID: getJiGouID(),
          pageSize: 10000,
        };
        this.keShiOptions = await MoJiKSList(params);
        const res = await getKeShiQLYPXXList();
        this.keShiQLYPListOld = cloneDeep(res);
        // 合并tableData
        this.tableData = res.reduce((pre, cur) => {
          const find = pre.find((item) => item.jiaGeID === cur.jiaGeID);
          if (!find) {
            cur.keQingLKSID = cur.keQingLKSID ? [cur.keQingLKSID] : [];
            cur.rowShow = true;
            pre.push(cur);
          } else {
            find.keQingLKSID.push(cur.keQingLKSID);
          }
          return pre;
        }, []);
        this.tableData.forEach((item) => {
          if (item.keQingLKSID.length) {
            const filterList = this.keShiOptions.filter((ot) =>
              item.keQingLKSID.includes(ot.keShiID),
            );
            item.keQingLKSMC = filterList.map((ot) => ot.keShiMC);
          }
          item.yaoPinMCObj = this.formatYaoPinMCObj(item);
        });
        this.$nextTick(() => {
          this.tableDataOld = cloneDeep(this.tableData);
          this.handleSearch();
        });
      } catch (error) {
        logger.error(error);
      } finally {
        this.loading = false;
      }
    },
    // 获取row类名
    getRowClassName({ row }) {
      return row.rowShow ? '' : 'keshiql-table-row-none';
    },
    async handleSearch() {
      const query = this.query;
      const jiaGeIDlist = this.tableData.filter(
        (item) =>
          !query.yaoPinMCObj?.jiaGeID ||
          item.jiaGeID === query.yaoPinMCObj?.jiaGeID,
      );
      const keShiIDlist = this.tableData.filter(
        (item) =>
          !query.keQingLKSID || item.keQingLKSID.includes(query.keQingLKSID),
      );
      this.tableData.forEach((item) => {
        const some1 = jiaGeIDlist.some((ot) => ot.jiaGeID === item.jiaGeID);
        const some2 = keShiIDlist.some((ot) => ot.jiaGeID === item.jiaGeID);
        item.rowShow = some1 && some2;
        if (!item.yaoPinMC && isEmpty(item.keQingLKSID)) {
          item.rowShow = true;
        }
      });
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      if (e) {
        this.query.yaoPinMCObj = {
          jiaGeID: e.jiaGeID,
          yaoPinMC: e.yaoPinMC,
          yaoPinGG: e.yaoPinGG,
        };
      } else this.query.yaoPinMCObj = null;
      this.handleSearch();
    },
    // 选择药品名称
    handleYaoPinMC(e, row, $index, cellRef) {
      const rowOld = cloneDeep(row);
      row.jiaGeID = e.jiaGeID || '';
      row.guiGeID = e.guiGeID || '';
      row.yaoPinMC = e.yaoPinMC || '';
      row.yaoPinGG = e.yaoPinGG || '';
      row.chanDiID = e.chanDiID || '';
      row.chanDiMC = e.chanDiMC || '';
      row.baoZhuangDW = e.baoZhuangDW || '';
      row.yuanNeiBM = e.yuanNeiBM || '';
      row.jiXingID = e.jiXingID || '';
      row.jiXingMC = e.jiXingMC || '';
      row.yaoPinMCObj = this.formatYaoPinMCObj(e);
      if (!e) row.yaoPinMCObj = null;
      const filterList = this.tableData.filter(
        (item) => item.jiaGeID === e.jiaGeID,
      );
      if (filterList.length < 2) {
        if (!row.yaoPinMC) row.yaoPinMCObj = null;
      } else {
        this.$message.warning('药品名称不能重复!');
        this.tableData.splice($index, 1, cloneDeep(rowOld));
      }
      this.yaoPinMCObjKey++;
    },
    // 编辑table里的科室
    handleRowKeShi(row) {},
    formatYaoPinMCObj(data) {
      return {
        baoZhuangDW: data.baoZhuangDW,
        baoZhuangLiang: data.baoZhuangLiang,
        chanDiMC: data.chanDiMC,
        danJia: data.danJia,
        guiGeID: data.guiGeID,
        jiaGeID: data.jiaGeID,
        jinJia: data.jinJia,
        kuCunSL: data.kuCunSL,
        yaoPinGG: data.yaoPinGG,
        yaoPinLXDM: data.yaoPinLXDM,
        yaoPinLXMC: data.yaoPinLXMC,
        yaoPinMC: data.yaoPinMC,
      };
    },
    newRow() {
      return {
        yaoPinMC: null,
        yaoPinMCObj: null,
        keQingLKSID: [],
        rowShow: true,
      };
    },
    handleDel(index) {
      this.tableData.splice(index, 1);
    },
    // handleSheZhi() {
    //   this.$refs.SheZhiDialog.showDialog()
    // },
    // 保存时格式化参数
    formatSaveParams() {
      const tableData = this.tableData.filter((item) => item.jiaGeID);
      tableData.forEach((item, index) => {
        item.shunXuHao = index * 50;
      });
      // 获取展开的列表
      const zhanKaiList = tableData.reduce((pre, cur, index) => {
        const keQingLKSIDList = cur.keQingLKSID || [];
        if (isEmpty(keQingLKSIDList)) {
          const current = cloneDeep(cur);
          current.keQingLKSID = null;
          current.keQingLKSMC = null;
          current.shunXuHao = index * 50;
          pre.push(current);
        } else {
          keQingLKSIDList.forEach((item, index2) => {
            const current = cloneDeep(cur);
            const find = this.keShiOptions.find((ot) => ot.keShiID === item);
            current.keQingLKSID = item;
            current.keQingLKSMC = find?.keShiMC;
            current.shunXuHao = index * 50 + index2;
            pre.push(current);
          });
        }
        return pre;
      }, []);
      const idList = [];
      const keShiQLYPKQLKSUpdateList = this.keShiQLYPListOld.reduce(
        (pre, cur) => {
          const find = zhanKaiList.find(
            (ot) =>
              cur.yaoPinMC === ot.yaoPinMC &&
              cur.keQingLKSID === ot.keQingLKSID,
          );
          if (find) {
            pre.push({
              id: cur.id,
              jiaGeID: find.jiaGeID,
              guiGeID: find.guiGeID,
              yaoPinMC: find.yaoPinMC,
              yaoPinGG: find.yaoPinGG,
              chanDiID: find.chanDiID,
              chanDiMC: find.chanDiMC,
              baoZhuangDW: find.baoZhuangDW,
              yuanNeiBM: find.yuanNeiBM,
              jiXingID: find.jiXingID,
              jiXingMC: find.jiXingMC,
              keQingLKSID: find.keQingLKSID,
              keQingLKSMC: find.keQingLKSMC,
              shunXuHao: find.shunXuHao,
            });
          } else {
            idList.push(cur.id);
          }
          return pre;
        },
        [],
      );
      const keShiQLYPKQLKSCreateList = zhanKaiList.reduce((pre, cur) => {
        const find = this.keShiQLYPListOld.find(
          (ot) =>
            cur.yaoPinMC === ot.yaoPinMC && cur.keQingLKSID === ot.keQingLKSID,
        );
        if (!find) {
          pre.push({
            jiaGeID: cur.jiaGeID,
            guiGeID: cur.guiGeID,
            yaoPinMC: cur.yaoPinMC,
            yaoPinGG: cur.yaoPinGG,
            chanDiID: cur.chanDiID,
            chanDiMC: cur.chanDiMC,
            baoZhuangDW: cur.baoZhuangDW,
            yuanNeiBM: cur.yuanNeiBM,
            jiXingID: cur.jiXingID,
            jiXingMC: cur.jiXingMC,
            keQingLKSID: cur.keQingLKSID,
            keQingLKSMC: cur.keQingLKSMC,
            shunXuHao: cur.shunXuHao,
          });
        }
        return pre;
      }, []);
      return {
        keShiQLYPKQLKSCreateList,
        keShiQLYPKQLKSUpdateList,
        idList,
      };
    },
    handleCancle() {
      this.tableData = cloneDeep(this.tableDataOld);
      this.query = {
        keQingLKSID: '',
        yaoPinMCObj: null,
      };
    },
    async handleSave() {
      try {
        this.loading = true;
        const find = this.tableData.find(
          (item) => item.yaoPinMC && isEmpty(item.keQingLKSID),
        );
        if (find) {
          this.$message.warning('已选药品，可请领科室不能为空！');
          return;
        }
        const params = this.formatSaveParams();
        await saveKeShiQLYPXX(params);
        this.$message.success('保存成功！');
        this.init();
      } catch (error) {
        logger.error(error);
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    BizYaoPinDW,
    SheZhiDialog,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-tooltip-box {
  width: 240px;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-keshiql {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--md-spacing-3);
    height: 54px;
    box-sizing: border-box;
    & > div:nth-child(1) {
      display: flex;
    }
    & > div:nth-child(2) {
      color: rgb(var(--md-color-5));
      & > span {
        margin-left: var(--md-spacing-2);
      }
    }
  }
  &-table {
    flex: 1;
    height: 0;
    padding: var(--md-spacing-3);
    padding-top: 0;
    ::v-deep .keshiql-table-row-none {
      display: none !important;
    }
  }
  &-bottom {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: var(--md-spacing-4);
    height: 46px;
    box-sizing: border-box;
    background-color: rgb(var(--md-color-1));
    ::v-deep .#{$md-prefix}-button {
      margin-left: var(--md-spacing-3);
      width: 64px;
    }
  }
}
</style>
