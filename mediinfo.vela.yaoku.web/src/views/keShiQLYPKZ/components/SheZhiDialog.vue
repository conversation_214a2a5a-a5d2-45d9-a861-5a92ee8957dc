<template>
  <md-dialog v-model="dialogVisible" title="设置" width="560" height="300">
    <md-form :model="formModel">
      <md-form-item label="请领控制设置">
        <md-radio-group v-model="formModel.qingLingDM" border>
          <md-radio
            v-for="(item, index) in qingLingKZOptions"
            :key="index"
            :label="item.biaoZhunDM"
            >{{ item.biaoZhunMC }}</md-radio
          >
        </md-radio-group>
      </md-form-item>
    </md-form>
    <template v-slot:footer>
      <md-button type="primary" plain @click="handleCancle">取消</md-button>
      <md-button type="primary" @click="handleSave"> 保存 </md-button>
    </template>
  </md-dialog>
</template>

<script>
export default {
  name: 'shezhidialog',
  data() {
    return {
      dialogVisible: false,
      formModel: {
        qingLingDM: null,
      },
      qingLingKZOptions: [
        {
          biaoZhunDM: '0',
          biaoZhunMC: '是，请领科室只能看到设置的药品',
        },
        {
          biaoZhunDM: '1',
          biaoZhunMC: '否，请领科室可以看到所有药品',
        },
      ],
    };
  },
  methods: {
    showDialog() {
      this.dialogVisible = true;
    },
    handleCancle() {
      this.dialogVisible = false;
    },
    handleSave() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style></style>
