<template>
  <md-drawer
    title="药品批次库存"
    v-model="drawer"
    :modal-class="prefixClass('JiYaGLCustom')"
    :direction="direction"
    @closed="handleClose"
    :destroyOnClose="true"
    size="550"
    :modal="true"
    :append-to-body="false"
    :class="prefixClass('JiYaGLDrawer')"
    ref="jiYaGLDrawer"
  >
    <div :class="prefixClass('JiYaGLDrawer-content-wrap')" v-if="rowData">
      <div :class="prefixClass('content-detail')">
        <h4>{{ rowData.yaoPinMC }} {{ rowData.yaoPinGG }}</h4>
        <p>
          产地名称:<span :class="prefixClass('bold')">{{
            rowData.chanDiMC
          }}</span>
        </p>
        <p>
          单位：<span :class="prefixClass('bold')">{{ rowData.yaoPinGG }}</span>
          零售价：<span :class="prefixClass('bold')">{{
            rowData.lingShouJia
          }}</span>
          进价：<span :class="prefixClass('bold')">{{ rowData.jinJia }}</span>
        </p>
      </div>
      <div :class="prefixClass('content-table-wrap')">
        <md-table-pro
          :columns="columns"
          :data="data"
          height="100%"
          :onFetch="handleFetch"
          :showPagination="false"
          autoLoad
        />
      </div>
    </div>
  </md-drawer>
</template>
<script>
import dayjs from 'dayjs';

import { GetPiCiKCListByJGID } from '@/service/yaoPinYK/JiYaGL';
import formatJiaGe from '@/system/utils/formatJiaGe';

export default {
  name: 'JiYaGLDrawer',
  data() {
    return {
      direction: 'rtl',
      drawer: false,
      rowData: null,
      columns: [
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          'min-width': 110,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 115,
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 88,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(3);
          },
        },

        {
          prop: 'jinJia',
          label: '进价',
          width: 88,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return formatJiaGe(cellValue);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 88,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return formatJiaGe(cellValue);
          },
        },
      ],
      data: [],
    };
  },
  methods: {
    showDrawer(row) {
      this.rowData = row;
      this.drawer = true;
      this.data = [];
      this.handleFetch();
    },
    async handleFetch() {
      this.load = true;
      await GetPiCiKCListByJGID({ JiaGeID: this.rowData.jiaGeID }).then(
        (res) => {
          this.data = res;
        },
      );

      return { items: this.data, total: this.data.length };
    },
    handleClose(done) {
      this.drawer = false;
    },
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-JiYaGLCustom {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  .#{$md-prefix}-drawer__header {
    background: #f0f5fb;
    margin-bottom: 0px;
  }
}
.#{$md-prefix}-JiYaGL-content .#{$md-prefix}-overlay {
  background-color: unset;
}
</style>

<style lang="scss" scoped>
.#{$md-prefix}-JiYaGLDrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 70%;
}

.#{$md-prefix}-JiYaGLDrawer-content-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 8px;
  box-sizing: border-box;

  .#{$md-prefix}-content-detail {
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 8px;

    h4 {
      font-weight: 600;
      color: #222222;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 11px;
    }

    p {
      color: #666666;
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 5px;

      .#{$md-prefix}-bold {
        color: #222222;
        margin-right: 28px;
      }
    }
  }

  .#{$md-prefix}-content-table-wrap {
    flex: 1;
    min-height: 0;
  }
}
</style>
