<template>
  <div
    v-loading="pageLoading"
    element-loading-text="正在加载中..."
    :class="prefixClass('JiYaGL-wrap')"
  >
    <div :class="prefixClass('JiYaGL-content')">
      <div :class="prefixClass('search-box')">
        <div :class="prefixClass('wrap-button-left')">
          <div :class="prefixClass('dayNum')">
            <div :class="prefixClass('dayNum-span')">积压天数</div>
            <md-input
              v-model="query.JiYaTS"
              placeholder="请输入天数"
              type="number"
              min="1"
              style="width: 134px; margin: 0 8px"
              @change="handleTableSearch"
              controls-position="right"
            >
            </md-input>
          </div>

          <biz-yaopindw
            v-model="medicineInfo"
            placeholder="请输入药品名称选择"
            :showSuffix="true"
            type="yk"
            :guiGeLX="guiGeLX"
            @change="handlerSearchData"
            ref="bizSearch"
          ></biz-yaopindw>
        </div>

        <div :class="prefixClass('wrap-button-right')">
          <!--          <md-button type="primary" :icon="prefixClass('icon-daochu')" noneBg-->
          <!--            >导出</md-button-->
          <!--          >-->
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            style="margin-right: 8px"
            @click="handleTableSearch"
            >刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handleYuLan"
            >预览</md-button
          >
          <!--           <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            :disabled="!canDaYin"
            @click="handleDaYin"
            >打印</md-button
          > -->
        </div>
      </div>
      <div :class="prefixClass('table-wrap')">
        <md-table-pro
          :columns="columns"
          :onFetch="handleFetch"
          height="100%"
          ref="JiYaGLTable"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <template v-slot:yaoPinMC="{ row }">
            {{ row.yaoPinMC }} {{ row.yaoPinGG }}
          </template>
        </md-table-pro>
      </div>

      <JiYaGLDrawer ref="JiYaGLDrawer"></JiYaGLDrawer>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT010'"
      :fileName="'积压管理'"
      :title="'积压管理打印预览'"
    />
  </div>
</template>

<script>
import { GetJiYaYPCount, GetJiYaYPList } from '@/service/yaoPinYK/JiYaGL';

import DaYinDialog from '@/components/DaYinDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2.js';
import {
  getJiGouID,
  getWeiZhiID,
  getKuCunGLLX,
} from '@/system/utils/local-cache';
import { printByUrl } from '@/system/utils/print';
import JiYaGLDrawer from './JiYaGLDrawer';

import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';

export default {
  name: 'JiYaGL',
  data() {
    return {
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      medicineInfo: '',
      pageLoading: false,
      canDaYin: false,
      params: {},
      guiGeLX: '1',
      query: {
        JiYaTS: 30,
        YaoPinMC: '',
      },
      columns: [
        {
          label: '序号',
          type: 'index',
          width: 54,
        },
        {
          label: '药品名称与规格  ',
          prop: 'yaoPinMC',
          'min-width': 200,
          slot: 'yaoPinMC',
          showOverflowTooltip: true,
        },
        {
          label: '产地',
          prop: 'chanDiMC',
          'min-width': 150,
          showOverflowTooltip: true,
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 60,
        },
        {
          label: '库存数量',
          prop: 'kuCunSL',
          width: 88,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return formatJiaGe_2(cellValue);
          },
        },
        {
          label: '进价',
          prop: 'jinJia',
          width: 88,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return formatJiaGe_2(cellValue, this.jinJiaXSDW);
          },
        },
        {
          label: '进价金额',
          prop: 'jinJiaJE',
          width: 120,
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
          align: 'right',
        },
        {
          label: '零售单价',
          prop: 'lingShouJia',
          width: 88,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return formatJiaGe_2(cellValue, this.lingShouXSDW);
          },
        },
        {
          label: '零售金额',
          prop: 'lingShouJE',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },

        {
          label: '摆放位置',
          prop: 'baiFangWZ',
          'min-width': 80,
        },
      ],
    };
  },
  async created() {
    this.init();
    const arr = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  // mounted () {
  //   this.handleTableSearch()
  // },
  methods: {
    async init() {
      const res = await getKuFangSZList(['yaoPinJYTS']);
      const find = res?.find((item) => item.weiZhiID === getWeiZhiID());
      if (find && find.xiangMuZDM) this.query.JiYaTS = find.xiangMuZDM;
      await this.$nextTick();
      this.handleTableSearch();
    },
    //预览
    async handleYuLan() {
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        jiaGeID: this.medicineInfo.jiaGeID,
        jiYaTS: this.query.JiYaTS,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handleDaYin() {
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        jiaGeID: this.medicineInfo.jiaGeID,
        jiYaTS: this.query.JiYaTS,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
      };
      try {
        this.pageLoading = true;
        await printByUrl('YKXT010', params);
        this.$message({
          type: 'success',
          message: '打印成功！',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    handleRowClick(row) {
      this.$refs.JiYaGLDrawer.showDrawer(row);
    },
    async handleFetch({ page, pageSize }, config) {
      let data = {
        jiaGeID: this.medicineInfo.jiaGeID,
        JiYaTS: this.query.JiYaTS,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
      };
      let [items, total] = await Promise.all([
        GetJiYaYPList({
          PageSize: pageSize == -1 ? 1 : pageSize,
          PageIndex: page,
          ...data,
        }),
        GetJiYaYPCount(data),
      ]);
      this.canDaYin = items.length !== 0;
      return { items, total };
    },
    handleTableSearch() {
      this.$refs.JiYaGLTable.search({ pageSize: 100 });
    },
    handlerSearchData(val) {
      this.medicineInfo = val;
      let JiYaTS = this.query.JiYaTS;
      JiYaTS = parseInt(JiYaTS);
      if (!JiYaTS || JiYaTS < 1) {
        JiYaTS = 1;
      }
      this.query.JiYaTS = JiYaTS;
      this.handleTableSearch();
    },
  },
  components: {
    JiYaGLDrawer,
    'biz-yaopindw': BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-JiYaGL-wrap {
  display: flex;
  flex: 1;
  min-height: 0;
  background-color: #f0f2f5;
  padding: 8px;
  .#{$md-prefix}-JiYaGL-content {
    position: relative;
    display: flex;
    height: 100%;
    flex-direction: column;
    width: 100%;
    background: #fff;
    padding: 8px 8px 0;
    box-sizing: border-box;
    .#{$md-prefix}-search-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      margin-bottom: 8px;
      .#{$md-prefix}-wrap-button-left {
        display: flex;
        .#{$md-prefix}-dayNum {
          display: flex;
          .#{$md-prefix}-dayNum-span {
            display: inline-block;
            width: max-content;
            line-height: 30px;
            vertical-align: top;
          }
        }
      }
    }
    .#{$md-prefix}-table-wrap {
      flex: 1;
      min-height: 0;
    }
  }
}
</style>
