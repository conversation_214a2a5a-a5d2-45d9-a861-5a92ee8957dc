<template>
  <div :class="prefixClass('procurement')">
    <div :class="prefixClass('procurement-content')">
      <div :class="prefixClass('procurement-top')">
        <div :class="prefixClass('procurement-left')">
          <div :class="prefixClass('procurement-left-lt')">
            <span :class="prefixClass('caigouJH-title')"
              ><i class="iconfont icondanju"></i>采购计划</span
            >
            <biz-yaopindw
              v-model="yaoPingDW"
              showSuffix
              :class="prefixClass('input-seach')"
              @change="handleDingWei($event)"
            >
            </biz-yaopindw>
            <md-popover
              v-model="popoverValue"
              placement="bottom"
              width="260"
              trigger="click"
              :class="prefixClass('pop-over')"
            >
            </md-popover>
          </div>
          <!-- <div
            :class="prefixClass('procurement-left-rt')"
            placement="top"
            width="160"
          >
            <span @click="hanldeAnKunCSC" v-show="false">按库存下限生成</span>
            <span @click="hanldeAnXiaoHLSC">按消耗量生成</span>
            <!-- <span @click="hanldeZongHeSC">综合生成</span> --
          </div> -->

          <md-button
            type="primary"
            plain
            style="margin-left: 8px"
            @click="hanldeAnXiaoHLSC"
          >
            按消耗量生成
          </md-button>

          <md-button
            type="primary"
            plain
            style="margin-left: 8px"
            @click="handleAnXiaoHKCBSC"
          >
            按消耗库存比生成
          </md-button>
          <md-button
            type="primary"
            plain
            style="margin-left: 8px"
            @click="handleMingXiZXHSC"
          >
            按明细账消耗生成
          </md-button>
        </div>
        <div :class="prefixClass('procurement-right')">
          <!-- <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            :class="prefixClass('main5')"
            @click="handlePrint"
            >打印</md-button
          > -->
          <md-button
            type="danger"
            :class="prefixClass('main5')"
            :icon="prefixClass('icon-shanchuwap')"
            noneBg
            @click="handleDelete"
            >删除</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('save-btn')"
            @click="handleSave(1)"
            >保存</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('save-btn')"
            @click="handleSave(2)"
            >保存并提交</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('note')">
        <span v-if="isShowLX" :class="prefixClass('note-title')">类型</span>
        <md-select
          v-if="isShowLX"
          v-model="yinPianKLBZ"
          placeholder="请选择"
          style="width: 300px"
        >
          <md-option
            v-for="item in yinPianKLBZList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </md-option>
        </md-select>
        <span :class="prefixClass('note-title')">备注</span>
        <md-input
          v-model="beiZhu"
          placeholder="请输入"
          :class="prefixClass('input-note')"
        ></md-input>
      </div>

      <div :class="prefixClass('procurement-table')">
        <md-editable-table-pro
          v-loading="loading"
          v-table-enter
          v-model="tableData"
          height="100%"
          row-key="id"
          auto-focus
          :columns="columns"
          :showDefaultOperate="false"
          :hide-add-button="true"
          :maxLength="9999"
          :row-class-name="tableRowClassName"
          :cell-style="cellStyle"
          :autoFill="true"
          id="caiGouTable"
          ref="mdEditTable"
          @sort-change="handleSortChange"
          @selection-change="selectionChange"
          :close-control-on-confirm="false"
          :close-control-on-recovery="false"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #yaoPinMCYGG="{ row, $index, cellRef }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              automatic-dropdown
              labelKey="yaoPinZC"
              :liangDingYPBZ="1"
              :yinPianKLBZ="yinPianKLBZ"
              :class="prefixClass('yaopin-select')"
              @change="handleTableYaoPinDWChange($event, row, $index, cellRef)"
            >
            </biz-yaopindw>
          </template>
          <template #previewYPMC="{ row, $index, cellRef }">
            <span :style="yaoPinSXStyle(row)[0]">{{
              yaoPinSXStyle(row)[1] || ''
            }}</span>
          </template>
          <template #gongHuoDWMC="{ row, cellRef }">
            <md-select
              v-model="row.gongHuoDWID"
              :remote-method="remoteGongHuoDW"
              remote
              filterable
              placeholder="输入选择供货单位"
              value-key="danWeiID"
              :defaultLabel="row.gongHuoDWMC"
              @change="handleSelect($event, row)"
              @visible-change="selectChange($event, cellRef)"
              @focus="handleFocus(row)"
            >
              <!-- <div
                :class="prefixClass('select-div')"
                v-infinite-scroll="loadMore"
              > -->
              <md-option
                v-for="item in gongHuoDWOptions"
                :key="item.danWeiID"
                :label="item.danWeiMC"
                :value="item.danWeiID"
              >
              </md-option>
              <!-- </div> -->
            </md-select>
          </template>
          <template #caiGouSL="{ row, $index }">
            <md-input
              v-model="row.caiGouSL"
              v-number.float="{ decimal: 2 }"
              placeholder="请填写"
              @input="handleCaiGouSL($event, row, $index)"
              @focus="focusInput($event.target)"
              @change="handleChangeCGSL($event, row, $index)"
            />
          </template>
          <template #caiGouXDSL="{ row }">
            <md-input
              v-model="row.caiGouXDSL"
              v-number.float="{ decimal: 2 }"
              placeholder="请填写"
              @focus="focusInput($event.target)"
              @input="handleCaiGouXDSL($event, row, 'caiGouXDSL')"
            />
          </template>
          <template #beiZhu="{ row }">
            <md-input
              v-model="row.beiZhu"
              placeholder="请填写"
              @change="handleCaiGouXDSL($event, row, 'beiZhu')"
            />
          </template>
          <template #xiaoHaoL="{ row, $index }">
            <template v-if="row.shujuLY === '2'">
              <span>{{ row.xiaoHaoLiang }}</span>
            </template>
            <!-- 消耗量浮窗 -->
            <template v-else>
              <md-popover ref="popoverRef" trigger="click" :width="480">
                <div>
                  <md-date-picker
                    v-model="row.kaiShiSJ"
                    type="daterange"
                    :teleported="false"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 93%; margin-bottom: 8px"
                    @change="($event) => handleRowXHL($event, row)"
                  />

                  <md-table :data="row.list" :columns="rowColumns"></md-table>
                </div>
                <template #reference>
                  <span class="xiaohao-color" @click="handleRowXHL('', row)">{{
                    row.xiaoHaoLiang
                  }}</span>
                </template>
              </md-popover>
            </template>
          </template>
          <template #jinJiCGBZ="{ row }">
            <i
              v-if="row.jinJiCGBZ"
              class="iconfont icongou"
              style="color: #1e88e5; font-size: 16px"
            />
          </template>
          <template #linShiYYBZ="{ row }">
            <i
              v-if="row.linShiYYBZ"
              class="iconfont icongou"
              style="color: #1e88e5; font-size: 16px"
            />
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('pagination-wrap')">
        <md-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 200, 300, 400, 500]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalData.yaoPinZS"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </md-pagination>
      </div>
      <div :class="prefixClass('caigou-footer')">
        <div :class="prefixClass('caigou-footer__info left')">
          <span>制单：</span>
          <span :class="prefixClass('info__name color-222')">{{
            zhiDanXX.zhiDanRXM
          }}</span>
          <span :class="prefixClass('info__time color-222')">{{
            zhiDanXX.zhiDanSJ
          }}</span>
        </div>
        <div :class="prefixClass('caigou-footer__info right')">
          <div :class="prefixClass('margin-right-12')">
            <span>共计：</span>
            <span :class="prefixClass('font-bold')">{{
              totalData.yaoPinZS
            }}</span>
            <span>种药品</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span style="margin-right: 8px">合计</span>
            <span>进价金额：</span>
            <span :class="prefixClass('font-bold')">{{
              totalData.jinJiaJE
            }}</span>
            <span>元</span>
          </div>
          <div>
            <span>零售金额：</span>
            <span :class="prefixClass('font-bold')">{{
              totalData.lingShouJE
            }}</span>
            <span>元</span>
          </div>
        </div>
      </div>
    </div>
    <anxiaohaokcb-dialog
      ref="anXiaoHaoKCBDialog"
      @handleSaveData="handleSaveData"
    />
    <an-xiao-hao-l-dialog
      ref="AnXiaoHLSCDialog"
      @handleSaveData="handleSaveData"
    />
    <an-mingxiz-xh-dialog
      ref="anMingXiZXHDialog"
      @handleSaveData="handleSaveData"
    />
  </div>
</template>
<script>
import { createPrompt } from '@mdfe/stark-app';
import {
  AddCaiGouJHD,
  BaoCunBTJBJCGJHD,
  BaoCunBTJCGJHD,
  GetCaiGouJHDGHXX,
  GetCaiGouJHDMX,
  GetGongHuoDWList,
  GetYaoPinXHLMXList,
  GetZongHeSCCGJHList,
  UpdateCaiGouJHD,
} from '@/service/yaoPinYK/caiGouJH.js';
import { makePY } from '@/system/utils/wubi-pinyin.js';
import { focusInput } from '@/system/utils/focusInput.js';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep, debounce, isEqual } from 'lodash';
import { sortBy } from 'lodash-es';
import { h } from 'vue';
import BizYaoPinDW from '../../../components/YaoKu/BizYaoPinDW';
import commonData from '../../../system/utils/commonData';
import eventBus from '../../../system/utils/eventbus';
import { focusEditTableDom } from '../../../system/utils/focusEditTable';
import { yaoKuZDJZTimeShow } from '../../../system/utils/formatDate';
import { getYongHuID, getYongHuXM } from '../../../system/utils/local-cache';
import { printByUrl } from '../../../system/utils/print';
import AnXiaoHaoKCBDialog from './components/AnXiaoHaoKCBDialog.vue';
import AnXiaoHLSCDialog from './components/AnXiaoHLSCDialog.vue';
import AnMingXiZXHDialog from './components/AnMingXiZXHDialog';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import columnMixin from '@/components/mixin/columnMixin';
const initData = () => {
  return {
    yaoPinLXDM: '', //药品类型代码
    yaoPinLXMC: '', //药品类型名称
    chanDiMC: null, //产地名称
    yaoPinMC: null, //药品名称
    yaoPinGG: '', // 药品规格
    yaoPinMCYGG: {}, //药品名称与规格
    jiaGeID: '', //价格ID
    baoZhuangDW: '', // 包装单位
    // yaoPinLX: null, //药品类型
    // danWei: null, //单位
    gongHuoDWID: null, //供货单位ID
    gongHuoDWMC: null, //供货单位名称
    shenQingSL: '', //申请数量 默认为1
    caiGouSL: '', //采购数量 默认为1
    jinJia: 0, //进价
    lingShouJia: 0, //零售价
    lingShouJE: 0, //零售金额
    jinJiaJE: 0, //进价金额
    // yaoKuKC: 0, //药库库存
    kuCunXX: 0, //库存下限
    kuCunSX: 0, //库存上限
    kuCunSL: 0, //库存数量
    keCaiGSL: 0,
    jinJiCGBZ: 0,
    linShiYYBZ: 0,
  };
};
export default {
  name: 'xinzengcg',
  inject: ['viewManager'],
  mixins: [columnMixin],
  props: {
    // id: {
    //   type: String,
    //   default: '',
    // },
    caiGouDH: {
      type: String,
      default: '',
    },
  },
  data() {
    this.stopPrompt = null;
    return {
      controlExtraColumns: [
        {
          slot: 'caoZuoSZDM',
          label: '键盘操作设置',
          cIndex: 5,
        },
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 6,
        },
      ],
      jinJiaXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      form: {
        kaiShiSJ: dayjs().startOf('month').format('YYYY-MM-DD'),
        jieShuSJ: dayjs().format('YYYY-MM-DD'),
      },
      xiaoHaoLSJ: '',
      id: '',
      caiGouDH: '',
      kaiShiSJRow: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      gongHuoFlag: true,
      loading: false,
      popoverValue: false, //控制popover显隐
      yaoPingDW: '', //药品定位
      tableData: [initData()], // 表格数据
      tableDataOrigin: [], //源表格数据.只用来判断是否有数据更改但未保存，temporary在computed被修改了
      temporary: [initData()], //源表格数据
      gongHuoDWID: '', //要被过滤的供货单位名ID
      yinPianKLBZ: '', //类型
      yinPianKLBZList: [
        { value: 1, label: '颗粒' },
        { value: 2, label: '饮片' },
      ],
      beiZhu: '', //备注
      zhiDanXX: {
        zhiDanRXM: getYongHuXM(),
        zhiDanRID: getYongHuID(),
        zhiDanSJ: dayjs().format('YYYY-MM-DD'),
      }, //制单信息
      gongHuoDWOptions: [], //供货单位下拉项
      columns: [
        // {
        //   label: '序号',
        //   type: 'index',
        // },
        {
          type: 'selection',
          align: 'center',
          width: 54,
          // selectable: (row, index) => {
          //   return this.tableData.length !== index + 1 && !row.ruKuMXDID;
          // },
          selectable: (row, index) => {
            return !(
              this.currentPage == this.getMaxPageindex() &&
              this.tableData.length - 1 == index &&
              !row.ruKuMXDID
            );
          },
        },
        {
          prop: 'xunHao',
          label: '序号',
          width: 70,
          align: 'center',
          formatter: (row, column, cellValue, index) => {
            return index + 1;
          },
        },
        {
          label: '转换系数',
          prop: 'liangDingXS',
          type: 'text',
          width: 76,
          field: true,
        },
        {
          label: '院内编码',
          prop: 'yuanNeiBM',
          type: 'text',
          width: 76,
          field: true,
        },
        {
          label: '省平台ID',
          prop: 'shengPingTBM',
          type: 'text',
          width: 76,
          field: true,
        },
        {
          prop: 'yaoPinLXDM',
          type: 'text',
          width: 32,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          slot: 'yaoPinMCYGG',
          field: true,
          formatter: (row, column, cellValue, index) => {
            // if (cellValue.yaoPinMC && cellValue.yaoPinGG)
            //   return (
            //     row.tianJiaWZ + cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG
            //   );
            return row.yaoPinZC;
          },
          endMode: 'custom',
          fieldDisabled: true,
          labelClassName: 'requireHeader',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
          sortable: 'custom',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          field: true,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          field: true,
          width: 60,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          slot: 'gongHuoDWMC',
          prop: 'gongHuoDWMC',
          label: '供货单位',
          width: 120,
          field: true,
          endMode: 'custom',
          startMode: 'click',
          formatter: (row) => {
            return row.gongHuoDWMC;
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'xiaoHaoL',
          slot: 'xiaoHaoL',
          field: true,
          label: '消耗量',
          width: 70,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'keShiLY',
          label: '科室领用',
          field: true,
          width: 80,
          align: 'right',
          type: 'text',
        },
        {
          slot: 'linShiYYBZ',
          prop: 'linShiYYBZ',
          label: '临时用药',
          field: true,
          width: 80,
          align: 'center',
          type: 'text',
        },
        {
          slot: 'jinJiCGBZ',
          prop: 'jinJiCGBZ',
          label: '紧急采购',
          field: true,
          width: 80,
          type: 'text',
          align: 'center',
        },
        {
          prop: 'keCaiGSL',
          label: '可采购数量',
          field: true,
          width: 100,
          type: 'text',
          align: 'right',
        },
        {
          prop: 'yaoFangLY',
          label: '出库量',
          width: 80,
          align: 'right',
          field: true,
          type: 'text',
        },
        {
          prop: 'caiGouBZL',
          label: '采购包装',
          field: true,
          width: 80,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'caiGouSL',
          label: '采购数量',
          field: true,
          width: 80,
          align: 'right',
          slot: 'caiGouSL',
          // // endMode: 'custom',
          // // startMode: 'click',
          formatter: (row) => {
            return Number(row.caiGouSL || 0).toFixed(4);
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
        },
        {
          prop: 'caiGouXDSL',
          slot: 'caiGouXDSL',
          field: true,
          label: '采购下单数量',
          width: 110,
          align: 'right',
          formatter: (row) => {
            return Number(row.caiGouXDSL || 0).toFixed(2);
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
        },
        {
          prop: 'kuCunSL',
          label: '药库库存',
          field: true,
          width: 80,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },

        {
          prop: 'kuCunZL',
          label: '全院库存',
          field: true,
          width: 80,
          align: 'right',
          type: 'text',
        },

        {
          prop: 'yuJiXHTS',
          label: '预计消耗天数',
          field: true,
          width: 125,
          type: 'text',
          sortable: 'custom',
        },
        {
          prop: 'kuCunZL',
          field: true,
          label: '最晚结束时间',
          width: 110,
          type: 'text',
          formatter: (v) => {
            return v.jieShuRQ ? dayjs(v.jieShuRQ).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'kuCunXX',
          field: true,
          label: '库存下限',
          width: 100,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },

        {
          prop: 'kuCunSX',
          field: true,
          label: '库存上限',
          align: 'right',
          width: 100,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          field: true,
          width: 80,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 100,
          field: true,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        // {
        //   slot: 'shenQingSL',
        //   prop: 'shenQingSL',
        //   label: '申请数量',
        //   align: 'right',
        //   width: 100,
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        //   renderHeader: ({ column, _self }) => {
        //     return h(
        //       'div',
        //       { class: _self.prefixClass('require') },
        //       column.label,
        //     );
        //   },
        //   disabled: ({ row }) => {
        //     return !!row.ruKuMXDID;
        //   },
        // },

        // {
        //   prop: 'lingShouJia',
        //   label: '零售价',
        //   width: 100,
        //   align: 'right',
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        // },
        // {
        //   prop: 'lingShouJE',
        //   label: '零售金额',
        //   width: 100,
        //   align: 'right',
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        // },

        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          minWidth: 100,
          field: true,
          type: 'text',
          // sortable: 'custom',
        },
        {
          prop: 'zhongBaoZXS',
          label: '中包装',
          minWidth: 140,
          type: 'text',
        },
        {
          slot: 'beiZhu',
          prop: 'beiZhu',
          field: true,
          control: true,
          label: '备注',
          minWidth: 140,
          endMode: 'custom',
          startMode: 'click',
          formatter: (row) => {
            return row.beiZhu;
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
        },
      ],
      currentPage: 1,
      pageSize: 200,
      rowColumns: [
        { label: '机构', prop: 'jiGouMC', showOverflowTooltip: true },
        { label: '药房', prop: 'weiZhiMC', width: 90 },
        { label: '消耗量', prop: 'xiaoHaoLiang' },
        { label: '库存数量', prop: 'kuCunSL', width: 120 },
      ],
      searchFormData: {
        LikeQuery: '',
        PageSize: 999,
        PageIndex: 1,
      },
      selections: [], //选中的行
      delTableList: [], //被删除的表格数据
      editInfo: {}, // 编辑初始化信息
      tableBodyEle: null, //表格tbody节点
      cloneTableData: [],
      editTableData: [], //编辑的数据
      searchFlag: false,
      type: '',
    };
  },
  computed: {
    /**
     * 计算表格合计进价和合计零售价以及药品总数
     */
    totalData() {
      let jinJiaJE = 0;
      let lingShouJE = 0;
      this.temporary.forEach((item) => {
        jinJiaJE += item.jinJiaJE;
        lingShouJE += item.lingShouJE;
      });
      return {
        yaoPinZS: this.temporary.length ? this.temporary.length : 0,
        jinJiaJE: Number(jinJiaJE).toFixed(this.jinJiaJEXSDW),
        lingShouJE: Number(lingShouJE).toFixed(this.lingShouJEXSDW),
      };
    },

    // 是否显示类型
    isShowLX() {
      return getKuCunGLLX().indexOf('3') > -1;
    },
  },
  async created() {
    await this.jiaoYanWeiBaoCun();
  },
  async mounted() {
    await this.getColumnInit();
    this.id = this.$route.query.id;
    this.caiGouDH = this.$route.query.caiGouDH;
    await this.editInit();
    const res = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'jinJiaXSDWS',
      'lingShouJEXSDWS',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.tableDataOrigin = cloneDeep(this.tableData);
  },
  beforeUnmount() {
    this.stopPrompt();
    this.stopPrompt = null;
  },
  methods: {
    /**
     * 校验数据是否未保存
     * **/
    jiaoYanWeiBaoCun() {
      this.stopPrompt = createPrompt(async (page) => {
        if (
          page.name !== 'xinzengcg' &&
          page.path?.indexOf('/mediinfo-vela-yaoku-web/#/XiuGai') < 0
        ) {
          return;
        }
        if (!isEqual(this.tableData, this.tableDataOrigin)) {
          await MdMessageBox.confirm('有未保存的数据，确定要离开？', '提示', {
            confirmButtonText: '离开',
            cancelButtonText: '取消',
            type: 'warning',
          });
        }

        // 阻止默认提示弹窗
        return {
          healess: true,
        };
      });
    },
    /**
     * 编辑初始化操作,根据传进来的id,来获取采购详情单
     */
    async editInit() {
      if (!this.id) return;
      try {
        this.loading = true;
        const result = await GetCaiGouJHDMX(this.id);
        //赋值
        this.editInfo = result;
        this.beiZhu = result.beiZhu;
        this.zhiDanXX.zhiDanRXM = result.zhiDanRXM;
        this.zhiDanXX.zhiDanRID = result.zhiDanRID;
        this.zhiDanXX.zhiDanSJ = yaoKuZDJZTimeShow(result.zhiDanSJ);
        result.caiGouDMXList.forEach((item) => {
          item.kaiShiSJ = [result.xiaoHaoKSSJ || '', result.xiaoHaoJSSJ || ''];
          item.yaoPinZC =
            item.xianShiXX &&
            item.xianShiXX.tianJiaWZ !== undefined &&
            item.xianShiXX.tianJiaWZ
              ? item.xianShiXX.tianJiaWZ + item.yaoPinMC + ' ' + item.yaoPinGG
              : item.yaoPinMC + ' ' + item.yaoPinGG;
          item.yaoPinMCYGG = {
            baoZhuangDW: item.baoZhuangDW,
            chanDiMC: item.chanDiMC,
            danJia: item.danJia,
            guiGeID: item.guiGeID,
            jiaGeID: item.jiaGeID,
            jinJia: item.jinJia,
            kuCunSL: item.kuCunSL,
            yaoPinGG: item.yaoPinGG,
            yaoPinLXDM: item.yaoPinLXDM,
            yaoPinLXMC: item.yaoPinLXMC,
            yaoPinMC: item.yaoPinMC,
            xianShiXX: item.xianShiXX,
          };
        });
        this.form.kaiShiSJ = result.xiaoHaoKSSJ;
        this.form.jieShuSJ = result.xiaoHaoJSSJ;
        this.editTableData = cloneDeep(result.caiGouDMXList);
        this.temporary = cloneDeep(result.caiGouDMXList);
        this.tableData = result.caiGouDMXList;
        this.tableData.push(initData());
        this.handleSizeChange(this.pageSize);
        //获取已有的采购单的所有的供货单位
        const obj = {};
        this.gongHuoDWOptions = this.editTableData.reduce((prev, item) => {
          if (item.gongHuoDWID && !obj[item.gongHuoDWID]) {
            obj[item.gongHuoDWID] = true;
            prev.push({
              danWeiID: item.gongHuoDWID,
              danWeiMC: item.gongHuoDWMC,
            });
          }
          return prev;
        }, []);
      } finally {
        this.loading = false;
      }
    },
    focusInput(obj) {
      focusInput(obj);
    },
    //药品属性样式
    yaoPinSXStyle(row) {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label =
        row.xianShiXX && row.xianShiXX.tianJiaWZ
          ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC + ' ' + row.yaoPinGG
          : row.yaoPinMC
            ? row.yaoPinMC + ' ' + row.yaoPinGG
            : '';

      const style =
        row.xianShiXX && row.jiaGeID ? tagStyles(row.xianShiXX) : '';
      return [style || '', label || ''];
    },
    /**
     * 选择药品change事件
     */
    handleTableYaoPinDWChange(data, row, index, cellRef) {
      if (!data) {
        Object.assign(row, initData());
        const currentIndex = this.pageSize * (this.currentPage - 1) + index;
        this.temporary[currentIndex] = {
          ...Object.assign(row, initData()),
        };
        // cellRef.endEdit();
        return;
      }
      if (data.jiaGeID === row.jiaGeID) return;
      //判断是否有重复的
      const isRepeat = this.temporary.some((item, i) => {
        if (index == i) return false; //排除自身
        return item.jiaGeID == data.jiaGeID;
      });
      if (isRepeat) {
        MdMessage.warning('药品不可以重复添加');
        Object.assign(row, initData());
        this.$refs.mdEditTable._current();
        return;
      } else {
        this.loading = true;
        GetCaiGouJHDGHXX(
          data.jiaGeID,
          data.guiGeID,
          this.form.kaiShiSJ,
          this.form.jieShuSJ,
        )
          .then((res) => {
            const tianJiaWZ = data.xianShiXX.tianJiaWZ
              ? data.xianShiXX.tianJiaWZ
              : '';
            const assingData = {
              ...res,
              shengPingTBM: res.shengPingTBM, //省平台ID
              yuanNeiBM: res.yuanNeiBM, //院内编码
              liangDingXS: res.liangDingXS, //转换系数
              baoZhuangDW: data.baoZhuangDW,
              chanDiMC: data.chanDiMC,
              chanDiID: data.chanDiID,
              kuCunSL: data.kuCunSL,
              yaoPinGG: data.yaoPinGG,
              yaoPinLXDM: data.yaoPinLXDM,
              yaoPinLXMC: data.yaoPinLXMC,
              yaoPinMC: data.yaoPinMC,
              yaoPinMCYGG: data,
              yaoPinZC: tianJiaWZ + ' ' + data.yaoPinMC + ' ' + data.yaoPinGG,
              jinJia: data.jinJia,
              jinJiaJE: (data.jinJia || 0) * (res.caiGouSL ?? 0),
              lingShouJia: data.danJia,
              lingShouJE: (data.danJia || 0) * (res.caiGouSL ?? 0),
              jiaGeID: data.jiaGeID,
              kuCunSX: res.kuCunSX,
              kuCunXX: res.kuCunXX,
              jiXingID: res.jiXingID,
              jiXingMC: res.jiXingMC,
              baoZhuangLiang: data.baoZhuangLiang,
              zhangBuLBID: res.zhangBuLBID,
              zhangBuLBMC: res.zhangBuLBMC,
              gongHuoDWID: res.gongHuoDWID,
              gongHuoDWMC: res.gongHuoDWMC,
              caiGouSL: res.caiGouSL || 0,
              kaiShiSJ: [this.form.kaiShiSJ, this.form.jieShuSJ],
              xianShiXX: data.xianShiXX,
            };
            cellRef.endEdit();
            // Object.assign(row, assingData);
            this.tableData[index] = { ...Object.assign(row, assingData) };
            const currentIndex = this.pageSize * (this.currentPage - 1) + index;
            this.temporary[currentIndex] = {
              ...Object.assign(row, assingData),
            };
            //获取已有的采购单的所有的供货单位
            const obj = {};
            this.gongHuoDWOptions = this.temporary.reduce((prev, item) => {
              if (item.gongHuoDWID && !obj[item.gongHuoDWID]) {
                obj[item.gongHuoDWID] = true;
                prev.push({
                  danWeiID: item.gongHuoDWID,
                  danWeiMC: item.gongHuoDWMC,
                });
              }
              return prev;
            }, []);
            // if (index === this.tableData.length - 1) {
            //   this.tableData.push(initData());
            // }

            if (
              this.temporary[this.temporary.length - 1].jiaGeID ==
              this.tableData[this.tableData.length - 1].jiaGeID
            ) {
              //  if(this.temporary.length==1) {this.temporary.push(initData());}
              //重新计算当前页
              this.currentPage = this.getMaxPageindex();
              this.handleSizeChange(this.pageSize);
            }
            this.loading = false;
            // this.$refs.mdEditTable._next()
          })
          .catch(() => {
            this.$refs.mdEditTable._current();
          })
          .finally(() => {
            this.loading = false;
          });

        this.type = 1;
      }
    },
    /**
     * 删除采购计划单
     */
    async handleDelete() {
      if (!(Array.isArray(this.selections) && this.selections.length > 0)) {
        return MdMessage.warning('至少选中一条!');
      }
      this.form.kaiShiSJ = dayjs().startOf('month').format('YYYY-MM-DD');
      this.form.jieShuSJ = dayjs().format('YYYY-MM-DD');
      MdMessageBox.confirm('确定删除当前选中的药品?', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        //原有的被删除存储起来,新增的直接被删除
        //新增的一行后删除 jiageid为空 tableLength控制不删除最后一行空行
        // const tableLength = this.tableData.length
        // this.tableData = this.selections.reduce((prev, item) => {
        //   const index = prev.findIndex((i) => {
        //     return i.jiaGeID == item.jiaGeID && i.itemIndex == item.itemIndex;
        //   });
        //   if (index > -1) {
        //     if (prev[index].id) {
        //       this.delTableList.push(prev[index]);
        //     }
        //     prev.splice(index, 1);
        //   }
        //   return prev;
        // }, this.tableData);
        // this.clearHeightLight()
        //删除表格当前页数据和存储的表格数据
        this.temporary = await this.handleDel(this.temporary);
        if (this.temporary.length == 0) {
          this.temporary.push(initData());
        }
        // this.tableData = this.handleDel(this.tableData);
        //处理末尾删除时，数据被清空则回退一页
        this.currentPage =
          this.currentPage > this.getMaxPageindex()
            ? this.getMaxPageindex()
            : this.currentPage;
        this.handleSizeChange(this.pageSize);
      });
    },
    //删除方法抽离
    handleDel(item) {
      return this.selections.reduce((prev, item) => {
        let index = prev.findIndex((i) => {
          return i.jiaGeID == item.jiaGeID;
        });
        //编辑状态存储id
        if (index > -1) {
          const IDindex = this.delTableList.findIndex(
            (fl) => fl.jiaGeID == prev[index].jiaGeID,
          );
          if (prev[index].id && IDindex < 0) {
            this.delTableList.push(prev[index]);
          }
          prev.splice(index, 1);
        }
        return prev;
      }, item);
    },
    // pageSize变化事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.tableData = this.temporary.slice(
        val * (this.currentPage - 1),
        val * this.currentPage,
      );
      //判断表格
      if (
        this.temporary[this.temporary.length - 1].jiaGeID &&
        this.tableData[this.tableData.length - 1].jiaGeID ==
          this.temporary[this.temporary.length - 1].jiaGeID
      ) {
        this.tableData.push(initData());
      }
    },
    // currentPage变化事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.tableData = this.temporary.slice(
        this.pageSize * (val - 1),
        this.pageSize * val,
      );
      //当表格处于最后一页时
      if (
        this.temporary[this.temporary.length - 1].jiaGeID ==
        this.tableData[this.tableData.length - 1].jiaGeID
      ) {
        this.tableData.push(initData());
      }
    },
    //获取最大页数
    getMaxPageindex() {
      return Math.ceil(this.temporary.length / this.pageSize);
    },
    //dom定位
    async tableDomHeightLight(index) {
      let pageIndex = parseInt(index / this.pageSize);
      let page = pageIndex > 0 ? pageIndex + 1 : 1;
      let domIndex = index % this.pageSize;
      await this.handleCurrentChange(page);
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(
          `#caiGouTable .mediinfo-vela-yaoku-web-base-table__body-wrapper .mediinfo-vela-yaoku-web-base-table__body tbody tr`,
        );
      }
      // 获取高亮dom节点
      const childDom = document.querySelectorAll(
        `#caiGouTable .mediinfo-vela-yaoku-web-base-table__body-wrapper .mediinfo-vela-yaoku-web-base-table__body tbody tr`,
      )[domIndex];

      childDom.classList.add(this.prefixClass('heightLight'));
      this.loading = false;
      return { page, domIndex };
    },
    /**
     * table选中触发的事件
     */
    selectionChange(selection) {
      this.selections = selection;
    },
    //升降序
    handleSortChange({ column, prop, order }) {
      if (this.temporary.length <= 1) return;
      if (order) {
        //删除最后一个空白行
        if (!this.temporary[this.temporary.length - 1].jiaGeID)
          this.temporary.splice(this.temporary.length - 1, 1);
        this.temporary = sortBy(this.temporary, (item) => {
          if (prop === 'yaoPinMCYGG') {
            const pinyin = makePY(item.yaoPinMC);
            if (pinyin) return pinyin.toLocaleUpperCase();
          } else if (prop === 'yuJiXHTS') {
            return item.yuJiXHTS;
          }
          // // 获取中文首字母
          // const pinyin =
          //   prop === 'yaoPinMCYGG' ? makePY(item.yaoPinMC) : makePY(prop);
          //   console.log(pinyin,'pinyin')
          // if (pinyin) return pinyin.toLocaleUpperCase();
        });
        if (order === 'descending') {
          this.temporary.reverse();
        }
        this.handleCurrentChange(this.currentPage);
      }
    },
    tableRowClassName({ row, rowIndex }) {
      row.itemIndex = rowIndex;
    },
    /**
     * 申请数量变化事件,用来修改金额和零售金额
     */
    handleCaiGouSL(event, row, indexs) {
      if (isNaN(event) || event < 0) {
        row.caiGouSL = 0;
        row.jinJiaJE = 0;
        row.lingShouJE = 0;
      } else {
        // 有临时用药标志且不是紧急标志的，判断可采购数量
        if (
          row.linShiYYBZ &&
          !row.jinJiCGBZ &&
          Number(event) > Number(row.keCaiGSL)
        ) {
          return false;
        }
        // 修改采购数量要同步更新分页的总数据 temporary
        const index = this.temporary.findIndex(
          (fl) => fl.jiaGeID == row.jiaGeID,
        );
        if (index > -1) {
          this.temporary[index].caiGouSL = event;
          this.temporary[index].jinJiaJE = event * row.jinJia || 0;
          this.temporary[index].lingShouJE = event * row.lingShouJia || 0;
          this.temporary[index].shenQingSL = event;
        }
        if (this.type == 0 || (this.id && this.type !== 1)) {
          //采购下单数量=采购数量/两顶系数 ，小数取2
          row.caiGouXDSL =
            event && row.liangDingXS
              ? (Number(event) / Number(row.liangDingXS)).toFixed(2)
              : 0;
          if (index > -1) {
            this.temporary[index].caiGouXDSL = row.caiGouXDSL;
          }
        }
        row.jinJiaJE = event * row.jinJia || 0;
        row.lingShouJE = event * row.lingShouJia || 0;
        row.shenQingSL = event;
      }
    },
    handleChangeCGSL(event, row, index) {
      // 有临时用药标志且不是紧急标志的，判断可采购数量
      if (
        row.linShiYYBZ &&
        !row.jinJiCGBZ &&
        Number(event) > Number(row.keCaiGSL)
      ) {
        row.caiGouSL = 0;
        MdMessage.warning(
          `第${index + 1}行${row.yaoPinMC}${row.yaoPinGG}，采购数量 不可大于 可采购数量`,
        );
        return this.$refs.mdEditTable._current();
      }
    },
    //修改采购下单数量
    handleCaiGouXDSL(event, row, ziDuanName) {
      const index = this.temporary.findIndex((fl) => fl.jiaGeID == row.jiaGeID);
      if (index > -1) {
        this.temporary[index][ziDuanName] = event;
      }
    },
    /**
     * 根据供货单位来搜索
     */
    handleFilterSearch() {
      if (!this.gongHuoDWID) return;
      //初始克隆一次
      if (!this.searchFlag) {
        this.searchFlag = true;
        this.cloneTableData = cloneDeep(this.tableData);
      }
      this.tableData = this.cloneTableData.filter((item) => {
        return item.gongHuoDWID == this.gongHuoDWID;
      });
      this.popoverValue = false;
    },
    /**
     * 清空搜索,返回之前的数据
     */
    handleClear() {
      this.gongHuoDWID = '';
      this.tableData = this.cloneTableData;
      if (this.tableData.length == 0) this.tableData.push(initData());
      this.searchFlag = false;
      this.popoverValue = false;
    },
    /**
     * 保存采购计划单
     */
    async handleSave(type) {
      //根据药品数来判断是否有药品
      if (this.totalData.yaoPinZS == 0) {
        return MdMessage.warning('采购单列表为空！');
      }
      //判断列表是否有空值
      const invalidRowIndex = this.temporary.findIndex((item, index) => {
        // if (index == this.temporary.length - 1) return false;
        return !(item.jiaGeID && item.gongHuoDWID && item.caiGouSL > 0);
      });
      const invalidProp = [
        { value: 'jiaGeID', label: '药品名称', key: 'yaoPinMCYGG' },
        { value: 'gongHuoDWID', label: '供货单位', key: 'gongHuoDWMC' },
        { value: 'caiGouSL', label: '采购数量', key: 'caiGouSL' },
      ];
      if (invalidRowIndex > -1) {
        //算出第几行是参数空行
        const { page, domIndex } =
          await this.tableDomHeightLight(invalidRowIndex);

        for (const item of invalidProp) {
          if (
            !this.tableData[domIndex][item.value] ||
            this.tableData[domIndex][item.value] == 0
          ) {
            focusEditTableDom({
              rowIndex: domIndex,
              columns: this.columns,
              key: item.key,
            });
            return MdMessage.error(
              `第${page}页第${invalidRowIndex + 1}行,${item.label}为空`,
            );
          }
        }
        return;
      }
      try {
        this.loading = true;
        const cloneTableData = cloneDeep(this.temporary);
        // cloneTableData.splice(cloneTableData.length - 1, 1);
        cloneTableData.forEach((item, index) => {
          delete item.yaoPinMCYGG;
          // delete item.gongHuoDWMC;
          // delete item.kuCunSX
          // delete item.kuCunXX
          item.shunXuHao = index + 1;
        });
        const params = {
          beiZhu: this.beiZhu,
          piFaJE: 0,
          jinJiaJE: Number(this.totalData.jinJiaJE),
          lingShouJE: Number(this.totalData.lingShouJE),
          yaoPinZS: this.totalData.yaoPinZS,
          zhiDanSJ: dayjs().format('YYYY-MM-DD hh:mm:ss'),
          zhiDanRID: this.zhiDanXX.zhiDanRID,
          zhiDanRXM: this.zhiDanXX.zhiDanRXM,
          xiaoHaoTS: this.form.xiaoHaoSJ
            ? this.form.xiaoHaoSJ
            : dayjs(this.kaiShiSJRow[1]).format('DD'),
          xiaoHaoKSSJ: this.form.kaiShiSJ
            ? this.form.kaiShiSJ
            : this.kaiShiSJRow[0],
          xiaoHaoJSSJ: this.form.jieShuSJ
            ? this.form.jieShuSJ
            : this.kaiShiSJRow[1],
        };
        let caiGouDID = null;
        if (!this.id) {
          params.tiJiaoBZ = 1;
          params.caiGouDMXList = cloneTableData;
          caiGouDID =
            type == 1
              ? await AddCaiGouJHD(params)
              : await BaoCunBTJCGJHD(params);
        } else {
          params.id = this.id;
          params.caiGouDH = this.caiGouDH;
          params.guanLianDJID = this.editInfo.guanLianDJID;
          params.guanLianDJLX = this.editInfo.guanLianDJLX;
          params.addCaiGouDMXList = cloneTableData.filter((item) => {
            return !item.id;
          });
          params.updateCaiGouDMXList = this.temporary.filter((item) => {
            return this.editTableData.some(
              (i) => i.id === item.id && !isEqual(item, i),
            );
          });
          params.delCaiGouDMXList = this.delTableList.map((item) => item.id);
          caiGouDID = this.id;
          if (type === 2) {
            await BaoCunBTJBJCGJHD(params);
          } else await UpdateCaiGouJHD(params);
        }
        // await this.handlePrint(caiGouDID)
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        eventBus.$emit('handleFetchCaiGou');
        this.closeTab();
      } finally {
        this.loading = false;
      }
    },
    /**
     * 用于保存成功关闭当前tab页面
     */
    closeTab() {
      const closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      this.viewManager.close(closeTabKey);
      // if (isMounted) {
      //   this.$router.push({
      //     name: 'CaiGouJH'
      //   })
      // }
    },
    /**
     * 按库存下限生成
     */
    async hanldeAnKunCSC() {
      await MdMessageBox({
        title: '提示',
        message: '按库存下限生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      this.$refs.AnXiaoHLSCDialog.showModal(1);
    },
    //按消耗量生成按钮
    async hanldeAnXiaoHLSC() {
      await MdMessageBox({
        title: '提示',
        message: '按消耗量生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      await this.$refs.AnXiaoHLSCDialog.showModal(0);
    },
    /**
     * 按消耗库存比生成药品
     * **/
    async handleAnXiaoHKCBSC() {
      await MdMessageBox({
        title: '提示',
        message: '按消耗库存比生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      await this.$refs.anXiaoHaoKCBDialog.showModel();
    },
    async handleMingXiZXHSC() {
      await MdMessageBox({
        title: '提示',
        message: '按明细账消耗生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      await this.$refs.anMingXiZXHDialog.showModel();
    },
    //处理按库存弹窗保存后的数据  type库存下线1，消耗量0
    handleSaveData(res, form, type) {
      if (res.length == 0) {
        this.tableData = [initData()];
        this.temporary = [initData()];
        this.handleSizeChange(this.pageSize);
        return;
      }
      this.type = type;
      const riqi =
        type == 1
          ? [
              dayjs().startOf('month').format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ]
          : [form.kaiShiSJ, form.jieShuSJ];
      res.forEach((item) => {
        item.kaiShiSJ = riqi;
        item.yaoPinZC =
          item.xianShiXX &&
          item.xianShiXX.tianJiaWZ !== undefined &&
          item.xianShiXX.tianJiaWZ
            ? item.xianShiXX.tianJiaWZ + item.yaoPinMC + ' ' + item.yaoPinGG
            : item.yaoPinMC + ' ' + item.yaoPinGG;
        item.yaoPinMCYGG = {
          ...item,
          baoZhuangDW: item.baoZhuangDW,
          chanDiMC: item.chanDiMC,
          danJia: item.danJia,
          guiGeID: item.guiGeID,
          jiaGeID: item.jiaGeID,
          jinJia: item.jinJia,
          kuCunSL: item.kuCunSL,
          yaoPinGG: item.yaoPinGG,
          yaoPinLXDM: item.yaoPinLXDM,
          yaoPinLXMC: item.yaoPinLXMC,
          yaoPinMC: item.yaoPinMC,
        };
      });
      this.delTableList = this.temporary.filter((item) => {
        return item.id;
      });
      this.form = {
        xiaoHaoSJ: form.xiaoHaoSJ,
        kaiShiSJ: riqi[0],
        jieShuSJ: riqi[1],
      };
      this.tableData = res;
      this.temporary = cloneDeep(res);
      // this.temporary.push(initData());
      this.handleSizeChange(this.pageSize);
      //获取已有的采购单的所有的供货单位
      const obj = {};
      this.gongHuoDWOptions = this.temporary.reduce((prev, item) => {
        if (item.gongHuoDWID && !obj[item.gongHuoDWID]) {
          obj[item.gongHuoDWID] = true;
          prev.push({
            danWeiID: item.gongHuoDWID,
            danWeiMC: item.gongHuoDWMC,
          });
        }
        return prev;
      }, []);
    },
    //点击行消耗量
    handleRowXHL(val, row) {
      const params = {
        jiaGeID: row.jiaGeID,
        kaiShiSJ: row.kaiShiSJ[0],
        jieShuSJ: row.kaiShiSJ[1],
      };
      GetYaoPinXHLMXList(params).then((res) => {
        row.list = res;
      });
    },
    hanldeZongHeSC() {
      MdMessageBox({
        title: '提示',
        message: '综合生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          this.loading = true;
          const result = await GetZongHeSCCGJHList();
          result.forEach((item) => {
            item.yaoPinMCYGG = {
              baoZhuangDW: item.baoZhuangDW,
              chanDiMC: item.chanDiMC,
              danJia: item.danJia,
              guiGeID: item.guiGeID,
              jiaGeID: item.jiaGeID,
              jinJia: item.jinJia,
              kuCunSL: item.kuCunSL,
              yaoPinGG: item.yaoPinGG,
              yaoPinLXDM: item.yaoPinLXDM,
              yaoPinLXMC: item.yaoPinLXMC,
              yaoPinMC: item.yaoPinMC,
            };
          });
          this.delTableList = this.tableData.filter((item) => {
            return item.id;
          });

          this.tableData = result;
          this.tableData.push(initData());
        } finally {
          this.loading = false;
        }
      });
    },
    handleBlur(row, cellRef) {
      cellRef.endEdit();
    },
    selectChange(value, cellRef) {
      if (!value) {
        setTimeout(() => {
          cellRef.endEdit();
        }, 100);
      }
    },
    /**
     * 表格供货单位选中
     */
    handleSelect(data, row) {
      if (data) {
        const result = this.gongHuoDWOptions.find((item) => {
          return item.danWeiID == data;
        });
        row.gongHuoDWMC = result.danWeiMC;
      } else {
        row.gongHuoDWMC = '';
        row.gongHuoDWID = '';
      }
    },
    /**
     * 供货单位远程搜索
     */
    remoteGongHuoDW(value) {
      this.searchFormData.LikeQuery = value;
      this.searchFormData.PageIndex = 1;
      this.gongHuoDWOptions = [];
      this.getGongHuoDW();
    },
    /**
     * 供货单位下拉加载更多
     */
    loadMore() {
      this.searchFormData.PageIndex++;
      if (this.gongHuoFlag) {
        // this.getGongHuoDW();
      }
    },
    /**
     * 获取供货单位列表
     */
    getGongHuoDW: debounce(function () {
      const { LikeQuery, PageIndex, PageSize } = this.searchFormData;
      GetGongHuoDWList(LikeQuery, PageIndex, PageSize).then((res) => {
        if (res.length < 10) {
          this.gongHuoFlag = false;
        }
        const gongHuoDWOptions = this.gongHuoDWOptions.concat(res);
        this.gongHuoDWOptions = this.gongHuoDWUnion(gongHuoDWOptions);
      });
    }, 300),
    /**
     * 供货单位去重处理
     */
    gongHuoDWUnion(data) {
      const obj = {};
      const gongHuoDWOptions = data.reduce((prev, next) => {
        if (obj[next.danWeiID]) {
          const index = prev.findIndex(
            (item) => next.danWeiID === item.danWeiID,
          );
          if (index > -1) {
            prev.splice(index, 1, next);
          }
        } else {
          obj[next.danWeiID] = true;
          prev.push(next);
        }
        return prev;
      }, []);
      return gongHuoDWOptions;
    },
    /**
     * 药品定位
     */
    async handleDingWei(data) {
      if (!data) return;
      if (!(Array.isArray(this.tableData) && this.tableData.length > 1)) {
        MdMessage.warning('采购单列表为空,不能定位！');
        return;
      }
      //先清除上次高亮
      this.clearHeightLight();
      const index = this.tableData.findIndex((item) => {
        return item.yaoPinMC == data.yaoPinMC;
      });
      if (index == -1) {
        return MdMessage.warning('未找到该药品');
      } else {
        //获取高亮dom节点
        if (!this.tableBodyEle) {
          this.tableBodyEle = document.querySelector(
            `#caiGouTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
          );
        }
        const childDom = this.tableBodyEle.querySelectorAll(
          `.mediinfo-vela-yaoku-web-base-table__row`,
        )[index];
        childDom.classList.add(this.prefixClass('heightLight'));
        childDom.scrollIntoView();
      }
    },
    /**
     * 清除table高亮
     */
    clearHeightLight() {
      const rowList = Array.from(
        document.querySelectorAll(
          `#caiGouTable .mediinfo-vela-yaoku-web-base-table__row`,
        ),
      );
      rowList.forEach((item) => {
        item.classList.remove(this.prefixClass('heightLight'));
      });
    },
    cellStyle({ row, column }) {
      if (row.ruKuMXDID) {
        return { backgroundColor: '#f5f5f5' };
      }
      if (Number(row.kuCunSL) == 0 && column.property === 'kuCunSL') {
        return { backgroundColor: '#ffd2cc' };
      }
    },
    handleFocus(row) {
      if (row.gongHuoDWID) {
        // const gongHuoDWOptions = [
        //   {
        //     danWeiID: row.gongHuoDWID,
        //     danWeiMC: row.gongHuoDWMC,
        //   },
        // ].concat(this.gongHuoDWOptions);
        // this.gongHuoDWOptions = this.gongHuoDWUnion(gongHuoDWOptions);
      }
    },
    async handlePrint(caiGouDID) {
      try {
        this.loading = true;
        await printByUrl('YKXT001', { id: caiGouDID });
        MdMessage.success('打印成功！');
      } catch (e) {
        //Message.error(e.message || '打印失败！')
        //暂时先提示打印失败
        // Message.error('打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: '打印失败！',
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
      }
    },
    //点击消耗量显示浮窗
    // onClickOutside() {
    //   unref(this.$refs.popoverRef).popperRef?.delayHide?.();
    // },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
    'anxiaohaokcb-dialog': AnXiaoHaoKCBDialog,
    'an-xiao-hao-l-dialog': AnXiaoHLSCDialog,
    'an-mingxiz-xh-dialog': AnMingXiZXHDialog,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-select-div {
  // height: 150px;
  overflow-y: auto;
  width: 200px;
}

.xiaohao-color {
  color: #1385f0;
  cursor: pointer;
  display: block;
  height: 100%;
}

.#{$md-prefix}-procurement {
  height: 100%;
  background: #eaeff3;
  padding: 8px;
  box-sizing: border-box;
}

.#{$md-prefix}-procurement-content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .#{$md-prefix}-pagination {
    display: flex;
    justify-content: end;
    padding-right: 8px;
    padding-bottom: 0px;
  }

  .#{$md-prefix}-procurement-top {
    padding: 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    // background-color: #edf6fd;
    background-color: rgb(var(--md-color-1));

    .#{$md-prefix}-procurement-left {
      display: flex;
      align-items: center;

      .#{$md-prefix}-procurement-left-lt {
        height: 38px;
        // width: 322px;
        // margin-right: 8px;
        // background-color: #e3ecf3;
        border-radius: 4px;
        float: left;
        justify-content: center;
        align-content: center;
        box-sizing: border-box;
        // padding-top: 4px;

        .#{$md-prefix}-omti {
          float: left;
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          // background-color: #f3f7fa;
          background-color: #fff;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 4px;
        }

        .#{$md-prefix}-caigouJH-title {
          float: left;
          color: rgb(var(--md-color-8));
          font-size: 16px;
          font-weight: bold;
          line-height: 28px;
          // padding-left: 23px;
          margin: 0 8px;
          position: relative;

          i {
            font-size: 18px;
          }
        }

        .#{$md-prefix}-input-seach {
          width: 180px;
          // background-color: #f3f7fa;
          background: #fff;
          float: left;
          border-radius: 4px;
        }
      }

      .#{$md-prefix}-procurement-left-rt {
        display: flex;
        justify-content: flex-start;
        margin-left: 8px;
        overflow: hidden;

        span {
          float: left;
          height: 30px;
          line-height: 30px;
          margin-right: 8px;
          padding: 0 8px;
          background-color: #ffffff;
          border: 1px solid #c3e5fd;
          border-color: rgb(var(--md-color-6));
          border-radius: 4px;
          // color: #1e88e5;
          color: rgb(var(--md-color-6));
          font-size: 14px;
          cursor: pointer;

          &:hover {
            background-color: rgb(var(--md-color-1));
          }
        }
      }
    }

    .#{$md-prefix}-procurement-right {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 28px;

      .#{$md-prefix}-main5 {
        margin: 0 2.5px;
      }

      span {
        color: #1e88e5;
        font-size: 14px;
        margin-left: 5px;
        line-height: 38px;

        i {
          margin-right: 5px;
        }
      }

      .#{$md-prefix}-save-btn {
        margin-left: 8px;
        height: 30px;
      }
    }
  }

  .#{$md-prefix}-procurement-table {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      z-index: 99;
      padding: 0 8px;
      overflow: hidden;
    }
  }

  .#{$md-prefix}-note {
    display: flex;
    justify-content: flex-start;
    margin: 7px 0;

    .#{$md-prefix}-note-title {
      line-height: 30px;
      padding: 0 11px;
      color: #333333;
      font-size: 14px;
    }

    .#{$md-prefix}-input-note {
      flex: 7;
      margin-right: 10px;
    }
  }
}

.#{$md-prefix}-drop-down-pop {
  .#{$md-prefix}-top-pop {
    display: flex;
    align-items: center;
    overflow: hidden;
    box-sizing: border-box;

    .#{$md-prefix}-item-inline-label {
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      line-height: 30px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .#{$md-prefix}-select {
      flex: 1;
    }

    .#{$md-prefix}-gonghuo-header {
      width: 195px;
      float: left;
    }

    .#{$md-prefix}-select-input {
      float: left;
      width: 175px;
      box-sizing: border-box;
    }
  }

  .#{$md-prefix}-pop-btn {
    text-align: right;
    margin-top: 10px;
  }
}

.#{$md-prefix}-icondanju {
  position: absolute;
  left: 0;
  top: 4px;
}

.#{$md-prefix}-caigou-footer {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  flex-shrink: 0;
  line-height: 20px;
  font-size: 14px;

  &__info {
    display: flex;

    .#{$md-prefix}-info__name {
      margin-right: 8px;
    }

    .#{$md-prefix}-margin-right-12 {
      margin-right: 12px;
    }

    &.#{$md-prefix}-right {
      span {
        color: #aaa;
      }
    }
  }

  span {
    color: #666666;

    &.#{$md-prefix}-color-222 {
      color: #222222;
    }

    &.#{$md-prefix}-font-bold {
      font-weight: 600;
      color: #222222;
    }
  }
}

::v-deep .#{$md-prefix}-base-table__body-wrapper {
  overflow: auto;
  flex: 1;

  .#{$md-prefix}-base-table__row.#{$md-prefix}-heightLight {
    > td {
      background: #e2efff;
    }
  }
}

::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}
</style>
