<template>
  <!--  <md-frameset :class="prefixClass('frameset')">-->
  <div :class="prefixClass('caigou-content')">
    <div :class="prefixClass('procurement-top')">
      <div :class="prefixClass('procurement-left')">
        <md-date-picker-range-pro
          :class="prefixClass('procurement-date')"
          v-model="query.timeRange"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleSearch"
        >
        </md-date-picker-range-pro>
        <md-input
          v-model="query.danJuHao"
          placeholder="输入单据号搜索"
          :suffix-icon="prefixClass('icon-seach cursor-pointer')"
          @keyup.enter.native="handleSearch"
        >
        </md-input>
        <biz-yaopindw
          v-model="yaoPinMCObj"
          placeholder="名称、输入码、别名、规格等关键字进行搜索"
          style="width: 340px; margin-left: 8px; flex-shrink: 0"
          @change="handleQueryYaoPinMC"
        >
        </biz-yaopindw>
      </div>
      <md-button
        type="primary"
        :icon="prefixClass('icon-shuaxin')"
        noneBg
        style="margin-left: auto"
        @click="handleSearch"
        >刷新</md-button
      >
      <md-button
        type="primary"
        :icon="prefixClass('icon-xinzeng')"
        noneBg
        @click="handleKaiDan"
        >开单</md-button
      >
    </div>
    <div :class="prefixClass('caigou-table')">
      <md-table-pro
        :columns="columns"
        :onFetch="handleFetch"
        height="100%"
        :stripe="false"
        :class="prefixClass('table')"
        ref="table"
      >
        <template v-slot:caiGouDH="{ row }">
          <md-tooltip
            trigger="hover"
            effect="light"
            :popper-class="prefixClass('his-cai-gou-dan')"
          >
            <template #content>
              <div @click="copy(row.caiGouDH)" :class="prefixClass('fuzhi')">
                复制
              </div>
            </template>
            <!-- <template v-slot:reference> -->
            <span
              :class="prefixClass('caigoudh')"
              @click="handleRowClick($event, row)"
              >{{ row.caiGouDH }}</span
            >
            <!-- </template> -->
          </md-tooltip>
          <span
            v-if="row.guanLianDJLX == 1"
            :class="prefixClass('public-span ling')"
            >领</span
          >
          <span
            v-if="row.guanLianDJLX == 2"
            :class="prefixClass('public-span ji')"
            >急</span
          >
        </template>
        <template v-slot:yaoPinMXMCList="{ row }">
          <biz-tag-list :list="row.yaoPinMXMCList" label-key="yaoPinMC">
          </biz-tag-list>
        </template>
        <template v-slot:zhuangTai="{ row }">
          <md-tag v-if="row.danJuZTDM == 1" type="primary">待提交</md-tag>
          <md-tag v-if="row.danJuZTDM == 2" type="warning">待审核</md-tag>
          <md-tag v-if="row.danJuZTDM == 3" type="success">已通过</md-tag>
          <md-tag v-if="row.danJuZTDM == 4" type="danger">已拒绝</md-tag>
          <md-tag v-if="row.danJuZTDM == 5" type="danger">已拒收</md-tag>
        </template>
        <template v-slot:operate="{ row }">
          <md-button
            type="text-bg"
            @click="handleEdit($event, row, 'submit')"
            v-if="row.danJuZTDM == 1"
          >
            提交
          </md-button>
          <md-button
            type="text-bg"
            @click="handleEdit($event, row, 'edit')"
            v-if="row.danJuZTDM == 1"
          >
            编辑
          </md-button>
          <md-button
            type="text-bg"
            @click="handleEdit($event, row, 'back')"
            v-if="row.danJuZTDM == 2"
          >
            撤回
          </md-button>
          <md-button
            type="text-bg"
            @click="handleEdit($event, row, 'check')"
            v-if="
              row.danJuZTDM == 4 || row.danJuZTDM == 3 || row.danJuZTDM == 5
            "
          >
            查看
          </md-button>
          <md-button
            v-if="row.danJuZTDM != 3"
            type="danger"
            noneBg
            @click="handleZuoFei(row)"
          >
            作废
          </md-button>
        </template>
      </md-table-pro>
    </div>
  </div>
  <xiangqiangdan ref="caigoudetail" size="75%" @refresh="handleSearch" />
  <!--  </md-frameset>-->
</template>
<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { logger } from '@/service/log';
import {
  CheHuiCGJHDXX,
  GetCaiGouJHDCount,
  GetCaiGouJHDList,
  TiJiaoCGJHDXX,
  ZuoFeiCaiGouJHD,
} from '@/service/yaoPinYK/caiGouJH.js';
import { MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import BizTagList from '../../../components/BizTagList';
import eventBus from '../../../system/utils/eventbus';
import XiangQingDan from './components/XiangQingDan';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';

export default {
  name: 'caigoujh',
  data() {
    return {
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      yaoPinMCObj: null,
      query: {
        jiaGeID: '',
      },
      columns: [
        {
          slot: 'caiGouDH',
          prop: 'caiGouDH',
          label: '采购单',
          width: 142,
        },
        {
          slot: 'yaoPinZS',
          prop: 'yaoPinZS',
          label: '药品数',
          align: 'right',
          width: 60,
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },
        {
          slot: 'yaoPinMXMCList',
          prop: 'yaoPinMXMCList',
          label: '药品明细',
          minWidth: 200,
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 108,
          formatter: (row, column, cellValue, index) => {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 108,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          width: 108,
          showOverflowTooltip: true,
        },
        {
          prop: 'shenHeRXM',
          label: '审核人',
          width: 108,
          showOverflowTooltip: true,
        },
        {
          prop: 'zhuangTai',
          slot: 'zhuangTai',
          label: '审核状态',
          width: 108,
          align: 'center',
          showOverflowTooltip: true,
        },
        {
          slot: 'operate',
          type: 'operate',
          label: '操作',
          fixed: 'right',
          width: 130,
        },
      ],
    };
  },
  async mounted() {
    this.query = {
      jiaGeID: '',
      timeRange: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      danJuHao: '', //单据号
    };
    this.handleSearch();
    eventBus.$on('handleFetchCaiGou', this.handleSearch);
    const res = await getKuFangSZList(['jinJiaJEXSDWS', 'lingShouJEXSDWS']);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  },
  beforeUnmount() {
    eventBus.$off('handleFetchCaiGou', this.handleSearch);
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          // console.log(err);
        });
    },
    async handleFetch({ page, pageSize }, config) {
      const { danJuHao, timeRange, jiaGeID } = this.query;
      const params = {
        jiaGeID,
        CaiGouDH: danJuHao,
        pageIndex: page,
        pageSize: pageSize < 0 ? 1 : pageSize,
      };
      // if (Array.isArray(timeRange) && timeRange.length > 0) {
      //   params.zhiDanSJKS = timeRange[0] ? timeRange[0] : null;
      //   params.zhiDanSJJS = timeRange[1] ? timeRange[1] : null;
      // }
      params.zhiDanSJKS = timeRange[0]
        ? dayjs(timeRange[0]).format('YYYY-MM-DD 00:00:00')
        : null;
      params.zhiDanSJJS = timeRange[1]
        ? dayjs(timeRange[1]).format('YYYY-MM-DD 23:59:59')
        : null;
      //获取列表
      const [items, total] = await Promise.all([
        GetCaiGouJHDList(params),
        GetCaiGouJHDCount(params),
      ]);
      return { items, total };
    },
    //调用table刷新
    handleSearch() {
      if (this.$refs.table) {
        this.$refs.table.search({ pageSize: 100 });
      } else {
        this.$nextTick(() => {
          this.$refs.table.search({ pageSize: 100 });
        });
      }
    },
    //新增采购计划单
    handleKaiDan() {
      this.$router.push({
        name: 'XinZengCGA',
      });
    },
    //编辑采购计划单
    async handleEdit(e, row, type) {
      switch (type) {
        case 'edit':
          this.$router.push({
            name: 'XiuGai',
            query: {
              title: '采购单-' + row.caiGouDH,
              id: row.id,
              caiGouDH: row.caiGouDH,
            },
          });
          break;
        case 'check':
          e.stopPropagation();
          this.$refs.caigoudetail.openDrawer(row);
          break;
        case 'back':
          await CheHuiCGJHDXX(row.id);
          this.handleSearch();
          break;
        case 'submit':
          await TiJiaoCGJHDXX(row.id);
          this.handleSearch();
          break;
      }
    },
    //点击采购单号--打开侧滑
    handleRowClick(e, row) {
      e.stopPropagation();
      this.$refs.caigoudetail.openDrawer(row);
    },
    //复制
    // handleCopy() {
    //   this.$message({
    //     type: 'success',
    //     message: '复制成功',
    //   });
    // },
    handleZuoFei(row) {
      MdMessageBox.confirm('是否要作废此单据？', '操作提醒！', {
        cancelButtonText: '否',
        confirmButtonText: '是',
        type: 'warning',
      })
        .then(async () => {
          try {
            await ZuoFeiCaiGouJHD(row.id);
            this.$message({
              type: 'success',
              message: '作废成功！',
            });
            this.handleSearch();
          } catch (err) {
            console.error(err);
            logger.error(err);
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消作废',
          });
        });
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.query.jiaGeID = e.jiaGeID || '';
      this.handleSearch();
    },
  },
  components: {
    'biz-tag-list': BizTagList,
    xiangqiangdan: XiangQingDan,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
}

.#{$md-prefix}-his-cai-gou-dan {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  float: left;

  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-caigoudh {
  float: left;
  margin-right: 5px;
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}

.#{$md-prefix}-frameset {
  padding-top: 0 !important;
  min-height: 0;
  min-width: 0;
  display: flex;
  flex: 1;
  height: auto;
  margin: 0 !important;
}

.#{$md-prefix}-caigou-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0 8px;
  min-height: 0;
  min-width: 0;

  .#{$md-prefix}-procurement-top {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .#{$md-prefix}-procurement-left {
    display: flex;
    align-items: center;
  }

  .#{$md-prefix}-procurement-date {
    margin-right: 12px;
    min-width: 250px;
  }

  .#{$md-prefix}-caigou-table {
    flex: 1;
    min-height: 0;
    display: flex;

    .#{$md-prefix}-table {
      flex: 1;
      min-height: 0;
      min-width: 0;
    }
  }

  .#{$md-prefix}-public-span {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
  }

  .#{$md-prefix}-ling {
    background-color: #1385f0;
  }

  .#{$md-prefix}-ji {
    background-color: #ff8600;
  }
}

::v-deep .has-gutter {
  .cell {
    font-weight: bold;
    font-size: 14px;
  }
}

::v-deep .cell {
  color: #222222;
}
</style>
