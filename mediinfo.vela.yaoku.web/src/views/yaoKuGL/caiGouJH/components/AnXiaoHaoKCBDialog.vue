<template>
  <bmis-blue-dialog
    v-model:visible="visible"
    width="560px"
    height="400px"
    title="按消耗库存比生成"
    @submit="handleSave"
    appendToBody
    :closeOnClickModal="false"
    save-text="生成"
  >
    <div>
      <md-form v-loading="loading" use-status-icon label-width="85px">
        <md-form-item label="消耗时间">
          <md-input
            v-model="formModel.xiaoHaoSJ"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @change="handleInput"
            :clearable="false"
          >
            <template #suffix>
              <i class="md-input__icon icon-text">天内</i>
            </template>
          </md-input>
        </md-form-item>
        <md-form-item label="消耗日期">
          <md-date-picker-com
            v-model="formModel.time"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleTimeChange"
          >
          </md-date-picker-com>
        </md-form-item>
        <md-form-item label-width="160px">
          <template #label>
            <span>
              消耗库存比小于等于
              <md-tooltip
                effect="dark"
                content="消耗库存比=当前位置库存/目标时间段内当前位置消耗量（包括药房未记账数量）"
                placement="bottom"
              >
                <md-icon name="shuoming"></md-icon>
              </md-tooltip>
            </span>
          </template>
          <md-input
            v-model="formModel.xiaoHaoKCB"
            placeholder="输入消耗库存比"
            v-number.float="{ decimal: 2 }"
          >
          </md-input>
        </md-form-item>
        <md-form-item label="对照省平台">
          <md-radio-group
            v-model="formModel.duiZhaoSPT"
            border
            style="width: 100%"
          >
            <md-radio v-for="item in duiZhaoSPTOptions" :label="item.label">
              {{ item.value }}
            </md-radio>
          </md-radio-group>
        </md-form-item>
        <md-form-item label="药品属性">
          <md-checkbox-group
            v-model="formModel.yaoPinSXList"
            :border="true"
            style="width: 100%"
          >
            <md-checkbox
              v-for="item in yaoPingSXOptions"
              :label="item.label"
              :key="item.label"
            >
              {{ item.value }}
            </md-checkbox>
          </md-checkbox-group>
        </md-form-item>
      </md-form>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import BlueDialog from '@/components/blue-dialog/index.vue';
import { pick } from 'lodash';
import dayjs from 'dayjs';

import { GetKuCunXXYPByYLList } from '@/service/yaoPinYK/caiGouJH.js';
const formModelInit = () => {
  return {
    xiaoHaoSJ: 3,
    time: [
      dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    xiaoHaoKCB: null,
    duiZhaoSPT: '',
    yaoPinSXList: [],
  };
};
export default {
  name: 'anxiaohaokcb-dialog',
  data() {
    return {
      loading: false,
      visible: false,
      formModel: formModelInit(),
      //对照省平台数据源
      duiZhaoSPTOptions: [
        {
          label: '0',
          value: '全部',
        },
        {
          label: '1',
          value: '已对照',
        },
        {
          label: '2',
          value: '未对照',
        },
      ],
      //药品属性数据源
      yaoPingSXOptions: [
        { label: '1', value: '国谈' },
        { label: '2', value: '毒麻精' },
        { label: '3', value: '集采' },
        { label: '4', value: '临时' },
      ],
    };
  },

  methods: {
    showModel() {
      // 初始化
      this.formModel = formModelInit();
      this.loading = false;
      this.visible = true;

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },

    async handleSave() {
      const params = {
        // ...pick(this.formModel, ['duiZhaoSPT', 'yaoPinSXList']),
        duiZhaoSPT: this.formModel.duiZhaoSPT,
        xiaoHaoKCB: Number(this.formModel?.xiaoHaoKCB) || null,
        kaiShiSJ: dayjs(this.formModel?.time?.[0])?.format('YYYY-MM-DD') || '',
        jieShuSJ: dayjs(this.formModel?.time?.[1])?.format('YYYY-MM-DD') || '',
        yaoPinSXs: this.formModel.yaoPinSXList.join('|'),
        // baoHanYKKCL: false,
      };
      this.loading = true;
      this.formModel.kaiShiSJ = dayjs(this.formModel?.time?.[0])?.format(
        'YYYY-MM-DD',
      );
      this.formModel.jieShuSJ = dayjs(this.formModel?.time?.[1])?.format(
        'YYYY-MM-DD',
      );
      const res = await GetKuCunXXYPByYLList(params);
      this.$emit('handleSaveData', res, this.formModel, 0);
      this.resolve(res);
      this.closeModel();
      this.loading = false;
    },

    closeModel() {
      this.visible = false;
      this.formModel = formModelInit();
    },

    /**
     * 消耗时间change
     * **/
    handleInput(val) {
      if (val) {
        this.formModel.time = [
          dayjs()
            .subtract(Number(val) - 1, 'day')
            .format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ];
      }
    },

    /**
     * 消耗日期change
     * **/
    handleTimeChange() {
      const time = this.formModel.time;
      if (time[0] && time[1]) {
        const kaiShiSJ = dayjs(dayjs(time[0]).format('YYYY-MM-DD 00:00:00'));
        const jieShuSJ = dayjs(dayjs(time[1]).format('YYYY-MM-DD 23:59:59'));
        const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
        this.formModel.xiaoHaoSJ = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      }
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>
