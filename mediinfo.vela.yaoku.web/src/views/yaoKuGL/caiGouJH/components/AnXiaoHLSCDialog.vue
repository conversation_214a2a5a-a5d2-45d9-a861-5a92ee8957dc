<template>
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="560px"
    height="400px"
    :title="!type ? '按消耗量生成' : '按库存下限生成'"
    saveText="生成"
    :isShowCheckBox="!type ? true : false"
    @submit="handleSave"
  >
    <div class="flex-kcsxx dialogDIV">
      <md-form v-loading="loading" use-status-icon label-width="85px">
        <md-form-item label="消耗时间" v-if="!type">
          <md-input
            v-model="formModel.xiaoHaoSJ"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @change="handleInput"
            :clearable="false"
          >
            <template #suffix>
              <i class="md-input__icon icon-text">天内</i>
            </template>
          </md-input>
        </md-form-item>
        <md-form-item label="消耗日期" v-if="!type">
          <div class="flex-kcsxx">
            <md-date-picker
              v-model="formModel.kaiShiSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleKaiShiSJ"
            ></md-date-picker>
            <span class="padding-r-l">-</span>
            <md-date-picker
              v-model="formModel.jieShuSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleJieShuSJ"
            ></md-date-picker>
          </div>
        </md-form-item>
        <md-form-item label="预计消耗" v-if="!type">
          <md-input
            v-model="formModel.xiaoHaoTS"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            :clearable="false"
          >
            <template #suffix>
              <i class="md-input__icon icon-text">天内</i>
            </template>
          </md-input>
        </md-form-item>
        <md-form-item label="对照省平台">
          <md-radio-group
            v-model="formModel.duiZhaoSPT"
            border
            style="width: 100%"
          >
            <md-radio label="0">全部</md-radio>
            <md-radio label="1">已对照</md-radio>
            <md-radio label="2">未对照</md-radio>
          </md-radio-group>
        </md-form-item>
        <md-form-item label="药品属性">
          <md-checkbox-group
            v-model="formModel.yaoPinSXList"
            :border="true"
            style="width: 100%"
          >
            <md-checkbox label="1">国谈</md-checkbox>
            <md-checkbox label="2">毒麻精</md-checkbox>
            <md-checkbox label="3">集采</md-checkbox>
            <md-checkbox label="4">临时</md-checkbox>
            <md-checkbox label="5">课题</md-checkbox>
          </md-checkbox-group>
        </md-form-item>
        <md-form-item label="药房名称">
          <md-select v-model="formModel.xiaoHaoYFWZID">
            <md-option
              v-for="item in yaoFangList"
              :key="item.weiZhiID"
              :label="item.weiZhiMC"
              :value="item.weiZhiID"
            >
            </md-option>
          </md-select>
        </md-form-item>
        <md-form-item label="排序规则">
          <md-select v-model="formModel.paiXuGZ">
            <md-option
              v-for="item in paiXuGZOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
        </md-form-item>
        <md-form-item label="供货单位" v-if="!type">
          <md-select-comma
            v-model="formModel.gongHuoDWs"
            :options="moRenGHDWList"
            placeholder="请选择"
          ></md-select-comma>
        </md-form-item>
      </md-form>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import MdSelectComma from '@mdfe/material.select-comma';
import { getJiGouID } from '@/system/utils/local-cache';
import BlueDialog from '@/components/blue-dialog/index.vue';
import { getZhangBuLBSelectList } from '@/service/xiTongSZ/zhangBuLBWH';
import {
  GetYaoPinBFWZSelect,
  GetYaoFangListByJGID,
} from '@/service/yaoPin/yaoPinZD';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  GetKuCunXXYPByYLList,
  GetKuCunXXYPList,
} from '@/service/yaoPinYK/caiGouJH.js';
import { GetDanWeiXXList } from '@/service/yaoPinYK/gongHuoDW';
import dayjs from 'dayjs';
const formModelInit = () => {
  return {
    xiaoHaoSJ: 3,
    kaiShiSJ: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
    jieShuSJ: dayjs().format('YYYY-MM-DD'),
    duiZhaoSPT: '',
    yaoPinSXList: [],
    xiaoHaoTS: 30,
    paiXuGZ: '',
    gongHuoDWs: [],
  };
};
export default {
  name: '',
  data() {
    return {
      paiXuGZOptions: [
        { label: '按药品名称', value: '1' },
        { label: '按供货单位名称', value: '2' },
        { label: '按消耗量降序', value: '3' },
      ],
      yaoFangList: [],
      visibleDialog: false,
      loading: false,
      zhangBuLBOptions: [],
      duLiFLOptions: [],
      formModel: formModelInit(),
      resolve: null,
      reject: null,
      baiFangWZList: [],
      moRenGHDWList: [],
      type: '',
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    showModal(type) {
      this.visibleDialog = true;
      this.type = type;
      this.formModel = formModelInit();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    //消耗时间事件
    handleInput(val) {
      if (val) {
        this.formModel.kaiShiSJ = dayjs()
          .subtract(Number(val) - 1, 'day')
          .format('YYYY-MM-DD');
      }
    },
    // 消耗开始事件
    handleKaiShiSJ(val) {
      if (val) {
        let kaiShiSJ = val.replace(/-/g, '');
        let jieShuSJ = this.formModel.jieShuSJ.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '开始时间不能大于当前时间！',
            type: 'warning',
          });
          return;
        }
        //计算消耗天数
        const startTime = this.formModel.kaiShiSJ;
        const endTime = this.formModel.jieShuSJ;
        if (startTime && endTime) {
          kaiShiSJ = dayjs(dayjs(startTime).format('YYYY-MM-DD') + ' 00:00:00');
          jieShuSJ = dayjs(dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59');
          const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
          this.formModel.xiaoHaoSJ = Math.ceil(
            timeDiff / (1000 * 60 * 60 * 24),
          );
        }
      } else {
        this.formModel.xiaoHaoSJ = null;
      }
    },
    // 消耗结束时间事件
    handleJieShuSJ(val) {
      if (val) {
        let kaiShiSJ = this.formModel.kaiShiSJ.replace(/-/g, '');
        let jieShuSJ = val.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '结束时间不能小于开始时间！',
            type: 'warning',
          });
          return;
        }
        //计算消耗天数
        const startTime = this.formModel.kaiShiSJ;
        const endTime = this.formModel.jieShuSJ;
        if (startTime && endTime) {
          kaiShiSJ = dayjs(dayjs(startTime).format('YYYY-MM-DD') + ' 00:00:00');
          jieShuSJ = dayjs(dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59');
          const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
          this.formModel.xiaoHaoSJ = Math.ceil(
            timeDiff / (1000 * 60 * 60 * 24),
          );
        }
      } else {
        this.formModel.xiaoHaoSJ = null;
      }
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    init() {
      getYaoPinShuJuYZYList(['YP0006']).then((res) => {
        this.duLiFLOptions = res[0].zhiYuList;
      });
      getZhangBuLBSelectList().then((res) => {
        this.zhangBuLBOptions = res;
      });
      GetYaoFangListByJGID({ zuZhiJGID: getJiGouID() }).then((res) => {
        this.yaoFangList = res;
      });

      GetDanWeiXXList({
        pageIndex: 1,
        pageSize: 9999,
        xianShiTY: false
      }).then((res) => {
        res.forEach((i) => {
          i.label = i.danWeiMC;
          i.value = i.danWeiID;
        });
        this.moRenGHDWList = res;
      });
    },
    searchBaiFangWZ(val) {
      if (val == '' || val == undefined) {
        this.baiFangWZList = [];
        return;
      }
      this.getBaiFangWZList(val);
    },
    // 获取摆放位置列表
    async getBaiFangWZList(val) {
      try {
        if (val.length == 0) return;
        const res = await GetYaoPinBFWZSelect({
          baiFangWZ: val,
        });
        if (res) {
          this.baiFangWZList = res;
        }
        this.baiFangWZList = res;
        this.$forceUpdate();
      } catch (e) {}
    },
    handleSave(baoHanYKKC) {
      const {
        kaiShiSJ,
        jieShuSJ,
        duiZhaoSPT,
        yaoPinSXList,
        xiaoHaoTS,
        xiaoHaoYFWZID,
        paiXuGZ,
        gongHuoDWs,
      } = this.formModel;
      const params = {
        kaiShiSJ,
        jieShuSJ,
        duiZhaoSPT,
        xiaoHaoYFWZID,
        xiaoHaoTS: xiaoHaoTS ? Number(xiaoHaoTS) : null,
        yaoPinSXs: yaoPinSXList.join('|'),
        paiXuGZ,
        gongHuoDWs: gongHuoDWs?.join('|') || '',
      };
      if (!this.type) {
        params.baoHanYKKCL = baoHanYKKC;
      }
      this.loading = true;
      const jieKou = (obj) => {
        return !this.type ? GetKuCunXXYPByYLList(obj) : GetKuCunXXYPList(obj);
      };
      jieKou(params)
        .then((res) => {
          this.$emit('handleSaveData', res, this.formModel, this.type);
          this.resolve(res);
          this.closeModal();
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  components: {
    MdSelectComma,
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
::v-deep .#{$md-prefix}-scrollbar__view {
  display: flex;
  justify-content: center;
}
::v-deep .#{$md-prefix}-checkbox {
  margin-right: 8px;
}

.dialogDIV {
  padding-top: 4px;
}
.flex-kcsxx {
  display: flex;
  align-items: center;
}

.padding-r-l {
  padding: 0 8px;
}

.icon-text {
  font-style: normal;
}
</style>
