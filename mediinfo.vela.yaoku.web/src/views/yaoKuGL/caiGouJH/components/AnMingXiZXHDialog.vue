<template>
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="560px"
    height="300px"
    title="按明细账消耗生成"
    saveText="生成"
    @submit="handleSave"
  >
    <div class="flex-kcsxx dialogDIV">
      <md-form v-loading="loading" use-status-icon label-width="85px">
        <md-form-item label="消耗日期">
          <div class="flex-kcsxx">
            <md-date-picker
              v-model="formModel.kaiShiSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleKaiShiSJ"
            ></md-date-picker>
            <span class="padding-r-l">-</span>
            <md-date-picker
              v-model="formModel.jieShuSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleJieShuSJ"
            ></md-date-picker>
          </div>
        </md-form-item>
        <md-form-item label="药房名称">
          <md-select v-model="formModel.xiaoHaoYFWZID">
            <md-option
              v-for="item in yaoFangList"
              :key="item.weiZhiID"
              :label="item.weiZhiMC"
              :value="item.weiZhiID"
            >
            </md-option>
          </md-select>
        </md-form-item>
      </md-form>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import { getJiGouID } from '@/system/utils/local-cache';
import BlueDialog from '@/components/blue-dialog/index.vue';
import { getZhangBuLBSelectList } from '@/service/xiTongSZ/zhangBuLBWH';
import {
  GetYaoPinBFWZSelect,
  GetYaoFangListByJGID,
} from '@/service/yaoPin/yaoPinZD';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { GetKuCunXXYPByMXZList } from '@/service/yaoPinYK/caiGouJH.js';
import dayjs from 'dayjs';
const formModelInit = () => {
  return {
    kaiShiSJ: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
    jieShuSJ: dayjs().format('YYYY-MM-DD'),
    xiaoHaoYFWZID: '',
    xiaoHaoSJ: 3,
  };
};
export default {
  name: '',
  data() {
    return {
      yaoFangList: [],
      visibleDialog: false,
      loading: false,
      zhangBuLBOptions: [],
      duLiFLOptions: [],
      formModel: formModelInit(),
      resolve: null,
      reject: null,
      baiFangWZList: [],
      type: '',
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      GetYaoFangListByJGID({ zuZhiJGID: getJiGouID() }).then((res) => {
        this.yaoFangList = res;
      });
    },
    showModel() {
      this.visibleDialog = true;
      this.formModel = formModelInit();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    // 消耗开始事件
    handleKaiShiSJ(val) {
      if (val) {
        let kaiShiSJ = val.replace(/-/g, '');
        let jieShuSJ = this.formModel.jieShuSJ.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '开始时间不能大于当前时间！',
            type: 'warning',
          });
          return;
        }
        //计算消耗天数
        const startTime = this.formModel.kaiShiSJ;
        const endTime = this.formModel.jieShuSJ;
        if (startTime && endTime) {
          kaiShiSJ = dayjs(dayjs(startTime).format('YYYY-MM-DD') + ' 00:00:00');
          jieShuSJ = dayjs(dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59');
          const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
          this.formModel.xiaoHaoSJ = Math.ceil(
            timeDiff / (1000 * 60 * 60 * 24),
          );
        }
      } else {
        this.formModel.xiaoHaoSJ = null;
      }
    },
    // 消耗结束时间事件
    handleJieShuSJ(val) {
      if (val) {
        let kaiShiSJ = this.formModel.kaiShiSJ.replace(/-/g, '');
        let jieShuSJ = val.replace(/-/g, '');
        if (Number(kaiShiSJ) > Number(jieShuSJ)) {
          this.$message({
            message: '结束时间不能小于开始时间！',
            type: 'warning',
          });
          return;
        }
        //计算消耗天数
        const startTime = this.formModel.kaiShiSJ;
        const endTime = this.formModel.jieShuSJ;
        if (startTime && endTime) {
          kaiShiSJ = dayjs(dayjs(startTime).format('YYYY-MM-DD') + ' 00:00:00');
          jieShuSJ = dayjs(dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59');
          const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
          this.formModel.xiaoHaoSJ = Math.ceil(
            timeDiff / (1000 * 60 * 60 * 24),
          );
        }
      } else {
        this.formModel.xiaoHaoSJ = null;
      }
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    handleSave(baoHanYKKC) {
      const { kaiShiSJ, jieShuSJ, xiaoHaoYFWZID } = this.formModel;
      const params = {
        kaiShiSJ,
        jieShuSJ,
        xiaoHaoYFWZID,
      };
      this.loading = true;
      const jieKou = (obj) => {
        return GetKuCunXXYPByMXZList(obj);
      };
      jieKou(params)
        .then((res) => {
          this.$emit('handleSaveData', res, this.formModel, 0);
          this.resolve(res);
          this.closeModal();
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
::v-deep .#{$md-prefix}-scrollbar__view {
  display: flex;
  justify-content: center;
}
::v-deep .#{$md-prefix}-checkbox {
  margin-right: 8px;
}

.dialogDIV {
  padding-top: 4px;
}
.flex-kcsxx {
  display: flex;
  align-items: center;
}

.padding-r-l {
  padding: 0 8px;
}

.icon-text {
  font-style: normal;
}
</style>
