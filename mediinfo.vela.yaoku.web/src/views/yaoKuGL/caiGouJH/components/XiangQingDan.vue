<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('shouliqldrawer')"
    :modalClass="prefixClass('shouliqldrawermodal')"
    @close="drawerClose"
    ref="shouLiQLDrawer"
  >
    <div
      v-loading="loading"
      :loading-text="loadingText"
      class="drawer-container"
    >
      <div :class="prefixClass('details-top')">
        <div :class="prefixClass('details-left')">
          {{ title }}
        </div>
        <div :class="prefixClass('details-right')">
          <!-- <md-button type="primary" :icon="prefixClass('icon-daochu')" noneBg>
            导出
          </md-button> -->
          <md-button
            type="primary"
            v-if="showInfo.danJuZTDM == 1 || showInfo.danJuZTDM == 4"
            :icon="prefixClass('icon-bianji')"
            noneBg
            @click="handleEdit('edit')"
            >编辑</md-button
          >
          <md-button
            type="primary"
            v-if="showInfo.danJuZTDM == 2"
            :icon="prefixClass('icon-chehui')"
            noneBg
            @click="handleEdit('back')"
            >撤回</md-button
          >
          <md-button
            type="primary"
            v-if="showInfo.danJuZTDM == 1 || showInfo.danJuZTDM == 4"
            :icon="prefixClass('icon-shangchuan')"
            noneBg
            @click="handleEdit('submit')"
            >提交</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handleYuLan"
            >预览</md-button
          >
          <!-- <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handlePrint"
          >
            打印
          </md-button> -->
          <span :class="prefixClass('title-close')" @click="drawerClose">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('note-txt')">
        <md-tag v-if="showInfo.danJuZTDM == 1" type="primary">待提交</md-tag>
        <md-tag v-if="showInfo.danJuZTDM == 2" type="warning">待审核</md-tag>
        <md-tag v-if="showInfo.danJuZTDM == 3" type="success">已通过</md-tag>
        <md-tag v-if="showInfo.danJuZTDM == 4" type="danger">已拒绝</md-tag>
        <span
          :class="prefixClass('beizhu-title')"
          v-if="showInfo.danJuZTDM == 4"
          >拒绝理由:</span
        >
        <span v-if="showInfo.danJuZTDM == 4" :class="prefixClass('jujueyy')">{{
          showInfo.juJueYY
        }}</span>
        <span :class="prefixClass('beizhu-title')">备注：</span
        >{{ showInfo.beiZhu }}
      </div>
      <div :class="prefixClass('details-table')">
        <md-table
          :data="tableData"
          :columns="columns"
          height="100%"
          :class="prefixClass('demo-table')"
        >
          <template #previewYPMC="{ row, $index, cellRef }">
            <span :style="yaoPinSXStyle(row)[0]">{{
              yaoPinSXStyle(row)[1] || ''
            }}</span>
          </template>
          <template #xiaoHaoL="{ row, $index }">
            <!-- 消耗量浮窗 -->
            <md-popover ref="popoverRef" trigger="click" :width="298">
              <div>
                <md-date-picker
                  v-model="row.kaiShiSJ"
                  type="daterange"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 93%; margin-bottom: 8px"
                  @change="($event) => handleRowXHL($event, row)"
                />

                <md-table :data="row.list" :columns="rowColumns"></md-table>
              </div>
              <template #reference>
                <span class="xiaohao-color" @click="handleRowXHL('', row)">{{
                  row.xiaoHaoLiang
                }}</span>
              </template>
            </md-popover>
          </template>
            <template #jinJiCGBZ="{ row }">
            <i
              v-if="row.jinJiCGBZ"
              class="iconfont icongou"
              style="color: #1e88e5; font-size: 16px"
            />
          </template>
            <template #linShiYYBZ="{ row }">
            <i
              v-if="row.linShiYYBZ"
              class="iconfont icongou"
              style="color: #1e88e5; font-size: 16px"
            />
          </template>
        </md-table>
      </div>
      <div :class="prefixClass('details-wrap')">
        <md-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 200, 300, 400, 500]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="showInfo.yaoPinZS"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </md-pagination>
      </div>
      <div :class="prefixClass('details-bottom')">
        <div :class="prefixClass('left')">
          <div>
            制单：<span :class="prefixClass('col22')"
              >{{ showInfo.zhiDanRXM }} {{ showInfo.zhiDanSJ }}</span
            >
          </div>
        </div>
        <div :class="prefixClass('right')">
          <div>
            共计：<span :class="prefixClass('col22 col22-bold')">{{
              showInfo.yaoPinZS
            }}</span
            >种药品 合计 进价金额：<span
              :class="prefixClass('col22 col22-bold')"
              >{{ totalData.jinJiaJE }}</span
            >元 零售金额：<span :class="prefixClass('col22 col22-bold')">{{
              totalData.lingShouJE
            }}</span
            >元
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT001'"
        :fileName="'采购计划单'"
        :title="'采购计划单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import {
  CheHuiCGJHDXX,
  GetCaiGouJHDMX,
  GetYaoPinXHLMXList,
  TiJiaoCGJHDXX,
} from '@/service/yaoPinYK/caiGouJH.js';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { printByUrl } from '@/system/utils/print';
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { sortBy } from 'lodash-es';

export default {
  name: 'xiangqingdan',
  props: {
    size: { type: String, default: '75%' },
  },
  data() {
    return {
      kaiShiSJRow: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      rowColumns: [
        { label: '药房', prop: 'weiZhiMC' },
        { label: '消耗量', prop: 'xiaoHaoLiang' },
        { label: '库存数量', prop: 'kuCunSL' },
      ],
      caiGouDID: '',
      loading: false,
      params: {},
      loadingText: '正在加载中...',
      drawer: false, //侧滑状态
      showInfo: {
        yaoPinZS: 0, //药品总数
        zhiDanRXM: '', //制单人姓名
        zhiDanSJ: '', //制单时间
        beiZhu: '', //备注
      },
      tableData: [], //表格数据
      title: '', //侧滑名称
      columns: [
        {
          type: 'index',
          label: '序号',
          align: 'center',
          width: 60,
        },
        {
          label: '转换系数',
          prop: 'liangDingXS',
          type: 'text',
          width: 76,
        },
        {
          label: '院内编码',
          prop: 'yuanNeiBM',
          type: 'text',
          width: 76,
        },
        {
          label: '省平台ID',
          prop: 'shengPingTBM',
          type: 'text',
          width: 76,
        },
        {
          prop: 'yaoPinLXDM',
          type: 'text',
          width: 32,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 301,
          endMode: 'custom',
          startMode: 'click',
          slot: 'previewYPMC', //预览插槽
          // formatter: (row) => {
          //   if (row.yaoPinMC && row.yaoPinGG) {
          //     return row.yaoPinMC + ' ' + row.yaoPinGG;
          //   }
          //   return '';
          // },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          width: 120,
          showOverflowTooltip: true,
        },
        {
          prop: 'xiaoHaoL',
          slot: 'xiaoHaoL',
          label: '消耗量',
          width: 70,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'keShiLY',
          label: '科室领用',
          width: 80,
          align: 'right',
          type: 'text',
        },
         {
          slot: 'linShiYYBZ',
          label: '临时用药',
          width: 80,
          align: 'center',
          type: 'text',
        },
        {
          slot: 'jinJiCGBZ',
          label: '紧急采购',
          width: 80,
          type: 'text',
          align: 'center',
        },
        {
          prop: 'keCaiGSL',
          label: '可采购数量',
          field: true,
          width: 100,
          type: 'text',
          align: 'right',
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 80,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'caiGouSL',
          label: '采购数量',
          width: 80,
          align: 'right',
        },
        {
          prop: 'caiGouXDSL',
          label: '采购下单数量',
          width: 110,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'kuCunSL',
          label: '药库库存',
          width: 80,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },

        {
          prop: 'kuCunZL',
          label: '全院库存',
          width: 80,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'yuJiXHTS',
          label: '预计消耗天数',
          width: 110,
          align: 'right',
          type: 'text',
        },
        {
          prop: 'kuCunZL',
          label: '最晚结束时间',
          width: 110,
          type: 'text',
          formatter: (v) => {
            return v.jieShuRQ ? dayjs(v.jieShuRQ).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'kuCunXX',
          label: '库存下限',
          width: 100,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },

        {
          prop: 'kuCunSX',
          label: '库存上限',
          align: 'right',
          width: 100,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        // {
        //   slot: 'shenQingSL',
        //   prop: 'shenQingSL',
        //   label: '申请数量',
        //   align: 'right',
        //   width: 100,
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        //   renderHeader: ({ column, _self }) => {
        //     return h(
        //       'div',
        //       { class: _self.prefixClass('require') },
        //       column.label,
        //     );
        //   },
        //   disabled: ({ row }) => {
        //     return !!row.ruKuMXDID;
        //   },
        // },

        // {
        //   prop: 'lingShouJia',
        //   label: '零售价',
        //   width: 100,
        //   align: 'right',
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        // },
        // {
        //   prop: 'lingShouJE',
        //   label: '零售金额',
        //   width: 100,
        //   align: 'right',
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(3);
        //   },
        // },

        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          minWidth: 100,
          type: 'text',
          // sortable: 'custom',
        },
        {
          prop: 'zhongBaoZXS',
          label: '中包装',
          type: 'text',
          minWidth: 140,
        },
        {
          // slot: 'beiZhu',
          prop: 'beiZhu',
          label: '备注',
          minWidth: 140,
        },
      ],
      currentPage: 1,
      pageSize: 200,
      temporary: [],
    };
  },
  computed: {
    /**
     * 计算表格合计进价和合计零售价以及药品总数
     */
    totalData() {
      let jinJiaJE = 0;
      let lingShouJE = 0;
      this.tableData.forEach((item) => {
        jinJiaJE += item.jinJiaJE;
        lingShouJE += item.lingShouJE;
      });
      return {
        jinJiaJE: Number(jinJiaJE).toFixed(3),
        lingShouJE: Number(lingShouJE).toFixed(3),
      };
    },
  },
  mounted() {
    // 点击页面事件绑定
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeUnmount() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    /**
     * 打开侧滑
     */
    async openDrawer(data) {
      try {
        this.drawer = true;
        this.loading = true;
        this.caiGouDID = data.id;
        const result = await GetCaiGouJHDMX(data.id);
        const assignObj = {
          id: result.id,
          caiGouDH: result.caiGouDH,
          yaoPinZS: result.yaoPinZS,
          zhiDanRXM: result.zhiDanRXM,
          zhiDanSJ: yaoKuZDJZTimeShow(result.zhiDanSJ),
          beiZhu: result.beiZhu,
          danJuZTDM: result.danJuZTDM,
          juJueYY: result.juJueYY,
        };
        Object.assign(this.showInfo, assignObj);
        this.title = `详情 - ${data.caiGouDH}`;
        this.tableData = result.caiGouDMXList;
        this.temporary = cloneDeep(result.caiGouDMXList);
        this.tableData.forEach((el) => {
          el.kaiShiSJ = [result.xiaoHaoKSSJ || '', result.xiaoHaoJSSJ || ''];
        });
      } finally {
        this.loading = false;
      }
    },
    async handleEdit(type) {
      switch (type) {
        case 'edit':
          this.$router.push({
            name: 'XiuGai',
            query: {
              title: '采购单-' + this.showInfo.caiGouDH,
              id: this.showInfo.id,
              caiGouDH: this.showInfo.caiGouDH,
            },
          });
          this.drawerClose();
          break;
        case 'back':
          await CheHuiCGJHDXX(this.showInfo.id);
          this.drawerClose();
          this.$emit('refresh');
          break;
        case 'submit':
          await TiJiaoCGJHDXX(this.showInfo.id);
          this.drawerClose();
          this.$emit('refresh');
          break;
      }
    },
    // pageSize变化事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.tableData = this.temporary.slice(
        val * (this.currentPage - 1),
        val * this.currentPage,
      );
    },
    // currentPage变化事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.tableData = this.temporary.slice(
        this.pageSize * (val - 1),
        this.pageSize * val,
      );
    },
    //获取最大页数
    getMaxPageindex() {
      return Math.ceil(this.temporary.length / this.pageSize);
    },
    /**
     * 关闭侧滑
     */
    drawerClose() {
      this.drawer = false;
    },
    // 点击页面事件
    handleClickBodyCloseDrawer(e) {
      if (!this.$refs.shouLiQLDrawer.$el.contains(e.target)) {
        this.drawerClose();
      }
    },
    //药品属性样式
    yaoPinSXStyle(row) {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label =
        row.xianShiXX && row.xianShiXX.tianJiaWZ
          ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC + ' ' + row.yaoPinGG
          : row.yaoPinMC
            ? row.yaoPinMC + ' ' + row.yaoPinGG
            : '';

      const style =
        row.xianShiXX && row.jiaGeID ? tagStyles(row.xianShiXX) : '';
      return [style || '', label || ''];
    },
    //点击行消耗量
    handleRowXHL(val, row) {
      const params = {
        jiaGeID: row.jiaGeID,
        kaiShiSJ: row.kaiShiSJ[0],
        jieShuSJ: row.kaiShiSJ[1],
      };
      GetYaoPinXHLMXList(params).then((res) => {
        row.list = res;
      });
    },
    //预览
    async handleYuLan() {
      const params = {
        id: this.caiGouDID,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handlePrint() {
      try {
        this.loading = true;
        this.loadingText = '正在打印中...';
        await printByUrl('YKXT001', { id: this.caiGouDID });
        MdMessage.success('打印成功！');
      } catch (e) {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message || `打印失败！`,
          confirmButtonText: '我知道了',
        });
        // Message.error(e.message || '打印失败！')
      } finally {
        this.loading = false;
        this.loadingText = '正在加载中...';
      }
    },
    //升降序
    handleSortChange({ column, prop, order }) {
      if (order) {
        //删除最后一个空白行
        // if (!this.tableData[this.tableData.length - 1].jiaGeID)
        //   this.tableData.splice(this.tableData.length - 1, 1);
        this.tableData = sortBy(this.tableData, (item) => {
          // 获取中文首字母
          const pinyin =
            prop === 'yaoPinMCYGG' ? makePY(item.yaoPinMC) : makePY(prop);
          if (pinyin) return pinyin.toLocaleUpperCase();
        });
        if (order === 'descending') {
          this.tableData.reverse();
        }
        this.handleCurrentChange(this.currentPage);
      }
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-shouliqldrawermodal {
  position: initial !important;
}
</style>

<style lang="scss" scoped>
.#{$md-prefix}-shouliqldrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;

  ::v-deep .#{$md-prefix}-drawer__body {
    height: 100%;
  }
}

.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.#{$md-prefix}-submit {
  color: #1e88e5;
}

.#{$md-prefix}-harvest {
  color: #222;
}

.#{$md-prefix}-failure {
  color: #f12933;
}

.#{$md-prefix}-details-table {
  flex: 1;
  display: flex;
  min-height: 0;
  margin: 0 8px;
}

.#{$md-prefix}-note-txt {
  color: #222222;
  font-size: 14px;
  line-height: 35px;
  padding: 0 8px;

  .#{$md-prefix}-beizhu-title {
    color: #666;
    padding-left: 8px;
  }

  .#{$md-prefix}-jujueyy {
    padding-right: 8px;
  }
}

.#{$md-prefix}-details-top {
  height: 36px;
  // background-color: #f0f5fb;
  background-color: rgb(var(--md-color-1));
  display: flex;
  justify-content: space-between;
  padding: 0 8px;

  .#{$md-prefix}-details-left {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    line-height: 35px;
    flex: 3;
  }

  .#{$md-prefix}-details-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    margin: 0 10px;
    line-height: 35px;
    color: #1e88e5;
    font-size: 14px;
    cursor: pointer;
  }
}

.#{$md-prefix}-details-wrap {
  padding-bottom: 0px;

  .#{$md-prefix}-pagination {
    display: flex;
    justify-content: end;
    padding-right: 8px;
    padding-bottom: 0px;
  }
}

.xiaohao-color {
  color: #1385f0;
  cursor: pointer;
  display: block;
  height: 100%;
}

.#{$md-prefix}-details-bottom {
  display: flex;
  justify-content: space-between;
  color: #666666;
  font-size: 14px;
  padding: 8px;

  .#{$md-prefix}-left {
    flex: 1;
  }

  .#{$md-prefix}-right {
    flex: 1;
    text-align: right;
    color: #aaa;
  }

  .#{$md-prefix}-col22 {
    color: #222;
  }

  .#{$md-prefix}-col22-bold {
    font-weight: bold;
  }
}

.#{$md-prefix}-title-close {
  color: #666;
}

::md-input .#{$md-prefix}-input__inner {
  text-align: right !important;
  padding-right: 3px !important;
}

::md-table td > .cell.md-tooltip,
.#{$md-prefix}-table th > .cell.md-tooltip {
  text-align: right !important;
}
</style>
