<template>
  <md-dialog
    :title="title"
    width="1000px"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    height="430px"
    ref="tk"
    @closed="closeDialog"
    append-to-body
    top="5vh"
  >
    <md-form
      :model="form.data"
      :rules="form.rules"
      use-status-tooltip-icon
      label-width="130px"
      ref="gongHuoDWForm"
    >
      <md-row>
        <md-col :span="8">
          <md-form-item label="单位ID" prop="danWeiID">
            <md-input v-model="form.data.danWeiID" disabled></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="16">
          <md-form-item
            label="单位名称"
            prop="danWeiMC"
            style="padding-right: 30px"
          >
            <md-autocomplete
              v-model="form.data.danWeiMC"
              :fetch-suggestions="remoteMethodDanWeiMC"
              placeholder="请输入内容"
              :clearable="true"
              :trigger-on-focus="false"
              style="width: 100%"
              @change="handleChange"
              @select="handleSelect"
              ref="autocomplete"
              :disabled="form.mode == 'edit'"
            ></md-autocomplete>
          </md-form-item>
        </md-col>
        <!-- <md-col :span="24">
          <md-form-item
            label="统一社会信用代码"
            prop="xinYongDM"
            label-width="140px"
            style="padding-right: 30px"
          >
            <md-input v-model="form.data.xinYongDM"></md-input>
          </md-form-item>
        </md-col> -->
        <md-col :span="16">
          <md-form-item label="统一社会信用代码" prop="shuiHao">
            <md-input
              v-model="form.data.shuiHao"
              :disabled="danweiDisabled"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item
            label="开户银行"
            prop="kaiHuYH"
            style="padding-right: 30px"
          >
            <md-input
              v-model="form.data.kaiHuYH"
              :disabled="danweiDisabled"
              placeholder="请选择"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item label="银行账号" prop="danWeiZH">
            <md-input
              v-model="form.data.danWeiZH"
              :disabled="danweiDisabled"
              placeholder="请输入"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxLength="24"
              @change="handleNumberChange"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item label="联系人" prop="lianXiRXM">
            <md-input
              v-model="form.data.lianXiRXM"
              placeholder="请选择"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item
            label="联系方式"
            prop="lianXiDH"
            style="padding-right: 30px"
          >
            <md-input
              v-model="form.data.lianXiDH"
              placeholder="请选择"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="16">
          <md-form-item label="单位地址" prop="danWeiDZ">
            <bmis-address-select
              v-model="form.data.danWeiDZ"
              :data="setData"
              ref="addressSelect"
              @change="handleCSDChange"
            />
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item
            prop="xiangXiDZ"
            label-width="8px"
            style="padding-right: 30px"
          >
            <md-input
              v-model="form.data.xiangXiDZ"
              :disabled="danweiDisabled"
              placeholder="请输入详细地址"
            />
          </md-form-item>
        </md-col>

        <md-col :span="8">
          <md-form-item label="顺序号" prop="shunXuHao">
            <md-input
              v-model="form.data.shunXuHao"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item label="拼音码" prop="shuRuMa1">
            <md-input
              v-model="form.data.shuRuMa1"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item
            label="五笔码"
            prop="shuRuMa2"
            style="padding-right: 30px"
          >
            <md-input
              v-model="form.data.shuRuMa2"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item label="自定义码" prop="shuRuMa3">
            <md-input
              v-model="form.data.shuRuMa3"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="8">
          <md-form-item prop="yingFuKGLBZ">
            <md-checkbox v-model="form.data.yingFuKGLBZ"
              >纳入应付款</md-checkbox
            >
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>

    <div :class="prefixClass('xuKeZheng-table-wrap')">
      <p :class="prefixClass('xuKeZheng-table-title')">许可证</p>
      <div :class="prefixClass('app-container')">
        <md-editable-table-pro
          v-enter
          v-model="list"
          :columns="columns"
          :new-row="newTableRow"
          :showDefaultOperate="false"
          hideAddButton
          ref="editTable"
        >
          <!-- :add-button-disabled="addButtonDisabled" -->
          <template v-slot:zhengShuMC="{ row, $index }">
            <span>
              <md-input
                v-model.trim="row.zhengShuMC"
                :enter-sort="$index * 3"
                @change="handleZhengShuChange($index)"
              ></md-input
            ></span>
          </template>
          <template v-slot:zhengShuXQ="{ row, $index }">
            <span>
              <md-date-picker
                v-model="row.zhengShuXQ"
                type="date"
                @change="handleZhengShuChange($index)"
                placeholder="选择日期"
                style="width: 100%"
                :enter-sort="$index * 3 + 1"
              ></md-date-picker>
            </span>
          </template>
          <template v-slot:beiZhu="{ row, $index }">
            <span
              ><md-input
                v-model.trim="row.beiZhu"
                :enter-sort="$index * 3 + 2"
                @change="handleZhengShuChange($index)"
              ></md-input
            ></span>
          </template>
          <template v-slot:operate="{ row, $index }">
            <md-button
              v-if="$index !== list.length - 1"
              type="primary"
              icon="mediinfo-vela-yaoku-web-icon-shanchu"
              noneBg
              @click="handleRemove(row, $index)"
            ></md-button>
          </template>
        </md-editable-table-pro>
      </div>
    </div>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button :class="prefixClass('normal__button')" @click="cancel"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="form.loading"
          @click="form.submit()"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import AddressSelect from '@/components/address-select/index.vue';
import {
  AddDanWeiXX,
  GetXuKeZhengListByDWID,
  UpdateDanWeiXX,
  ZuoFeiXuKeZhengXX,
  getCheckDanWeiZT,
  getGongYingShangList,
  getGongYingShangXX,
  getShengShiQList,
} from '@/service/yaoPinYK/gongHuoDW';
import { getJiGouID, getJiGouMC } from '@/system/utils/local-cache';
import { makePY, makeWb } from '@/system/utils/wubi-pinyin.js';
import { useForm } from '@mdfe/medi-hooks';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { cloneDeep, debounce, isEqual } from 'lodash';

export default {
  name: 'gongHuoDW-dialog',
  data() {
    return {
      dialogVisible: false,
      title: '',
      setData: [],
      form: useForm({
        data() {
          return {
            zuZhiJGID: getJiGouID(),
            zuZhiJGMC: getJiGouMC(),
            danWeiID: '',
            danWeiMC: '',
            danWeiDZ: '',
            weiZhiID: '',
            weiZhiMC: '',
            shengFenDM: '',
            shengFenMC: '',
            kaiHuYH: '',
            danWeiZH: '',
            yingFuKGLBZ: 0,
            biaoZhunBM: '',
            danWeiLXDM: '1',
            danWeiLXMC: '供货单位',
            lianXiRXM: '',
            lianXiDH: '',
            shiDiQuDM: '',
            shiDiQuMC: '',
            xianQuDM: '',
            xianQuMC: '',
            xiangXiDZ: '',
            xianShiBZ: 0,
            shunXuHao: 0,
            shuiHao: '',
            shuRuMa1: '',
            shuRuMa2: '',
            shuRuMa3: '',
            danWeiZZSL: 0,
            id: null,
          };
        },
        rules: {
          danWeiID: [{ required: false, message: '请输入', trigger: 'blur' }],
          danWeiMC: [
            { required: true, message: '请输入', trigger: 'blur' },
            { validator: this.danWeiMCRules },
          ],
          xinYongDM: [{ required: true, message: '请输入', trigger: 'blur' }],
          shengFenDM: [{ required: true, message: '请输入', trigger: 'blur' }],
        },
        onFetch(options) {
          return options.data;
        },
        onValidate: async () => {
          // 获取表单组件
          const formModelComp = this.$refs.gongHuoDWForm;
          const formModel = this.form;
          let newList = [];
          this.list.forEach((item, index) => {
            // 最后一行直接删除。
            if (index === this.list.length - 1) return;
            if (!item.id) {
              //  CaoZuoZD   string  1新增，2修改，3删除
              item.CaoZuoZD = '1';
            }
            item.danWeiMC = formModel.data.danWeiMC;
            item.danWeiID = formModel.data.danWeiID;
            newList.push(item);
          });
          if (this.cloneList && this.cloneList.length > 0) {
            this.cloneList.forEach((item) => {
              if (item.CaoZuoZD) {
                newList.push(item);
              }
            });
          }
          this.newList = newList;

          try {
            await formModelComp.validate();
          } catch (err) {
            if (err === false) return false;
            return Promise.reject(err);
          }
        },
        onCreate: async (data) => {
          this.loading = true;
          data.yingFuKGLBZ = data.yingFuKGLBZ ? 1 : 0;
          data.xianShiBZ = data.xianShiBZ ? 1 : 0;
          data.danWeiZZCreateDtos = this.newList;
          let idFullStr = this.newList.every((item) => {
            return item.zhengShuMC && item.zhengShuXQ && item.zhengShuMC.trim();
          });
          if (!idFullStr) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: `证书名称或证书效期不能为空`,
              confirmButtonText: '我知道了',
            });
            return;
          }
          try {
            const result = await AddDanWeiXX(data);
            if (result) {
              MdMessage({ type: 'success', message: '新增成功!' });
              this.dialogVisible = false;
              this.resolve({ mode: 'new', value: data });
            } else {
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: `新增失败!`,
                confirmButtonText: '我知道了',
              });
            }
          } catch (error) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        },
        onUpdate: async (data) => {
          this.loading = true;
          data.yingFuKGLBZ = data.yingFuKGLBZ ? 1 : 0;
          data.xianShiBZ = data.xianShiBZ ? 1 : 0;
          data.danWeiZZUpdateDtos = this.newList;
          let idFullStr = this.newList.every((item) => {
            return item.zhengShuMC && item.zhengShuXQ && item.zhengShuMC.trim();
          });
          if (!idFullStr) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: `证书名称或证书效期不能为空`,
              confirmButtonText: '我知道了',
            });
            return;
          }
          try {
            const result = await UpdateDanWeiXX(data);
            if (result) {
              MdMessage({ type: 'success', message: '更新成功!' });
              this.dialogVisible = false;
              this.resolve({ mode: 'edit', value: data });
            } else {
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: `更新失败！`,
                confirmButtonText: '我知道了',
              });
            }
          } catch (error) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        },
      }),
      shengFengOption: [],
      addDisabled: false,
      list: [],
      newList: [],
      cloneList: [],
      columns: [
        {
          slot: 'zhengShuMC',
          prop: 'zhengShuMC',
          label: '证书名称',
          'min-width': 312,
        },
        {
          slot: 'zhengShuXQ',
          prop: 'zhengShuXQ',
          label: '证书效期',
          'min-width': 218,
        },
        {
          slot: 'beiZhu',
          label: '备注',
          'min-width': 378,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 50,
          align: 'center',
        },
      ],
    };
  },
  computed: {
    addButtonDisabled() {
      let len = this.list.length;
      if (
        len > 0 &&
        (!this.list[len - 1].zhengShuMC || !this.list[len - 1].zhengShuXQ)
      ) {
        return true;
      }
      return false;
    },
    danweiDisabled({ form }) {
      return false; //form.data.danWeiID;
    },
  },
  mounted() {
    this.getShengShiQList();
  },
  methods: {
    //获取省市区List
    async getShengShiQList() {
      this.setData = await getShengShiQList();
    },
    async handleRemove(rows, index) {
      let row = this.list[index];
      if (row.id) {
        ZuoFeiXuKeZhengXX({ id: row.id });
        this.list.splice(index, 1);
      } else {
        this.list.splice(index, 1);
      }
    },
    //处理地址数据
    handleDiZhiData(list) {
      let data = {};
      if (list && list.length > 0) {
        let diZhiMCList = list[0];
        let diZhiDMList = list[1];
        data.diZhiSFDM = diZhiDMList[0] ?? ''; //省份代码
        data.diZhiSFMC = diZhiMCList[0] ?? ''; //省份名称
        data.diZhiSDQDM = diZhiDMList[1] ?? ''; //市地区代码
        data.diZhiSDQMC = diZhiMCList[1] ?? ''; //市地区名称
        data.diZhiXQDM = diZhiDMList[2] ?? ''; //县区代码
        data.diZhiXQMC = diZhiMCList[2] ?? ''; //县区名称
      }
      return data;
    },
    handleCSDChange(obj) {
      let diZhiXX = this.handleDiZhiData(obj);
      let data = {};
      data.shengFenDM = diZhiXX.diZhiSFDM; //省份代码
      data.shengFenMC = diZhiXX.diZhiSFMC; //省份名称
      data.shiDiQuDM = diZhiXX.diZhiSDQDM; //市地区代码
      data.shiDiQuMC = diZhiXX.diZhiSDQMC; //市地区名称
      data.xianQuDM = diZhiXX.diZhiXQDM; //县区代码
      data.xianQuMC = diZhiXX.diZhiXQMC; //县区名称
      Object.assign(this.form.data, data);
    },
    handleNumberChange(value) {
      this.form.data.danWeiZH = value;
    },
    handleTableEnter({ length, activeIndex, callback }) {
      if ((activeIndex + 1) % length == 0) {
        this.newTableRow();
        callback({});
        return;
      }
      callback();
    },
    async danWeiMCRules(_, value, callback) {
      let res = await getCheckDanWeiZT({
        danWeiMC: value,
        id: this.form.data.id,
      });

      if (res) {
        callback(new Error('不能重复'));
      } else {
        callback();
      }
    },
    closeDialog() {
      this.$refs.gongHuoDWForm.resetFields();
    },
    //业务搜索
    remoteMethodDanWeiMC: debounce(async function (queryString, cb) {
      const params = {
        likeQuery: queryString,
        xianShiTY: false,
        pageIndex: 1,
        pageSize: 100,
      };
      let list = await getGongYingShangList(params);
      let arr = list.map((data) => {
        return {
          value: data.danWeiMC,
          id: data.id,
        };
      });
      cb(arr);
      if (arr.length == 0) {
        this.form.data.danWeiID = '';
      }
      return arr.length;
    }, 200),
    handleChange(val) {
      this.handleShuRuMa(val);
    },
    async handleSelect(val) {
      if (val.id) {
        let res = await getGongYingShangXX({ id: val.id });
        this.form.data.danWeiMC = val.value;
        let data = {
          gongYongQYID: res.id,
          danWeiID: res.danWeiID,
          danWeiMC: res.danWeiMC,
          kaiHuYH: res.kaiHuYH,
          lianXiDH: res.lianXiDH,
          lianXiRXM: res.lianXiRXM,
          shengFenDM: res.shengFenDM,
          shengFenMC: res.shengFenMC,
          shiDiQuDM: res.shiDiQuDM,
          shiDiQuMC: res.shiDiQuMC,
          xianQuDM: res.xianQuDM,
          xianQuMC: res.xianQuMC,
          danWeiDZ: [res.shengFenMC, res.shiDiQuMC, res.xianQuMC],
          shuiHao: res.shuiHao,
          danWeiZH: res.yinHangZH,
          xiangXiDZ: res.xiangXiDZ,
          // shuRuMa1: res.shuRuMA1,
          // shuRuMA2: res.shuRuMA2
        };
        Object.assign(this.form.data, data);
        this.handleShuRuMa(res.danWeiMC);
      } else {
        this.form.data.danWeiMC = val.value;
      }
    },
    handleShuRuMa(val) {
      this.form.data.shuRuMa1 = makePY(val);
      this.form.data.shuRuMa2 = makeWb(val);
    },
    handleZhengShuChange(index) {
      //  CaoZuoZD   string  1新增，2修改，3删除
      this.list[index].CaoZuoZD = '2';
      this.newTableRow();
    },
    delRow(index) {
      if (this.list[index].id) {
        let id = this.list[index].id;
        let curIndex = this.cloneList.findIndex((item) => item.id == id);
        //  CaoZuoZD   string  1新增，2修改，3删除
        this.cloneList[curIndex].CaoZuoZD = '3';
      }
      this.list.splice(index, 1);
      this.form.listChange = true;
    },
    newTableRow() {
      let len = this.list.length;
      if (
        len == 0 ||
        this.list[len - 1].zhengShuMC ||
        this.list[len - 1].zhengShuXQ
      ) {
        this.list.push({
          zhengShuMC: '',
          beiZhu: '',
          zhengShuXQ: '',
        });
      }
      /* return {
        zhengShuMC: "",
        zhengShuXQ: "",
        beiZhu: "",
      } */
    },

    async getXuKeZhengListList(danWeiID) {
      await GetXuKeZhengListByDWID({ danWeiID: danWeiID }).then((res) => {
        this.list = res;
        this.cloneList = cloneDeep(this.list);
      });
    },
    async cancel() {
      if (!isEqual(this.cloneList, this.list)) {
        try {
          await MdMessageBox.confirm('数据有修改, 是否关闭弹窗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          });
          this.dialogVisible = false;
        } catch {}
      } else {
        this.dialogVisible = false;
      }
    },

    async showDialog(option) {
      const formModel = this.form;
      this.shengFengOption = option.citys;
      this.list = [];

      if (option.mode === 'new') {
        this.title = '新增供货单位';

        await formModel.reset({ mode: 'new' });
      } else {
        this.title = '编辑供货单位';
        let data = option.data;
        data.yingFuKGLBZ = data.yingFuKGLBZ == 1 ? true : false;
        data.xianShiBZ = data.xianShiBZ == 1 ? true : false;
        if (data.shengFenDM) {
          data.danWeiDZ = [data.shengFenMC, data.shiDiQuMC, data.xianQuMC];
        }
        await formModel.reset({ mode: 'edit', data: option.data });
        await this.getXuKeZhengListList(option.data.danWeiID);
      }
      this.dialogVisible = true;
      this.newTableRow();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
  },
  components: {
    'bmis-address-select': AddressSelect,
  },
};
</script>

<style lang="scss" scoped>
/* .#{$md-prefix}-cancel__button {
  color: #1e88e5;
  background: #ffffff;
  border: 1px solid #c3e5fd;
  border-radius: 4px;
} */
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}

.#{$md-prefix}-table-addbtn {
  display: block;
  margin: 10px auto;
}

::v-deep .#{$md-prefix}-checkbox:last-of-type {
  margin-right: 0;
}

.#{$md-prefix}-xuKeZheng-table-title {
  font-weight: 500;
  color: #222222;
  font-size: 16px;
  line-height: 22px;
  padding-left: 12px;
  position: relative;
  margin-bottom: 8px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 3px;
    // @include md-def('background-color', 'color-6');
    background-color: getCssVar('color-6');
    height: 16px;
  }
}

.#{$md-prefix}-address-select {
  width: 100%;
}
</style>
