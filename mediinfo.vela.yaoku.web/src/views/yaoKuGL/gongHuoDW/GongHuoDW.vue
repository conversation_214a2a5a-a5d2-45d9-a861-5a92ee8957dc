<template>
  <div :class="prefixClass('gongHuoDW-box')">
    <div :class="prefixClass('gongHuoDW')">
      <div :class="prefixClass('top')">
        <md-form
          :model="query"
          @keyup.enter.native="searchHandle"
          :class="prefixClass('top-form')"
          label-width="100px"
        >
          <md-select
            :class="prefixClass('select__alias')"
            v-model="query.ShengFenDM"
            placeholder="请选择"
            clearable
            @change="searchHandle"
          >
            <md-option
              v-for="option in options"
              :key="option.quHuaDM"
              :label="option.quHuaMC"
              :value="option.quHuaDM"
            >
            </md-option>
          </md-select>

          <md-input
            v-model="query.LikeQuery"
            :class="prefixClass('input__alias')"
            :suffix-icon="prefixClass('icon-seach cursor-pointer')"
            placeholder="输入单位ID或者名称"
            @input="searchHandle"
          >
          </md-input>
          <md-checkbox v-model="query.guoQiBZ" @change="searchHandle"
            >过期</md-checkbox
          >
        </md-form>
        <div :class="prefixClass('top_buttons')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="searchHandle"
            style="margin-right: 8px"
          >
            刷新</md-button
          >
          <md-button
            v-show="isBtnShow"
            :class="prefixClass('changYongAN')"
            type="primary"
            :icon="prefixClass('icon-jia')"
            @click="handleAdd"
          >
            新增
          </md-button>
        </div>
      </div>
      <div :class="prefixClass('border-table')">
        <md-table-pro
          :columns="columns"
          height="100%"
          :onFetch="handleFetch"
          @current-change="handleRowClick"
          highlight-current-row
          ref="table"
        >
          <template v-slot:xuKeZheng="{ row }">
            <p>
              <span>{{ row.guoQiXKZ }}</span
              ><span v-if="row.guoQiXKZ && row.xuKeZheng">,</span
              >{{ row.xuKeZheng }}
            </p>
          </template>
          <template v-slot:yingFuKGLBZ="{ row }">
            <div style="text-align: center">
              <i
                v-if="row.yingFuKGLBZ"
                class="iconfont icongou"
                style="color: #1e88e5"
              />
            </div>
          </template>
          <template v-slot:kaiDan="{ row }">
            <div style="text-align: center">
              <i
                class="iconfont icongou"
                style="color: #1e88e5"
                v-if="row.kaiDan == 1"
              />
            </div>
          </template>
          <!-- 启用 -->
          <template v-slot:qiYong="{ row }">
            <md-switch
              v-model="row.qiYongBZ"
              :activeValue="1"
              :inactiveValue="0"
              size="small"
              @change="handleQiYong(row)"
            ></md-switch>
          </template>
          <template v-slot:operate="{ row, $index }">
            <md-button
              type="text-bg"
              style="margin-left: 0; padding: 0"
              @click.stop="handleEdit(row, $index)"
            >
              编辑
            </md-button>
            <md-button
              type="danger"
              noneBg
              style="margin-left: 12px; padding: 0"
              v-show="isBtnShow"
              @click.stop="handleDelete(row, $index)"
            >
              作废
            </md-button>
          </template>
        </md-table-pro>
      </div>
    </div>
    <div :class="prefixClass('bottom-table-wrap')">
      <p :class="prefixClass('bottom-table-title')">
        <span>许可证</span>
      </p>
      <div :class="prefixClass('bottom-table')">
        <md-table
          :columns="bottomColumns"
          :data="bottomTableData"
          height="100%"
          ref="bottomTable"
          v-loading="tableLoading"
          stripe
        >
          <template v-slot:zhengShuMC="{ row }">
            <p v-if="row.zhengShuXQ.substr(0, 10) >= bottomTableData.datetime">
              {{ row.zhengShuMC }}
            </p>
            <p v-if="row.zhengShuXQ.substr(0, 10) < bottomTableData.datetime">
              {{ row.zhengShuMC }}
            </p>
          </template>
          <template v-slot:zhengShuXQ="{ row }">
            <p v-if="row.zhengShuXQ.substr(0, 10) >= bottomTableData.datetime">
              {{ row.zhengShuXQ.substr(0, 10) }}
            </p>
            <p v-if="row.zhengShuXQ.substr(0, 10) < bottomTableData.datetime">
              {{ row.zhengShuXQ.substr(0, 10) }}
            </p>
          </template>
          <template v-slot:operate="{ row, $index }">
            <md-button
              type="text-bg"
              style="margin-left: 0; padding: 0"
              @click="handleEditXuKZ(row, $index)"
            >
              编辑
            </md-button>
            <md-button
              v-show="isBtnShow"
              type="danger"
              noneBg
              style="margin-left: 12px; padding: 0"
              @click="handleDeleteXuKZ(row, $index)"
            >
              作废
            </md-button>
          </template>
        </md-table>
      </div>
    </div>
    <GongHuoDW-dialog ref="GongHuoDWDialog"></GongHuoDW-dialog>
  </div>
</template>

<script>
import {
  GetDanWeiXXCount,
  GetDanWeiXXList,
  GetShengFenXXList,
  GetXuKeZhengListByDWID,
  ZuoFeiDanWeiXX,
  ZuoFeiXuKeZhengXX,
  getUpdateGongHuoDWZT,
} from '@/service/yaoPinYK/gongHuoDW';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import GongHuoDW from './components/GongHuoDW-dialog';
import { logger } from '@/service/log';
export default {
  name: 'GongHuoDW',
  data() {
    return {
      selectJG: '',
      isBtnShow: true,
      query: {
        ShengFenDM: '',
        LikeQuery: '',
        guoQiBZ: 0,
      },
      options: [],
      columns: [
        { prop: 'danWeiID', label: '单位ID', 'min-width': 60 },
        {
          prop: 'danWeiMC',
          label: '单位名称',
          'min-width': 180,
          showOverflowTooltip: true,
        },
        { prop: 'shuiHao', label: '统一社会信用代码', 'min-width': 140 },
        { prop: 'shengFenMC', label: '所属省', 'min-width': 100 },
        {
          prop: 'kaiHuYH',
          label: '开户银行',
          'min-width': 160,
          showOverflowTooltip: true,
        },
        {
          prop: 'danWeiZH',
          label: '银行账号',
          'min-width': 160,
          showOverflowTooltip: true,
        },
        // { prop: 'biaoZhunBM', label: '标准编码', 'min-width': 120 },
        { prop: 'danWeiZZSL', label: '许可证数', 'min-width': 70 },
        {
          slot: 'xuKeZheng',
          prop: 'xuKeZheng',
          label: '许可证',
          'min-width': 300,
        },
        {
          slot: 'yingFuKGLBZ',
          prop: 'yingFuKGLBZ',
          label: '纳入应付款',
          'min-width': 100,
        },
        {
          label: '启用',
          slot: 'qiYong',
          width: 80,
        },
        // { slot: "xianSi", prop: "xianShiBZ", label: "显示", "min-width": 49 },
        { slot: 'operate', label: '操作', 'min-width': 90, fixed: 'right' },
      ],
      data: [],
      total: 0,

      bottomColumns: [
        {
          slot: 'zhengShuMC',
          prop: 'zhengShuMC',
          label: '证书名称',
          'min-width': 396,
        },
        {
          slot: 'zhengShuXQ',
          prop: 'zhengShuXQ',
          label: '证书效期',
          'min-width': 218,
        },
        { prop: 'beiZhu', label: '备注', 'min-width': 576 },
        {
          slot: 'operate',
          label: '操作',
          'min-width': 90,
          fixed: 'right',
        },
      ],
      bottomTableData: [],
      activeRow: null,
      activeDanWeiID: null,
      tableLoading: false,
    };
  },
  created() {
    GetShengFenXXList().then((res) => {
      this.options = res;
    });
    this.$nextTick(() => {
      this.searchHandle();
    });
  },
  methods: {
    searchHandle() {
      this.activeDanWeiID = null;
      this.bottomTableData = [];
      return this.$refs.table.search({ pageSize: 100 });
    },
    handleRowClick(row) {
      this.activeRow = row;
      if (this.activeDanWeiID != row.danWeiID) {
        this.activeDanWeiID = row.danWeiID;
        this.getXuKeZhengListList();
      }
    },
    async handleFetch({ page, pageSize }, config) {
      const list = await GetDanWeiXXList({
        pageIndex: page,
        pageSize: pageSize,
        shengFenDM: this.query.ShengFenDM,
        likeQuery: this.query.LikeQuery,
        guoQiBZ: this.query.guoQiBZ ? 1 : 0,
      });
      const total = await GetDanWeiXXCount({
        shengFenDM: this.query.ShengFenDM,
        likeQuery: this.query.LikeQuery,
        guoQiBZ: this.query.guoQiBZ ? 1 : 0,
      });
      this.data = list;
      this.activeDanWeiID = this.data[0] ? this.data[0].danWeiID : null;
      if (this.activeDanWeiID) {
        await this.getXuKeZhengListList();
      }
      // this.$nextTick(() => {
      this.$refs.table.getComp('table').setCurrentRow(this.data[0]);
      // });
      return {
        items: list,
        total: total,
      };
    },
    getXuKeZhengListList() {
      if (!this.activeDanWeiID) {
        return;
      }
      this.tableLoading = true;
      GetXuKeZhengListByDWID({ danWeiID: this.activeDanWeiID })
        .then((res) => {
          this.bottomTableData = res;
          this.bottomTableData.datetime = dayjs().format('YYYY-MM-DD');
        })
        .finally((e) => {
          this.tableLoading = false;
        });
    },
    async handleQiYong(row) {
      try {
        if (row.qiYongBZ && row.qiYongBZ == 1) {
          await MdMessageBox.confirm(
            '启用后，可进行相应的业务操作，确认启用吗？',
            `确定要启用【${row.danWeiMC}】吗？`,
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          );
        } else if (row.qiYongBZ == 0) {
          await MdMessageBox.confirm(
            '停用后，将无法进行相应的业务操作！',
            `确定要停用【${row.danWeiMC}】吗？`,
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          );
        }
        await getUpdateGongHuoDWZT({ id: row.id });
      } catch (error) {
        console.error(error);
        logger.error(error);
      } finally {
        this.$refs.table.search({ pageSize: 100 });
      }
    },
    async handleAdd() {
      const res = await this.$refs.GongHuoDWDialog.showDialog({
        mode: 'new',
        citys: this.options,
      });
      if (res.mode === 'new') {
        await this.$refs.table.search({ pageSize: 100 });
      } else {
        await this.data.unshift(res.data);
      }
    },

    async handleEdit(row, i) {
      const res = await this.$refs.GongHuoDWDialog.showDialog({
        mode: 'edit',
        data: row,
        citys: this.options,
      });
      if (res.mode === 'edit') {
        await this.$refs.table.search({ pageSize: 100 });
      } else {
        await this.data.splice(i, 1);
      }
    },

    async handleDelete(row, index) {
      const flag = await MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          return ZuoFeiDanWeiXX({ id: row.id });
        })
        .then((response) => {
          MdMessage({ message: '作废成功', type: 'success' });
          this.searchHandle();
        })
        .catch((error) => {
          if (error !== 'cancel') {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
            // MdMessage({ message: error.message, type: "error" });
          }
        });
    },
    async handleDeleteXuKZ(row, index) {
      const flag = await MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          return ZuoFeiXuKeZhengXX({ id: row.id });
        })
        .then((response) => {
          MdMessage({ message: '作废成功', type: 'success' });
          this.bottomTableData.splice(index, 1);
        })
        .catch((error) => {
          if (error !== 'cancel') {
            // MdMessage({ message: error.message, type: "error" });
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
            this.bottomTableData.splice(index, 1);
          }
        });
    },
    async handleEditXuKZ(row, i) {
      const res = await this.$refs.GongHuoDWDialog.showDialog({
        mode: 'edit',
        data: this.activeRow,
        citys: this.options,
      });
      if (res.mode === 'edit') {
        await this.$refs.table.search({ pageSize: 100 });
      } else {
        await this.data.splice(i, 1);
      }
    },
  },
  components: {
    'GongHuoDW-dialog': GongHuoDW,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-select__alias {
  width: 140px;
  height: 30px;
  margin-right: 8px;
}

.#{$md-prefix}-input__alias {
  width: 180px;
  height: 30px;
  margin-right: 8px;
}

.#{$md-prefix}-changYongAN {
  // width: 62px;
}

::v-deep .cell {
  color: #222222;
}
.#{$md-prefix}-top {
  padding: 8px;
  display: flex;
  justify-content: space-between;

  .#{$md-prefix}-top-form {
    display: flex;

    .#{$md-prefix}-form-jigoutree {
      width: 260px;
      margin-right: 8px;
    }
  }
  &_buttons {
    display: flex;
    justify-content: flex-start;
    height: 30px;
  }
}

.#{$md-prefix}-icon-right {
  margin-right: 4px;
}

.#{$md-prefix}-gongHuoDW-box {
  flex: 1;
  background: #eaeff3;
  display: flex;
  flex-direction: column;
  padding: 8px;
  min-height: 0;
  box-sizing: border-box;
}
.#{$md-prefix}-gongHuoDW {
  background: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  .#{$md-prefix}-border-table {
    flex: 1;
    overflow: auto;
    padding: 0 8px 0 8px;
    .iconfont.icongou {
      // @include md-def('color', 'color-6');
      color: getCssVar('color-6');
    }
  }
}
.#{$md-prefix}-bottom-table-wrap {
  height: 175px;
  background: #fff;
  margin-top: 8px;
  padding: 8px;
  box-sizing: border-box;

  .#{$md-prefix}-bottom-table-title {
    margin-bottom: 2px;
    position: relative;
    height: 30px;
    flex: 0 0 auto;
    span {
      font-weight: 500;
      // color: #1e88e5;
      // @include md-def('color', 'color-6');
      color: getCssVar('color-6');
      font-size: 14px;
      line-height: 20px;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        display: block;
        width: 100%;
        height: 2px;
        margin-top: 8px;
        // background: #1e88e5;
        // @include md-def('background-color', 'color-6');
        color: getCssVar('color-6');
      }
    }
  }
  .#{$md-prefix}-bottom-table {
    flex: 1;
    overflow: auto;
    height: 127px;
  }
}
::v-deep .#{$md-prefix}-bottom-table .#{$md-prefix}-pagination {
  display: none;
}
</style>
