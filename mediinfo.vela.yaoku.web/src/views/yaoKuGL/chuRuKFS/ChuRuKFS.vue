<template>
  <div :class="prefixClass('chuRuKFS-box')">
    <div :class="prefixClass('chuRuKFS')">
      <div :class="prefixClass('top')">
        <md-form
          :model="query"
          @keyup.enter="searchHandle"
          :class="prefixClass('top-form')"
          label-width="100px"
        >
          <biz-weiZhi
            :youWuTY="true"
            :weiZhiLXDMs="this.weiZhiLXDM"
            @change="searchJiGou"
            @after-fetch="handleJiGouFetch"
            style="margin-right: 8px"
            :moRenWZID="moRenWZID"
            v-if="isShow"
            @isTongYong="handleTongYongFlag($event)"
          />
          <md-select
            :class="prefixClass('select__alias')"
            v-model="query.ChuRuKFXDM"
            placeholder="请选择出入库方向"
            @change="searchHandle"
            clearable
          >
            <md-option
              v-for="option in chuRkMCOptions"
              :key="option.biaoZhunDM"
              :label="option.biaoZhunMC"
              :value="option.biaoZhunDM"
            >
            </md-option>
          </md-select>

          <md-input
            v-model="query.FangShiMC"
            :class="prefixClass('input__alias')"
            placeholder="输入名称或ID搜索"
            @keydown.enter="searchHandle"
            @clear="searchHandle"
          >
            <template v-slot:suffix>
              <i
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
                @click="searchHandle"
              ></i>
            </template>
          </md-input>
        </md-form>
        <div :class="prefixClass('top_buttons')">
          <md-button
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            type="primary"
            style="margin-right: 8px"
            @click="searchHandle"
            >刷新</md-button
          >
          <md-button
            type="primary"
            style="margin-right: 8px"
            @click="handleUpdate"
            v-if="!this.isTongYong || this.query.weiZhiID != '0'"
            >更新</md-button
          >
          <md-button
            type="primary"
            v-show="isBtnShow"
            @click="handleAdd"
            :icon="prefixClass('icon-jia')"
          >
            新增
          </md-button>
        </div>
      </div>
      <div :class="prefixClass('border-table')">
        <md-table-pro
          :columns="columns"
          height="100%"
          :autoLoad="false"
          :resize="false"
          :onFetch="handleFetch"
          ref="table"
          :rowHeight="39"
        >
          <template v-slot:faPiaoHMBZ="{ row }">
            <div style="text-align: center">
              <i class="iconfont icongou" v-if="row.faPiaoHMBZ" />
            </div>
          </template>
          <template v-slot:piCIBHJJ="{ row }">
            <div style="text-align: center">
              <i class="iconfont icongou" v-if="row.piCiBHJJBZ" />
            </div>
          </template>
          <template v-slot:chuChang="{ row }">
            <div style="text-align: center">
              <i class="iconfont icongou" v-if="row.chuChangBZ" />
            </div>
          </template>
          <template v-slot:xianSi="{ row }">
            <div style="text-align: center">
              <i class="iconfont icongou" v-if="row.xianShiBZ" />
            </div>
          </template>
          <template v-slot:operate="{ row, $index }">
            <div :class="prefixClass('operate')">
              <md-button
                type="text-bg"
                @click="handleEdit(row, $index)"
                style="margin-right: 4px"
              >
                编辑
              </md-button>
              <md-button
                type="danger"
                noneBg
                :disabled="row.chuChangBZ"
                v-show="isBtnShow"
                @click="handleDelete(row, $index)"
              >
                作废
              </md-button>
            </div>
          </template>
        </md-table-pro>
      </div>
    </div>
    <ChuRuKFS-dialog ref="ChuRuKFSDialog"></ChuRuKFS-dialog>
    <ChuRuKFSUpdate-dialog ref="ChuRuKFSUpdatedialog"></ChuRuKFSUpdate-dialog>
  </div>
</template>

<script>
import BizWeiZhiSelect from '@/components/BizWeiZhiSelect';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  GetChuRuKFSCount,
  GetChuRuKFSList,
  ZuoFeiChuRuKFS,
} from '@/service/yaoPinYK/chuRuKFSNew';
import { getWeiZhiID } from '@/system/utils/local-cache';
import lyra from '@mdfe/lyra';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { cloneDeep, debounce } from 'lodash';
import ChuRuKFSDialog from './components/ChuRuKFSNew-dialog';
import ChuRuKFSUpdatedialog from './components/ChuRuKFSUpdate-dialog';
export default {
  name: 'ChuRuKFS',
  data() {
    return {
      selectJG: '',
      isBtnShow: true,
      isTongYong: false,
      isShow: false,
      weiZhiLXDM: '0',
      weiZhiOptions: [],
      moRenWZID: '',
      query: {
        ChuRuKFXDM: null,
        FangShiMC: '',
        weiZhiID: null,
        weiZhiMC: null,
        zuZhiJGID: null,
      },
      options: [],
      columns: [
        {
          prop: 'fangShiID',
          label: '出入库方式ID',
          'min-width': 140,
        },
        {
          prop: 'fangShiMC',
          label: '出入库方式名称',
          'min-width': 160,
        },
        { prop: 'chuRuKFXMC', label: '出入库方向', 'min-width': 110 },
        {
          prop: 'kuCunZJMC',
          label: '库存增减方向',
          'min-width': 102,
        },
        { prop: 'danWeiBMMC', label: '单位/部门', 'min-width': 88 },
        // { prop: "danJiaHSFSMC", label: "进价核算方式", "min-width": 152 },
        // { prop: "daYinHS", label: "每页打印行数", "min-width": 102 },
        { prop: 'chuKuCLMC', label: '出库策略', 'min-width': 92 },
        // { prop: "guiGeSYFSMC", label: "规格使用方式", "min-width": 118 },
        // {
        //   slot: "faPiaoHMBZ",
        //   prop: "FaPiaoHMBZ",
        //   label: "发票号码标志",
        //   "min-width": 104,
        // },
        // {
        //   slot: "piCIBHJJ",
        //   prop: "PiCiBHJJBZ",
        //   label: "批次包含进价",
        //   "min-width": 104,
        // },
        // { prop: "weiZhiYPLXMC", label: "位置药品类型", "min-width": 88, hidden:this.isTongYong?false:false},
        {
          slot: 'chuChang',
          prop: 'chuChangBZ',
          label: '出厂',
          'min-width': 54,
        },
        { slot: 'xianSi', prop: 'xianShiBZ', label: '显示', 'min-width': 54 },
        //{ slot: "kaiDan", prop: "KaiDanBZ", label: "开单", "min-width": 54 },
        { slot: 'operate', label: '操作', width: 94, fixed: 'right' },
      ],
      data: [],
      total: 0,
      kuCunZJFXOption: [],
      danWeiBMOptions: [],
      jingJhsfsOptions: [],
      chuKuCLOptions: [],
      guiGeSYFSOptions: [],
      chuRkMCOptions: [],
      weiZhiLXOptions: [],
      yaoKuFangWZOpyiong: [],
    };
  },
  created() {
    this.getList();
    // this.checkJiGOu()
    this.isShow = true;
  },
  methods: {
    //刷新表格
    searchHandle: debounce(function () {
      return this.$refs.table.search({ pageSize: 100 });
    }, 300),
    getList() {
      const yaoPinZD = {
        YP0035: 'chuRkMCOptions', // 出入库方向
        YP0036: 'kuCunZJFXOption', // 库存增减
        YP0037: 'danWeiBMOptions', // 单位部门
        YP0038: 'jingJhsfsOptions', // 单价核算方式
        YP0039: 'chuKuCLOptions', // 出库策略
        YP0040: 'guiGeSYFSOptions', // 规格使用方式
        YP0064: 'weiZhiLXOptions', // 位置类型代码
        YP0098: 'yaoKuFangWZOpyiong', // 出入库方式对照单位部门类别
      };
      const { WeiZhiLXDM } = lyra.getShareDataSync();
      this.weiZhiLXDM = WeiZhiLXDM;
      getYaoPinShuJuYZYList([
        'YP0035',
        'YP0036',
        'YP0037',
        'YP0038',
        'YP0039',
        'YP0040',
        'YP0064',
        'YP0098',
      ]).then((res) => {
        if (res && res.length) {
          res.forEach((item) => {
            let options = yaoPinZD[item.shuJuYLBID];
            // 初始化字典
            this[options] = item.zhiYuList;
          });
          // this.options = res[0].zhiYuList;
          // this.chuRkMCOptions = res[0].zhiYuList;
          // this.kuCunZJFXOption = res[1].zhiYuList;
          // this.danWeiBMOptions = res[2].zhiYuList;
          // this.jingJhsfsOptions = res[3].zhiYuList;
          // this.chuKuCLOptions = res[4].zhiYuList;
          // this.guiGeSYFSOptions = res[5].zhiYuList;
          // this.yaoKuFangWZOpyiong= res[7].zhiYuList

          const weiZhiYPLXYK = ['1', '2']; //药库位置药品类型集合
          const weiZhiYPLXYF = ['3', '4']; //药房位置药品类型集合
          if (this.weiZhiLXDM == '4') {
            this.weiZhiLXOptions = this.weiZhiLXOptions.filter(
              (x) => weiZhiYPLXYF.indexOf(x.biaoZhunDM) > -1,
            );
          } else if (this.weiZhiLXDM == '3') {
            this.weiZhiLXOptions = this.weiZhiLXOptions.filter(
              (x) => weiZhiYPLXYK.indexOf(x.biaoZhunDM) > -1,
            );
          }
        }
      });
      this.query.ChuRuKFXDM = '';
      this.moRenWZID = getWeiZhiID();
      this.$nextTick(() => {
        this.$refs.table.search({ pageSize: 100 });
      });
    },
    async handleTongYongFlag(val) {
      this.isTongYong = val;
    },
    async searchJiGou(j) {
      if (!j.value) return;
      this.query.weiZhiID = j.value;
      this.query.weiZhiMC = j.label;
      this.$refs.table.search({ pageSize: 100 });
    },
    async checkJiGOu() {
      // var res = await getGetHisZuZhiJGTree({
      //   shiFouQB: false,
      //   youWuTY: true
      // })
      // if (res.filter(x => x.id == '0').length > 0) {
      //   //当前用户包含通用机构的权限
      //   this.isTongYong = true
      // }
      //this.weiZhiLXDM= '3'
      this.isShow = true;
      // this.$nextTick(() => {
      //   // this.$refs.table.search()
      // })
    },
    handleJiGouFetch(checkNode) {
      this.query.weiZhiID = checkNode.value;
      this.query.weiZhiMC = checkNode.label;
    },
    async handleFetch({ page, pageSize }, config) {
      this.query.zuZhiJGID = null;
      if (this.query.weiZhiID == '0') {
        this.query.zuZhiJGID = '0';
      }

      await GetChuRuKFSList({
        pageSize: pageSize,
        PageIndex: page,
        ...this.query,
      }).then((res) => {
        this.data = res;
      });
      await GetChuRuKFSCount(this.query).then((res) => {
        this.total = res;
      });

      return {
        items: this.data,
        total: this.total,
      };
    },

    async handleAdd() {
      const res = await this.$refs.ChuRuKFSDialog.showDialog({
        mode: 'new',
        //zuZhiJGID:this.isTongYong? this.zuZhiJG.zuZhiJGID:null,
        weiZhiID: this.query.weiZhiID,
        weiZhiMC: this.query.weiZhiMC,
        zuZhiJGID: this.query.weiZhiID == '0' ? '0' : null,
        kuCunZJFXOption: this.kuCunZJFXOption,
        danWeiBMOptions: this.danWeiBMOptions,
        jingJhsfsOptions: this.jingJhsfsOptions,
        chuKuCLOptions: this.chuKuCLOptions,
        guiGeSYFSOptions: this.guiGeSYFSOptions,
        chuRkMCOptions: this.chuRkMCOptions,
        weiZhiLXOptions: this.weiZhiLXOptions,
        yaoKuFangWZOpyiong: this.yaoKuFangWZOpyiong,
      });
      if (res.mode === 'new') {
        this.$refs.table.search({ pageSize: 100 });
      } else {
        this.data.unshift(res.data);
      }
    },
    //更新
    async handleUpdate() {
      if (this.isTongYong) {
        await this.$refs.ChuRuKFSUpdatedialog.showDialog({
          weiZhiID: this.query.weiZhiID,
          weiZhiMC: this.query.weiZhiMC,
          zuZhiJGID: this.query.weiZhiID == '0' ? '0' : null,
          weiZhiLXOptions: this.weiZhiLXOptions,
        });
      } else {
        await this.$refs.ChuRuKFSUpdatedialog.showDialog({
          weiZhiID: null,
          weiZhiMC: null,
          zuZhiJGID: null,
          weiZhiLXOptions: this.weiZhiLXOptions,
        });
      }
      this.$refs.table.search({ pageSize: 100 });
    },
    async handleEdit(row, i) {
      if (row.chuChangBZ && !this.isTongYong) {
        MdMessage({ message: '出厂方式，不可编辑', type: 'error' });
        return;
      }
      const res = await this.$refs.ChuRuKFSDialog.showDialog({
        mode: 'edit',
        data: cloneDeep(row),
        weiZhiID: this.query.weiZhiID,
        weiZhiMC: this.query.weiZhiMC,
        zuZhiJGID: this.query.weiZhiID == '0' ? '0' : null,
        kuCunZJFXOption: this.kuCunZJFXOption,
        danWeiBMOptions: this.danWeiBMOptions,
        jingJhsfsOptions: this.jingJhsfsOptions,
        chuKuCLOptions: this.chuKuCLOptions,
        guiGeSYFSOptions: this.guiGeSYFSOptions,
        chuRkMCOptions: this.chuRkMCOptions,
        weiZhiLXOptions: this.weiZhiLXOptions,
        yaoKuFangWZOpyiong: this.yaoKuFangWZOpyiong,
      });
      if (res.mode === 'edit') {
        this.$refs.table.search({ pageSize: 100 });
      } else {
        this.data.splice(i, 1);
      }
    },

    async handleDelete(row, index) {
      MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          ZuoFeiChuRuKFS({ id: row.id }).then((res) => {
            this.searchHandle();
            MdMessage({ message: '作废成功', type: 'success' });
          });
        })
        .catch((error) => {
          if (error !== 'cancel') {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
  },
  components: {
    'ChuRuKFS-dialog': ChuRuKFSDialog,
    'biz-weiZhi': BizWeiZhiSelect,
    'ChuRuKFSUpdate-dialog': ChuRuKFSUpdatedialog,
  },
};
</script>

<style lang="scss" scoped>
// .el-table--border{
//   border: 0;
// }

::v-deep .mediinfo-vela-yaoku-web-base-table__row {
  height: 33px !important;
}

.#{$md-prefix}-select__alias {
  width: 160px;
  height: 30px;
  margin-right: 8px;
}

.#{$md-prefix}-input__alias {
  width: 180px;
  height: 30px;
}

::v-deep .has-gutter {
  .cell {
    font-weight: bold;
    // font-size: 14px;
    // @include md-def('font-size', 'font-2');
    font-size: var(--md-font-2);
  }
}

::v-deep .cell {
  color: #222222;
}
.#{$md-prefix}-top {
  padding: 8px;
  display: flex;
  justify-content: space-between;

  .#{$md-prefix}-top-form {
    display: flex;

    .#{$md-prefix}-form-jigoutree {
      width: 260px;
      margin-right: 8px;
    }
  }
  &_buttons {
    display: flex;
    justify-content: flex-start;
    height: 30px;
  }
}

.#{$md-prefix}-icon-right {
  margin-right: 4px;
}

.#{$md-prefix}-chuRuKFS-box {
  flex: 1;
  background: #eaeff3;
  overflow: hidden;
}
.#{$md-prefix}-chuRuKFS {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .#{$md-prefix}-border-table {
    flex: 1;
    height: 100%;
    overflow: hidden;
    // min-height: 0;
    padding: 0 8px;
    .#{$md-prefix}-operate {
      display: flex;
      justify-content: center;
      align-content: center;
    }
    .iconfont.icongou {
      // @include md-def('color', 'color-6');
      color: rgb(var(--md-color-6));
    }
  }
}
.#{$md-prefix}-jigou-select-tree {
  width: 220px;
  height: 30px;
  margin-right: 8px;
}
</style>
