<template>
  <md-dialog
    :title="'选择位置药品类型'"
    width="300px"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    height="200px"
    ref="tk"
    @closed="closeDialog"
    append-to-body
    top="5vh"
  >
    <ul class="ChuRuKFSUpdate-btn two">
      <li
        @click="handleRadioChange(index, item)"
        v-for="(item, index) in weiZhiLXOptions"
        :label="item.biaoZhunDM"
        :key="item.biaoZhunDM"
        :class="[activeIndex == index ? 'active' : '']"
      >
        <span>{{ item.biaoZhunMC.indexOf('西') > -1 ? '西' : '草' }}</span
        >{{ item.biaoZhunMC }}
        <md-icon class="ChuRuKFSUpdate-icon" name="xuanzhongjiaobiao" />
      </li>
    </ul>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button :class="prefixClass('normal__button')" @click="cancel"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { UpdateChuRuKFSList } from '@/service/yaoPinYK/chuRuKFS';
import { MdMessage } from '@mdfe/medi-ui';
export default {
  name: 'ChuRuKFSUpdate-dialog',
  data() {
    const refs = this.$refs;
    return {
      dialogVisible: false,
      loading: false,
      weiZhiLXOptions: [],
      weiZhiLX: '',
      weiZhiLXDM: '0',
      activeIndex: null,
      weiZhiID: '',
      weiZhiMC: '',
    };
  },
  methods: {
    handleRadioChange(index, item) {
      this.activeIndex = index;
      this.weiZhiLX = item.biaoZhunDM;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.weiZhiLX = '';
      this.activeIndex = null;
    },
    async handleSave() {
      const params = {
        weiZhiID: this.weiZhiID,
        weiZhiMC: this.weiZhiMC,
        WeiZhiYPLXDM: this.weiZhiLX,
      };
      await UpdateChuRuKFSList(params);
      MdMessage({ message: '更新成功', type: 'success' });
      this.resolve();
      this.dialogVisible = false;
      this.weiZhiLX = '';
      this.activeIndex = null;
    },
    async cancel() {
      this.dialogVisible = false;
      this.weiZhiLX = '';
      this.activeIndex = null;
    },
    async showDialog(option) {
      this.weiZhiLXOptions = option.weiZhiLXOptions;
      this.dialogVisible = true;
      this.weiZhiID = option.weiZhiID;
      this.weiZhiMC = option.weiZhiMC;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
.#{$md-prefix}-checkBox {
  text-align: left;
  padding-right: 30px;
  margin-bottom: 8px;
}
.ChuRuKFSUpdate-btn {
  overflow: hidden;
  &.two {
    li {
      width: 119px;
      &:nth-child(2n-1) {
        margin-right: 10px;
      }
    }
  }
  li {
    position: relative;
    width: 100%;
    float: left;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 4px 8px;
    margin-bottom: 8px;
    overflow: hidden;
    color: #666;
    cursor: pointer;
    span {
      display: inline-block;
      width: 25px;
      height: 25px;
      line-height: 25px;
      // @include md-def('background', 'color-1');
      background: rgb(var(--md-color-1));
      // @include md-def('color', 'color-6');
      color: rgb(var(--md-color-6));
      text-align: center;
      margin-right: 8px;
    }
    .ChuRuKFSUpdate-icon {
      display: none;
      position: absolute;
      right: -1px;
      bottom: 0;
      font-size: 18px;
    }
    &.active {
      // @include md-def('border-color', 'color-6');
      border-color: rgb(var(--md-color-6));
      // @include md-def('background', 'color-2');
      background: rgb(var(--md-color-2));
      .ChuRuKFSUpdate-icon {
        display: block;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
      }
    }
  }
}
</style>
