<template>
  <md-dialog
    :title="title + '出入库方式'"
    width="800px"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    height="430px"
    ref="tk"
    @closed="closeDialog"
    append-to-body
    top="5vh"
    :class="prefixClass('chuRuKu-dialog')"
  >
    <md-tabs v-model="activeName" type="card">
      <md-tab-pane label="基本信息" name="1"></md-tab-pane>
      <md-tab-pane
        v-if="isShowTab"
        label="出入库目标单位/部门"
        name="2"
      ></md-tab-pane>
    </md-tabs>
    <md-form
      v-if="dialogVisible && activeName === '1'"
      :model="form.data"
      :rules="form.rules"
      use-status-icon
      label-width="116px"
      ref="form"
    >
      <md-row>
        <md-col :span="12">
          <md-form-item label="出入库ID" prop="fangShiID">
            <md-input
              v-model="form.data.fangShiID"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
            ></md-input>
          </md-form-item>
        </md-col>

        <md-col :span="12">
          <md-form-item label="出入库名称" prop="fangShiMC">
            <md-input
              v-model="form.data.fangShiMC"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
              @input="handleShuRuMa"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="出入库方向" prop="chuRuKFXDM">
            <md-select
              style="width: 100%"
              v-model="form.data.chuRuKFXDM"
              placeholder="请选择"
              clearable
              :disabled="form.data.chuChangBZ"
              @change="chuRuKFXDMChange"
            >
              <md-option
                v-for="(item, index) in chuRkMCOptions"
                :key="index"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item
            label="库存增减方向"
            :prop="form.data.chuRuKFXDM == 3 ? '' : 'kuCunZJDM'"
          >
            <md-select
              style="width: 100%"
              v-model="form.data.kuCunZJDM"
              placeholder="请选择"
              clearable
              :disabled="form.data.chuChangBZ"
            >
              <md-option
                v-for="item in kuCunZJFXOption"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
                <div @click="clickOption('kuCunZJMC', 'kuCunZJDM', item)">
                  {{ item.biaoZhunMC }}
                </div>
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item
            label="单位/部门"
            :prop="form.data.chuRuKFXDM == 3 ? '' : 'danWeiBMDM'"
          >
            <md-select
              style="width: 100%"
              v-model="form.data.danWeiBMDM"
              placeholder="请选择"
              clearable
              :disabled="form.data.chuChangBZ"
              @change="handleDanWeiBM"
            >
              <md-option
                v-for="item in danWeiBMOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
                ><div @click="clickOption('danWeiBMMC', 'danWeiBMDM', item)">
                  {{ item.biaoZhunMC }}
                </div></md-option
              >
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12" v-if="false">
          <md-form-item label="每页打印行数" prop="daYinHS">
            <md-input
              v-model="form.data.daYinHS"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="出库策略" prop="chuKuCLDM">
            <md-select
              style="width: 100%"
              v-model="form.data.chuKuCLDM"
              placeholder="请选择"
              clearable
              :disabled="form.data.chuChangBZ"
            >
              <md-option
                v-for="item in chuKuCLOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
                ><div @click="clickOption('chuKuCLMC', 'chuKuCLDM', item)">
                  {{ item.biaoZhunMC }}
                </div></md-option
              >
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12" v-if="this.zuZhiJGID">
          <md-form-item label="位置药品类型" prop="weiZhiYPLXDM">
            <md-select
              style="width: 100%"
              v-model="form.data.weiZhiYPLXDM"
              placeholder="请选择"
              clearable
              :disabled="form.data.chuChangBZ"
            >
              <md-option
                v-for="item in weiZhiLXOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
                {{ item.biaoZhunMC }}
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="拼音码" prop="shuRuMa1">
            <md-input
              v-model="form.data.shuRuMa1"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="五笔码" prop="shuRuMa2">
            <md-input
              v-model="form.data.shuRuMa2"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="自定义码" prop="shuRuMa3">
            <md-input
              v-model="form.data.shuRuMa3"
              placeholder="请输入"
              :disabled="form.data.chuChangBZ"
              :class="prefixClass('input-height')"
            ></md-input>
          </md-form-item>
        </md-col>

        <md-col :span="12" :class="prefixClass('checkBox')">
          <md-form-item label="" prop="biaozhi">
            <md-checkbox v-model="form.data.xianShiBZ">显示</md-checkbox>
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
    <!-- **************************** -->
    <div v-else :class="prefixClass('chuRuKu-DW')">
      <md-table
        v-if="chuRuKMBDWList.length"
        ref="dragTable"
        :columns="columns"
        :data="chuRuKMBDWList"
        row-key="id"
        class="demo-table"
        edit
      >
        <template v-slot:muBiaoZZJGMC="{ row, $index }">
          <md-select
            v-model="row.muBiaoZZJGID"
            clearable
            filterable
            @change="
              (e) => {
                handleSelectchange(e, $index);
              }
            "
          >
            <md-option
              v-for="item in keGuanLiJGList"
              :key="item.keGuanLJGID"
              :label="item.keGuanLJGMC"
              :value="item.keGuanLJGID"
            >
            </md-option>
          </md-select>
        </template>
        <template v-slot:danWeiBMMC="{ row, $index }">
          <div>
            <md-select
              v-model="row.danWeiBMID"
              clearable
              filterable
              @change="handleYaoKuFangWZ(row)"
              @focus="focusYaoKuFangWZ(row, $index)"
            >
              <md-option
                v-for="val in row.yaoKuFangList"
                :key="val.weiZhiID"
                :label="val.weiZhiMC"
                :value="val.weiZhiID"
              >
              </md-option>
            </md-select>
          </div>
        </template>
      </md-table>
    </div>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="title == '编辑'"
          :disabled="form.data.chuChangBZ"
          :class="prefixClass('normal__button')"
          @click="handleDelete"
          plain
          style="float: left"
          type="danger"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="cancel"
          >取 消</md-button
        >
        <md-button
          v-if="activeName === '1'"
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="form.loading"
          @click="form.submit()"
          >保 存</md-button
        >
        <md-button
          v-else
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="form.loading"
          @click="handleSaveData"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import {
  AddChuRuKFS,
  GetFangShiID,
  UpdateChuRuKFS,
  ZuoFeiChuRuKFS,
} from '@/service/yaoPinYK/chuRuKFSNew';

import { getKeGuanLiJGList } from '@/service/gongYong/zuzhixx';
import {
  getYaoKuFangListByJGID,
  getChuRuKFSDZListByZuZhiJGIDAndFangShiID,
  saveChuRuKFSDZ,
} from '@/service/yaoPin/YaoPinZDJCSJ';

import { makePY, makeWb } from '@/system/utils/wubi-pinyin.js';
import { useForm } from '@mdfe/medi-hooks';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { cloneDeep, isEqual } from 'lodash';

import { getJiGouID } from '@/system/utils/local-cache';
import { logger } from '@/service/log';
export default {
  name: 'ChuRuKFSNew-dialog',
  data() {
    return {
      dialogVisible: false,
      mode: '',
      title: '',
      activeName: '1',
      isShowTab: false,
      zuZhiJGID: '',
      weiZhiID: '',
      zuZhiJGMC: '',
      weiZhiMC: '',
      weiZhiLXDM: '0',
      weiZhiLXOptions: [],
      form: useForm({
        data() {
          return {
            id: null,
            fangShiID: null,
            fangShiMC: null,
            kuCunZJDM: null,
            kuCunZJMC: null,
            danWeiBMDM: null,
            danWeiBMMC: null,
            danJiaHSFSDM: null,
            danJiaHSFSMC: null,
            chuRuKFXDM: null,
            chuRuKFXMC: null,
            daYinHS: 0,
            chuKuCLDM: null,
            chuKuCLMC: null,
            guiGeSYFSDM: null,
            guiGeSYFSMC: null,
            faPiaoHMBZ: 0,
            piCiBHJJBZ: 0,
            xianShiBZ: true,
            //kaiDanBZ: 0,
            zuZhiJGMC: null,
            zuZhiJGID: null,
            shuRuMa1: null,
            shuRuMa2: null,
            shuRuMa3: null,
            weiZhiYPLXDM: null,
            weiZhiYPLXMC: null,
          };
        },

        rules: {
          fangShiID: [
            { required: true, message: '请输入', trigger: 'blur' },
            { min: 3, message: '方式ID长度大于2', trigger: 'blur' },
          ],
          fangShiMC: [{ required: true, message: '请输入', trigger: 'blur' }],
          chuRuKFXDM: [{ required: true, message: '请输入', trigger: 'blur' }],
          kuCunZJDM: [{ required: true, message: '请输入', trigger: 'blur' }],
          danWeiBMDM: [{ required: true, message: '请输入', trigger: 'blur' }],
          daYinHS: [{ required: false, message: '请输入', trigger: 'blur' }],
          chuKuCLDM: [{ required: true, message: '请输入', trigger: 'blur' }],
          weiZhiYPLXDM: [
            { required: true, message: '请输入', trigger: 'blur' },
          ],
          guiGeSYFSDM: [
            { required: false, message: '请输入', trigger: 'blur' },
          ],
        },
        onFetch(option) {
          return option.data;
        },
        onValidate: async () => {
          // 获取表单组件
          const formModelComp = this.$refs.form;
          try {
            await formModelComp.validate();
          } catch (err) {
            if (err === false) return false;
            return Promise.reject(err);
          }
        },
        onCreate: async (data) => {
          this.loading = true;
          data.kaiDanBZ = data.kaiDanBZ ? 1 : 0;
          data.xianShiBZ = data.xianShiBZ ? 1 : 0;
          data.zuZhiJGID = this.zuZhiJGID;
          data.weiZhiID = this.weiZhiID;
          data.zuZhiJGMC = this.zuZhiJGMC;
          data.weiZhiMC = this.weiZhiMC;
          data.weiZhiYPLXMC = this.weiZhiLXOptions.find(
            (x) => x.biaoZhunDM == data.weiZhiYPLXDM,
          )?.biaoZhunMC;
          try {
            // this.fangShiID = data.fangShiID
            // this.fangShiMC = data.fangShiMC
            const result = await AddChuRuKFS(data);
            if (result) {
              MdMessage({ type: 'success', message: '新增成功!' });
              // 选择单位/部门=其他机构，保存后出现【出入库目标单位/部门】
              if (data.danWeiBMDM === '8') {
                this.isShowTab = true;
                this.activeName = '2';
                this.getChuRuKMBDWList();
              } else {
                this.dialogVisible = false;
                this.resolve({ mode: 'new', value: data });
              }
            } else {
              MdMessage({ type: 'error', message: '新增失败!' });
            }
          } catch (error) {
            // MdMessage({ type: 'error', message: error.message })
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        },
        onUpdate: async (data) => {
          this.loading = true;
          data.kaiDanBZ = data.kaiDanBZ ? 1 : 0;
          data.xianShiBZ = data.xianShiBZ ? 1 : 0;
          data.weiZhiYPLXMC = this.weiZhiLXOptions.find(
            (x) => x.biaoZhunDM == data.weiZhiYPLXDM,
          )?.biaoZhunMC;
          try {
            const result = await UpdateChuRuKFS(data);
            if (result) {
              MdMessage({ type: 'success', message: '更新成功!' });
              if (data.danWeiBMDM === '8') {
                this.isShowTab = true;
                this.activeName = '2';
                this.getChuRuKMBDWList();
              } else {
                this.dialogVisible = false;
                this.resolve({ mode: 'edit', value: data });
              }
            } else {
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: `更新失败！`,
                confirmButtonText: '我知道了',
              });
            }
          } catch (error) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        },
      }),
      oldFormData: {},
      kuCunZJFXOption: [],
      danWeiBMOptions: [],
      jingJhsfsOptions: [],
      chuKuCLOptions: [],
      guiGeSYFSOptions: [],
      chuRkMCOptions: [],

      chuRuKMBDWList: [], // 出入库目标单位/机构List
      keGuanLiJGList: [], // 机构下拉数据
      columns: [
        {
          slot: 'muBiaoZZJGMC',
          label: '机构名称',
        },
        {
          slot: 'danWeiBMMC',
          label: '',
          width: 200,
        },
        {
          type: 'operate',
          label: '',
          count: 1,
          actions: [
            {
              icon: 'mediinfo-vela-yaoku-web-icon-shanchu',
              type: 'danger',
              onPressed: ({ row, $index }) => {
                this.delRow(row, $index);
              },
            },
          ],
        },
      ],
      yaoKuFangWZ: {}, // 第二列药库房位置
      // fangShiID: '',
      // fangShiMC: '',
      deleteIDList: [], // 删除数据
    };
  },
  watch: {
    chuRuKMBDWList: {
      handler(val, oldVal) {
        const list = this.chuRuKMBDWList;

        if (list && list.length > 0) {
          const muBiaoZZJGID = list[list.length - 1].muBiaoZZJGID;
          if (muBiaoZZJGID) {
            this.addRow();
          }
        }
      },
      deep: true,
    },
  },
  created() {
    window.aa = this;
  },
  methods: {
    clickOption(str1, str2, item) {
      this.form.data[str1] = item.biaoZhunMC;
      this.form.data[str2] = item.biaoZhunDM;
    },
    closeDialog() {
      // this.$refs.form.resetFields();
      this.dialogVisible = false;
    },
    handleShuRuMa(val) {
      this.form.data.shuRuMa1 = makePY(val);
      this.form.data.shuRuMa2 = makeWb(val);
    },
    async cancel() {
      if (!isEqual(this.oldFormData, this.form.data)) {
        await MdMessageBox.confirm('数据有修改, 是否关闭弹窗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.dialogVisible = false;
          })
          .catch(() => {});
      } else {
        this.dialogVisible = false;
      }
    },
    async handleDelete() {
      const formModel = this.form;
      await MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          return ZuoFeiChuRuKFS({ id: formModel.data.id });
        })
        .then((response) => {
          this.resolve({ mode: 'edit', value: this.form.data });
          MdMessage({ message: '作废成功', type: 'success' });
          this.dialogVisible = false;
        })
        .catch((error) => {
          if (error !== 'cancel') {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
    async showDialog(option) {
      this.mode = option.mode;
      this.activeName = '1';
      this.chuRuKMBDWList = [];
      this.columns[1].label =
        option.yaoKuFangWZOpyiong[0]?.biaoZhunMC || '药库房位置';
      this.yaoKuFangWZ = option.yaoKuFangWZOpyiong[0] || {};
      const formModel = this.form;
      await this.getTreeData();
      // 外部传递的数据
      // TODO 是否需要拷贝数据
      if (option.mode === 'new') {
        this.title = '新增';
        this.isShowTab = false;
        await formModel.reset(option);
        formModel.data.fangShiID = await GetFangShiID({
          zuZhiJGID: this.zuZhiJGID,
          weiZhiID: this.weiZhiID,
        });
      } else {
        this.title = '编辑';
        const data = option.data;
        data.kaiDanBZ = data.kaiDanBZ == 1 ? true : false;
        data.xianShiBZ = data.xianShiBZ == 1 ? true : false;
        await formModel.reset(option);
        if (formModel.data.danWeiBMDM === '8') {
          this.isShowTab = true;
          await this.getChuRuKMBDWList();
        } else {
          this.isShowTab = false;
        }
      }
      this.dialogVisible = true;
      this.oldFormData = cloneDeep({ ...formModel.data });
      this.chuRkMCOptions = option.chuRkMCOptions;
      this.kuCunZJFXOption = option.kuCunZJFXOption;
      this.danWeiBMOptions = option.danWeiBMOptions;
      this.jingJhsfsOptions = option.jingJhsfsOptions;
      this.chuKuCLOptions = option.chuKuCLOptions;
      this.guiGeSYFSOptions = option.guiGeSYFSOptions;
      this.weiZhiLXOptions = option.weiZhiLXOptions;
      this.zuZhiJGID = option.zuZhiJGID;
      this.weiZhiID = option.weiZhiID;
      this.zuZhiJGMC = option.zuZhiJGMC;
      this.weiZhiMC = option.weiZhiMC;

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    async chuRuKFXDMChange(val) {
      this.form.data.chuRuKFXMC = this.chuRkMCOptions.find(
        (item) => item.biaoZhunDM == val,
      ).biaoZhunMC;

      if (val === '入库') {
        this.form.data.kuCunZJDM = '1';
        this.form.data.kuCunZJMC = this.kuCunZJFXOption.find(
          (item) => item.biaoZhunDM == '1',
        ).biaoZhunMC;
      } else if (val === '出库') {
        this.form.data.kuCunZJDM = '2';
        this.form.data.kuCunZJMC = this.kuCunZJFXOption.find(
          (item) => item.biaoZhunDM == '2',
        ).biaoZhunMC;
      }
    },
    // 获取机构下拉数据
    async getTreeData() {
      let result = await getKeGuanLiJGList({
        yeWuLXDM: '1',
        guanLiJGID: getJiGouID(),
      });
      this.keGuanLiJGList = result || [];
    },
    // 获取药库位置配置下拉
    async getYaoKuFangWZ(zuZhiJGID) {
      let weiZhiList = await getYaoKuFangListByJGID({
        zuZhiJGID: zuZhiJGID,
      });
      return weiZhiList;
    },
    // 获取列表
    async getChuRuKMBDWList() {
      try {
        let result = await getChuRuKFSDZListByZuZhiJGIDAndFangShiID({
          zuZhiJGID: getJiGouID(),
          fangShiID: this.form.data.fangShiID,
          pageSize: '99999',
          pageIndex: 1,
        });
        let list = result.items || [];
        list.forEach(async (val) => {
          val.yaoKuFangList = [
            {
              weiZhiID: val.danWeiBMID,
              weiZhiMC: val.danWeiBMMC,
            },
          ];
        });
        this.chuRuKMBDWList = list;
        this.delList = [];
        this.addRow();
      } catch (error) {
        console.error(error);
        logger.error(e);
      }
    },
    // 切换第一列机构
    async handleSelectchange(e, index) {
      this.chuRuKMBDWList[index].muBiaoZZJGID = e;
      this.chuRuKMBDWList[index].muBiaoZZJGMC = this.keGuanLiJGList.find(
        (item) => item.keGuanLJGID === e,
      )?.keGuanLJGMC;
      // 根据组织机构ID获取位置列表(批量)
      if (e) {
        let list = this.chuRuKMBDWList;
        let newList = await this.getYaoKuFangWZ(e);
        list[index].yaoKuFangList = [...newList];
        this.chuRuKMBDWList = cloneDeep(list);
      }
    },
    // 第二列获取焦点初始化下拉选项
    async focusYaoKuFangWZ(row, index) {
      if (row.muBiaoZZJGID) {
        let list = this.chuRuKMBDWList;
        let newList = await this.getYaoKuFangWZ(row.muBiaoZZJGID);
        list[index].yaoKuFangList = [...newList];
        this.chuRuKMBDWList = cloneDeep(list);
      }
    },
    // 切换第二列药库位置
    handleYaoKuFangWZ(row) {
      row.danWeiBMMC = row.yaoKuFangList.find(
        (val) => val.weiZhiID === row.danWeiBMID,
      )?.weiZhiMC;
    },
    delRow(row, index) {
      if (row.id) {
        this.deleteIDList.push(row.id);
      }
      this.chuRuKMBDWList.splice(index, 1);
    },
    addRow() {
      this.chuRuKMBDWList.push({
        fangShiID: this.form.data.fangShiID,
        fangShiMC: this.form.data.fangShiMC,
        danWeiBMLBDM: this.yaoKuFangWZ.biaoZhunDM || '',
        danWeiBMLBMC: this.yaoKuFangWZ.biaoZhunMC || '',
        muBiaoZZJGID: '',
        muBiaoZZJGMC: '',
        danWeiBMID: '',
        danWeiBMMC: '',
        yaoKuFangList: [],
      });
    },
    // 保存列表
    async handleSaveData() {
      try {
        let list = [...this.chuRuKMBDWList];
        list = list.filter(
          (val) => val.muBiaoZZJGID !== '' && val.danWeiBMID !== '',
        );
        // list.forEach(item => {
        //   delete item.yaoKuFangList
        // })
        if (this.checkDuplicates(list)) {
          MdMessage({ message: '保存数据不可重复', type: 'warning' });
        } else {
          let result = await saveChuRuKFSDZ({
            saveChuRukFSDZList: list,
            deleteIDList: this.deleteIDList,
          });
          this.dialogVisible = false;
          this.resolve({ mode: this.mode });
        }
      } catch (error) {
        console.error(error);
      }
    },
    checkDuplicates(arr) {
      return arr.some(function (current, index) {
        return arr.some(function (other, otherIndex) {
          return (
            index !== otherIndex &&
            current.muBiaoZZJGID === other.muBiaoZZJGID &&
            current.danWeiBMID === other.danWeiBMID
          );
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-chuRuKu-DW {
  padding: 8px;
}
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
.#{$md-prefix}-checkBox {
  text-align: left;
  padding-right: 30px;
  margin-bottom: 8px;
}
</style>
<style lang="scss">
.#{$md-prefix}-chuRuKu-dialog
  .#{$md-prefix}-dialog__body
  .#{$md-prefix}-dialog__scroll-view {
  padding: 0 !important;
}
.#{$md-prefix}-chuRuKu-dialog {
  .#{$md-prefix}-tabs--card > .#{$md-prefix}-tabs__header {
    padding-top: 6px;
    margin-bottom: 8px;
    border: none;
    .#{$md-prefix}-tabs__item.is-active {
      border: none;
      background: #fff;
    }
  }
}
</style>
