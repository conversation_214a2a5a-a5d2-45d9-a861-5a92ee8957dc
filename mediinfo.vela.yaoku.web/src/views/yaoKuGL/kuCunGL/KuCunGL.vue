<template>
  <div :class="prefixClass('kucungl-wrap')">
    <div :class="prefixClass('kucungl-wrap-top')">
      <md-radio-group v-model="topQuery.kuCunSFWK" @change="handleSearch">
        <md-radio :label="1">库存不为0</md-radio>
        <md-radio :label="2">库存为0</md-radio>
        <md-radio :label="0">所有</md-radio>
      </md-radio-group>
      <div class="line"></div>
      <md-checkbox
        v-model="topQuery.anPiHXS"
        :true-label="1"
        :false-label="0"
        @change="handleSearch"
      >
        按批号显示
      </md-checkbox>
      <div class="line"></div>
      <md-checkbox-group v-model="roles" @change="handleChange">
        <md-checkbox
          label="0"
          style="color: #1e88e5"
          :disabled="topQuery.anPiHXS === 1"
          >高于上限</md-checkbox
        >
        <md-checkbox
          label="1"
          style="color: #ffa70f"
          :disabled="topQuery.anPiHXS === 1"
          >低于下限</md-checkbox
        >
      </md-checkbox-group>
      <md-button
        type="primary"
        :icon="prefixClass('icon-dayinji')"
        noneBg
        @click="handleDaYin"
        >打印</md-button
      >
      <md-button
        type="primary"
        style="margin-right: 156px"
        noneBg
        @click="handleZeroKC"
        >新增零库存
      </md-button>
      <md-button
        type="primary"
        :icon="prefixClass('icon-dayinji')"
        noneBg
        style="margin-right: 60px"
        @click="handleYuLanQB"
        >总库存预览</md-button
      >
      <md-button
        v-show="false"
        type="primary"
        :icon="prefixClass('icon-dayinji')"
        noneBg
        style="margin-right: 160px"
        @click="handleYuLan"
        >预览</md-button
      >
    </div>
    <div :class="prefixClass('kucungl-wrap-table')">
      <md-form-search-filter
        :model="query"
        :height="40"
        :hideExpandOnSearch="true"
        label-width="110px"
        @search="handleSearch"
        @clear="handleClear"
        @onToogle="handleChangeShowState"
        ref="searchFilter"
      >
        <div v-show="!hasSearchValue" style="display: flex; flex-wrap: wrap">
          <md-form-item label="药品类型" labelWidth="72px">
            <md-select
              v-model="query.yaoPinLXDM"
              placeholder="全部"
              style="width: 219px"
              ref="yaoPinLXSelete"
              @visible-change="isShowSelectOptions($event, 'yaoPinLXSelete')"
            >
              <md-option
                v-for="item in yaoPinLXOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <md-form-item label="剂型" labelWidth="72px">
            <md-select
              v-model="query.jiXingID"
              placeholder="全部类型"
              style="width: 219px"
              @visible-change="isShowSelectOptions($event, 'jiXingSelete')"
              ref="jiXingSelete"
            >
              <md-option
                v-for="item in jiXingOptions"
                :key="item.jiXingID"
                :label="item.jiXingMC"
                :value="item.jiXingID"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <md-form-item label="毒理分类" labelWidth="72px">
            <md-select
              v-model="query.duLiFLDM"
              placeholder="全部类型"
              style="width: 219px"
              @visible-change="isShowSelectOptions($event, 'duLiFLSelete')"
              ref="duLiFLSelete"
            >
              <md-option
                v-for="item in duLiFLOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <md-form-item label="药品名称" labelWidth="72px">
            <biz-yaopindw
              v-model="yaoPinMX"
              type="yk"
              guiGeLX="1"
              :kuCunSFWK="
                topQuery.kuCunSFWK == 0
                  ? null
                  : topQuery.kuCunSFWK == 2
                    ? true
                    : false
              "
              style="width: 219px"
              @change="handleYaoPinChange"
            >
            </biz-yaopindw>
          </md-form-item>
          <md-form-item label="位置" labelWidth="72px">
            <md-input
              v-model="query.baiFangWZ"
              placeholder="请输入"
              width="219px"
            ></md-input>
          </md-form-item>
          <md-form-item label="价值类别" labelWidth="72px">
            <md-select
              v-model="query.jiaZhiFLDM"
              placeholder="全部类型"
              style="width: 219px"
              @visible-change="isShowSelectOptions($event, 'jiaZhiFLSelete')"
              ref="jiaZhiFLSelete"
            >
              <md-option
                v-for="item in jiaZhiLBOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <md-form-item label="药品属性" labelWidth="72px">
            <md-select
              :modelValue="query.yaoPinSXList"
              placeholder="全部属性"
              filterable
              multiple
              remote
              collapse-tags
              :remote-method="fetchYaoPinSX"
              style="width: 219px"
              @change="handleChangeYPSX"
            >
              <md-option
                v-for="item in yaoPinSXOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <md-form-item label="账簿类别" labelWidth="72px">
            <md-select
              v-model="query.zhangBuLBList"
              placeholder="全部属性"
              multiple
              collapse-tags
              style="width: 219px"
            >
              <md-option
                v-for="item in zhangBuLBOptions"
                :value="item.zhangBuLBID"
                :label="item.zhangBuLBMC"
                :key="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </div>
        <div class="search-conditions-result" v-if="hasSearchValue">
          <div class="form-wrap">
            <div class="f-left label">查询条件：</div>
            <div class="content">
              <template v-for="item in queryShow" :key="item.valueKey">
                <span v-show="query[item.valueKey]" class="condition-item">
                  <span>{{ item.label }}：{{ getLabel(item) }} </span>
                  <md-icon name="cha" @click="handleSearchTabClose(item)" />
                </span>
              </template>
            </div>
          </div>
        </div>
      </md-form-search-filter>

      <div :class="prefixClass('table__alias')">
        <md-table-pro
          :columns="columns"
          :stripe="false"
          highlight-current-row
          height="100%"
          :row-class-name="hasTipsName"
          :cell-class-name="hasSDKCName"
          :onFetch="handleFetch"
          @row-click="handleCurrentChange"
          ref="table"
        >
          <template v-slot:yaoPinMCGG="{ row }">
            <YaoPinShow
              :styleData="row.xianShiXX || {}"
              :yaoPinMC="row.yaoPinMC + ' ' + row.yaoPinGG"
            />
            <!-- {{ row.yaoPinGG }} -->
          </template>
          <template v-slot:suoDingKZSL="{ row }">
            <md-popover trigger="click" width="170">
              <div :class="prefixClass('kucunGL_popover')">
                <div :class="prefixClass('kucunGL_popover_item')">
                  <div class="left"><span class="icon1"></span>暂控库存</div>
                  <span class="num1">{{
                    Number(row.zanKongSL).toFixed(3)
                  }}</span>
                </div>
                <div :class="prefixClass('kucunGL_popover_item')">
                  <div class="left"><span class="icon2"></span>退货锁定</div>
                  <span class="num2">{{
                    Number(row.tuiHuoSL).toFixed(3)
                  }}</span>
                </div>
              </div>
              <template v-slot:reference>{{
                Number(row.suoDingKZSL).toFixed(3)
              }}</template>
            </md-popover>
          </template>
        </md-table-pro>
        <div :class="prefixClass('kucungl-table-footer')">
          <span>合计</span>
          进价金额：
          <span :class="prefixClass('number')">{{ jinJiaJE }}</span> 元
          <span style="margin-left: 5px"> 零售金额：</span>
          <span :class="prefixClass('number')">{{ lingShouJJE }}</span> 元
          <span style="margin-left: 5px"> 进零差额：</span>
          <span :class="prefixClass('number')">{{ jinLingCE }}</span> 元
        </div>
      </div>
    </div>

    <div
      :class="prefixClass('expand')"
      v-show="!drawerVisible"
      @click="openDrawer"
    >
      <md-icon name="zuojiantou-k"></md-icon>
    </div>
    <chakankcfb-drawer
      ref="chaKanKCFBDrawer"
      :visible.sync="drawerVisible"
      :class="prefixClass('drawer__alias')"
    />

    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="daYinLXID"
      :fileName="'药库库存单'"
      :title="'药库库存打印预览'"
    />
    <add-lkc-dialog ref="AddLingKuCDialog" @refresh="handleSearch" />
  </div>
</template>

<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
import YaoPinShow from '@/components/YaoPinShow.vue';
import { getJiXingSelectList } from '@/service/xiTongSZ/jiXingWH';
import {
  getShuJuYZYList,
  getYaoPinShuJuYZYList,
} from '@/service/yaoPin/yeWuZD';
import { GetYaoPinTSSX } from '@/service/yaoPinYF/common';
import {
  getPiCiKCCount,
  getPiCiKCList,
  getZongLiangKCCount,
  getZongLiangKCList,
} from '@/service/yaoPinYK/kuCunGL';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { GetCanShuZhi } from '@/system/utils/canShu';
import commonData from '@/system/utils/commonData';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { printByUrl } from '@/system/utils/print';
import { MdFormSearchFilter, MdTablePro } from '@mdfe/medi-ui-pro';
import dayjs from 'dayjs';
import { logger } from '@/service/log';
import ChaKanKCFBDrawer from './components/ChaKanKCFBDrawer';
import AddLingKuCDialog from './components/AddLingKuCunDialog';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
const query = () => ({
  yaoPinLXDM: '',
  yaoPinMC: '',
  jiaGeID: '',
  jiXingID: '',
  duLiFLDM: '',
  baiFangWZ: '',
  fenLeiID: '',
  jiaZhiFLDM: '',
  yaoPinSXList: [],
  zhangBuLBList: [],
});
export default {
  name: 'kucungl',
  data() {
    return {
      kuCunCXMRFW: 1,
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      zhangBuLBOptions: [],
      roles: [],
      params: {},
      xiaoShuDianWS: 2,
      daYinLXID: '',
      yaoPinSXOptions: [],
      topQuery: {
        kuCunSFWK: 0,
        shiFouGYSX: 0,
        shiFouDYXX: 0,
        anPiHXS: 0,
      },
      yaoPinMX: {},
      query: query(),
      hasSearchValue: false,
      queryShow: [
        {
          label: '药品类型',
          key: 'label',
          valueKey: 'yaoPinLXDM',
          options: 'yaoPinLXOptions',
        },
        {
          label: '剂型',
          key: 'jiXingMC',
          valueKey: 'jiXingID',
          options: 'jiXingOptions',
        },
        {
          label: '毒理分类',
          key: 'label',
          valueKey: 'duLiFLDM',
          options: 'duLiFLOptions',
        },
        {
          label: '药品名称',
          key: 'yaoPinMC',
          valueKey: 'yaoPinMC',
        },
        {
          label: '位置',
          key: 'baiFangWZ',
          valueKey: 'baiFangWZ',
        },
        {
          label: '价值类别',
          key: 'label',
          valueKey: 'jiaZhiFLDM',
          options: 'jiaZhiLBOptions',
        },
      ],
      kunCunSFWK: '',
      yaoPinLXOptions: commonData.yaoPinLBArr,
      jiXingOptions: [],
      duLiFLOptions: [],
      yaoPinFLOptions: [],
      jiaZhiLBOptions: [],
      columns: [],
      otherColumns: [
        {
          prop: 'yuanNeiBM',
          label: '院内编码',
          minWidth: 150,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 150,
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 32,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          slot: 'yaoPinMCGG',
          label: '药品名称与规格',
          minWidth: 245,
          showOverflowTooltip: true,
        },
        // {
        //   prop: 'yaoPinGG',
        //   label: '药品规格',
        //   minWidth: 150,
        // },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 180,
          showOverflowTooltip: true,
        },
        // {
        //   prop: 'guoJiaYPID',
        //   label: '国家药品ID',
        //   minWidth: 90,
        // },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 50,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'suoDingKZSL',
          slot: 'suoDingKZSL',
          label: '锁定库存',
          width: 100,
          align: 'right',
        },
        {
          prop: 'kuCunSX',
          label: '库存上限',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'kuCunXX',
          label: '库存下限',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 70,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 90,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.lingShouXSDW);
          },
        },
        {
          prop: 'yiLingWFSL',
          label: '已领未发',
          width: 100,
          align: 'right',
          hidden: true,
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 100,
        },
        {
          prop: 'yiBaoDJMC',
          label: '医保等级',
          width: 120,
        },
        {
          prop: 'kuCunJE',
          label: '库存金额',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return cellValue || cellValue === 0
              ? Number(cellValue).toFixed(this.xiaoShuDianWS)
              : cellValue;
          },
        },
        {
          prop: 'yaoPinYBDM',
          label: '药品医保代码',
          minWidth: 180,
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
        },
      ],
      anPiHXSColumns: [
        {
          prop: 'yuanNeiBM',
          label: '院内编码',
          minWidth: 150,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 150,
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 35,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          slot: 'yaoPinMCGG',
          label: '药品名称与规格',
          minWidth: 245,
        },
        // {
        //   prop: 'yaoPinGG',
        //   label: '药品规格',
        //   minWidth: 150,
        // },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 180,
        },
        // {
        //   prop: 'guoJiaYPID',
        //   label: '国家药品ID',
        //   minWidth: 90,
        // },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 50,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'suoDingKZSL',
          slot: 'suoDingKZSL',
          label: '锁定库存',
          width: 100,
          align: 'right',
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 140,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          formatter: (row, column, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 70,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 90,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.lingShouXSDW);
          },
        },
        {
          prop: 'kuCunJE',
          label: '库存金额',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return cellValue || cellValue === 0
              ? Number(cellValue).toFixed(this.xiaoShuDianWS)
              : cellValue;
          },
        },
        {
          prop: 'yiBaoDJMC',
          label: '医保等级',
          width: 120,
        },
        {
          prop: 'yiLingWFSL',
          label: '已领未发',
          width: 100,
          align: 'right',
          hidden: true,
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 100,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          minWidth: 180,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinYBDM',
          label: '药品医保代码',
          minWidth: 180,
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
        },
      ],
      drawerVisible: false,
      currentRow: null,
      jinJiaJE: '',
      lingShouJJE: '',
      jinLingCE: '',
      xianShiXX: [],
      showQuanYuanKC: '1',
      showYiLingWF: false,
    };
  },
  watch: {
    'topQuery.anPiHXS': {
      handler: function (val) {
        if (!val) this.columns = this.otherColumns;
        if (val) this.columns = this.anPiHXSColumns;
        this.changeColumsHidden('yiLingWFSL', !this.showYiLingWF);
      },
      immediate: true,
    },
  },
  async created() {
    try {
      const res = await getKuFangSZList([
        'jinJiaJEXSDWS',
        'lingShouJEXSDWS',
        'lingShouJXSDWS',
        'jinJiaXSDWS',
        'kuCunCXSFCKQYKC',
        'weiShouLiSFSDKC',
        'kuCunCXMRCXFW',
      ]);
      if (res.length > 0) {
        res.forEach((el) => {
          if (el.xiangMuDM == 'jinJiaXSDWS') {
            this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
          } else if (el.xiangMuDM == 'lingShouJXSDWS') {
            this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
          } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
            this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
          } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
            this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
          } else if (el.xiangMuDM == 'kuCunCXSFCKQYKC') {
            this.showQuanYuanKC = el.xiangMuZDM ? el.xiangMuZDM : '1';
          } else if (el.xiangMuDM == 'weiShouLiSFSDKC') {
            this.showYiLingWF = el.xiangMuZDM === '1' ? true : false;
          } else if (el.xiangMuDM == 'kuCunCXMRCXFW') {
            this.kuCunCXMRFW = el.xiangMuZDM;
          }
        });
        this.topQuery.kuCunSFWK = this.kuCunCXMRFW
          ? Number(this.kuCunCXMRFW)
          : 1;
      }
    } catch (e) {
      console.error(e);
    } finally {
      this.getZiDianSJ();
      this.fetchYaoPinSX('');
      document.addEventListener('keyup', this.handleKeyupSearch);
      this.changeColumsHidden('yiLingWFSL', !this.showYiLingWF);
      //如果是中药库
      const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
      // 判断进价零售价是否设置了值，没有则赋默认值
      this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
      this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
    }
  },

  beforeDestroy() {
    document.removeEventListener('keyup', this.handleKeyupSearch);
  },
  methods: {
    // todo:columns改为入参，拓展为公共函数，记得改哈
    // 修改列的隐藏属性
    changeColumsHidden(prop, val) {
      const index = this.columns.findIndex((item) => item.prop === prop);
      if (index !== -1) {
        this.columns[index].hidden = val;
      }
    },
    handleKeyupSearch(e) {
      if (e.keyCode === 13) {
        this.handleSearch();
      }
    },
    fetchYaoPinSX(query) {
      getShuJuYZYList({
        pageIndex: 1,
        pageSize: 9999,
        likeQuery: query,
        shuJuYLBID: 'YP0001',
      }).then((res) => {
        this.yaoPinSXOptions = res.map((m) => {
          return {
            label: m.biaoZhunMC,
            value: m.biaoZhunDM,
          };
        });
        this.yaoPinSXOptions.unshift({
          label: '全部',
          value: 'all',
        });
      });
    },
    isShowSelectOptions(val, key) {
      if (!val) {
        this.$refs[key].blur();
      }
    },
    // 勾选药品属性
    handleChangeYPSX(val) {
      // 全选
      if (val[val.length - 1] == 'all') {
        this.query.yaoPinSXList = this.yaoPinSXOptions.map((m) => m.value);
        return;
      }
      //判断是否取消勾选了全部
      if (
        val.indexOf('all') == -1 &&
        this.query.yaoPinSXList.indexOf('all') > -1
      ) {
        this.query.yaoPinSXList = [];
        return;
      }
      if (
        (val.length < this.yaoPinSXOptions.length && val.indexOf('all') > -1) ||
        val.length < this.yaoPinSXOptions.length - 1
      ) {
        this.query.yaoPinSXList = val.filter((fl) => fl !== 'all');
        return;
      }
      if (
        val.length == this.yaoPinSXOptions.length - 1 &&
        val.indexOf('all') == -1
      ) {
        this.query.yaoPinSXList = this.yaoPinSXOptions.map((m) => m.value);
        return;
      }
    },
    getLabel(item) {
      let data = null;
      if (item.options) {
        let options = this[item.options];
        let key = item.valueKey === 'jiXingID' ? item.valueKey : 'value';
        data = options.find(
          (item1) => item1[key] === this.query[item.valueKey],
        );
      }
      return data ? data[item.key] : this.query[item.key];
    },
    handleSearchTabClose(item) {
      this.query[item.key] = '';
      this.query[item.valueKey] = '';
    },
    // 新增零库存
    handleZeroKC() {
      this.$refs.AddLingKuCDialog.showModal();
    },
    //预览全部
    async handleYuLanQB() {
      this.daYinLXID = 'YKXT016';
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        ...this.topQuery,
      };
      if (this.query.yaoPinMC) {
        params.yaoPinMC = this.query.yaoPinMC;
        params.jiaGeID = this.query.jiaGeID;
      } else {
        Object.assign(params, this.query);
        params.yaoPinSXList =
          this.query.yaoPinSXList.indexOf('all') > -1
            ? []
            : this.query.yaoPinSXList;
      }
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    //预览
    async handleYuLan() {
      let params = {
        pageIndex: 1,
        pageSize: 9999,
        ...this.topQuery,
      };
      this.daYinLXID = 'YKXT008';
      if (this.query.yaoPinMC) {
        params.yaoPinMC = this.query.yaoPinMC;
        params.jiaGeID = this.query.jiaGeID;
      } else {
        Object.assign(params, this.query);
        params.yaoPinSXList =
          this.query.yaoPinSXList.indexOf('all') > -1
            ? []
            : this.query.yaoPinSXList;
      }
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handleDaYin() {
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        ...this.topQuery,
      };
      if (this.query.yaoPinMC) {
        params.yaoPinMC = this.query.yaoPinMC;
        params.jiaGeID = this.query.jiaGeID;
      } else {
        Object.assign(params, this.query);
        params.yaoPinSXList =
          this.query.yaoPinSXList.indexOf('all') > -1
            ? []
            : this.query.yaoPinSXList;
      }
      try {
        this.pageLoading = true;
        await printByUrl('YKXT016', params);
        this.$message({
          type: 'success',
          message: '打印成功！',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    handleDaoChu() {
      this.$message({
        type: 'success',
        message: '导出成功！',
      });
    },
    handleYaoPinChange(val) {
      this.query = query();
      this.query.yaoPinMC = val.yaoPinMC;
      this.query.jiaGeID = val.jiaGeID;
      this.handleSearch();
    },
    getZiDianSJ() {
      let daiMaList = ['YP0005', 'YP0006', 'YP0007', '7474'];
      let optionKeys = [
        'yaoPinLXOptions',
        'duLiFLOptions',
        'jiaZhiLBOptions',
        'yaoPinFLOptions',
      ];

      getYaoPinShuJuYZYList(daiMaList).then((res) => {
        res.forEach((item, index) => {
          let options = [];
          item.zhiYuList.forEach((item1) => {
            let data = {
              label: item1.biaoZhunMC,
              value: item1.biaoZhunDM,
            };
            options.push(data);
          });
          this[optionKeys[index]] = options;
        });
      });
      getJiXingSelectList().then((res) => {
        this.jiXingOptions = res;
      });
      getZhangBuQXXQ().then((res) => {
        this.zhangBuLBOptions = res.zhangBuLBXXList || [];
      });
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    handleChange(val) {
      let data = val;
      if (val.length === 2) val.splice(0, 1);
      this.topQuery.shiFouGYSX = 0;
      this.topQuery.shiFouDYXX = 0;
      switch (val[0]) {
        case '0':
          this.topQuery.shiFouGYSX = 1;
          break;
        case '1':
          this.topQuery.shiFouDYXX = 1;
          break;
      }
      this.handleSearch();
    },
    handleClear() {
      this.query = query();
      this.yaoPinMX = {};
      this.hasSearchValue = false;
    },
    handleChangeShowState() {
      this.hasSearchValue = false;
    },
    handleSearch() {
      this.hasSearchValue = !!this.query.baiFangWZ || !!this.query.jiaZhiFLDM;
      if (this.query.yaoPinMC && this.query.jiaGeID) {
        this.query.jiXingID = '';
        this.query.duLiFLDM = '';
        this.query.baiFangWZ = '';
        this.query.fenLeiID = '';
        this.query.jiaZhiFLDM = '';
        this.query.jiaZhiFLDM = '';
        this.query.yaoPinLXDM = '';
      }
      this.$refs.table.search({ pageSize: 100 });
    },
    handleCurrentChange(val, column) {
      if (!val) return;
      if (this.currentRow !== val) this.currentRow = val;
      //点击锁定库存时不出现侧滑某块，出现浮层
      if (column.property === 'suoDingKZSL') return;
      this.openDrawer();
    },
    hasTipsName({ row, rowIndex }) {
      if (this.topQuery.anPiHXS === 0) {
        let kuCunSL = row.kuCunSL ? Number(row.kuCunSL) : 0;
        let kuCunXX = row.kuCunXX ? Number(row.kuCunXX) : 0;
        let kuCunSX = row.kuCunSX ? Number(row.kuCunSX) : 0;
        //库存数量小于库存下限 且 维护了库存下限
        if (kuCunSL < kuCunXX && row.kuCunXX) {
          return this.prefixClass('row__tips-low');
        }
        //库存数量大于于库存下限
        if (kuCunSL > kuCunSX && row.kuCunSX) {
          return this.prefixClass('row__tips-high');
        }
      }
      return '';
    },
    //给锁定库存单元格添加鼠标小手，提示可点击查看浮层
    hasSDKCName({ column }) {
      if (column.property === 'suoDingKZSL') {
        return this.prefixClass('cell__pointer');
      }
    },
    async handleFetch({ page, pageSize }, config) {
      let params = {
        pageIndex: page,
        pageSize,
        ...this.topQuery,
      };
      if (this.query.yaoPinMC) {
        // params.yaoPinMC = this.query.yaoPinMC;
        params.jiaGeID = this.query.jiaGeID;
      } else {
        Object.assign(params, this.query);
        params.yaoPinSXList =
          this.query.yaoPinSXList.indexOf('all') > -1
            ? []
            : this.query.yaoPinSXList;
      }
      if (this.topQuery.anPiHXS === 1) {
        let [items, total] = await Promise.all([
          getPiCiKCList(params, config),
          getPiCiKCCount(params, config),
        ]);
        this.jinJiaJE = Number(total.jinJiaJE).toFixed(this.jinJiaJEXSDW);
        this.lingShouJJE = Number(total.lingShouJJE).toFixed(
          this.lingShouJEXSDW,
        );
        this.jinLingCE = (this.lingShouJJE - this.jinJiaJE).toFixed(2);
        await this.getXianShiXX(items);
        const xianShiXX = this.xianShiXX;
        items.forEach((item) => {
          item.xianShiXX = xianShiXX[item.jiaGeID] || {};
        });

        return {
          items: items,
          total: total.count,
        };
      } else {
        let [items, total] = await Promise.all([
          getZongLiangKCList(params, config),
          getZongLiangKCCount(params, config),
        ]);
        this.jinJiaJE = Number(total.jinJiaJE).toFixed(this.jinJiaJEXSDW);
        this.lingShouJJE = Number(total.lingShouJJE).toFixed(
          this.lingShouJEXSDW,
        );
        this.jinLingCE = (this.lingShouJJE - this.jinJiaJE).toFixed(2);
        await this.getXianShiXX(items);
        const xianShiXX = this.xianShiXX;
        items.forEach((item) => {
          item.xianShiXX = xianShiXX[item.jiaGeID] || {};
        });
        return {
          items: items,
          total: total.count,
        };
      }
    },
    openDrawer() {
      if (!this.currentRow) {
        this.$message({
          type: 'warning',
          message: '请先选中一条药品数据',
        });
        return;
      }
      if (this.showQuanYuanKC == '1')
        this.$refs.chaKanKCFBDrawer.openDrawer({ row: this.currentRow });
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        res = await GetYaoPinTSSX({
          jiaGeIDList,
          xianShiLXDM: '1',
        });
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      this.xianShiXX = { ...this.xianShiXX, ...xianShiXX };
    },
  },
  components: {
    'biz-yaopindw': BizYaopindw,
    'md-form-search-filter': MdFormSearchFilter,
    'md-table-pro': MdTablePro,
    'chakankcfb-drawer': ChaKanKCFBDrawer,
    'add-lkc-dialog': AddLingKuCDialog,
    'dayin-dialog': DaYinDialog,
    YaoPinShow,
  },
};
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.#{$md-prefix}-kucungl-wrap {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  min-height: 0;

  .#{$md-prefix}-kucungl-wrap-top {
    display: flex;
    position: relative;
    width: 100%;
    height: 32px;
    align-items: center;
    padding: 0 8px;
    box-sizing: border-box;

    .line {
      width: 1px;
      height: 20px;
      margin-left: 12px;
      background: #ddd;
    }

    .#{$md-prefix}-checkbox {
      margin-left: 12px;
      margin-right: 0;
    }

    /* .#{$md-prefix}-checkbox-group {
      margin-left: 4px;
    } */

    .#{$md-prefix}-button {
      position: absolute;
      right: 8px;
    }
  }

  .#{$md-prefix}-kucungl-table-footer {
    position: absolute;
    bottom: 16px;
    color: #aaa;
    font-size: 14px;

    .#{$md-prefix}-number {
      font-weight: 600;
      color: #222222;
    }
  }

  .#{$md-prefix}-kucungl-wrap-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
    border: 8px solid #f0f2f5;
    position: relative;

    ::v-deep
      .#{$md-prefix}-form-search-filter
      .#{$md-prefix}-form-search-filter__inner {
      padding-top: 8px;
    }

    /* ::v-deep .list-search-filter--expand .list-search-filter__inner {
      border: 0;
    } */

    .#{$md-prefix}-table__alias {
      flex: 1;
      min-height: 0;
      padding: 8px;

      ::v-deep .table-wrapper {
        min-height: 0;
      }

      ::v-deep .#{$md-prefix}-row__tips-low {
        background-color: rgba(255, 153, 0, 0.12);
      }

      ::v-deep .#{$md-prefix}-row__tips-high {
        background-color: #f0f5fb;
      }

      ::v-deep .#{$md-prefix}-cell__pointer {
        cursor: pointer;
      }
    }
  }

  .#{$md-prefix}-drawer__alias {
    position: absolute;
    right: 0;
  }

  .#{$md-prefix}-expand {
    display: block;
    width: 16px;
    height: 56px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: #f0f5fb;
    border: 1px solid #1e88e5;
    border-radius: 4px 0px 0px 4px;
    line-height: 56px;
    text-align: center;
    cursor: pointer;
    border-right: none;
    z-index: 100;
  }

  .search-conditions-result {
    .label {
      line-height: 30px;
      text-align: right;
      float: left;
    }

    .content {
      height: 30px;
      overflow: hidden;
      margin-left: 8px;

      .condition-item {
        display: inline-block;
        line-height: 28px;
        padding: 0 8px;
        border-radius: 4px;
        border: 1px solid $gray-ddd;
        margin-left: 5px;
        margin-bottom: 5px;

        .md-icon {
          margin-left: 8px;
          font-size: 12px;
          color: $gray-999;
          cursor: pointer;
        }
      }
    }
  }

  ::v-deep .#{$md-prefix}-form-search-filter__form {
    flex: 1;
  }

  ::v-deep
    .#{$md-prefix}-checkbox__input.is-disabled
    + span.#{$md-prefix}-checkbox__label {
    color: unset;
  }

  ::v-deep .#{$md-prefix}-form-search-filter__inner {
    padding: 4px 16px;
  }

  ::v-deep
    .#{$md-prefix}-form-search-filter--expand
    .#{$md-prefix}-form-search-filter__inner {
    box-shadow: unset;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-top: unset;
  }

  .#{$md-prefix}-form-search-filter-wrap.is-size-1 .#{$md-prefix}-form-item {
    width: 25%;
  }
}
</style>
<style lang="scss">
@import '@/assets/styles/variables.scss';
.#{$md-prefix}-kucunGL_popover {
  &_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 22px;
    line-height: 22px;
    font-weight: 500;
    color: #222222;
    .left {
      display: flex;
      align-items: center;
    }
    .icon1 {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: var(--md-spacing-3);
      background: #5ad8a6;
    }
    .num1 {
      color: #5ad8a6;
    }
    .icon2 {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: var(--md-spacing-3);
      background: var(--md-color-warning);
    }
    .num2 {
      color: var(--md-color-warning);
    }
  }
}
</style>
