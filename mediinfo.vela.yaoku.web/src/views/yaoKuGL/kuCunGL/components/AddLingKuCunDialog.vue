<template>
  <md-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    v-loading="loading"
    title="新增零库存"
    content-padding="normal"
    :close-on-click-modal="false"
    size="small"
    @closed="handleCloseDialog"
    :before-save="handleSave"
    append-to-body
  >
    <div :class="prefixClass('kucungl-dialog')">
      <md-form
        :model="yaoPinInfo"
        label-width="75px"
        :rules="rules"
        use-status-tooltip-icon
        ref="yaoPinInfo"
        :class="prefixClass('form-header')"
      >
        <md-form-item label="药品名称" prop="yaoPinMCYGG">
          <biz-yaopindw
            v-model="yaoPinInfo.yaoPinMCYGG"
            guiGeLX="1"
            labelKey="yaoPinMC"
            :class="prefixClass('yaopin-select')"
            @change="handleTableYaoPinDWChange($event)"
            :lingKCBZ="true"
          >
          </biz-yaopindw>
        </md-form-item>
        <md-form-item label="规格" prop="yaoPinGG">
          <md-input v-model="yaoPinInfo.yaoPinGG" disabled />
        </md-form-item>
        <md-form-item label="产地" prop="chanDiMC">
          <md-input v-model="yaoPinInfo.chanDiMC" disabled />
        </md-form-item>
        <div class="flex1">
          <md-form-item label="生产批号" prop="shengChanPH">
            <md-input v-model="yaoPinInfo.shengChanPH" style="width: 80%" />
          </md-form-item>
          <div style="flex: 1">
            <md-form-item label="药品效期" prop="yaoPinXQ">
              <md-date-picker
                v-model="yaoPinInfo.yaoPinXQ"
                format="YYYYMMDD"
                type="date"
                style="width: 100%"
              >
              </md-date-picker>
            </md-form-item>
          </div>
        </div>
        <md-form-item label="进价" prop="jinJia">
          <md-input v-model="yaoPinInfo.jinJia" disabled />
        </md-form-item>
      </md-form>
    </div>
    <template v-slot:footer>
      <md-button
        :class="prefixClass('normal__button')"
        @click="handleCloseDialog"
        >取 消</md-button
      >
      <md-button
        :class="prefixClass('normal__button')"
        type="primary"
        @click="handleSave"
        >保 存</md-button
      >
    </template>
  </md-dialog>
</template>
<script>
import { XinZengLKC } from '@/service/yaoPinYK/kuCunGL';
// import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS'
import MdSelectTable from '@mdfe/material.select-table';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
export default {
  name: 'add-lkc-dialog',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      yaoPinInfo: {
        yaoPinMCYGG: {},
        jiaGeID: '',
        yaoPinGG: '',
        chanDiMC: '',
        shengChanPH: '',
        yaoPinXQ: '',
        jinJia: '',
      },
      rules: {
        yaoPinMCYGG: [
          { required: true, message: '现虚库存不能为空', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value.jiaGeID) {
                callback(new Error('请选择药品名称'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
      },
    };
  },
  methods: {
    async showModal() {
      this.dialogVisible = true;
      this.yaoPinInfo = {
        yaoPinMCYGG: {},
        jiaGeID: '',
        yaoPinGG: '',
        chanDiMC: '',
        shengChanPH: dayjs().format('YYYYMMDD'),
        yaoPinXQ: dayjs().add(1, 'years').format('YYYY-MM-DD'),
        jinJia: '',
      };
    },
    handleTableYaoPinDWChange(val) {
      if (val) {
        val.shengChanPH = this.yaoPinInfo.shengChanPH;
        val.yaoPinXQ = this.yaoPinInfo.yaoPinXQ;
        val.yaoPinMCYGG = val;
        Object.assign(this.yaoPinInfo, val);
      } else {
        this.yaoPinInfo = {
          yaoPinMCYGG: {},
          jiaGeID: '',
          yaoPinGG: '',
          chanDiMC: '',
          shengChanPH: this.yaoPinInfo.shengChanPH,
          yaoPinXQ: this.yaoPinInfo.yaoPinXQ,
          jinJia: '',
        };
      }
    },
    handleCloseDialog() {
      this.dialogVisible = false;
      this.$refs.yaoPinInfo.resetFields();
    },
    //点击保存，确认打印
    async handleSave() {
      try {
        const result = await this.$refs.yaoPinInfo.validate();
        if (!result) return;
        const { jiaGeID, yaoPinXQ, shengChanPH } = this.yaoPinInfo;
        await XinZengLKC({ jiaGeID, yaoPinXQ, shengChanPH });
        this.$message({
          type: 'success',
          message: '保存成功！',
        });
        this.handleCloseDialog();
        this.$emit('refresh');
      } catch (error) {
        //取消
        this.$logger.error(error);
      }
    },
  },
  components: {
    'biz-yaopindw': BizYaopindw,
    'md-select-table': MdSelectTable,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-kucungl-dialog {
  .yaoPinInfo {
    /* @include md-linear((color-1 0%, (color-1 0.1) 100%), 180deg); */
    padding: var(--md-spacing-3);
    box-sizing: border-box;

    .yaoPinMC {
      font-weight: 600;
      padding-right: var(--md-spacing-3);
      color: #222;
    }

    .kucunl {
      margin-top: var(--md-spacing-3);
    }
  }

  .#{$md-prefix}-form-header {
    padding: var(--md-spacing-4);
    box-sizing: border-box;

    .flex1 {
      display: flex;
    }
  }
}
</style>
