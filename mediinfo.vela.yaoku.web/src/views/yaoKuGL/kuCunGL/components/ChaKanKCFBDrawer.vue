<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :modal-class="`${prefixClass('bizdrawer')}`"
    :with-header="false"
    :modal="true"
    :append-to-body="false"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    :show-close="false"
    :size="size"
    class="bizdrawer"
    @closed="handleClose"
  >
    <div class="title">
      <span class="title-left">
        {{ title }}
        <span class="shuxian"></span>
      </span>
      <span class="title-close" @click="closeDrawer">
        <md-icon name="cha" />
      </span>
    </div>

    <div class="content">
      <div class="content-desc">
        <h3>
          <span>{{ yaoPinData.yaoPinMC }}</span>
          <span style="margin-left: 4px">{{ yaoPinData.yaoPinGG }}</span>
        </h3>
        <div :class="prefixClass('content-descriptions')">
          <div :class="prefixClass('content-description-item')">
            <label>产地名称：</label><span>{{ yaoPinData.chanDiMC }}</span>
          </div>
        </div>
        <div :class="prefixClass('content-descriptions')">
          <div :class="prefixClass('content-description-item')">
            <label>单位：</label><span>{{ yaoPinData.baoZhuangDW }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>进价：</label
            ><span>{{
              Number(formatJiaGe(yaoPinData.jinJia)).toFixed(this.xiaoShuDianWS)
            }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>零售价：</label
            ><span>{{
              Number(formatJiaGe(yaoPinData.lingShouJia)).toFixed(
                this.xiaoShuDianWS,
              )
            }}</span>
          </div>
        </div>
      </div>
      <md-tabs v-model="activeName">
        <md-tab-pane label="库存分布" name="first">
          <md-table
            :data="tableData"
            :columns="columns"
            :cellClassName="heJiCell"
          >
          </md-table>
        </md-tab-pane>
      </md-tabs>
      <div class="expand" @click="closeDrawer">
        <md-icon name="youjiantou-k"></md-icon>
      </div>
    </div>
  </md-drawer>
</template>

<script>
import { getKuCunFBByJGID } from '@/service/yaoPinYK/kuCunGL';
import { GetCanShuZhi } from '@/system/utils/canShu';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { logger } from '@/service/log';
export default {
  name: 'bizDrawer',
  props: {
    title: { type: String, default: '查看库存分布' },
    size: { type: [String, Number], default: '520px' },
    drawerVisible: Boolean,
    xiaoShuDianWS: { type: [String, Number], default: 2 },
  },
  data() {
    return {
      drawer: false,
      formatJiaGe: formatJiaGe,
      yaoPinData: {},
      xiaoShuDianWS: 2,
      columns: [
        {
          prop: 'zuZhiJGMC',
          label: '机构名称', // 多院区改造
          minWidth: 200,
        },
        {
          prop: 'weiZhiMC',
          label: '药库房名称',
          minWidth: 160,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 80,
          formatter: (row, column, cellValue) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'ruKuWJZSL',
          label: '入库单未记账数量',
          width: 130,
          // formatter: (row, column, cellValue) => {
          //   return Math.abs( Number(cellValue).toFixed(3))
          // }
        },
      ],
      activeName: 'first',
      tableData: [],
    };
  },
  methods: {
    formatJinE(jinE) {
      return jinE ? Number(jinE).toFixed(this.xiaoShuDianWS) : jinE;
    },
    //打开
    openDrawer(option) {
      this.getDetail(option.row.jiaGeID);
      Object.assign(this.yaoPinData, option.row);
      this.drawer = true;
      this.$emit('update:input', true);
    },
    async getDetail(id) {
      let list = await getKuCunFBByJGID({ jiaGeID: id });
      let heJiData = {
        kuCunSL: 0,
        ruKuWJZSL: 0,
        yaoKuFMC: '合计',
        isHeJi: 1,
      };
      list.forEach((item, index) => {
        heJiData.kuCunSL += item.kuCunSL;
        heJiData.ruKuWJZSL += Math.abs(item.ruKuWJZSL);
      });
      list.forEach((item, index) => {
        if (item.weiZhiLX == '3') {
          item.ruKuWJZSL = '-';
        }
      });
      list.push(heJiData);
      this.tableData = list;
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    heJiCell({ row }) {
      return row.isHeJi ? this.prefixClass('heji-row') : '';
    },
    handleClose() {
      this.$emit('closed');
      this.$emit('update:input', false);
    },
  },
  async created() {
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      this.xiaoShuDianWS = await GetCanShuZhi(params);
    } catch (error) {
      logger.error(error);
    }
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-bizdrawer {
  position: absolute !important;
}

.#{$md-prefix}-kucungl-wrap .#{$md-prefix}-overlay {
  background-color: unset;
}
</style>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

::v-deep .#{$md-prefix}-drawer {
  overflow: visible;
}

.bizdrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 520px;

  ::v-deep .#{$md-prefix}-heji-row .cell {
    // color: #1e88e5;
    color: getCssVar('coloc-6');
  }

  .title {
    // background: #f0f5fb;
    background-color: getCssVar('color-1');
    height: 37px;
    line-height: 37px;

    .title-left {
      display: inline-block;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      height: 22px;
      // font-weight:400;
      margin-left: 9px;

      .shuxian {
        width: 1px;
        height: 16px;
        background: #dddddd;
        display: inline-block;
        vertical-align: middle;
        margin-left: 15px;
        margin-right: 15px;
      }
    }

    .title-info {
      .xingming {
        font-size: 16px;
        color: #333333;
        margin-left: 6px;
      }

      .md-icon {
        font-size: 15px;
      }

      .nianling {
        font-size: 14px;
        color: #333333;
        margin-left: 6px;
      }

      .jiuzhenkh {
        font-size: 14px;
        color: #666666;
        margin-left: 10px;
      }

      .jiuzhenkh-info {
        font-size: 14px;
        color: #222222;
        margin-left: 5px;
      }
    }

    .title-close {
      i {
        font-size: 14px;
        float: right;
        margin-right: 14px;
        margin-top: 11px;
        color: #aaaaaa;
        cursor: pointer;
      }
    }
  }
}

.man {
  color: #1e88e5;
}

.woman {
  color: #f12933;
}

::v-deep .md-drawer__body {
  display: flex;
  flex-direction: column;
}

.#{$md-prefix}-content-descriptions {
  display: flex;

  .#{$md-prefix}-content-description-item {
    margin-top: 4px;

    &:nth-last-child(n + 1) {
      margin-right: 28px;
    }

    > label {
      height: 20px;
      color: #666666;
      font-size: 14px;
      line-height: 20px;
    }

    > span {
      height: 20px;
      color: #222222;
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.content {
  flex: 1;
  box-sizing: border-box;
  padding: 8px;
  min-height: 0;

  &-desc {
    width: 100%;
    box-sizing: border-box;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;

    h3 {
      width: 100%;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #222222;
      font-size: 16px;
    }

    .content-desc-com {
      .md-description-item {
        padding: 1px 0;
      }
    }
  }

  .expand {
    display: block;
    width: 16px;
    height: 56px;
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 4px 0px 0px 4px;
    line-height: 56px;
    text-align: center;
    cursor: pointer;
    border-right: none;
  }
}

::v-deep .md-drawer {
  overflow: inherit;
}
</style>
