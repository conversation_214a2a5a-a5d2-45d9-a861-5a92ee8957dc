<template>
  <div
    v-loading="pageLoading"
    element-loading-text="正在加载中..."
    :class="prefixClass('XiaoQiGL-wrap')"
  >
    <div :class="prefixClass('XiaoQiGL-content')">
      <div :class="prefixClass('search-box')">
        <div :class="prefixClass('wrap-button-left')">
          <div :class="prefixClass('XiaoQiGL-selectDay')">
            <md-select
              :class="prefixClass('XiaoQiGL-select')"
              v-model="selectType"
              style="width: 100px"
              :clearable="false"
              @change="handleSelectType"
            >
              <md-option label="失效天数" value="1"></md-option>
              <md-option label="失效月份" value="2"></md-option>
            </md-select>
            <md-input
              v-show="selectType == 1"
              type="number"
              placeholder="输入天数"
              v-model="query.ShiXiaoTS"
              :class="prefixClass('selectType-input')"
              @keyup.enter.native="handlechangeRQ"
              @input="handlechangeRQ"
            >
            </md-input>
            <md-date-picker-range-pro
              :class="prefixClass('selectType-picker')"
              v-show="selectType == 2"
              v-model="query.dataRange"
              range-separator="/"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM"
              value-format="YYYY-MM"
              type="month"
              @change="handlechangeRQ"
            >
            </md-date-picker-range-pro>
          </div>

          <div :class="prefixClass('XiaoQiGL-shuxing')">
            <md-select
              :class="prefixClass('XiaoQiGL-select')"
              v-model="query.chaXunLX"
              style="width: 100px"
              :clearable="false"
              @change="handleChaXunLX"
            >
              <md-option label="药品属性" value="2"></md-option>
              <md-option label="剔除药品属性" value="1"></md-option>
            </md-select>
            <md-select
              :class="prefixClass('XiaoQiGL-select')"
              v-model="query.yaoPinSXList"
              style="width: 200px"
              multiple
              inline
              @change="handlechangeRQ"
            >
              <md-option
                v-for="item in chaXunLXOptions"
                :label="item.fenLeiMC"
                :value="item.fenLeiID"
              ></md-option>
            </md-select>
          </div>

          <biz-yaopindw
            v-model="medicineInfo"
            placeholder="请输入药品名称选择"
            :showSuffix="true"
            type="yk"
            @change="handlerSearchData"
            ref="bizSearch"
          ></biz-yaopindw>
        </div>

        <div :class="prefixClass('wrap-button-right')">
          <md-checkbox-group
            v-model="youXiaoBZList"
            @change="handleCLickYouXiaoType"
            style="margin-right: 8px"
          >
            <md-checkbox :label="-1">
              <span
                :class="prefixClass('text')"
                :style="{ backgroundColor: '#fff' }"
              >
                正常</span
              >
            </md-checkbox>
            <md-checkbox :label="num">
              <span
                :class="prefixClass('text')"
                :style="{ backgroundColor: '#ffd2cc' }"
              >
                <md-input
                  class="xiaoqi-input"
                  v-number
                  :clearable="false"
                  v-model="num"
                  placeholder=""
                  @change="handleNumChange"
                ></md-input>
                个月内有效</span
              >
            </md-checkbox>
            <md-checkbox
              v-for="item in xiaoQiOption"
              :label="item.value"
              :key="item.value"
            >
              <span
                :class="prefixClass('text')"
                :style="{ backgroundColor: item.color }"
              >
                {{ item.label }}</span
              >
            </md-checkbox>
          </md-checkbox-group>
          <!-- <ul :class="prefixClass('colorType')">
            <li @click="handleCLickYouXiaoType(-1)">
              <span :class="prefixClass('colorBlock white')"></span>
              <span :class="prefixClass('text')">正常</span>
            </li>
            <li @click="handleCLickYouXiaoType(4)">
              <span :class="prefixClass('colorBlock red')"></span>
              <span :class="prefixClass('text')">4个月内有效</span>
            </li>
            <li @click="handleCLickYouXiaoType(6)">
              <span :class="prefixClass('colorBlock yellow')"></span>
              <span :class="prefixClass('text')">6个月内有效</span>
            </li>
            <li @click="handleCLickYouXiaoType(12)">
              <span :class="prefixClass('colorBlock green')"></span>
              <span :class="prefixClass('text')">12个月内有效</span>
            </li>
          </ul> -->
          <!--          <md-button type="primary" :icon="prefixClass('icon-daochu')" noneBg-->
          <!--            >导出</md-button-->
          <!--          >-->
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            style="margin-right: 8px"
            @click="handlechangeRQ"
            >刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handleYuLan"
            >预览</md-button
          >
          <!--           <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            :disabled="!canDaYin"
            @click="handleDaYin"
            >打印</md-button
          > -->
          <!-- <md-button
            type="primary"
            :icon="prefixClass('icon-tuichu1')"
            noneBg
            @click="handleTuiKu"
            >退库</md-button
          > -->
        </div>
      </div>
      <div :class="prefixClass('table-wrap')">
        <md-table-pro
          :columns="columns"
          height="100%"
          :class="prefixClass('XiaoQiGL-table')"
          :row-class-name="tableRowClassName"
          :cell-class-name="tableCellClassName"
          :onFetch="handleFetch"
          ref="XiaoQiGLTable"
          @selection-change="handleSelectionChange"
        ></md-table-pro>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT011'"
      :fileName="'效期管理'"
      :title="'效期管理打印预览'"
    />
  </div>
</template>

<script>
import {
  GetShiXiaoYPCount,
  GetShiXiaoYPList,
} from '@/service/yaoPinYK/XiaoQiGL';
import {
  getJiGouID,
  getWeiZhiID,
  getKuuCunGLLX,
} from '@/system/utils/local-cache';
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { debounce, pick } from 'lodash';

import DaYinDialog from '@/components/DaYinDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { printByUrl } from '@/system/utils/print';

import {
  getKuFangSZList,
  getYaoPinSXFJList,
} from '@/service/yaoPin/YaoPinZDJCSJ';

export default {
  name: 'XiaoQiGL',
  data() {
    return {
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      youXiaoBZList: [],
      selectType: '1',
      medicineInfo: '',
      canDaYin: false,
      pageLoading: false,
      params: {},
      query: {
        chaXunLX: '2',
        yaoPinSXList: [],
        dataRange: [],
        ShiXiaoJSY: '',
        ShiXiaoQSY: '',
        ShiXiaoTS: 30,
        youXiaoBZ: '',
      },
      columns: [
        {
          type: 'selection',
          width: 35,
        },
        {
          label: '序号',
          type: 'index',
        },
        {
          label: '药品名称与规格',
          prop: 'yaoPinMC',
          'min-width': 288,
          formatter(row, column, cellValue) {
            if (row.yaoPinMC && row.yaoPinGG) {
              return row.yaoPinMC + ' ' + row.yaoPinGG;
            }
          },
        },

        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 58,
        },
        {
          label: '产地',
          prop: 'chanDiMC',
          'min-width': 200,
          showOverflowTooltip: true,
        },
        {
          label: '效期',
          prop: 'yaoPinXQ',
          width: 120,
          formatter(row, column, cellValue) {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          label: '批号',
          prop: 'shengChanPH',
          'min-width': 103,
          showOverflowTooltip: true,
        },
        {
          label: '供货单位',
          prop: 'gongHuoDWMC',
          'min-width': 200,
          showOverflowTooltip: true,
        },
        {
          label: '库存数量',
          prop: 'kuCunSL',
          width: 88,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(3);
          },
        },
        {
          label: '进价',
          prop: 'jinJia',
          width: 88,
          align: 'right',
          formatter: (row, column, cellValue) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          label: '进价金额',
          prop: 'jinJiaJE',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          label: '零售单价',
          prop: 'lingShouJia',
          width: 88,
          align: 'right',
          formatter: (row, column, cellValue) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.lingShouXSDW);
          },
        },
        {
          label: '零售金额',
          prop: 'lingShouJE',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue) => {
            cellValue = cellValue || 0;
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },
      ],
      selectedList: null,
      xiaoQiOption: [
        {
          label: '6个月内有效',
          color: '#ffe7bd',
          value: 6,
        },
        {
          label: '12个月内有效',
          color: '#c3f5e3',
          value: 12,
        },
      ],
      num: 3,
      chaXunLXOptions: [],
    };
  },
  async created() {
    this.init();
    const arr = await getKuFangSZList([
      'yaoPinSXTS',
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'yaoPinSXTS') {
          if (el.xiangMuZDM) this.query.ShiXiaoTS = el.xiangMuZDM;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  async mounted() {
    await this.iniOptions();
  },
  methods: {
    handleNumChange() {
      this.handleCLickYouXiaoType(this.num);
    },
    async init() {
      const res = await getKuFangSZList(['yaoPinSXTS']);
      const find = res?.find((item) => item.weiZhiID === getWeiZhiID());
      if (find && find.xiangMuZDM) this.query.ShiXiaoTS = find.xiangMuZDM;
      await this.$nextTick();
      this.handlechangeRQ();
    },
    async iniOptions() {
      const params = {
        shuXingLX: this.query.chaXunLX,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
      };
      const res = await getYaoPinSXFJList(params);
      this.chaXunLXOptions =
        res?.reduce((prev, item) => {
          return prev.concat(item?.children || []);
        }, []) || [];
    },
    //预览
    async handleYuLan() {
      let data = this.getParams();
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        ...data,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
        yaoPinSXList: data.yaoPinSXList.join(),
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handleDaYin() {
      let data = this.getParams();
      const params = {
        pageIndex: 1,
        pageSize: 9999,
        ...data,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
        yaoPinSXList: data.yaoPinSXList.join(),
      };
      try {
        this.pageLoading = true;
        await printByUrl('YKXT011', params);
        this.$message({
          type: 'success',
          message: '打印成功！',
        });
      } finally {
        this.pageLoading = false;
      }
    },

    handleTuiKu() {
      if (!this.selectedList || this.selectedList.length === 0) {
        MdMessage.warning('请选择要退库的药');
        return;
      }
      let GongHuoDW = [];
      this.selectedList.forEach((item) => {
        item.ruKuSL = item.kuCunSL;
        item.yaoPinMCYGG = item.yaoPinMC + item.yaoPinGG;
        if (!GongHuoDW.includes(item.gongHuoDWID)) {
          GongHuoDW.push(item.gongHuoDWID);
        }
      });
      if (GongHuoDW.length > 1) {
        MdMessage.warning('供货单位不同，不可以同时退库');
        return;
      }
      this.$store.commit('yaoku/SET_XiaoQiList', this.selectedList);
      this.$router.push({
        path: '/XinZengRKD',
        query: { type: 'yaokuTY' },
      });
    },
    //失效天数月份
    handleCLickYouXiaoType(val) {
      if (val.length > 0) {
        this.youXiaoBZList = [val[val.length - 1]];
        this.query.youXiaoBZ = val[val.length - 1];
      } else {
        this.query.youXiaoBZ = null;
      }
      this.handlechangeRQ();
    },
    handleSelectType() {
      this.query.ShiXiaoTS = null;
      this.query.youXiaoBZ = '';
      this.query.dataRange = [];
      this.youXiaoBZList = [];
    },
    handleChaXunLX() {
      this.query.yaoPinSXList = [];
      if (this.query.chaXunLX == '2') {
        this.handlechangeRQ();
      }
    },
    tableRowClassName({ row, rowIndex }) {
      let className = '';
      if (row.youXiaoBZ == 3) {
        className = this.prefixClass('XiaoQiGL-row-red').trim();
      } else if (row.youXiaoBZ == 6) {
        className = this.prefixClass('XiaoQiGL-row-yellow').trim();
      } else if (row.youXiaoBZ == 12) {
        className = this.prefixClass('XiaoQiGL-row-green').trim();
      }
      return className;
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      let className = '';
      if (columnIndex === 0) {
        className = this.prefixClass('XiaoQiGL-cell-select').trim();
      }
      return className;
    },
    handleSelectionChange(value) {
      this.selectedList = value;
    },
    getParams() {
      let dataRange = this.query.dataRange;
      this.query.ShiXiaoJSY = dataRange[1] ? dataRange[1] : '';
      this.query.ShiXiaoQSY = dataRange[0] ? dataRange[0] : '';
      let data = {};
      if (!this.query.youXiaoBZ && this.query.youXiaoBZ !== 0) {
        data = {
          ...pick(this.query, ['chaXunLX', 'yaoPinSXList']),
          jiaGeID: this.medicineInfo ? this.medicineInfo.jiaGeID : '',
          ShiXiaoJSY: this.selectType == 2 ? this.query.ShiXiaoJSY : '',
          ShiXiaoQSY: this.selectType == 2 ? this.query.ShiXiaoQSY : '',
          ShiXiaoTS: this.selectType == 1 ? this.query.ShiXiaoTS : '',
          weiZhiID: getWeiZhiID(),
          zuZhiJGID: getJiGouID(),
        };
      } else {
        data = {
          ...pick(this.query, ['chaXunLX', 'yaoPinSXList']),
          jiaGeID: '',
          ShiXiaoJSY: '',
          ShiXiaoQSY: '',
          ShiXiaoTS: '',
          youXiaoBZ: this.query.youXiaoBZ || '',
          weiZhiID: getWeiZhiID(),
          zuZhiJGID: getJiGouID(),
        };
      }
      return data;
    },
    async handleFetch({ page, pageSize }, config) {
      let data = this.getParams();
      const params = {
        ...data,
        PageSize: pageSize,
        PageIndex: page,
        weiZhiID: getWeiZhiID(),
        zuZhiJGID: getJiGouID(),
      };
      let [items, total] = await Promise.all([
        GetShiXiaoYPList(params),
        GetShiXiaoYPCount(data),
      ]);
      this.canDaYin = items.length !== 0;
      return { items, total: total };
    },
    //刷新
    handlechangeRQ() {
      this.$refs.XiaoQiGLTable.search({ pageSize: 100 });
    },
    handlerSearchData: debounce(function (data) {
      this.medicineInfo = data;
      this.query.ShiXiaoTS = this.query.ShiXiaoTS
        ? parseInt(this.query.ShiXiaoTS)
        : null;
      if (this.query.ShiXiaoTS <= 0) {
        this.query.ShiXiaoTS = null;
      }
      this.query.youXiaoBZ = '';
      this.handlechangeRQ();
    }, 500),
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-XiaoQiGL-wrap {
  display: flex;
  flex: 1;
  min-height: 0;
  background-color: #f0f2f5;
  padding: 8px;
  .xiaoqi-input {
    width: 30px;
  }
  .#{$md-prefix}-XiaoQiGL-content {
    display: flex;
    height: 100%;
    flex-direction: column;
    width: 100%;
    background: #fff;
    padding: 8px 8px 0;
    box-sizing: border-box;
    .#{$md-prefix}-search-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      margin-bottom: 8px;
      .#{$md-prefix}-wrap-button-left {
        display: flex;
        .#{$md-prefix}-XiaoQiGL-selectDay {
          display: flex;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-right: 12px;
          height: 30px;
          // box-sizing: border-box;
          .#{$md-prefix}-selectType-input {
            width: 142px;
            height: 28px;
            background: transparent;
          }

          ::v-deep .#{$md-prefix}-input__wrapper {
            box-shadow: unset !important;
          }
          .#{$md-prefix}-selectType-picker {
            border: none;
          }
        }

        .#{$md-prefix}-XiaoQiGL-shuxing {
          display: flex;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-right: 12px;
          height: 30px;

          ::v-deep .#{$md-prefix}-input__wrapper {
            box-shadow: unset !important;
          }
          .#{$md-prefix}-selectType-picker {
            border: none;
          }
        }
      }
      .#{$md-prefix}-wrap-button-right {
        flex: 1;
        min-width: 0;
        display: flex;
        text-align: right;
        align-items: center;
        justify-content: flex-end;
        .#{$md-prefix}-colorType {
          display: flex;
          li {
            display: flex;
            align-items: center;
            margin-right: 20px;
            color: #666666;
            font-size: 14px;
            line-height: 14px;
            cursor: pointer;
            span {
              display: inline-block;
              &.#{$md-prefix}-colorBlock {
                background-color: #ffffff;
                margin-right: 4px;
                width: 14px;
                height: 14px;
                box-sizing: border-box;
                &.#{$md-prefix}-white {
                  border: 1px solid #eeeeee;
                }
                &.#{$md-prefix}-red {
                  background: #ffd2cc;
                }
                &.#{$md-prefix}-yellow {
                  background: #ffe7bd;
                }
                &.#{$md-prefix}-green {
                  background: #c3f5e3;
                }
              }
              &.#{$md-prefix}-text {
                line-height: 16px;
              }
            }
          }
        }
      }
    }
    .#{$md-prefix}-table-wrap {
      flex: 1;
      min-height: 0;
      ::v-deep .#{$md-prefix}-checkbox:last-of-type {
        margin-right: 0;
      }
    }
    .#{$md-prefix}-bottom-wrap {
      color: #aaaaaa;
      font-size: 14px;
      line-height: 20px;
      height: 20px;
      text-align: right;
      margin-top: 8px;
      span {
        font-weight: bold;
        color: #222;
      }
    }
  }
}
.#{$md-prefix}-XiaoQiGL-table {
  ::v-deep .#{$md-prefix}-base-table {
    tr.#{$md-prefix}-XiaoQiGL-row-red td {
      background-color: #ffd2cc !important;
    }
    tr.#{$md-prefix}-XiaoQiGL-row-yellow td {
      background-color: #ffe7bd !important;
    }
    tr.#{$md-prefix}-XiaoQiGL-row-green td {
      background-color: #c3f5e3 !important;
    }
    td.#{$md-prefix}-XiaoQiGL-cell-select div {
      min-width: unset;
      padding: 0px;
    }
  }
}

::v-deep .#{$md-prefix}-XiaoQiGL-select .#{$md-prefix}-input__inner {
  border: none;
}
::v-deep .#{$md-prefix}-selectType-input .#{$md-prefix}-input__inner {
  border: none;
  padding-left: 0;
}
::v-deep .#{$md-prefix}-input-field {
  box-shadow: unset;
}
::v-deep .#{$md-prefix}-base-table-column--selection > .cell {
  justify-content: center;
}
</style>
