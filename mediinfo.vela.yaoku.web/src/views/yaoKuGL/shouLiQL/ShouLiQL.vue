<template>
  <div :class="prefixClass('shouliql-wrap')">
    <md-tabs v-model="activeName" :class="prefixClass('custom-tab-default')">
      <md-tab-pane
        v-for="tab in tabs"
        :name="tab.gongNengDM"
        :label="tab.mingCheng"
        lazy
        :key="tab.gongNengDM"
      >
        <component
          v-if="activeName === tab.gongNengDM"
          :is="tab.component"
          :active="activeName === tab.gongNengDM"
          :biaoQian="tab"
          :research="tab.research"
          :moKuaiDM="moKuaiDM"
          :ref="tab.gongNengDM"
          @add-extra="onAddExtra(tab, $event)"
        ></component>
      </md-tab-pane>
      <template slot="extra">
        <component :is="extra"></component>
      </template>
    </md-tabs>
    <shouliql-drawer ref="shouLiQLDrawer" size="75%"> </shouliql-drawer>
  </div>
</template>

<script>
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { MdTabPane, MdTabs } from '@mdfe/medi-ui';
import { defineAsyncComponent } from 'vue';
import shouLiQLDrawer from './components/ShouLiQLDrawer';
export default {
  name: 'shouLiQL',
  provide() {
    return {
      $ShouLiQL: this,
    };
  },
  data() {
    return {
      showZhangBLB: '',
      activeName: 'DaiShouLi',
      tabs: [],
      extra: null,
      moKuaiDM: 'ShouLiQL',
    };
  },
  watch: {
    tabs: {
      handler() {
        this.extra = this.current?.extra;
      },
      deep: true,
    },
    activeName(activeName) {
      this.extra = this.current?.extra;
    },
    // $route: {
    //   //监听路由变化
    //   handler: function(to, from) {
    //     this.initData()
    //   }
    // }
  },
  async created() {
    this.getTabs();
    const arr = await getKuFangSZList(['shiFouAZBLBGL']);
    this.showZhangBLB = arr && arr.length > 0 ? arr[0].xiangMuZDM : '0';
  },

  methods: {
    getTabs() {
      let tabs = [
        {
          url: 'DaiShouLi',
          gongNengDM: 'DaiShouLi',
          mingCheng: '待受理',
        },
        {
          url: 'YiShouLi',
          gongNengDM: 'YiShouLi',
          mingCheng: '已受理',
        },
      ];
      tabs.length > 0 &&
        tabs.forEach((item) => {
          item.component = defineAsyncComponent(() => import(`./${item.url}`));
          item.extra = null;
        });
      this.tabs = tabs;
    },
  },
  components: {
    'md-tabs': MdTabs,
    'md-tab-pane': MdTabPane,
    'shouliql-drawer': shouLiQLDrawer,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-shouliql-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0px;
  .#{$md-prefix}-custom-tab-default {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    ::v-deep .#{$md-prefix}-tabs__header {
      margin-bottom: 8px;
      background: #fff;
    }
    ::v-deep .#{$md-prefix}-tabs__content {
      flex: 1;
      padding: 0 8px;
      padding-bottom: 8px;
    }
  }
}
::v-deep .#{$md-prefix}-tabs__extra {
  height: 33px;
  line-height: 33px;
}
::v-deep .#{$md-prefix}-data-table__pagination {
  height: 33px;
}
</style>
