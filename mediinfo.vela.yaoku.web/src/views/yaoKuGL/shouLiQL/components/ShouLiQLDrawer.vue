<template>
  <md-drawer
    direction="rtl"
    v-model="drawer"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    ref="shouLiQLDrawer"
    :class="prefixClass('shouliqldrawer')"
    :modalClass="prefixClass('shouliqldrawermodal')"
    @closed="closeDrawer"
  >
    <div
      v-loading="loading"
      :loading-text="loadingText"
      :class="prefixClass('drawer__alias')"
    >
      <div :class="prefixClass('shouLiQL-title')">
        <span :class="prefixClass('title-left')">
          {{ title }}
          <span v-if="jiYongBZ == 1" :class="prefixClass('jiyongtag')">急</span>
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <!-- <md-button type="primary" :icon="prefixClass('icon-daochu')" noneBg
              >导出</md-button
            > -->
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handlePrint"
              >打印</md-button
            > -->
            <md-button
              v-show="type === 1"
              type="primary"
              noneBg
              @click="handleShouLi"
              ><i
                class="iconfont iconshenhe1"
                :class="prefixClass('icon-right-4')"
              ></i
              >受理</md-button
            >
            <md-button
              v-show="type === 1"
              type="primary"
              noneBg
              @click="handleJuJue"
              ><i
                class="iconfont iconjinyong"
                :class="prefixClass('icon-right-4')"
              ></i
              >拒绝</md-button
            >
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <div :class="prefixClass('content-zhuangtai')">
          <span
            v-if="type == 2"
            :class="
              prefixClass([
                'zhuangtaitag',
                { jujue: isJuJueDJ, shouli: danJuZTDM == '4' },
              ])
            "
            >{{ !isJuJueDJ ? '已受理' : '已拒绝' }}</span
          >
          <div v-if="type == 2 && isJuJueDJ" class="itemInfo">
            <span class="textStyle">拒绝原因：</span>
            {{ juJueYYMC }}
          </div>
          <div v-if="type == 2 && isJuJueDJ" class="itemInfo">
            <span class="textStyle">备注：</span>
            {{ juJueYYMS }}
          </div>
          <div class="itemInfo">
            <span class="textStyle">请领来源：</span>
            {{ weiZhiMC }}
          </div>
          <div class="itemInfo">
            <span class="textStyle">请领类型：</span>
            {{ qingLingLXMC }}
          </div>
          <div class="itemInfo" v-if="$ShouLiQL.showZhangBLB == 1">
            <span class="textStyle">账簿类别：</span>
            {{ zhangBuLBMC }}
          </div>
          <div class="itemInfo">
            <span class="textStyle">备注：</span>
            {{ beiZhu }}
          </div>
        </div>

        <md-table
          :columns="qingLingDanColumns"
          :data="qingLingDanData"
          :span-method="spanMethod"
          height="100%"
          :class="prefixClass('shouliql-table')"
        >
          <template v-slot:yaoPinMCGG="{ row }">
            <YaoPinShow
              :styleData="row.xianShiXX || {}"
              :yaoPinMC="row.yaoPinMC + ' ' + row.yaoPinGG"
            />
            <!-- {{ row.yaoPinMC + ' ' + row.yaoPinGG }} -->
          </template>
          <template v-slot:xiaoHaoLiang="{ row }">
            <md-popover
              placement="bottom"
              title=""
              :width="315"
              trigger="click"
            >
              <div>
                <md-date-picker
                  v-model="timeRange"
                  type="daterange"
                  :disabled="true"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 93%; margin-bottom: 8px"
                />
                <md-table :columns="columns" :data="data" />
              </div>

              <template v-slot:reference>
                <span
                  :class="prefixClass('xiaohaoliang')"
                  @click="handleRowXHL(row)"
                >
                  {{ Math.abs(Number(row.xiaoHaoLiang)) }}
                </span>
              </template>
            </md-popover>
          </template>
          <template v-slot:jiYongBZ="{ row }">
            <div v-if="row.jiYongBZ == 1" :class="prefixClass('jiYongBZ')">
              <i class="iconfont icongou" />
            </div>
          </template>
          <template v-slot:lengCangBZ="{ row }">
            <div v-if="row.lengCangBZ == 1" :class="prefixClass('lengcangbz')">
              <i class="iconfont icongou" />
            </div>
          </template>
          <template v-slot:biHuan="{ row }">
            <div :class="prefixClass('bihuan')">
              <md-popover
                trigger="click"
                :width="biHuanClass[row.shiFaSL == 0 || !row.shiFaSL ? 0 : 1]"
                :popper-class="prefixClass('bihuan-popper')"
                @show="handleQingLingBH(row)"
              >
                <div
                  v-loading="biHuanLoading"
                  :class="prefixClass(['bihuan-content'])"
                >
                  <div
                    v-for="(item, i) in biHuanList.biHuanXXList"
                    :key="item.xuHao"
                    :class="prefixClass('bihuan-item')"
                  >
                    <div :class="prefixClass('bihuan-item__title')">
                      <span
                        :class="
                          prefixClass([
                            'title-name',
                            !item.danHao ? 'title-name-without' : '',
                          ])
                        "
                        >{{ item.xuHao + '.' + item.mingCheng }}</span
                      >
                      <hr
                        v-show="i !== biHuanList.biHuanXXList.length - 1"
                        :class="
                          prefixClass([
                            'title-xian',
                            !item.danHao ? 'title-xian-without' : '',
                          ])
                        "
                      />
                    </div>
                    <div :class="prefixClass('bihuan-item__time')">
                      {{ item.shiJian }}
                    </div>
                    <div :class="prefixClass('description description-top')">
                      <div
                        :class="prefixClass('description-item')"
                        v-show="item.danHao"
                      >
                        <span :class="prefixClass('description-item__label2')"
                          >操作人:</span
                        >
                        <span
                          :class="prefixClass('description-item__content')"
                          >{{ item.caoZuoRen }}</span
                        >
                      </div>
                      <div
                        :class="prefixClass('description-item')"
                        v-show="item.danHao"
                      >
                        <span :class="prefixClass('description-item__label2')"
                          >单号:</span
                        >
                        <span
                          :class="prefixClass('description-item__content')"
                          >{{ item.danHao }}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <template v-slot:reference>
                  <md-icon name="bihuan"></md-icon>
                </template>
              </md-popover>
            </div>
          </template>
        </md-table>
        <div :class="prefixClass('description')" style="flex-shrink: 0">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">{{
              zhiDanRXM + ' ' + zhiDanSJ
            }}</span>
          </div>
          <div :class="prefixClass('description-item')"></div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YFXT006'"
        :fileName="'受理请领单'"
        :title="'受理请领单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import { GetYaoPinTSSX } from '@/service/yaoPinYF/common';
import eventBus from '@/system/utils/eventbus';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';

import { GetYaoPinXHLMXList } from '@/service/yaoPinYK/caiGouJH.js';
import {
  GetQingLingBHList,
  GetQingLingDMXList,
} from '@/service/yaoPinYK/shouLiQL';

import DaYinDialog from '@/components/DaYinDialog.vue';
import YaoPinShow from '@/components/YaoPinShow.vue';
import { printByUrl } from '@/system/utils/print';
import tableData from '../tableData';
export default {
  name: 'shouLiQLDrawer',
  inject: ['$ShouLiQL'],
  props: {
    size: { type: String, default: '75%' },
  },
  data() {
    return {
      shouLiBZ: '',
      drawer: false,
      loading: false,
      params: {},
      loadingText: '正在加载中...',
      biHuanLoading: false,
      title: '',
      type: 1, // 默认1 1-待受理 2-已受理
      id: '',
      jiYongBZ: 0,
      danJuZTDM: 0,
      zhiDanRXM: '',
      zhiDanSJ: '',
      weiZhiMC: '',
      juJueYYMS: '',
      juJueYYMC: '',
      qingLingLXMC: '',
      zhangBuLBMC: '',
      beiZhu: '-',
      isShow: true,
      timeRange: [],
      columns: [
        {
          label: '药房',
          prop: 'weiZhiMC',
          width: 96,
          align: 'left',
        },
        {
          label: '消耗量',
          prop: 'xiaoHaoLiang',
          width: 100,
          align: 'right',
        },
        {
          label: '库存数量',
          prop: 'kuCunSL',
          width: 100,
          align: 'right',
        },
      ],
      data: [],
      qingLingDanColumns: [],
      qingLingDanData: [],
      spanID: {},
      value: false,
      biHuanClass: [304, 456],
      biHuanList: [],
      xianShiXX: [],
      selectData: {}, // 选种的数据
    };
  },
  computed: {
    isJuJueDJ() {
      return this.danJuZTDM == '5';
    },
  },
  mounted() {
    // 点击页面事件绑定
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    // 打开请领单详情
    openDrawer(options, type) {
      this.loading = true;
      this.title = `详情 - ${options.qingLingDH}`;
      this.type = type ? type : 1;
      this.selectData = options;
      this.jiYongBZ = options.jiYongBZ;
      this.danJuZTDM = options.danJuZTDM;
      this.id = options.qingLingDID;
      this.shouLiBZ = options.shouLiBZ;
      this.zhiDanRXM = options.zhiDanRXM ? options.zhiDanRXM : '';
      this.zhiDanSJ = yaoKuZDJZTimeShow(options.zhiDanSJ);
      this.weiZhiMC = options.weiZhiMC;
      this.juJueYYMS = options.juJueYYMS;
      this.juJueYYMC = options.juJueYYMC;
      this.beiZhu = options.beiZhu;
      this.qingLingLXMC = options.qingLingLXMC;
      this.zhangBuLBMC = options.zhangBuLBMC;

      // 判断1 待受理/ 2 已受理
      this.qingLingDanColumns =
        type == 2
          ? this.isJuJueDJ
            ? tableData.yiJuJueQColumns
            : tableData.yiShouLXQColumns
          : tableData.daiShouLXQColumns;
      this.drawer = true;

      GetQingLingDMXList({
        qingLingDID: options.qingLingDID,
        shouLiBZ: options.shouLiBZ,
      })
        .then(async (res) => {
          this.yaoPinSL = this.qingLingDanData.length;
          this.jinJiaJE = this.qingLingDanData.reduce(
            (total, item) => total + item.jinJiaJE,
            0,
          );
          this.lingShouJE = this.qingLingDanData.reduce(
            (total, item) => total + item.lingShouJE,
            0,
          );
          // 判断行合并
          if (type == 2) {
            //已受理序号
            this.spanID = {};
            let rowIndex = 1;
            let jiaGeID = res[0].jiaGeID;
            res.forEach((item, index) => {
              //序号
              if (jiaGeID == item.jiaGeID) {
                item.xuHao = rowIndex;
              } else {
                jiaGeID = item.jiaGeID;
                rowIndex++;
                item.xuHao = rowIndex;
              }
              if (!this.spanID[item.jiaGeID]) {
                this.spanID[item.jiaGeID] = [];
              }
              this.spanID[item.jiaGeID].push(index);
            });
          } else {
            res.forEach((item, index) => {
              //序号
              item.xuHao = index + 1;
            });
          }
          await this.getXianShiXX(res);
          const xianShiXX = this.xianShiXX;
          res.forEach((item) => {
            item.xianShiXX = xianShiXX[item.jiaGeID] || {};
          });
          this.qingLingDanData = res;

          // console.log('qingLingDanData',this.qingLingDanData[this.qingLingDanData.length-1]);
        })
        .catch((e) => {
          // this.$message({
          //   type: 'error',
          //   message: '获取请领单详情失败！'
          // })
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `获取请领单详情失败！`,
            confirmButtonText: '我知道了',
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    // 点击页面事件
    handleClickBodyCloseDrawer(e) {
      // if (!this.$refs.shouLiQLDrawer.$el.contains(e.target)) {
      //   this.closeDrawer()
      // }
    },
    // 合并行判断
    spanMethod({ row, column, rowIndex }) {
      try {
        // 已受理的才合并行
        if (this.type !== 2) return;
        // 1.行是否合并
        if (
          this.spanID[row.jiaGeID]?.length > 1 &&
          this.spanID[row.jiaGeID].indexOf(rowIndex) >= 0 &&
          this.spanID[row.qingLingSL]?.length > 1 &&
          this.spanID[row.qingLingSL].indexOf(rowIndex) >= 0
        ) {
          // 2.是否是第一行
          if (rowIndex == this.spanID[row.jiaGeID][0]) {
            // 3.列是否合并
            if (
              column.property == 'leiXingMC' ||
              column.property == 'zhiDanSL' ||
              column.property == 'shiFaSL' ||
              column.property == 'danHao' ||
              column.property == 'biHuan'
            ) {
              return [1, 1];
            } else {
              return [2, 1];
            }
          } else {
            if (
              column.property == 'leiXingMC' ||
              column.property == 'zhiDanSL' ||
              column.property == 'shiFaSL' ||
              column.property == 'danHao' ||
              column.property == 'biHuan'
            ) {
              return [1, 1];
            } else {
              return [0, 0];
            }
          }
        } else {
          return [1, 1];
        }
      } catch (error) {
        // console.log(error)
      }
    },
    // 受理
    handleShouLi() {
      this.drawer = false;
      // 单个请领
      eventBus.$emit('qingLingDrawer', this.selectData);
    },
    // 拒绝
    handleJuJue() {
      this.drawer = false;
      eventBus.$emit('handleJuJueQL', this.id);
    },
    // 获取请领单闭环
    handleQingLingBH(row) {
      this.biHuanLoading = true;
      GetQingLingBHList({ qingLingDMXID: row.id, leiXing: row.leiXingDM })
        .then((res) => {
          this.biHuanList = res;
        })
        .finally(() => {
          this.biHuanLoading = false;
        });
    },
    async handleYuLan() {
      const params = {
        qingLingDID: this.id,
        shouLiBZ: this.shouLiBZ,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handlePrint() {
      try {
        this.loading = true;
        this.loadingText = '正在打印中...';
        const params = {
          qingLingDID: this.id,
          shouLiBZ: this.shouLiBZ,
        };
        await printByUrl('YFXT006', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // Message.error(e.message || '打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
        this.loadingText = '正在加载中...';
      }
    },
    async handleRowXHL(row) {
      this.data = [];
      const xiaoHaoKSSJ = row.xiaoHaoKSSJ
        ? dayjs(row.xiaoHaoKSSJ).format('YYYY-MM-DD')
        : dayjs().add(-2, 'day').format('YYYY-MM-DD');
      const xiaoHaoJSSJ = row.xiaoHaoJSSJ
        ? dayjs(row.xiaoHaoJSSJ).format('YYYY-MM-DD')
        : dayjs().startOf('day').format('YYYY-MM-DD');
      const time = [xiaoHaoKSSJ, xiaoHaoJSSJ];
      this.timeRange = time;
      let res = await GetYaoPinXHLMXList({
        jiaGeID: row.jiaGeID,
        kaiShiSJ: time[0],
        jieShuSJ: time[1],
      });
      if (res) {
        this.data = res;
      }
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        res = await GetYaoPinTSSX({
          jiaGeIDList,
          xianShiLXDM: '1',
        });
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      this.xianShiXX = { ...this.xianShiXX, ...xianShiXX };
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
    YaoPinShow,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-bihuan-popper {
  .#{$md-prefix}-bihuan-content {
    display: flex;

    .#{$md-prefix}-bihuan-item {
      &__title {
        display: flex;
        width: 156px;
        // height: 26px;
        align-items: flex-start;
        color: #333333;

        .#{$md-prefix}-title-name {
          display: inline-block;
          width: 120px;
          // height: 26px;
          padding: 4px 8px;
          box-sizing: border-box;
          color: #333333;
          font-size: 14px;
          // line-height: 26px;
          background:
            linear-gradient(135deg, transparent 0, #ade2ff 0) top left,
            linear-gradient(225deg, transparent 9px, #ade2ff 0) top right,
            linear-gradient(-45deg, transparent 9px, #ade2ff 0) bottom right,
            linear-gradient(45deg, transparent 0, #ade2ff 0) bottom left;
          background-color: #ade2ff;
          background-size: 50% 50%;
          background-repeat: no-repeat;

          &-without {
            background:
              linear-gradient(135deg, transparent 0, #ddd 0) top left,
              linear-gradient(225deg, transparent 9px, #ddd 0) top right,
              linear-gradient(-45deg, transparent 9px, #ddd 0) bottom right,
              linear-gradient(45deg, transparent 0, #ddd 0) bottom left;
            background-color: #ddd;
            background-size: 50% 50%;
            background-repeat: no-repeat;
          }
        }

        .#{$md-prefix}-title-xian {
          width: 36px;
          height: 0px;
          margin-top: 12px;
          // margin: auto 0;
          border-top: solid #ade2ff 1px;

          &-without {
            border-top: solid #ddd 1px;
          }
        }
      }

      &__time {
        margin-top: 4px;
        font-weight: 500;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
      }

      .#{$md-prefix}-description-top {
        margin-top: 4px;
        flex-direction: column;

        .#{$md-prefix}-description-item {
          padding: 0 0;
        }
      }
    }
  }
}

.#{$md-prefix}-description {
  display: flex;

  &-item {
    line-height: 20px;
    min-height: 20px;
    font-size: 14px;
    color: #333;
    padding: 5px 0;

    &__label {
      color: #666;
      margin-left: 8px;
    }

    &__label2 {
      color: #666;
    }

    &__content {
      padding-left: 5px;

      &.#{$md-prefix}-fontWeight {
        font-weight: bold;
      }

      .#{$md-prefix}-content-color {
        color: #666;
        font-weight: normal;
      }
    }
  }
}

.#{$md-prefix}-xiaohaoliangtip {
  min-width: 30px;
  // color: #1e88e5;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-shouliqldrawermodal {
  position: initial !important;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-shouliqldrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;

  .#{$md-prefix}-drawer__alias {
    display: flex;
    flex-direction: column;
    height: 100%;

    .#{$md-prefix}-shouLiQL-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-toolbar {
        margin-right: 8px;
      }

      .#{$md-prefix}-icon-right-4 {
        margin-right: 4px;
        font-size: 14px;
      }

      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }

    .#{$md-prefix}-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      padding: 0 8px 8px 8px;

      .#{$md-prefix}-jiYongBZ {
        text-align: center;
        color: #ff9900;
      }

      .#{$md-prefix}-lengcangbz {
        text-align: center;
        color: #1e88e5;
      }

      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }

      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        align-items: center;
        width: 100%;
        flex-wrap: wrap;
        padding: 8px 0 0;
        box-sizing: border-box;
        .itemInfo {
          margin-right: 24px;
          margin-bottom: 8px;
          .textStyle {
            color: #949494;
          }
        }
      }

      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        margin: 3px 8px 0 0;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        margin-bottom: 8px;

        &.#{$md-prefix}-shouli {
          background-color: #e2efff;
          color: #1e88e5;
        }

        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }

      .#{$md-prefix}-shouliql-table {
        flex: 1;
        min-height: 0;

        .#{$md-prefix}-xiaohaoliang {
          cursor: pointer;
          color: rgb(var(--md-color-6));

          &:hover {
            // color: #1e88e5;
            color: rgb(var(--md-color-6));
            text-decoration: underline;
            line-height: 20px;
          }
        }
      }
    }
  }
}

.#{$md-prefix}-pos-bottom-direction {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 98%;
  justify-content: space-between;
}

.#{$md-prefix}-bihuan_width456 {
  width: 456px;
}

.#{$md-prefix}-bihuan_width764 {
  width: 764px;
}

::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}

::v-deep .#{$md-prefix}-description-item {
  color: #222;
}

::v-deep .#{$md-prefix}-description-item {
  font-size: 14px;
}
</style>
