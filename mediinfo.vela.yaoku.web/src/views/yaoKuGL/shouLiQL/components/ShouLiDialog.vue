<template>
  <md-dialog
    v-model="visibleDialog"
    size="large"
    :body-loading="loading"
    title="选择样品"
    :class="prefixClass('shoulidialog')"
    :before-close="closeModal"
    :before-save="handleSave"
  >
    <div v-if="visibleDialog" :class="prefixClass('shoulidialog-top')">
      <md-select-comma
        v-model="formData.duLiFLDMS"
        :options="duLiFLOptions"
        allLabel="全部毒理分类"
        placeholder="请选择"
        class="duLiSelect"
        @change="getData"
      ></md-select-comma>
      <md-checkbox
        v-model="formData.jiYongBZ"
        :true-label="1"
        :false-label="0"
        label="急用"
        :class="prefixClass('space-8')"
        @change="getData"
      >
      </md-checkbox>
      <md-checkbox
        v-model="formData.lengCangBZ"
        :true-label="1"
        :false-label="0"
        label="冷藏"
        :class="prefixClass('space-8')"
        @change="getData"
      >
      </md-checkbox>
    </div>
    <md-table
      ref="table"
      :columns="columns"
      :data="yaoPinData"
      class="demo-table"
      tooltip-effect="dark"
      @selection-change="handleSelectionChange"
    >
      <template v-slot:yaoPinMC="{ row }">
        {{ row.yaoPinMC }} {{ row.yaoPinGG }}
      </template>
      <template v-slot:jiYongBZ="{ row }">
        <i v-if="row.jiYongBZ" class="iconfont icongou" />
      </template>
      <template v-slot:lengCangBZ="{ row }">
        <i v-if="row.lengCangBZ" class="iconfont icongou" />
      </template>
    </md-table>
  </md-dialog>
</template>

<script>
import { MdMessage } from '@mdfe/medi-ui';
import MdSelectComma from '@mdfe/material.select-comma';
import { GetQingLingDMXList, ShouLiQL } from '@/service/yaoPinYK/shouLiQL';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { logger } from '@/service/log';
const formModelInit = () => {
  return {
    qingLingDID: '',
    shouLiBZ: '0',
    jiYongBZ: null, // 是否急用
    lengCangBZ: null, // 是否急用
    duLiFLDMS: [], // 毒理分类
  };
};
export default {
  name: '',
  data() {
    return {
      visibleDialog: false,
      loading: false,
      formData: formModelInit(),
      duLiFLOptions: [],
      yaoPinData: [],
      columns: [
        {
          type: 'selection',
        },
        {
          slot: 'yaoPinMC',
          label: '药品名称与规格',
          minWidth: 200,
          showOverflowTooltip: true,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 160,
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 100,
        },
        {
          prop: 'qingLingSL',
          label: '请领数量',
          width: 100,
          align: 'right',
        },
        {
          slot: 'jiYongBZ',
          label: '急用',
          width: 80,
          align: 'center',
        },
        {
          slot: 'lengCangBZ',
          label: '冷藏',
          width: 80,
          align: 'center',
        },
        {
          prop: 'duLiFLMC',
          label: '毒理分类',
          width: 120,
        },
      ],
      multipleSelection: [],
      resolve: null,
      reject: null,
    };
  },
  methods: {
    showModal(data) {
      this.visibleDialog = true;
      let { id, jiYongBZ, lengCangBZ } = data;
      this.formData.qingLingDID = id;
      // this.formData.jiYongBZ = jiYongBZ
      // this.formData.lengCangBZ = lengCangBZ
      this.getDuLiFL();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    // 获取毒理分类下拉
    getDuLiFL() {
      getYaoPinShuJuYZYList(['YP0006']).then((res) => {
        let zhiYuList = res[0].zhiYuList;
        zhiYuList.forEach((i) => {
          i.label = i.biaoZhunMC;
          i.value = i.biaoZhunDM;
        });
        this.duLiFLOptions = zhiYuList;
        let vals = [];
        this.duLiFLOptions.map((val) => {
          vals.push(val.value);
        });
        this.formData.duLiFLDMS = vals;
        this.getData();
      });
    },
    // 获取药品列表
    async getData() {
      let obj = { ...this.formData };
      obj.duLiFLDMS = this.formData.duLiFLDMS.join();
      let result = await GetQingLingDMXList(obj);
      this.yaoPinData = result;
    },
    // 选择药品
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 点击保存
    handleSave() {
      if (this.multipleSelection.length === 0) {
        MdMessage.warning('请勾选数据');
        return;
      }
      let qingLingDMXIDList = [];
      this.multipleSelection.map((val) => {
        qingLingDMXIDList.push(val.id);
      });
      const params = {
        qingLingDID: this.formData.qingLingDID,
        qingLingDMXIDList,
      };
      this.loading = true;
      ShouLiQL(params)
        .then((res) => {
          MdMessage({
            message: '受理请领成功',
            type: 'success',
          });
          this.closeModal();
          this.resolve(res);
        })
        .catch((e) => {
          console.error(e);
          logger.error(e);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 关闭弹框
    closeModal() {
      this.visibleDialog = false;
      this.formData = formModelInit();
    },
  },
  components: {
    MdSelectComma,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-shoulidialog {
  &-top {
    display: flex;
    margin-bottom: 8px;
    &-select {
      width: 160px;
      margin: 0 8px 8px 0;
    }
    .duLiSelect {
      width: 200px;
      margin-right: 8px;
      ::v-deep .mediinfo-vela-yaoku-web-select__tags {
        padding: 4px 8px;
      }
    }
  }
  .iconfont.icongou {
    color: rgb(var(--md-color-6));
  }
}
</style>
