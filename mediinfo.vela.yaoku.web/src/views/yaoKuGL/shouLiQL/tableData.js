import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';

export default {
  daiShouLiColumns: [
    {
      type: 'selection',
      width: 50,
      showOverflowTooltip: false,
    },
    // {
    //   label: '序号',
    //   prop: 'xuHao',
    //   width:50,
    // },
    {
      slot: 'qingLingDH',
      label: '请领单',
      prop: 'qingLingDH',
      width: 140,
    },
    {
      label: '请领位置',
      prop: 'weiZhiMC',
      width: 140,
      showOverflowTooltip: true,
    },
    {
      label: '请领类型',
      prop: 'qingLingLXMC',
      width: 88,
      showOverflowTooltip: true,
    },
    {
      label: '药品数',
      prop: 'yaoPinZS',
      align: 'right',
      width: 65,
    },
    {
      prop: 'beiZhu',
      label: '备注',
      minWidth: 120,
    },
    {
      slot: 'yaoPinMX',
      label: '药品明细',
      prop: 'yaoPinMX',
      minWidth: 400,
      showOverflowTooltip: true,
    },
    {
      label: '请领日期',
      prop: 'zhiDanSJ',
      width: 160,
      formatter: (row, column, cellValue, index) => {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '请领人',
      prop: 'zhiDanRXM',
      width: 74,
      showOverflowTooltip: true,
    },
    {
      slot: 'operate',
      label: '操作',
      prop: 'caoZuo',
      fixed: 'right',
      width: 84,
    },
  ],
  yiShouLiColumns: [
    {
      slot: 'qingLingDH',
      label: '请领单',
      prop: 'qingLingDH',
      width: 140,
    },
    {
      label: '请领位置',
      prop: 'weiZhiMC',
      width: 104,
      showOverflowTooltip: true,
    },
    {
      label: '请领类型',
      prop: 'qingLingLXMC',
      width: 88,
      showOverflowTooltip: true,
    },
    {
      label: '药品数',
      prop: 'yaoPinZS',
      align: 'right',
      width: 65,
    },
    {
      prop: 'beiZhu',
      label: '备注',
      minWidth: 120,
    },
    {
      slot: 'yaoPinMX',
      label: '药品明细',
      prop: 'yaoPinMX',
      minWidth: 420,
    },
    {
      label: '请领日期',
      prop: 'zhiDanSJ',
      width: 160,
      formatter: (row, column, cellValue, index) => {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '请领人',
      prop: 'zhiDanRXM',
      width: 74,
      showOverflowTooltip: true,
    },
    {
      slot: 'danJuZTDM',
      label: '状态',
      prop: 'danJuZTDM',
      fixed: 'right',
      width: 76,
    },
  ],
  daiShouLXQColumns: [
    {
      label: '序号',
      prop: 'xuHao',
      width: 50,
    },
    {
      slot: 'lengCangBZ',
      label: '冷藏',
      width: 48,
    },
    {
      prop: 'yaoPinLXMC',
      label: '',
      width: 34,
      align: 'center',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 240,
    },
    {
      prop: 'chanDiMC',
      label: '产地',
      width: 220,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
    },
    {
      slot: 'xiaoHaoLiang',
      label: '消耗量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'qingLingSL',
      label: '请领数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'chuKuSL',
      label: '出库数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'caiGouBZ',
      label: '采购包装',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'beiQingLWZKCSL',
      label: '药库库存数量',
      align: 'right',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue));
      },
    },
    {
      slot: 'jiYongBZ',
      label: '急用',
      width: 48,
    },
    {
      prop: 'duLiFLMC',
      label: '毒理分类',
      width: 90,
    },
  ],
  yiShouLXQColumns: [
    {
      label: '序号',
      prop: 'xuHao',
      width: 50,
    },
    {
      slot: 'lengCangBZ',
      label: '冷藏',
      width: 48,
    },
    {
      prop: 'yaoPinLXMC',
      label: '',
      width: 34,
      align: 'center',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 240,
    },
    {
      prop: 'chanDiMC',
      label: '产地',
      width: 200,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
    },
    {
      slot: 'xiaoHaoLiang',
      label: '消耗量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'qingLingSL',
      label: '请领数量',
      align: 'right',
      width: 80,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'chuKuSL',
      label: '出库数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'caiGouBZ',
      label: '采购包装',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      align: 'right',
      width: 90,
    },
    {
      slot: 'jiYongBZ',
      label: '急用',
      width: 46,
    },
    {
      prop: 'duLiFLMC',
      label: '毒理分类',
      width: 90,
    },
    // {
    //   prop: 'leiXingMC',
    //   label: '类型',
    //   width: 60
    // },
    // {
    //   prop: 'zhiDanSL',
    //   label: '制单数量',
    //   align: 'right',
    //   width: 80,
    //   formatter: (row, column, cellValue, index) => {
    //     return Math.abs(Number(cellValue)).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'shiFaSL',
    //   label: '实发数量',
    //   align: 'right',
    //   width: 80,
    //   formatter: (row, column, cellValue, index) => {
    //     return Math.abs(Number(cellValue)).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'chuKuDH',
    //   label: '出库单号',
    //   width: 126,
    // },
    // {
    //   prop: 'qingLingDH',
    //   label: '请领单号',
    //   width: 126,
    // },
    {
      slot: 'biHuan',
      prop: 'biHuan',
      label: '',
      fixed: 'right',
      width: 48,
    },
  ],
  yiJuJueQColumns: [
    {
      label: '序号',
      prop: 'xuHao',
      width: 50,
    },
    {
      slot: 'lengCangBZ',
      label: '冷藏',
      width: 48,
    },
    {
      prop: 'yaoPinLXMC',
      label: '',
      width: 34,
      align: 'center',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 240,
    },
    {
      prop: 'chanDiMC',
      label: '产地',
      width: 200,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
    },
    {
      slot: 'xiaoHaoLiang',
      label: '消耗量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'qingLingSL',
      label: '请领数量',
      align: 'right',
      width: 80,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'chuKuSL',
      label: '出库数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'caiGouBZ',
      label: '采购包装',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      align: 'right',
      width: 90,
    },
    {
      slot: 'jiYongBZ',
      label: '急用',
      width: 46,
    },
    {
      prop: 'duLiFLMC',
      label: '毒理分类',
      width: 90,
    },
    // {
    //   prop: 'leiXingMC',
    //   label: '类型',
    //   width: 60,
    // },
    // {
    //   prop: 'zhiDanSL',
    //   label: '制单数量',
    //   align: 'right',
    //   width: 80,
    //   formatter: (row, column, cellValue, index) => {
    //     return Math.abs(Number(cellValue)).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'shiFaSL',
    //   label: '实发数量',
    //   align: 'right',
    //   width: 80,
    //   formatter: (row, column, cellValue, index) => {
    //     return Math.abs(Number(cellValue)).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'danHao',
    //   label: '单号',
    //   width: 126,
    // },
  ],
};
