<template>
  <md-dialog
    v-model="kczkData.visible"
    :title="title + '药品'"
    :before-close="handleClose"
    :draggable="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="HISYK-kuCunZK-addDialog"
  >
    <md-form :model="kczkData.formData" use-status-icon ref="formValidata">
      <md-row>
        <md-col :span="12">
          <md-form-item label="药品名称" prop="yaoPinMC">
            <BizYaoPinDW
              v-model="kczkData.yaoPinMC"
              v-if="kczkData.visible"
              placeholder="药品名称与规格"
              type="zkzy"
              :columnsList="column"
              :disabled="kczkData.formData.id ? true : false"
              @change="handleChange"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="规格" prop="yaoPinGG">
            <md-input
              v-model="kczkData.formData.yaoPinGG"
              disabled
              placeholder="带出不可改"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="产地" prop="chanDiMC">
            <md-input
              v-model="kczkData.formData.chanDiMC"
              disabled
              placeholder="带出不可改"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="单位" prop="baoZhuangDW">
            <md-input
              v-model="kczkData.formData.baoZhuangDW"
              disabled
              placeholder="带出不可改"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="库存" prop="kuCunSL">
            <md-input
              v-number
              v-model="kczkData.formData.kuCunSL"
              disabled
              placeholder="带出不可改"
              maxLength="5"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="暂控数量" prop="zanKongSL">
            <md-input
              v-number
              v-model="kczkData.formData.zanKongSL"
              maxLength="5"
            ></md-input>
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
    <template #footer>
      <md-button
        v-if="kczkData.formData.id"
        type="danger"
        plain
        class="zfButton"
        @click="handleCancel"
      >
        作废
      </md-button>
      <md-button type="primary" plain @click="handleClose">取消</md-button>
      <md-button type="primary" @click="handleClick" :loading="saveLoading">
        保存
      </md-button>
    </template>
  </md-dialog>
</template>
<script setup>
import { ref, reactive, nextTick, computed, getCurrentInstance } from 'vue';
import { cloneDeep, debounce } from 'lodash';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import formatJiaGe from '@/system/utils/formatJiaGe';
import {
  addKuCunKZXX,
  getKuCunKZXQXX,
  updateKuCunKZXX,
  zuoFeiKuCunKZXX,
} from '@/service/yaoPinYK/kuCunZK';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
defineOptions({ name: 'kuCunZK-addDialog' });
const initFromData = () => {
  return {
    id: null,
    yaoPinMC: null,
    yaoPinGG: null,
    jiaGeID: null,
    chanDiMC: null,
    chanDiID: null,
    baoZhuangDW: null,
    zanKongSL: null,
    kuCunSL: null,
  };
};
const column = Object.freeze([
  {
    label: '',
    prop: 'yaoPinLXMC',
    width: 50,
    align: 'center',
    formatter(v) {
      return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
    },
    showOverflowTooltip: true,
  },
  {
    render: (h, { row, $index }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label =
        row.xianShiXX && row.xianShiXX.tianJiaWZ
          ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
          : row.yaoPinMC;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    label: '药品名称',
    prop: 'yaoPinMCYGG',
    width: 400,
    showOverflowTooltip: true,
  },
  {
    label: '规格',
    prop: 'yaoPinGG',
    width: 200,
    formatter(v) {
      if (v.jiaGeID) {
        return v.yaoPinGG;
      } else {
        return '';
      }
    },
    render: (h, { row, $index }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label = row.yaoPinGG;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    showOverflowTooltip: true,
  },
  {
    label: '产地名称',
    prop: 'chanDiMC',
    width: 140,
    showOverflowTooltip: true,
  },
  {
    label: '单位',
    prop: 'baoZhuangDW',
    width: 50,
  },
  {
    label: '库存量',
    prop: 'kuCunSL',
    align: 'right',
    width: 100,
    formatter: (v) => {
      return v.kuCunSL ? Number(v.kuCunSL).toFixed(2) : Number(0).toFixed(3);
    },
  },
  {
    label: '单价',
    prop: 'danJia',
    width: 70,
    align: 'right',
    formatter: (v) => {
      return v.danJia ? formatJiaGe(v.danJia) : formatJiaGe(0);
    },
  },
]);
let kczkData = reactive({
  formData: {},
  yaoPinMC: {},
  visible: false,
});
let resolve;
let reject;
const showModel = async ({ mode, params }) => {
  kczkData.visible = true;
  kczkData.formData = initFromData();
  await nextTick();
  if (mode == 'edit') {
    const res = await getKuCunKZXQXX({ id: params.id });
    Object.assign(kczkData.formData, { ...res });
    kczkData.yaoPinMC = { ...kczkData.formData };
  }
  return new Promise((resolves, rejects) => {
    resolve = resolves;
    reject = rejects;
  });
};
//父组件调用子组件方法，需要在子组件中使用defineExpose把方法抛出
defineExpose({
  showModel,
});
const title = computed(() => {
  return kczkData.formData.id ? '编辑' : '新增';
});
const handleChange = (datas) => {
  let {
    yaoPinMC,
    yaoPinGG,
    jiaGeID,
    chanDiMC,
    chanDiID,
    baoZhuangDW,
    kuCunSL,
  } = datas;
  Object.assign(kczkData.formData, {
    yaoPinMC,
    yaoPinGG,
    jiaGeID,
    chanDiMC,
    chanDiID,
    baoZhuangDW,
    kuCunSL,
  });
};
const handleClose = () => {
  kczkData.yaoPinMC = {};
  kczkData.visible = false;
};
const handleCancel = async () => {
  await MdMessageBox.confirm('', '确定作废该数据', {
    cancelButtonText: '取消',
    confirmButtonText: '作废',
    type: 'warning',
  });
  await zuoFeiKuCunKZXX({ id: kczkData.formData.id });
  MdMessage({
    type: 'success',
    message: '作废成功!',
  });
  handleClose();
  resolve(true);
};
const saveLoading = ref(false);
const { proxy } = getCurrentInstance();
//防抖和loading
const handleClick = debounce(function () {
  handleSave();
}, 1000);
const handleSave = async () => {
  if (saveLoading.value) return;
  saveLoading.value = true;
  try {
    if (Number(kczkData.formData.zanKongSL) > kczkData.formData.kuCunSL) {
      MdMessage({
        type: 'warning',
        message: '暂控数量不得大于库存数量！',
      });
      saveLoading.value = false;
      return;
    }
    let params = {
      ...kczkData.formData,
    };
    if (kczkData.formData.id) {
      await updateKuCunKZXX(params);
    } else {
      delete params.id;
      await addKuCunKZXX(params);
    }
    saveLoading.value = false;
    resolve(true);
    MdMessage({
      type: 'success',
      message: `${kczkData.formData.id ? '编辑' : '保存'}成功！`,
    });
    handleClose();
  } catch (error) {
    saveLoading.value = false;
    proxy.$logger.error(error);
  }
};
</script>
<style lang="scss" scoped>
.zfButton {
  float: left;
}
</style>
