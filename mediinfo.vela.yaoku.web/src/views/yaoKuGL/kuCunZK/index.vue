<template>
  <div class="HISYK-kuCunZK">
    <div class="HISYK-kuCunZK-header">
      <div class="HISYK-kuCunZK-header-left">
        <BizYaoPinDW
          v-model="yaoPinMC"
          placeholder="药品名称与规格"
          type="zkzy"
          class="ypmc"
          @change="handleYaoPin"
        />
        <md-checkbox
          v-model="qiYongBZ"
          :true-label="1"
          :false-label="0"
          label="只显示启用"
          @change="handleSearch"
        ></md-checkbox>
      </div>
      <md-button
        type="primary"
        :icon="prefixClass('icon-jia')"
        @click="handleAdd('add')"
        >药品</md-button
      >
    </div>
    <div class="HISYK-kuCunZK-content">
      <md-table-pro
        height="100%"
        :columns="columns"
        autoLoad
        resize
        :onFetch="handleFetch"
        ref="table"
      >
        <template v-slot:qiYongBZ="{ row }">
          <md-switch
            v-model="row.qiYongBZ"
            :activeValue="1"
            :inactiveValue="0"
            @change="handleSwitchChange($event, row)"
          ></md-switch>
          <!-- :before-change="onBeforeChange(row)" -->
        </template>
      </md-table-pro>
    </div>
    <KuCunZKDialog ref="addDialog" />
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { MdMessage } from '@mdfe/medi-ui';
import {
  getkuCunKZXXList,
  getkuCunKZXXCount,
  kuCunKZQYBZBG,
} from '@/service/yaoPinYK/kuCunZK';
import KuCunZKDialog from './components/KuCunZKDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
defineOptions({ name: 'KuCunZK' });
const columns = Object.freeze([
  {
    label: '药品名称',
    prop: 'yaoPinMC',
    'min-width': 200,
  },
  {
    label: '规格',
    prop: 'yaoPinGG',
    width: 220,
  },
  {
    label: '产地',
    prop: 'chanDiMC',
    'min-width': 200,
  },
  {
    label: '单位',
    prop: 'baoZhuangDW',
    width: 120,
  },
  {
    label: '暂控数量',
    prop: 'zanKongSL',
    width: 120,
    align: 'right',
  },
  {
    label: '',
    prop: 'qiYongBZ',
    slot: 'qiYongBZ',
    width: 50,
    align: 'center',
  },
  {
    type: 'operate',
    label: '操作',
    width: 60,
    align: 'center',
    count: 1,
    actions: [
      {
        text: '编辑',
        onPressed: ({ row }) => {
          handleAdd('edit', row);
        },
      },
    ],
  },
]);
let yaoPinMC = reactive({});
let qiYongBZ = ref(1);
const handleFetch = async ({ page, pageSize }, config) => {
  const params = {
    pageSize: pageSize,
    pageIndex: page,
    jiaGeID: yaoPinMC.jiaGeID || '',
    qiYongBZ: qiYongBZ.value ? qiYongBZ.value : '',
  };
  const [items, total] = await Promise.all([
    getkuCunKZXXList(params, config),
    getkuCunKZXXCount(params, config),
  ]);
  return {
    items,
    total,
  };
};
const { proxy } = getCurrentInstance();
const handleSwitchChange = async (value, row) => {
  try {
    await kuCunKZQYBZBG({
      id: row.id,
      qiYongBZ: value,
    });
    MdMessage({
      type: 'success',
      message: `${!value ? '停用' : '启用'}成功！`,
    });
    handleSearch();
  } catch (error) {
    row.qiYongBZ = row.qiYongBZ ? 0 : 1;
    MdMessage({
      message: `更新失败!`,
      type: 'error',
    });
    proxy.$logger.error(error);
  }
};
const handleYaoPin = (value) => {
  yaoPinMC = value ? value : {};
  handleSearch();
};
const handleSearch = () => {
  proxy.$refs.table.search();
};
const handleAdd = async (mode, row) => {
  await proxy.$refs.addDialog.showModel({
    mode: mode,
    params: {
      ...row,
    },
  });
  handleSearch();
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.HISYK-kuCunZK {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--md-spacing-3);
  padding-bottom: 0;

  .HISYK-mr8 {
    margin-right: var(--md-spacing-3);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--md-spacing-3);
    &-left {
      display: flex;
      .ypmc {
        margin-right: var(--md-spacing-3);
        width: 300px;
      }
    }
  }
  &-content {
    flex: 1;
    min-height: 0;
  }
}
</style>
