<template>
  <div :class="prefixClass('shuangKongPage')">
    <!-- 左边部分 -->
    <div :class="prefixClass('leftBox')">
      <!-- 查询部分 -->
      <md-row :gutter="4" class="left-content-top">
        <md-col :span="9">
          <!-- 年 -->
          <md-date-picker
            v-model="nianFen"
            type="year"
            placeholder="请选择"
            format="YYYY"
            value-format="YYYY"
            :clearable="false"
            @change="handleSortList()"
          />
        </md-col>
        <md-col :span="9">
          <!-- 月 -->
          <md-select
            v-model="yueFen"
            placeholder="请选择"
            class="monthSelect"
            :clearable="false"
            @change="handleSortList()"
          >
            <md-option
              v-for="item in monthList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
        </md-col>
        <md-col :span="6">
          <md-button
            v-show="weiZhiLXDM == '3'"
            type="primary"
            noneBg
            :disabled="isDisabled"
            @click="handleAddFL('add')"
          >
            <md-icon slot="icon" name="xinzeng" class="md-icon-xinzeng" />
            分类
          </md-button>
        </md-col>
      </md-row>
      <!-- 分类模块 -->
      <md-scrollbar class="scrollbar-height">
        <div class="left-content-sort">
          <!-- 无分类数据时 -->
          <div v-show="!hasLeftData">
            <div :class="prefixClass('nodata')">
              <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
              <span>暂无数据...</span>
            </div>
          </div>
          <!-- 有分类数据时 -->
          <div
            v-show="hasLeftData"
            v-for="item in sortList"
            :key="item.id"
            :class="['sort-box', item.select ? 'active' : '']"
            @click="handleSort(item.id)"
          >
            <!-- 分类编辑 -->
            <md-icon
              v-show="weiZhiLXDM == '3' && !isDisabled"
              name="bianji"
              class="md-icon-bianji"
              @click="handleAddFL('edit', item)"
            />
            <div>{{ item.fenLeiMC }}</div>
            <div>
              <span>数量：</span>
              <span class="green">{{ item.yiShiYSL }}/</span>
              <span class="orange">{{ item.huangXianSL || '-' }}/</span>
              <span class="red">{{ item.hongXianSL || '-' }}</span>
            </div>
            <div>
              <span>金额：</span>
              <span class="green">{{ item.yiShiYJE }}/</span>
              <span class="orange">{{ item.huangXianJE || '-' }}/</span>
              <span class="red">{{ item.hongXianJE || '-' }}</span>
            </div>
          </div>
        </div>
      </md-scrollbar>
    </div>
    <!-- 右边部分 -->
    <div :class="prefixClass('rightBox')" v-loading="rightPageLoading">
      <!-- 表格搜索栏 -->
      <div :class="prefixClass('content-top')">
        <div :class="prefixClass('content-top-filters')">
          <BizYaoPinDW
            v-model="query.jiaGeMC"
            :type="type"
            :columnsList="columnsList"
            :placeholder="placeholder"
            :class="prefixClass('yaopin-search')"
            showSuffix
            style="margin-right: 8px"
            @change="handleYaoPinDWChange($event)"
          />
          <md-checkbox
            v-show="weiZhiLXDM == '3'"
            v-model="query.qiYongBZ"
            :true-label="0"
            :false-label="1"
            @change="handleSearch"
            >只显示停用</md-checkbox
          >
          <md-checkbox
            v-model="query.yiDaoLBZ"
            :true-label="1"
            :false-label="0"
            @change="handleSearch"
            >只显示已到量</md-checkbox
          >
        </div>
        <div :class="prefixClass('content-top-buttons')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
            >刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handleDaYin"
            >预览</md-button
          >
          <md-button
            v-show="weiZhiLXDM == '3' && !isDisabled"
            type="primary"
            :icon="prefixClass('icon-shezhi')"
            noneBg
            @click="handleKongZhiJG"
            >控制机构</md-button
          >
          <md-button
            v-show="weiZhiLXDM == '3' && !isDisabled"
            type="primary"
            :icon="prefixClass('icon-shezhi')"
            noneBg
            @click="handleXianliangSZ"
            >限量设置</md-button
          >
          <md-button
            v-show="weiZhiLXDM == '3' && !isDisabled"
            type="primary"
            :icon="prefixClass('icon-shezhi')"
            noneBg
            @click="handleHuanSuanBL"
            >换算比例</md-button
          >

          <md-button
            v-show="weiZhiLXDM == '3' && !isDisabled"
            type="primary"
            noneBg
            :icon="prefixClass('icon-jia')"
            @click="handleYaoPinXZ('add')"
            >限制</md-button
          >
        </div>
      </div>
      <!-- 表格部分 -->
      <div :class="prefixClass('table-box-border')">
        <md-table-pro
          v-if="fenLeiID"
          :columns="weiZhiLXDM == '3' ? yaokuColumns : yaofangColumns"
          :onFetch="handleFetch"
          :autoLoad="false"
          ref="table"
          height="100%"
          :class="prefixClass('shuangkongYPXL-table')"
          :cell-class-name="tableCellClassName"
        >
          <template #yaoPinMCGG="{ row }">
            <YaoPinShow
              :styleData="row.xianShiXX ? row.xianShiXX : {}"
              :yaoPinMC="row.yaoPinMCGG"
            />
            <!-- {{ row.yaoPinMC }} {{ row.yaoPinGG }} -->
          </template>
          <template #qiYongBZ="{ row }">
            <!-- 0停用，1启用 -->
            <md-switch
              v-model="row.qiYongBZ"
              :activeValue="1"
              :inactiveValue="0"
              @change="handleQiYong(row, 1)"
            />
          </template>
          <template #xianShiQYBZ="{ row }">
            <!-- 0停用，1启用 -->
            <md-switch
              :disabled="!row.qiYongBZ && weiZhiLXDM == '3'"
              v-model="row.xianShiQYBZ"
              :activeValue="1"
              :inactiveValue="0"
              @change="handleQiYong(row, 2)"
            />
          </template>
          <template #operate="{ row, $index }">
            <md-button
              type="primary"
              v-show="weiZhiLXDM == '3' && !isDisabled"
              plain
              noneBg
              @click="handleYaoPinXZ('edit', row)"
            >
              编辑
            </md-button>
            <md-button type="primary" plain noneBg @click="handleJilu(row)">
              记录
            </md-button>
            <md-button
              type="primary"
              plain
              noneBg
              @click="handleXianZhiFW(row)"
            >
              限制范围
            </md-button>
            <md-button
              type="primary"
              plain
              noneBg
              @click="handleMianKongFW(row)"
            >
              免控设置
            </md-button>
          </template>
        </md-table-pro>
      </div>
    </div>

    <biz-yaopinph ref="yaopinph" v-if="false" />

    <!-- 新增分类弹框 -->
    <AddFenLeiDialog ref="addFenLeiDialog" />
    <!-- 新增编辑限制弹框 -->
    <YaoPinXZDialog ref="yaoPinXZDialog" />
    <!-- 限制范围设置 -->
    <XianZhiFWDialog ref="xianZhanFWDialog" />
    <!-- 免控设置 -->
    <MianKongSZDialog ref="mianKongSZDialog" />
    <!-- 限量设置弹框 -->
    <XianLiangSZDialog ref="xiangLiangSZDialog" />
    <KongZhiJGDialog ref="kongZhiJGDialog" />
    <JiLuDrawer ref="jiLuDrawer" />
    <DaYinDialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT030'"
      :fileName="'限量单'"
      :title="'限量单打印预览'"
    />
  </div>
</template>

<script setup>
import {
  onMounted,
  onUnmounted,
  ref,
  reactive,
  nextTick,
  getCurrentInstance,
  h,
} from 'vue';
const { proxy } = getCurrentInstance();
import dayjs from 'dayjs';
import lyra from '@mdfe/lyra';
import { cloneDeep } from 'lodash';
import formatJiaGe from '@/system/utils/formatJiaGe';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import YaoPinShow from '@/components/YaoPinShow.vue';
import { MdMessageBox, MdTooltip, MdMessage } from '@mdfe/medi-ui';
import {
  getSortList,
  GetYaoPinXHLXZPZList,
  GetYaoPinXHLXZPZCount,
  QiTingYXHLXZPZ,
  GengXinHSBL,
} from '@/service/yaoPin/shuangKongYPXL';
import { getJiGouGLPZListByYeWuLXDM } from '@/service/gongYong/zuzhixx.js';
import AddFenLeiDialog from './components/AddFenLeiDialog.vue';
import YaoPinXZDialog from './components/YaoPinXZDialog.vue';
import XianZhiFWDialog from './components/XianZhiFWDialog.vue';
import XianLiangSZDialog from './components/XianLiangSZDialog.vue';
import KongZhiJGDialog from './components/KongZhiJGDialog.vue';
import JiLuDrawer from '../../xiaoHaoZLXZ/components/JiLuDrawer.vue';
import DaYinDialog from '@/components/DaYinDialog.vue';
import MianKongSZDialog from './components/MianKongSZDialog';
onMounted(async () => {
  await handleSortList();
  handleSearch();
});

//左侧搜索查询start
const handleSearch = () => {
  proxy.$refs.table && proxy.$refs.table.search({ pageSize: 500 });
};

//分类模块查询条件变量
let nianFen = ref(dayjs().format('YYYY'));
let yueFen = ref(dayjs().month() + 1);
const monthList = ref([]);
for (let i = 1; i < 13; i++) {
  monthList.value.push({
    value: i,
    label: i + '月',
  });
}

const sortList = ref([]);
let fenLeiID = ref(null);
let fenLeiMC = ref(null);
let huanSuanBL = ref(null);
let isDisabled = ref(false);
const hasLeftData = ref(false);
const hasRightData = ref(true);
//获取分类列表数据
const handleSortList = async (id) => {
  if (
    nianFen.value != dayjs().format('YYYY') ||
    yueFen.value != dayjs().month() + 1
  ) {
    isDisabled.value = true;
  } else if (
    nianFen.value == dayjs().format('YYYY') &&
    yueFen.value == dayjs().month() + 1
  ) {
    isDisabled.value = false;
  }

  const res = await getSortList({
    nianFen: nianFen.value,
    yueFen: yueFen.value,
  });
  if (res == null || res.length == 0) {
    sortList.value = [];
    hasLeftData.value = false;
    hasRightData.value = false;
    handleSearch();
    return;
  }
  hasLeftData.value = true;
  hasRightData.value = true;
  res &&
    res.forEach((item, index) => {
      if ((id && id == item.fenLeiID) || (!id && index == 0)) {
        item.select = true;
        fenLeiID.value = item.fenLeiID;
        huanSuanBL.value = item.huanSuanBL;
        fenLeiMC.value = item.fenLeiMC;
      } else if ((id && id != item.fenLeiID) || (!id && index != 0)) {
        item.select = false;
      }
    });
  sortList.value = [...res];
  loading.value = false;
  if (fenLeiID.value) {
    handleSearch();
  }
};

//分类模块高亮
const handleSort = (id) => {
  sortList.value.forEach((item) => {
    if (item.id == id) {
      item.select = true;
      fenLeiID.value = item.fenLeiID;
      fenLeiMC.value = item.fenLeiMC;
      huanSuanBL.value = item.huanSuanBL;
    } else {
      item.select = false;
    }
  });
  handleSearch();
};

//分类按钮禁用
let disabledSort = ref(false);

// 新增分类
const handleAdd = async (mode, row) => {};

//右侧搜索查询start
const placeholder = '药品名称与规格';
//加载按钮
let rightPageLoading = ref(false);
let loading = ref(true);

let weiZhiLXDM = ref(null);
const { WeiZhiLXDM } = lyra.getShareDataSync();
weiZhiLXDM.value = WeiZhiLXDM;

const handleYaoPinDWChange = async (data) => {
  query.value.jiaGeID = '';
  if (data) {
    if (data.jiaGeID) query.value.jiaGeID = cloneDeep(data.jiaGeID);
  }
  await handleSearch();
};

//搜索表格
const columnsList = Object.freeze([
  {
    label: '',
    prop: 'yaoPinLXMC',
    width: 50,
    align: 'center',
    formatter(v) {
      return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
    },
    showOverflowTooltip: true,
  },
  {
    render: (h, { row, $index }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label =
        row.xianShiXX && row.xianShiXX.tianJiaWZ
          ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
          : row.yaoPinMC;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    label: '药品名称',
    prop: 'yaoPinMCYGG',
    width: 400,
    showOverflowTooltip: true,
  },
  {
    label: '规格',
    prop: 'yaoPinGG',
    width: 200,
    formatter(v) {
      if (v.jiaGeID) {
        return v.yaoPinGG;
      } else {
        return '';
      }
    },
    render: (h, { row, $index }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label = row.yaoPinGG;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    showOverflowTooltip: true,
  },
  {
    label: '产地名称',
    prop: 'chanDiMC',
    width: 140,
    showOverflowTooltip: true,
  },
  {
    label: '单位',
    prop: 'baoZhuangDW',
    width: 50,
  },
  {
    label: '单价',
    prop: 'danJia',
    width: 70,
    align: 'right',
    formatter: (v) => {
      return formatJiaGe(v.danJia);
    },
  },
]);
//右侧搜索查询end

// 右侧表格

const query = ref({
  jiaGeID: '',
  jiaGeMC: '',
  qiYongBZ: 1,
  yiDaoLBZ: 0,
});
const params = ref({}); // 打印插件参数
const tableData = ref([]);
const xianShiXX = ref([]);
const handleFetch = async ({ page, pageSize }, config) => {
  if (!fenLeiID.value) return;
  if (!hasRightData.value) {
    items = [];
    total = 0;
    return { items, total };
  } else {
    rightPageLoading.value = true;
    let params = null;
    //只显示停用
    if (query.value.qiYongBZ === 0) {
      params = query.value;
    } else {
      params = { jiaGeID: query.value.jiaGeID };
    }
    params = {
      ...params,
      fenLeiID: fenLeiID.value,
      yiDaoLBZ: query.value.yiDaoLBZ,
      pageIndex: page,
      pageSize: pageSize,
    };
    let [items, total] = await Promise.all([
      GetYaoPinXHLXZPZList(params, config),
      GetYaoPinXHLXZPZCount(params, config),
    ]);
    if (items.length > 0) {
      await getXianShiXX(items);
      items.forEach((item) => {
        item.leiXing = item.yaoPinLXMC ? item.yaoPinLXMC.charAt(0) : '';
        item.xianShiXX = xianShiXX[item.jiaGeID] || {};
        item.yaoPinMCGG = item.yaoPinMC + ' ' + item.yaoPinGG;
      });
    }
    tableData.value = cloneDeep(items);
    rightPageLoading.value = false;
    return { items, total };
  }
};

// 获取药品显示信息
const type = 'ypxh';
const getXianShiXX = async (data) => {
  const list = data.filter((item) => item.jiaGeID);
  const xianShiKeys = Object.keys(xianShiXX.value);
  let jiaGeIDList = [];
  list.forEach((item) => {
    if (!xianShiKeys.includes(item.jiaGeID)) {
      jiaGeIDList.push(item.jiaGeID);
    }
  });

  if (jiaGeIDList.length === 0) return;
  let isError = false;
  let res = null;
  try {
    res = await GetYaoPinTSSX({
      jiaGeIDList,
      xianShiLXDM: '1',
    });
  } catch (e) {
    isError = true;
  }
  if (isError) return;
  const xianShiXX1 = {};
  if (res.length === 0) {
    jiaGeIDList.forEach((item) => {
      xianShiXX1[item] = {};
    });
  } else {
    res.forEach((item) => {
      xianShiXX1[item.jiaGeID] = item;
    });
  }

  xianShiXX.value = { ...xianShiXX, ...xianShiXX1 };
};

// 已使用数量>红线消耗量，变红
const tableCellClassName = ({ row, column, rowIndex, columnIndex }) => {
  if (
    (column.property === 'shiJiXHL' || column.property === 'shiJiXHJE') &&
    Number(row.shiJiXHL > Number(row.xianZhiZL))
  ) {
    return 'qinglingdan-row';
  }
};

//启用
const handleQiYong = async (row, type) => {
  await QiTingYXHLXZPZ({ id: row.id, qiYongLX: type });
  handleSearch();
};
const handleDaYin = async () => {
  params.value = {
    fenLeiID: fenLeiID.value,
  };
  await proxy.$refs.daYinDialog.showModal();
};
// 药库表格
const yaokuColumns = Object.freeze([
  {
    prop: 'yuanNeiBM',
    label: '院内编码',
    width: 100,
  },
  {
    prop: 'shengPingTBM',
    label: '省平台ID',
    width: 100,
  },
  {
    prop: 'leiXing',
    label: '',
    width: 40,
    showOverflowTooltip: false,
  },
  {
    slot: 'yaoPinMCGG',
    label: '药品名称与规格',
    minWidth: 240,
  },
  {
    prop: 'chanDiMC',
    label: '产地名称',
    width: 280,
    showOverflowTooltip: false,
  },

  {
    prop: 'baoZhuangDW',
    label: '单位',
    width: 60,
    showOverflowTooltip: false,
  },
  {
    prop: 'xianZhiZL',
    label: '红线消耗量',
    width: 100,
    align: 'center',
  },
  {
    prop: 'menZhenHXSL',
    label: '门诊红线数量',
    width: 106,
    align: 'center',
    formatter: (row) => {
      return row.menZhenHXSL || '';
    }
  },
  {
    prop: 'zhuYuanHXSL',
    label: '住院红线数量',
    width: 106,
    align: 'center',
    formatter: (row) => {
      return row.zhuYuanHXSL || '';
    }
  },
  {
    prop: 'hongXianXHJE',
    label: '红线金额',
    width: 96,
    align: 'center',
  },
  {
    prop: 'tiShiLiang',
    label: '黄线消耗量',
    width: 100,
    align: 'center',
  },
  {
    prop: 'huangXianXHJE',
    label: '黄线金额',
    width: 96,
    align: 'center',
  },
  {
    prop: 'tiShiBL',
    label: '换算比例',
    width: 96,
    align: 'center',
  },
  {
    prop: 'shiJiXHL',
    label: '已使用数量',
    width: 90,
  },
  {
    prop: 'shiJiXHJE',
    label: '已使用金额',
    width: 120,
  },
  {
    label: '启用',
    slot: 'qiYongBZ',
    width: 50,
    align: 'center',
    fixed: 'right',
  },
  {
    slot: 'xianShiQYBZ',
    width: 94,
    align: 'center',
    fixed: 'right',
    renderHeader: ({ column }) => {
      return h('div', [
        h('span', '限时启用'),
        h(MdTooltip, null, {
          content: () =>
            h(
              'span',
              { style: 'color: #fff;font-size:12px;' },
              '启用5-10分钟后自动关闭',
            ),
          default: () =>
            h('md-icon', {
              class: 'mediinfo-vela-yaoku-web-icon-wenhao',
              style: 'color:#222;cursor:pointer;margin-left:4px',
            }),
        }),
      ]);
    },
  },
  {
    slot: 'operate',
    label: '操作',
    width: 220,
    fixed: 'right',
  },
]);
// 药房表格
const yaofangColumns = Object.freeze([
  {
    prop: 'yuanNeiBM',
    label: '院内编码',
    width: 100,
  },
  {
    prop: 'shengPingTBM',
    label: '省平台ID',
    width: 100,
  },
  {
    prop: 'leiXing',
    label: '',
    width: 40,
    showOverflowTooltip: false,
  },
  {
    slot: 'yaoPinMCGG',
    label: '药品名称与规格',
    minWidth: 240,
  },
  {
    prop: 'chanDiMC',
    label: '产地名称',
    'min-width': 240,
    showOverflowTooltip: false,
  },

  {
    prop: 'baoZhuangDW',
    label: '单位',
    width: 60,
    showOverflowTooltip: false,
  },
  {
    prop: 'tiShiBL',
    label: '换算比例',
    width: 96,
    align: 'center',
  },
  // {
  //   prop: 'xianZhiZL',
  //   label: '红线消耗量',
  //   width: 100,
  //   align: 'center',
  // },
  // {
  //   prop: 'hongXianXHJE',
  //   label: '红线金额',
  //   width: 96,
  //   align: 'center',
  // },
  // {
  //   prop: 'tiShiLiang',
  //   label: '黄线消耗量',
  //   width: 100,
  //   align: 'center',
  // },
  // {
  //   prop: 'huangXianXHJE',
  //   label: '黄线金额',
  //   width: 96,
  //   align: 'center',
  // },
  // {
  //   prop: 'shiJiXHL',
  //   label: '已使用数量',
  //   width: 90,
  // },
  // {
  //   prop: 'shiJiXHJE',
  //   label: '已使用金额',
  //   width: 120,
  // },
  {
    label: '限时启用',
    slot: 'xianShiQYBZ',
    width: 94,
    align: 'center',
    renderHeader: ({ column }) => {
      return h('div', [
        h('span', '限时启用'),
        h(MdTooltip, null, {
          content: () =>
            h(
              'span',
              { style: 'color: #fff;font-size:12px;' },
              '启用5-10分钟后自动关闭',
            ),
          default: () =>
            h('md-icon', {
              class: 'mediinfo-vela-yaoku-web-icon-wenhao',
              style: 'color:#222;cursor:pointer;margin-left:4px',
            }),
        }),
      ]);
    },
  },
  {
    slot: 'operate',
    label: '操作',
    width: 140,
    fixed: 'right',
  },
]);

//  ********************
// 新增分类弹框1
const handleAddFL = async (mode, row) => {
  let res = await proxy.$refs.addFenLeiDialog.showModel({
    mode: mode,
    params: {
      ...row,
      nianFen: nianFen.value,
      yueFen: yueFen.value,
    },
  });
  if (res) {
    // 更新左侧数据

    handleSortList(fenLeiID.value);
  }
};

// 控制机构弹窗
const handleKongZhiJG = async () => {
  let res = await proxy.$refs.kongZhiJGDialog.showModel();
};

// 限量设置弹框
const handleXianliangSZ = async () => {
  let res = await proxy.$refs.xiangLiangSZDialog.showModel({
    fenLeiID: fenLeiID.value,
    fenLeiMC: fenLeiMC.value,
  });
  if (res) {
    // 更新左侧数据
    handleSortList(fenLeiID.value);
  }
};

// 新增/编辑限制弹框
const handleYaoPinXZ = async (mode, row) => {
  let res = await proxy.$refs.yaoPinXZDialog.showModel({
    mode: mode,
    fenLeiList: sortList.value,
    data: {
      nianFen: nianFen.value,
      yueFen: yueFen.value,
      fenLeiID: fenLeiID.value,
      fenLeiMC: fenLeiMC.value,
      tiShiBL: huanSuanBL.value,
      ...row,
    },
  });
  if (res) {
    // 更新右侧数据
    handleSearch();
  }
};
// 限制范围弹框
const handleXianZhiFW = async (row) => {
  await proxy.$refs.xianZhanFWDialog.showModel({
    params: {
      nianFen: nianFen.value,
      yueFen: yueFen.value,
      fenLeiID: fenLeiID.value,
      fenLeiMC: fenLeiMC.value,
      ...row,
    },
  });
};
const handleMianKongFW = async (row) => {
  await proxy.$refs.mianKongSZDialog.showModel({
    params: {
      nianFen: nianFen.value,
      yueFen: yueFen.value,
      fenLeiID: fenLeiID.value,
      fenLeiMC: fenLeiMC.value,
      ...row,
    },
  });
  handleSearch();
};
//记录 打开详情
const handleJilu = async (row) => {
  await proxy.$refs.jiLuDrawer.openDrawer(row);
};

//换算比例
const handleHuanSuanBL = () => {
  MdMessageBox.prompt('换算比例', '', {
    confirmButtonText: '应用',
    inputValue: huanSuanBL.value,
    // showCancelButton: false,
    closeOnClickModal: false,
    cancelButtonText: '取消',
  })
    .then(async ({ value }) => {
      let params = {
        fenLeiID: fenLeiID.value,
        huanSuanBL: value,
      };
      let res = await GengXinHSBL(params);
      if (res) {
        MdMessage({
          type: 'success',
          message: `保存成功`,
        });
        handleSortList(fenLeiID.value);
      }
    })
    .catch((err) => {});
};

// ************************
</script>

<style lang="scss">
.#{$md-prefix}-shuangkongYPXL-table.#{$md-prefix}-data-table .qinglingdan-row {
  background-color: #ffd2cc !important;
  color: red;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-shuangKongPage {
  display: flex;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 8px;
  background-color: #f0f2f5;
}

.#{$md-prefix}-rightBox {
  flex: 1;
  min-width: 0;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: #fff;
  margin-left: 8px;
  padding: 8px;
  box-sizing: border-box;
  padding-bottom: 0;
}

.#{$md-prefix}-table-box-border {
  flex: 1;
  min-height: 0;
}

.#{$md-prefix}-content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  height: 30px;

  &-filters {
    display: flex;

    .#{$md-prefix}-filter-width {
      width: 230px;
    }
  }

  &-buttons {
    display: flex;
  }
}

.#{$md-prefix}-radio-box {
  height: 36px;
  display: flex;
  align-items: center;
  // background-color: #edf6fd;
  background-color: rgb(var(--md-color-1));
  padding: 0 8px;
}

.#{$md-prefix}-radio-text {
  height: 20px;
  color: #222222;
  font-size: 14px;
  line-height: 20px;
}
.#{$md-prefix}-nodata {
  height: 400px;
}
.#{$md-prefix}-leftBox {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  .md-icon-xinzeng {
    margin-right: 8px;
    cursor: pointer;
  }
  .left-content-top {
    height: 30px;
    display: flex;
    align-items: center;
  }
  .scrollbar-height {
    flex: 1;
  }
  .left-content-sort {
    padding: 8px 8px 0 8px;
    .sort-box {
      width: calc(100% - 16px);
      height: 60px;
      padding: 12px 8px;
      background: #f5f5f5;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      margin-bottom: 8px;
      position: relative;
      cursor: pointer;
      .md-icon-bianji {
        color: #1385f0;
        position: absolute;
        top: 8px;
        right: 8px;
      }
      .green {
        color: #4ac110;
      }
      .orange {
        color: #ff8600;
      }
      .red {
        color: #f12933;
      }
    }
    .active {
      background: #b8ddff;
    }
  }
}
</style>
