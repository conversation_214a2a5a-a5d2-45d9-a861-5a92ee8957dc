<template>
  <md-dialog
    v-model="initData.visible"
    title="控制机构"
    size="small"
    :draggable="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    class="HISYAOKU-kongZhiJG"
  >
    <div class="HISYAOKU-kongZhiJG-content">
      <md-table :columns="columns" :data="tableData" edit class="zkzy_table">
        <template v-slot:keGuanLJGMC="{ row, $index }">
          <md-select
            v-model="row.keGuanLJGID"
            class="m-2"
            placeholder="请选择"
            @change="handleJiGou($event, row, $index)"
          >
            <md-option
              v-for="item in keGuanLJGOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </md-select>
        </template>
      </md-table>
    </div>
    <template #footer>
      <md-button type="primary" plain @click="handleClose">取消</md-button>
      <md-button type="primary" @click="handleSave"> 保存 </md-button>
    </template>
  </md-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, nextTick } from 'vue';
import { MdMessage } from '@mdfe/medi-ui';
import lyra from '@mdfe/lyra';
import {
  getJiGouGLPZListByYeWuLXDM,
  saveJiGouGLPZ,
} from '@/service/gongYong/zuzhixx.js';

// 组件名称
defineOptions({ name: 'kongZhiJGDialog' });
const { proxy } = getCurrentInstance() as any;
interface Item {
  [key: string]: any;
}
// 定义初始化数据
const initData = reactive({
  visible: false,
});
let deleteIDList: any = [];
let requestPamras: any = [];
// 表格数据
let tableData = ref<Item[]>([]);
const keGuanLJGOptions = ref<[]>([]);
// 删除行
const delRow = (row: any, $index: any) => {
  if (row.id) {
    deleteIDList.push(row.id);
  }
  tableData.value.splice($index, 1);
  if (tableData.value[tableData.value.length - 1].keGuanLJGID) {
    addRow();
  }
};
const initRow = () => {
  return {
    yeWuLXDM: '2',
    yeWuLXMC: '双控药品限制范围',
    keGuanLJGID: '',
    keGuanLJGMC: '',
    shunXuHao: null,
  };
};
const addRow = () => {
  if (
    !tableData.value[tableData.value.length - 1]?.keGuanLJGID &&
    tableData.value.length !== 0
  )
    return;
  else {
    tableData.value.push(initRow());
  }
};
const columns = Object.freeze([
  {
    slot: 'keGuanLJGMC',
    label: '可管理机构名称',
  },
  {
    type: 'operate',
    label: '',
    count: 1,
    width: 44,
    align: 'center',
    actions: [
      {
        icon: 'mediinfo-vela-yaoku-web-icon-shanchu',
        type: 'danger',
        onPressed: ({ row, $index }) => {
          delRow(row, $index);
        },
        hidden: ({ row, $index }) => {
          if (row.keGuanLJGID) return false;
          else return true;
        },
      },
    ],
  },
]);
const { JiGouID, JiGouMC } = lyra.getShareDataSync();
let resolve: (arg0: boolean) => void;
let reject;
const showModel = async () => {
  initData.visible = true;
  deleteIDList = [];
  await getJiGouGLPZListByYeWuLXDMFunc();
  await getJiGouOptions();
  await nextTick();
  return new Promise((resolves, rejects) => {
    resolve = resolves;
    reject = rejects;
  });
};
const handleJiGou = async (val: any, row: any, index: number) => {
  const isRepeat =
    tableData.value.filter((item) => item.keGuanLJGID === val).length > 1;
  if (isRepeat) {
    tableData.value[index] = initRow();
    MdMessage({
      type: 'error',
      message: '已存在该机构',
    });
    return;
  } else {
    const matchedItem = keGuanLJGOptions.value.find(
      (item) => item.keGuanLJGID === val,
    );
    matchedItem.yeWuLXDM = '2';
    matchedItem.yeWuLXMC = '双控药品限制范围';

    Object.assign(tableData.value[index], matchedItem);
    addRow();
  }
};
// 使用defineExpose抛出父组件调用的方法
defineExpose({ showModel });
// 获取字典数据
const getJiGouOptions = async function () {
  const res = await getJiGouGLPZListByYeWuLXDM({
    yeWuLXDM: 1,
  });

  keGuanLJGOptions.value = res
    .find((item: any) => item.guanLiJGID === JiGouID)
    .keGuanLJGList.map((item: any) => ({
      value: item.keGuanLJGID,
      label: item.keGuanLJGMC,
      ...item,
    }));
};

// 获取已保存的数据
const getJiGouGLPZListByYeWuLXDMFunc = async () => {
  const res = await getJiGouGLPZListByYeWuLXDM({
    yeWuLXDM: 2,
  });
  requestPamras = res;
  tableData.value = res?.[0]?.keGuanLJGList || [];
  addRow();
};

// 保存数据
const handleSave = async () => {
  if (requestPamras.length === 0) {
    requestPamras.push({
      guanLiJGID: JiGouID,
      guanLiJGMC: JiGouMC,
      keGuanLJGList: [],
    });
  }
  for (let item of requestPamras) {
    if (item.keGuanLJGList && item.keGuanLJGList.length > 0) {
      item.keGuanLJGList = item.keGuanLJGList.filter((i: any) => i.keGuanLJGID);
    }
  }
  requestPamras[0].keGuanLJGList = tableData.value.filter(
    (i: any) => i.keGuanLJGID,
  );
  requestPamras[0].deleteIDList = deleteIDList;
  let res = await saveJiGouGLPZ(requestPamras);
  if (res) {
    MdMessage({
      type: 'success',
      message: '保存成功',
    });
    resolve(true);
    handleClose();
  }
};
// 关闭弹框并初始化数据
const handleClose = () => {
  tableData.value = [];
  initData.visible = false;
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.HISYAOKU-kongZhiJG {
  &-content {
    padding: var(--md-spacing-3);
  }
  .gongNengDMC {
    margin-left: var(--md-spacing-3);
  }
  .bottom-tip {
    font-size: 12px;
    color: #666666;
    line-height: 30px;
    bottom: 0px;
    left: 16px;
    float: left;
  }
}
</style>
