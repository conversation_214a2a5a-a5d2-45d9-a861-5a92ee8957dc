<template>
  <md-dialog
    v-model="initData.visible"
    title="限量设置"
    size="small"
    :draggable="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    class="HISYAOKU-xianLiangSZ"
  >
    <div class="HISYAOKU-xianLiangSZ-content">
      <md-table :columns="columns" :data="tableData" edit class="zkzy_table">
        <template v-slot:gongNengDMC="{ row }">
          <span class="gongNengDMC"> {{ row.gongNengDMC }} </span>
        </template>
        <template v-slot:hongXianKZLXDM="{ row, $index }">
          <md-radio-group
            v-model="row.hongXianKZLXDM"
            :key="$index"
            @change="handleChangeHong(row)"
          >
            <md-radio
              v-for="item in kongZhiLXOptions"
              :key="item.biaoZhunDM"
              :label="item.biaoZhunDM"
              size="large"
              >{{ item.biaoZhunMC }}</md-radio
            >
          </md-radio-group>
        </template>
        <template v-slot:huangXianKZLXDM="{ row, $index }">
          <md-radio-group
            v-model="row.huangXianKZLXDM"
            :key="$index"
            @change="handleChangeHuang(row)"
          >
            <md-radio
              v-for="val in kongZhiLXOptions"
              :key="val.biaoZhunDM"
              :label="val.biaoZhunDM"
              size="large"
              >{{ val.biaoZhunMC }}</md-radio
            >
          </md-radio-group>
        </template>
      </md-table>
    </div>
    <template #footer>
      <!-- 提示 -->
      <span class="bottom-tip">
        <md-icon name="tixing-s" color="#ff9900" />
        费用录入的限制范围只取患者
      </span>
      <md-button type="primary" plain @click="handleClose">取消</md-button>
      <md-button type="primary" @click="handleSave"> 保存 </md-button>
    </template>
  </md-dialog>
</template>
<script lang="ts" setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  watch,
  computed,
  nextTick,
} from 'vue';
import lyra from '@mdfe/lyra';
// import { cloneDeep } from 'lodash';
import { MdMessage, MdMessageBox, useNamespace } from '@mdfe/medi-ui';
import SelectTable from '@mdfe/material.select-table';
import formatJiaGe from '@/system/utils/formatJiaGe';
import type { FormInstance, FormRules } from '@mdfe/medi-ui';

import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { getXiangLSZ, xianLiangSZ } from '@/service/yaoPin/xiaoHaoLX';

// 组件名称
defineOptions({ name: 'XianLiangSZDialog' });
const { proxy } = getCurrentInstance() as any;
interface Item {
  [key: string]: any;
}
// 定义初始化数据
const initData = reactive({
  visible: false,
  fenLeiID: '',
  fenLeiMC: '',
});
const kongZhiGNDOptions = ref<Item[]>([]);
const kongZhiLXOptions = ref<Item[]>([]);
let kongZhiLXObj = ref({}) as any;
// 表格数据
let tableData = ref<Item[]>([]);
const columns = Object.freeze([
  {
    slot: 'gongNengDMC',
    prop: 'gongNengDMC',
    label: '功能点',
  },
  {
    slot: 'hongXianKZLXDM',
    prop: 'hongXianKZLXDM',
    label: '红线',
    width: 160,
  },
  {
    slot: 'huangXianKZLXDM',
    prop: 'huangXianKZLXDM',
    label: '黄线',
    width: 160,
  },
]);
let resolve: (arg0: boolean) => void;
let reject;
const showModel = async (data: any) => {
  initData.visible = true;
  let { fenLeiID, fenLeiMC } = data;
  initData.fenLeiID = fenLeiID;
  initData.fenLeiMC = fenLeiMC;
  await nextTick();
  ziDianLoad();
  return new Promise((resolves, rejects) => {
    resolve = resolves;
    reject = rejects;
  });
};
// 使用defineExpose抛出父组件调用的方法
defineExpose({ showModel });
// 获取字典数据
const ziDianLoad = async () => {
  try {
    const datas = await getYaoPinShuJuYZYList(['YP0118', 'YP0119']);
    kongZhiGNDOptions.value =
      datas && datas[0].zhiYuList ? datas[0].zhiYuList : [];
    kongZhiLXOptions.value =
      datas && datas[1].zhiYuList ? datas[1].zhiYuList : [];

    kongZhiLXObj.value = kongZhiLXOptions.value.reduce((obj, item) => {
      obj[item.biaoZhunDM] = item.biaoZhunMC;
      return obj;
    }, {});
    getXiangLSZList();
  } catch (error) {
    proxy.$logger.error(error);
  }
};
// 获取已保存的数据
const getXiangLSZList = async () => {
  try {
    const result = await getXiangLSZ({
      fenLeiID: initData.fenLeiID,
    });
    if (result && result.length == 0) {
      let { fenLeiID, fenLeiMC } = initData;
      let list: Item[] = [];
      kongZhiGNDOptions.value.map((val: any) => {
        list.push({
          fenLeiID,
          fenLeiMC,
          hongXianKZLXDM: '',
          hongXianKZLXMC: '',
          huangXianKZLXDM: '',
          huangXianKZLXMC: '',
          gongNengDDM: val.biaoZhunDM,
          gongNengDMC: val.biaoZhunMC,
        });
      });
      tableData.value = list;
    } else {
      tableData.value = result;
    }
  } catch (error) {
    proxy.$logger.error(error);
  }
};
const handleChangeHong = async (row: any) => {
  row.hongXianKZLXMC = kongZhiLXObj.value[row.hongXianKZLXDM];
};
const handleChangeHuang = async (row: any) => {
  row.huangXianKZLXMC = kongZhiLXObj.value[row.huangXianKZLXDM];
};
// 保存数据
const handleSave = async () => {
  let res = await xianLiangSZ(tableData.value);
  if (res) {
    MdMessage({
      type: 'success',
      message: '保存成功',
    });
    resolve(true);
    handleClose();
  }
};
// 关闭弹框并初始化数据
const handleClose = () => {
  tableData.value = [];
  initData.visible = false;
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.HISYAOKU-xianLiangSZ {
  &-content {
    padding: var(--md-spacing-3);
  }
  .gongNengDMC {
    margin-left: var(--md-spacing-3);
  }
  .bottom-tip {
    font-size: 12px;
    color: #666666;
    line-height: 30px;
    bottom: 0px;
    left: 16px;
    float: left;
  }
}
</style>
