<template>
  <md-dialog
    v-model="initData.visible"
    :title="initData.mode == 'add' ? '新增分类' : '编辑分类'"
    size="small"
    :draggable="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    class="HISYAOKU-addfenLei-dialog"
  >
    <md-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      use-status-tooltip-icon
      class="HISYAOKU-addfenLei-content"
    >
      <md-form-item label="分类名称" prop="fenLeiMC">
        <md-input v-model="ruleForm.fenLeiMC" maxlength="30">
          <template #suffix>30字内</template>
        </md-input>
      </md-form-item>
      <md-form-item label="红线数量" prop="hongXianSL">
        <md-input v-model.number="ruleForm.hongXianSL" />
      </md-form-item>
      <md-form-item label="黄线数量" prop="huangXianSL">
        <md-input v-model.number="ruleForm.huangXianSL" />
      </md-form-item>
      <md-form-item label="红线金额" prop="hongXianJE">
        <md-input v-model="ruleForm.hongXianJE" />
      </md-form-item>
      <md-form-item label="黄线金额" prop="huangXianJE">
        <md-input v-model="ruleForm.huangXianJE" />
      </md-form-item>
    </md-form>
    <template #footer>
      <md-button
        v-if="initData.mode == 'edit'"
        type="danger"
        plain
        class="dialog-footer-zuofei"
        @click="handleCancel"
      >
        作废
      </md-button>
      <md-button type="primary" plain @click="handleClose(ruleFormRef)"
        >取消</md-button
      >
      <md-button type="primary" @click="handleSave(ruleFormRef)">
        保存
      </md-button>
    </template>
  </md-dialog>
</template>
<script lang="ts" setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  watch,
  computed,
  nextTick,
} from 'vue';
import lyra from '@mdfe/lyra';
// import { cloneDeep,debounce } from 'lodash';
import { MdMessage, MdMessageBox, useNamespace } from '@mdfe/medi-ui';
import SelectTable from '@mdfe/material.select-table';
import formatJiaGe from '@/system/utils/formatJiaGe';
// import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import type { FormInstance, FormRules } from '@mdfe/medi-ui';

import { saveXianLiangFL, zuoFeiXianLiangFL } from '@/service/yaoPin/xiaoHaoLX';

// 组件名称
defineOptions({ name: 'AddFenLeiDialog' });

// 定义初始化数据
const initData = reactive({
  visible: false,
  mode: '',
});

// 初始化表单数据
const ruleFormRef = ref<FormInstance>();
let ruleForm = ref({
  id: '',
  nianFen: '',
  yueFen: '',
  fenLeiID: '',
  fenLeiMC: '',
  hongXianSL: null,
  huangXianSL: null,
  hongXianJE: null,
  huangXianJE: null,
});
// 整数校验
const validateNum = (rule: any, value: Number, callback: any) => {
  if (value && !Number.isInteger(value)) {
    callback(new Error('请输入整数'));
  } else {
    callback();
  }
};
// 保留两位小数校验
const validateNum2 = (rule: any, value: string, callback: any) => {
  const regex = /^(\d+)?(\.\d{1,2})?$/;
  if (value && !regex.test(value)) {
    return callback(new Error('请输入正确的数字，小数点后最多两位'));
  } else {
    callback();
  }
};
// 表单校验数据
const rules = reactive({
  fenLeiMC: [{ required: true, message: '请输入', trigger: 'blur' }],
  hongXianSL: [{ required: false, validator: validateNum, trigger: 'blur' }],
  huangXianSL: [{ required: false, validator: validateNum, trigger: 'blur' }],
  hongXianJE: [{ required: false, validator: validateNum2, trigger: 'blur' }],
  huangXianJE: [{ required: false, validator: validateNum2, trigger: 'blur' }],
});

let resolve: (arg0: boolean) => void;
let reject;
const showModel = async (data: any) => {
  initData.mode = data.mode;
  initData.visible = true;
  reset();
  ruleForm.value = data.params;
  await nextTick();
  return new Promise((resolves, rejects) => {
    resolve = resolves;
    reject = rejects;
  });
};
// 使用defineExpose抛出父组件调用的方法
defineExpose({ showModel });

// 保存数据
const handleSave = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let result = await saveXianLiangFL(ruleForm.value);
      if (result) {
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        resolve(true);
        handleClose(formEl);
      }
    }
  });
};
const reset = () => {
  ruleForm.value = {
    id: '',
    nianFen: '',
    yueFen: '',
    fenLeiID: '',
    fenLeiMC: '',
    hongXianSL: null,
    huangXianSL: null,
    hongXianJE: null,
    huangXianJE: null,
  };
};

const handleCancel = async () => {
  await MdMessageBox.confirm('', '确定作废该数据?', {
    confirmButtonText: '作废',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      return zuoFeiXianLiangFL({ id: ruleForm.value.id });
    })
    .then(() => {
      MdMessage({ message: '作废成功', type: 'success' });
      resolve(true);
      initData.visible = false;
    })
    .catch((error) => {
      if (error !== 'cancel') {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: error.message,
          confirmButtonText: '我知道了',
        });
      }
    });
};
// 关闭弹框并初始化数据
const handleClose = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  reset();
  formEl.resetFields();
  initData.visible = false;
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYAOKU-addfenLei-dialog {
  height: 100%;
  background: #fff;
  // margin-bottom: var(--md-spacing-3);
  display: flex;
  flex-direction: column;
  .HISYAOKU-addfenLei-content {
    padding-top: var(--md-spacing-3);
    padding-right: var(--md-spacing-4);
  }
  .dialog-footer-zuofei {
    float: left;
  }
}
</style>
<style lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.#{$md-prefix}-ZhuanKeZYYPGL-dialog {
  &-content {
    .#{$md-prefix}-tabs--card > .#{$md-prefix}-tabs__header {
      padding-top: var(--md-spacing-2);
      margin-bottom: 0px;
      border: none;
      .#{$md-prefix}-tabs__item {
        padding: 0 var(--md-spacing-6);
      }
      .#{$md-prefix}-tabs__item.is-active {
        border: none;
        background: #fff;
      }
    }
  }
}
</style>
