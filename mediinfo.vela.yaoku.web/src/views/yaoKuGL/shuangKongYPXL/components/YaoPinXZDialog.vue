<template>
  <md-dialog
    :title="mode == 'add' ? '新增药品总量限制' : '编辑药品总量限制'"
    v-model="visible"
    :v-loading="formLoading"
    class="HISYK-XiaoHaoZLXZ-addDialog"
    :before-close="handleClose"
    :content-scrollable="false"
  >
    <!-- <md-row :gutter="16" v-if="visible"> -->
    <md-form
      :model="formData"
      :rules="formRules"
      label-width="120px"
      use-status-tooltip-icon
      ref="form"
      style="width: 100%"
    >
      <md-row>
        <md-col :span="24">
          <md-form-item label="分类名称" prop="fenLeiID">
            <md-select
              v-model="formData.fenLeiID"
              filterable
              :disabled="mode == 'add' ? false : true"
            >
              <md-option
                v-for="item in fenLeiList"
                :key="item.fenLeiID"
                :label="item.fenLeiMC"
                :value="item.fenLeiID"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="药品名称与规格" prop="yaoPinMCYGG">
            <biz-yaopindw
              v-if="visible"
              v-model="formData.yaoPinMCYGG"
              :type="type"
              :placeholder="placeholder"
              :class="prefixClass('yaopin-search')"
              showSuffix
              :disabled="mode == 'add' ? false : true"
              @change="handleYaoPinDWChange($event)"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="产地名称" prop="chanDiMC">
            <md-input v-model="formData.chanDiMC" :disabled="true" />
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="院内编码" prop="yuanNeiBM">
            <md-input v-model="formData.yuanNeiBM" :disabled="true" />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="省平台ID" prop="shengPingTBM">
            <md-input v-model="formData.shengPingTBM" :disabled="true" />
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12" class="huanSuanBL-text">
          <md-tooltip
            class="huanSuanBL-shuoming"
            effect="dark"
            content="黄线=红线*换算比例"
            placement="bottom"
          >
            <md-icon name="shuoming" />
          </md-tooltip>
          <md-form-item label="换算比例" prop="tiShiBL">
            <md-input
              v-model="formData.tiShiBL"
              v-number.float="{ decimal: 2 }"
              @change="handleTiShiLiang(1)"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="零售价" prop="lingShouJia">
            <md-input v-model="formData.lingShouJia" :disabled="true" />
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="红线消耗量" prop="xianZhiZL">
            <md-input
              v-model="formData.xianZhiZL"
              v-number.float="{ decimal: 2 }"
              @change="handleTiShiLiang(2)"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="黄线消耗量" prop="tiShiLiang">
            <md-input
              v-model="formData.tiShiLiang"
              v-number.float="{ decimal: 2 }"
              @change="handleJiSuan('1')"
            />
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="红线消耗金额" prop="hongXianXHJE">
            <md-input
              v-model="formData.hongXianXHJE"
              v-number.float="{ decimal: 2 }"
              @change="handleJiSuan('2')"
            >
              <template #suffix>
                <i class="HISYK-input__icon">元</i>
              </template>
            </md-input>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="黄线消耗金额" prop="huangXianXHJE">
            <md-input
              v-number.float="{ decimal: 2 }"
              v-model="formData.huangXianXHJE"
              @change="handleJiSuan('3')"
            >
              <template #suffix>
                <i class="HISYK-input__icon">元</i>
              </template>
            </md-input>
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="门诊红线数量" prop="menZhenHXSL">
            <md-input
              v-model="formData.menZhenHXSL"
              v-number.float="{ decimal: 2 }"
              @change="handleMenZhenZYJS('1')"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="门诊红线金额" prop="menZhenHXJE">
            <md-input
              v-model="formData.menZhenHXJE"
              v-number.float="{ decimal: 2 }"
              @change="handleMenZhenZYJS('2')"
            >
              <template #suffix>
                <i class="HISYK-input__icon">元</i>
              </template>
            </md-input>
          </md-form-item>
        </md-col>
      </md-row>
      <md-row>
        <md-col :span="12">
          <md-form-item label="住院红线数量" prop="zhuYuanHXSL">
            <md-input
              v-model="formData.zhuYuanHXSL"
              v-number.float="{ decimal: 2 }"
              @change="handleMenZhenZYJS('3')"
            />
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="住院红线金额" prop="zhuYuanHXJE">
            <md-input
              v-model="formData.zhuYuanHXJE"
              v-number.float="{ decimal: 2 }"
              @change="handleMenZhenZYJS('4')"
            >
              <template #suffix>
                <i class="HISYK-input__icon">元</i>
              </template>
            </md-input>
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
    <template #footer>
      <span class="dialog-footer">
        <div style="text-align: right">
          <md-button
            v-if="isEdit"
            type="danger"
            plain
            :disabled="formLoading"
            style="float: left"
            @click="handleZuoFei(formData.id)"
          >
            作废
          </md-button>
          <div>
            <md-button
              type="primary"
              plain
              :disabled="formLoading"
              @click="handleClose"
            >
              取消
            </md-button>

            <md-button type="primary" :loading="formLoading" @click="handleSave"
              >确定</md-button
            >
          </div>
        </div>
      </span>
    </template>
  </md-dialog>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  zuoFeiYaoPinXHLXZPZ,
  saveYaoPinXHLXZPZ,
} from '@/service/yaoPin/xiaoHaoLX';
import { MdMessageBox, MdMessage } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
const formData = () => {
  return {
    id: '',
    nianFen: '',
    yueFen: '',
    fenLeiID: '',
    fenLeiMC: '',
    guiGeID: '',
    jiaGeID: '',
    yuanNeiBM: '',
    shengPingTBM: '',
    yaoPinMC: '',
    yaoPinMCYGG: null,
    yaoPinGG: '',
    chanDiID: '',
    chanDiMC: '',
    baoZhuangDW: '',
    baoZhuangLiang: '',
    xianZhiZL: '', // 红线
    huangXianXHJE: '', // 黄线金额
    tiShiBL: '', // 换算比例
    tiShiLiang: '', // 黄线
    hongXianXHJE: '', // 黄线金额
    menZhenHXSL: '',
    menZhenHXJE: '',
    zhuYuanHXJE: '',
    zhuYuanHXSL: '',
    yaoPinLXDM: '',
    yaoPinLXMC: '',
    yaoPinID: '',
    lingShouJia: null,
  };
};
export default {
  name: 'XiaoHaoZLXZ-addDialog',
  data() {
    return {
      isEdit: false,
      saveLoading: false,
      yaoPinFLOptions: [], // 药品分类数据
      type: 'ypxh', // 药品规格入参
      placeholder: '药品名称与规格', // 药品规格入参
      visible: false,
      activeFW: null,
      formData: formData(),
      formRules: {
        fenLeiID: [
          { required: true, message: '请选择药品分类', trigger: 'change' },
        ],
        // yaoPinMCYGG: [
        //   { required: true, message: '请选择药品', trigger: 'change' },
        // ],
        // xianZhiZL: [
        //   { required: true, message: '请输入限制总量', trigger: 'change' },
        // ],
        // tiShiBL: [
        //   { required: true, message: '请输入提示比例', trigger: 'change' },
        // ],
      },
      formLoading: false,
      yaoPinDWList: [],
      canKaoFWList: [],
    };
  },
  computed: {
    resFanWeiList() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('大于'));
    },
    resFanWeiList2() {
      return this.fanWeiList.filter((item) => item.biaoZhunMC.includes('小于'));
    },
  },
  methods: {
    async showModel({ mode, fenLeiList, data }) {
      this.visible = true;
      this.mode = mode;
      this.fenLeiList = fenLeiList;
      await this.$nextTick();
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.formData = formData();
      this.isEdit = false;
      if (mode == 'add') {
        data.yaoPinMCYGG = null;
      } else {
        this.isEdit = true;
        this.formLoading = true;
        data.yaoPinMCYGG = {
          nianFen: data.nianFen,
          yueFen: data.yueFen,
          fenLeiID: data.fenLeiID,
          fenLeiMC: data.fenLeiMC,
          guiGeID: data.guiGeID,
          jiaGeID: data.jiaGeID,
          yuanNeiBM: data.yuanNeiBM,
          shengPingTBM: data.shengPingTBM,
          yaoPinGG: data.yaoPinGG,
          chanDiID: data.chanDiID,
          chanDiMC: data.chanDiMC,
          kuCunSL: 0,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          yaoPinMC: data.yaoPinMC,
          yaoPinID: data.yaoPinID,
        };
      }
      this.formData = cloneDeep(data);
      this.formLoading = false;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    /**
     * 选择药品change事件
     */
    async handleYaoPinDWChange(data) {
      if (data) {
        const params = {
          guiGeID: data.guiGeID,
          jiaGeID: data.jiaGeID,
          yuanNeiBM: data.yuanNeiBM,
          shengPingTBM: data.shengPingTBM,
          yaoPinGG: data.yaoPinGG,
          chanDiID: data.chanDiID,
          chanDiMC: data.chanDiMC,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          yaoPinMC: data.yaoPinMC,
          yaoPinID: data.yaoPinID,
          lingShouJia: data.danJia,
        };
        Object.assign(this.formData, params);
      } else {
        this.formData.yaoPinMCYGG = '';
        this.formData.yaoPinMC = '';
        this.formData.yaoPinGG = '';
      }
    },
    // 提示量=限制总量*提示比例
    // 黄 = 红 * 比例
    handleTiShiLiang(type) {
      let formData = this.formData;
      if (
        (type == 1 && formData.tiShiBL) ||
        (type == 2 && formData.xianZhiZL)
      ) {
        // 换算比例
        if (!formData.lingShouJia && formData.lingShouJia != 0) {
          if (formData.tiShiBL && formData.xianZhiZL) {
            // 黄线量
            formData.tiShiLiang =
              parseFloat(formData.xianZhiZL) * parseFloat(formData.tiShiBL);
          }
          return;
        }
        if (Number(formData.tiShiBL) > 1) {
          formData.tiShiBL = 1.0;
        }

        if (formData.xianZhiZL && formData.tiShiBL) {
          // 红线量
          // 黄线量
          formData.tiShiLiang =
            parseFloat(formData.xianZhiZL) * parseFloat(formData.tiShiBL);
          // 黄线金额
          formData.huangXianXHJE = parseFloat(
            formData.tiShiLiang * formData.lingShouJia,
          ).toFixed(2);
          // 红线金额
          formData.hongXianXHJE = parseFloat(
            formData.xianZhiZL * formData.lingShouJia,
          ).toFixed(2);
        }
      }

      //换算比例没有值或者红线消耗量没有值时，黄线消耗量，黄线消耗金额不计算为空
      if (!formData.tiShiBL || !formData.xianZhiZL) {
        //黄线量
        formData.tiShiLiang = '';
        // 黄线金额
        formData.huangXianXHJE = '';
        //红线消耗量没有的话红线金额为空
        if (!formData.xianZhiZL) {
          formData.hongXianXHJE = '';
        } else {
          formData.hongXianXHJE = parseFloat(
            formData.xianZhiZL * formData.lingShouJia,
          ).toFixed(2);
        }
      }
    },
    // 黄线消耗量/金额，红线金额计算公式
    handleJiSuan(type) {
      let formData = this.formData;
      // 如果换算比例有值
      if (formData.tiShiBL) {
        switch (type) {
          // 1是黄线消耗量
          case '1':
            formData.xianZhiZL = parseFloat(
              formData.tiShiLiang / formData.tiShiBL,
            ).toFixed(2);
            formData.huangXianXHJE = formData.huangXianXHJE = parseFloat(
              formData.tiShiLiang * formData.lingShouJia,
            ).toFixed(2);
            formData.hongXianXHJE = parseFloat(
              formData.xianZhiZL * formData.lingShouJia,
            ).toFixed(2);
            break;
          // 2是红线金额
          case '2':
            formData.xianZhiZL = parseFloat(
              formData.hongXianXHJE / formData.lingShouJia,
            ).toFixed(2);
            formData.tiShiLiang =
              parseFloat(formData.xianZhiZL) * parseFloat(formData.tiShiBL);
            formData.huangXianXHJE = parseFloat(
              formData.tiShiLiang * formData.lingShouJia,
            ).toFixed(2);
            break;
          // 3是黄线金额
          case '3':
            formData.tiShiLiang = parseFloat(
              formData.huangXianXHJE / formData.lingShouJia,
            ).toFixed(2);
            formData.xianZhiZL = parseFloat(
              formData.tiShiLiang / formData.tiShiBL,
            ).toFixed(2);
            formData.hongXianXHJE = parseFloat(
              formData.xianZhiZL * formData.lingShouJia,
            ).toFixed(2);
            break;
        }
      }
    },
    //门诊住院数量金额计算
    handleMenZhenZYJS(type) {
      let formData = this.formData;
      switch (type) {
        case '1':
          formData.menZhenHXJE = parseFloat(
            formData.menZhenHXSL * formData.lingShouJia,
          ).toFixed(2);
          break;
        case '2':
          formData.menZhenHXSL = parseFloat(
            formData.menZhenHXJE / formData.lingShouJia,
          ).toFixed(2);
          break;
        case '3':
          formData.zhuYuanHXJE = parseFloat(
            formData.zhuYuanHXSL * formData.lingShouJia,
          ).toFixed(2);
          break;
        case '4':
          formData.zhuYuanHXSL = parseFloat(
            formData.zhuYuanHXJE * formData.lingShouJia,
          ).toFixed(2);
          break;
      }
    },
    //作废，停用才能作废
    async handleZuoFei(id) {
      MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return zuoFeiYaoPinXHLXZPZ({ id });
        })
        .then((response) => {
          MdMessage({ message: '作废成功', type: 'success' });
          this.resolve(true);
          this.visible = false;
        })
        .catch((error) => {
          if (error !== 'cancel') {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
    /**
     * 提交操作
     * @param {Object} data form.data内容
     */
    async handleSave() {
      const result = await this.$refs.form.validate();
      if (!result) return;
      let params = this.formData;
      this.saveLoading = true;
      await saveYaoPinXHLXZPZ(params);
      this.$message({
        type: 'success',
        message: '保存成功！',
      });
      this.resolve(true);
      this.visible = false;
    },
    handleClose() {
      this.visible = false;
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.formData = formData();
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-XiaoHaoZLXZ-addDialog {
  .huanSuanBL-text {
    position: relative;
    .huanSuanBL-shuoming {
      position: absolute;
      left: 36px;
      top: 9px;
    }
  }
  .padleft-8 {
    padding-left: getCssVar('spacing-3');
  }

  .HISYK-XiaoHaoZLXZ-checkbox {
    width: 100%;
    border: 1px dashed #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 0 getCssVar('spacing-3');
  }

  .HISYK-XiaoHaoZLXZ-canKaoFW {
    ::v-deep .#{$namespace}-select {
      &:hover {
        border-color: unset;
      }
    }

    .select-wrap {
      width: 100px;
    }
  }
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select:hover
  .#{$namespace}-input__wrapper {
  box-shadow: unset !important;
}

::v-deep
  .#{$namespace}-input-group--prepend
  .#{$namespace}-input-group__prepend
  .#{$namespace}-select
  .#{$namespace}-input
  .#{$namespace}-input__wrapper {
  box-shadow: unset;
}
</style>
