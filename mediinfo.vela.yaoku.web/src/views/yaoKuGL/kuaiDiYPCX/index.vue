<template>
  <div :class="prefixClass('kuaiDiYPCX')">
    <div :class="prefixClass('kuaiDiYPCX-top')">
      <div>
        <md-form inline>
          <md-form-item label="开方日期" labelWidth="73px">
            <md-date-picker
              v-model="formData.kaiDanDate"
              type="daterange"
              style="width: 230px"
              range-separator="/"
              start-placeholder="开始日期"
              end-placeholder=" 结束日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              unlink-panels
              @change="handleJianSuo"
            >
            </md-date-picker>
          </md-form-item>
          <md-form-item label="">
            <md-select v-model="formData.zhuangTai" @change="handleJianSuo">
              <md-option
                v-for="item in zhuangTaiOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </md-form-item>
          <md-form-item label="">
            <md-search-input
              v-model="formData.peiSongDW"
              style="width: 200px"
              placeholder="配送单位"
              @search="handleJianSuo"
            ></md-search-input>
          </md-form-item>
          <md-form-item label="">
            <md-checkbox
              v-model="formData.yiDa"
              type="bordered"
              :true-label="1"
              :false-label="0"
              @change="handleJianSuo"
            >
              显示已打
            </md-checkbox></md-form-item
          >
        </md-form>
      </div>
      <div>
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          @click="handleJianSuo"
          >刷新</md-button
        >
        <!-- <md-button
          type="primary"
          :icon="prefixClass('icon-dayinji')"
          noneBg
          @click="handleDaYin"
          >汇总单</md-button
        > -->
        <md-button
          type="primary"
          :icon="prefixClass('icon-dayinji')"
          noneBg
          @click="handleHuiZong"
          >打印</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('kuaiDiYPCX-table')">
      <md-table-pro
        ref="table"
        :autoLoad="false"
        loading="true"
        height="100%"
        :columns="columns"
        highlight-current-row
        :onFetch="handleFetch"
      >
        <template v-slot:zhaungTai="{ row }">
          <md-tag v-if="row.daYinBZ === 0">未打印</md-tag>
          <md-tag v-else type="success">已打印</md-tag></template
        >
        <template v-slot:faYaoZBZ="{ row }">
          <md-tag v-if="row.faYaoBZ === 0">未发药</md-tag>
          <md-tag v-if="row.faYaoBZ === 1" type="success">已发药</md-tag>
          <md-tag v-if="row.faYaoBZ === 2" type="danger">缺药</md-tag>
        </template>
        <template v-slot:caoZuo="{ row }">
          <md-button
            type="primary"
            noneBg
            size="small"
            @click.stop="handleXiangQing(row)"
            >详情</md-button
          >
        </template>
      </md-table-pro>
    </div>
    <dayin-dialog
      ref="daYinHZDDialog"
      :params="daYinObj"
      :id="'YFXT0121'"
      :title="'打印预览'"
      @savePrint="savePrint"
    />
    <dayin-dialog
      ref="daYinDialog"
      :params="daYinObj"
      :id="'YFXT0122'"
      :title="'打印预览汇总单'"
      @savePrint="savePrint"
    />
    <xiangQingDrawer ref="chouTi"></xiangQingDrawer>
  </div>
</template>
<script>
import dayjs from 'dayjs';

import { logger } from '@/service/log';
import { GetKuaiDiYPCount, GetKuaiDiYPList } from '@/service/yaoPinYF/kuaiDiCX';
import { MdMessage } from '@mdfe/medi-ui';
import DaYinDialog from './components/daYinHZDDialog.vue';
import xiangQingDrawer from './components/xiangQingDrawer.vue';

export default {
  name: 'kuaiDiYPCX',
  data() {
    return {
      dateData: [],

      daYinObj: {},
      columns: [
        {
          type: 'selection',
          width: 35,
        },
        {
          prop: 'danJuHao',
          label: '单据号',
          width: 130,
        },
        {
          prop: 'kaiDanSJ',
          label: '开单时间',
          width: 140,
          formatter(row, column, cellValue) {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : '';
          },
        },
        {
          prop: 'yiZhuFLMC',
          label: '类型',
          width: 80,
          formatter(row, column, cellValue) {
            return cellValue;
          },
        },
        {
          prop: 'yiZhuDH',
          label: '处方号码',
          width: 120,
        },
        {
          prop: 'xingMing',
          label: '姓名',
          width: 100,
        },
        {
          prop: 'kaiDanKSMC',
          label: '开单科室',
          width: 120,
        },
        {
          prop: 'kaiDanYSXM',
          label: '开单人',
          width: 100,
        },
        {
          prop: 'jiuZhenKH',
          label: '就诊卡号',
          width: 120,
        },
        {
          prop: 'jianYaoDWMC',
          label: '配送单位',
          minWidth: 300,
        },
        {
          prop: 'faYaoZBZ',
          width: 100,
          label: '发药状态',
          slot: 'faYaoZBZ',
        },
        {
          slot: 'zhaungTai',
          label: '打印状态',
          width: 100,
          align: 'center',
        },
        {
          slot: 'caoZuo',
          label: '操作',
          width: 100,
          align: 'center',
        },
      ],
      formData: {
        kaiDanDate: [
          dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        peiSongDW: '',
        yiDa: 0,
        zhuangTai: '',
      },
      zhuangTaiOptions: [
        {
          label: '全部状态',
          value: '',
        },
        {
          label: '未发药',
          value: '0',
        },
        {
          label: '已发药',
          value: '1',
        },
        {
          label: '缺药',
          value: '2',
        },
        // {
        //   label: '不发药',
        //   value: '3',
        // },
      ],
    };
  },
  mounted() {
    this.handleJianSuo();
  },
  methods: {
    // 刷新列表
    handleJianSuo() {
      this.$refs.table.search({ pageSize: 100 });
    },
    async handleFetch({ page, pageSize }) {
      try {
        let params = {
          pageIndex: page,
          pageSize: pageSize,
          kaiDanKSSJ:
            this.formData.kaiDanDate && this.formData.kaiDanDate[0]
              ? this.formData.kaiDanDate[0]
              : '',
          kaiDanJSSJ:
            this.formData.kaiDanDate && this.formData.kaiDanDate[1]
              ? this.formData.kaiDanDate[1]
              : '',
          peiSongDW: this.formData.peiSongDW,
          daYinBZ: this.formData.yiDa === 0 ? this.formData.yiDa : '',
          faYaoBZ: this.formData.zhuangTai,
        };
        let items = await GetKuaiDiYPList(params);
        let total = await GetKuaiDiYPCount(params);

        return { items, total };
      } catch (err) {
        logger.error(err);
      }
    },

    handleXiangQing(row) {
      this.$refs.table.getComp('table').setCurrentRow(row);
      this.$refs.chouTi.showChouti(row);
    },
    // 打印
    handleHuiZong() {
      let daYinData = this.$refs.table.getComp('table').getAllCheckedRows();
      if (!daYinData.length) {
        MdMessage.warning('请选择打印数据');
        return;
      }
      this.getRow();
      this.$refs.daYinHZDDialog.showModal(daYinData);
    },
    // // 打印汇总
    // handleDaYin() {
    //   let daYinData = this.$refs.table.getComp('table').getAllCheckedRows();
    //   if (!daYinData.length) {
    //     MdMessage.warning('请选择打印数据');
    //     return;
    //   }
    //   this.getRow();
    //   this.$refs.daYinDialog.showModal(daYinData);
    // },
    getRow() {
      let daYinData = this.$refs.table.getComp('table').getAllCheckedRows();
      let id = [];
      let yiZhuDH = [];
      daYinData.forEach((e) => {
        id.push(e.id);
        yiZhuDH.push(e.yiZhuDH);
      });
      this.daYinObj = { ids: id.join(), yiZhuDHs: yiZhuDH.join() };
    },
    // 打印后回调
    async savePrint() {
      try {
        // let params = {};
        // params = {
        //   yiZhuDHs: this.daYinObj.yiZhuDHs,
        //   ids: this.daYinObj.ids,
        // };
        // }
        // await UpdateKuaiDiDYZT({}, { params });
        this.handleJianSuo();
      } catch (error) {
        logger.error(error);
      }
    },
  },
  components: { 'dayin-dialog': DaYinDialog, xiangQingDrawer },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-kuaiDiYPCX {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 8px;

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-riqi {
      width: 100px;
    }
  }

  &-table {
    flex: 1;
    min-height: 0;
  }
}
</style>
