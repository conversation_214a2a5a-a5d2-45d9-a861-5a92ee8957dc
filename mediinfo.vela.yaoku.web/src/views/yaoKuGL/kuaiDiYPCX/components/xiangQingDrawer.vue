<template>
  <div>
    <md-drawer
      :append-to-body="false"
      :close-on-press-escape="false"
      :wrapperClosable="false"
      v-model="visible"
      size="70%"
      :withHeader="false"
    >
      <div :class="prefixClass('kuaiDiCXCT')">
        <div class="kuaiDiCXCT-top">
          <span style="font-weight: 600">详情{{ data.danJuHao }}</span>
          <md-icon
            name="cha"
            @click="
              () => {
                visible = false;
              }
            "
          />
        </div>
        <div class="bodyer">
          <div class="bodyer-top">
            <div class="item">
              <div style="width: 25%">单据号：{{ data.danJuHao }}</div>
              <div style="width: 25%">处方号码：{{ data.yiZhuDH }}</div>
              <div style="width: 25%">开单科室：{{ data.kaiDanKSMC }}</div>
              <div style="width: 25%">开单人：{{ data.kaiDanYSXM }}</div>
            </div>
            <div class="item">
              <div style="width: 25%">患者姓名：{{ data.xingMing }}</div>
              <div style="width: 25%">就诊卡号：{{ data.jiuZhenKH }}</div>
              <div style="width: 50%">配送单位：{{ data.jianYaoDWMC }}</div>
            </div>
            <div class="item">
              <div style="width: 25%">收件人：{{ data.shouJianRXM }}</div>
              <div style="width: 25%">收件人电话：{{ data.shouJianRDH }}</div>
              <div style="width: 50%">配送地址：{{ data.shouJianDZXX }}</div>
            </div>
          </div>
          <div class="bodyer-table">
            <md-table
              :columns="columns"
              :data="data.yaoPinMXList"
              style="height: 100%"
            />
          </div>
          <div class="bodyer-bottom">
            <span style="color: rgb(182, 182, 182)">共计：</span
            >{{ data.yaoPinMXList?.length || 0 }}
            <span style="color: rgb(182, 182, 182)"
              >种药品&nbsp;&nbsp;合计零售金额：</span
            >{{ data.heJiJE }}
          </div>
        </div>
      </div></md-drawer
    >
  </div>
</template>

<script>
import { GetKuaiDiCFXQ } from '@/service/yaoPinYF/kuaiDiCX';
import { logger } from '@/service/log';
export default {
  data() {
    return {
      visible: false,
      data: [],
      columns: [
        {
          prop: 'yaoPinMCGG',
          label: '药品名称规格',
          minWidth: 215,
          formatter(row) {
            return row.yaoPinMC + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地',
          width: 240,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
        },
        {
          prop: 'shuLiang',
          label: '数量',
          width: 60,
        },
        {
          prop: 'danJia',
          label: '单价',
          width: 80,
          align: 'right',
        },
        {
          prop: 'jinE',
          label: '金额',
          width: 80,
          align: 'right',
        },
        {
          prop: 'baoZhuangDW',
          label: '皮试',
          width: 100,
          formatter(row) {
            if (row.piShiBZ === 0) {
              return '不需要皮试';
            } else if (row.piShiBZ === 1) {
              return '普通皮试';
            } else {
              return '原液皮试';
            }
          },
        },

        {
          prop: 'geiYaoFSMC',
          label: '给药方式',
          width: 100,
        },
      ],
    };
  },
  methods: {
    async showChouti(row) {
      try {
        this.visible = true;
        this.data = await GetKuaiDiCFXQ({ yiZhuDH: row.yiZhuDH, id: row.id });
      } catch (error) {
        console.error(error);
        logger.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-kuaiDiCXCT {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .kuaiDiCXCT-top {
    padding: 8px;
    padding-left: 12px;
    background-color: rgb(var(--md-color-1));
    display: flex;
    justify-content: space-between;
  }
  .bodyer {
    flex: 1;
    min-height: 0;
    padding: 8px;
    display: flex;
    flex-direction: column;

    &-top {
      .item {
        display: flex;
        padding-bottom: 8px;
        border-bottom: 1px dashed #ddd;
        margin-bottom: 8px;
      }
    }
    &-table {
      flex: 1;
      min-height: 0;
    }
    &-bottom {
      padding-top: 8px;
    }
  }
}
</style>
