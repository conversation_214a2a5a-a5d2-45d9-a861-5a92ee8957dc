<template>
  <md-dialog
    title="快递药品打印"
    width="90%"
    height="90%"
    :content-scrollable="false"
    :body-loading="loading"
    v-model="dialogVisible"
    :before-close="handleCancel"
  >
    <div :class="prefixClass('dialog-box')">
      <div :class="prefixClass('dialog-left')">
        <md-scrollbar :native="false" style="height: 100%">
          <md-checkbox-group v-model="checkedDaYinDan" :inline="false">
            <p v-for="item in daYinDanOptions" :key="item.biaoZhunDM">
              <md-checkbox
                :label="item.biaoZhunDM"
                :class="{ active: item.biaoZhunDM === activeDaYinDM }"
                @change="handlecheckDaYinDan(item)"
              >
                <span
                  style="
                    display: block;
                    width: 100%;
                    line-height: 20px;
                    height: 20px;
                  "
                  @click.prevent="handleLabel(item)"
                  >{{ item.biaoZhunMC }}</span
                >
                <md-icon size="14" name="youjiantou-s" class="icon-you" />
              </md-checkbox>
            </p>
          </md-checkbox-group>
        </md-scrollbar>
      </div>
      <div :class="prefixClass('dialog-right')">
        <div :class="prefixClass('dialog-right-content')">
          <div v-show="!hasData" :class="prefixClass('nodata')">
            <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
            <span>暂无内容</span>
          </div>
          <dayin-baobiao
            v-if="hasData"
            :params="params"
            :headers="headers"
            :toolbarVisible="true"
            :id="id"
            ref="baobiao"
          />
        </div>
      </div>
    </div>
    <template v-slot:footer>
      <div class="dialog-footer">
        <md-button
          type="primary"
          plain
          :disabled="pageLoading"
          @click="handleCancel"
          >取消</md-button
        >
        <md-button type="primary" :loading="pageLoading" @click="handleSave"
          >打印</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import DayinBaobiao from '@/components/DaYinBB';
import { UpdateKuaiDiDYZT } from '@/service/yaoPinYF/kuaiDiCX';
import { getUserInfo } from '@/system/utils/local-cache';
import { printByUrl } from '@/system/utils/print';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
export default {
  name: '',
  data() {
    return {
      params: {},
      headers: {},
      pageLoading: false,
      dialogVisible: false,
      loading: false,
      daYinDanOptions: [
        {
          biaoZhunDM: 'YFXT0121',
          biaoZhunMC: '送货单',
        },
        {
          biaoZhunDM: 'YFXT0122',
          biaoZhunMC: '汇总单',
        },
      ],
      checkedDaYinDan: [],
      activeDaYinDM: '',
      filterTableData: {
        YFXT0121: {
          id: 'YFXT0121',
          params: { ids: '', yiZhuDHs: '' },
        },
        YFXT0122: {
          id: 'YFXT0122',
          params: { ids: '', yiZhuDHs: '' },
        },
      },
    };
  },
  created() {
    this.headers = getUserInfo([
      'WeiZhiID',
      'WeiZhiMC',
      'KeShiID',
      'KeShiMC',
      'BingQuID',
      'BingQuMC',
      'JiGouID',
      'JiGouMC',
      'ShuRuMLX',
      'CaiDanID',
    ]);
  },
  computed: {
    hasData() {
      return this.params.ids;
    },
  },
  methods: {
    //隐藏显示弹框
    async showModal(data) {
      let ids = [];
      let yiZhuDHs = [];
      data &&
        data.forEach((el) => {
          ids.push(el.id);
          yiZhuDHs.push(el.yiZhuDH);
        });

      this.filterTableData['YFXT0121'].params = {
        ids: ids.join(),
        yiZhuDHs: yiZhuDHs.join(),
      };
      this.filterTableData['YFXT0122'].params = {
        ids: ids.join(),
        yiZhuDHs: yiZhuDHs.join(),
      };
      this.dialogVisible = true;
    },
    handleLabel(item) {
      this.activeDaYinDM = item.biaoZhunDM;
      this.daYinDDM = item.biaoZhunDM;
      this.daYinDMC = item.biaoZhunMC;
      this.id = item.biaoZhunDM;
      this.params = this.filterTableData[item.biaoZhunDM].params;
    },
    //点击选中打印单
    handlecheckDaYinDan(item) {
      //this.activeDaYinDM = item.biaoZhunDM;
      this.daYinDDM = item.biaoZhunDM;
      this.daYinDMC = item.biaoZhunMC;
      // this.id = item.biaoZhunDM;
      // this.params = this.filterTableData[item.biaoZhunDM].params;
    },

    async handleSave() {
      if (this.checkedDaYinDan.length == 0) {
        MdMessage.warning('请选择打印类型！');
        return;
      }
      MdMessageBox.confirm('确认打印？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          this.pageLoading = true;
          this.checkedDaYinDan.forEach(async (i) => {
            if (this.filterTableData[i].params) {
              await printByUrl(
                this.filterTableData[i].id,
                this.filterTableData[i].params,
              );
              UpdateKuaiDiDYZT({}, { params: this.filterTableData[i].params });
            }
          });
          this.$message({
            type: 'success',
            message: '打印成功！',
          });
          // 快递药品回调
          this.$emit('savePrint');
        } finally {
          this.pageLoading = false;
        }
      });
    },
    handleCancel() {
      this.checkedDaYinDan = [];
      this.dialogVisible = false;
      this.activeDaYinDM = '';
    },
  },
  components: {
    'dayin-baobiao': DayinBaobiao,
  },
};
</script>

<style scoped lang="scss">
::v-deep .#{$md-prefix}-scrollbar__view {
  height: 100%;
  box-sizing: border-box;
}

.#{$md-prefix}-dialog-box {
  display: flex;
  height: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  .#{$md-prefix}-dialog-left {
    width: 250px;
    margin-right: 8px;
    border: 1px solid;
    border-color: #dddddd;
    .#{$md-prefix}-checkbox-group {
      margin-right: -1px;
    }
    .#{$md-prefix}-checkbox {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 12px;

      ::v-deep .#{$md-prefix}-checkbox__input {
        top: 0;
      }

      ::v-deep .#{$md-prefix}-checkbox__label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        width: 0;

        .icon-you {
          display: none;
          color: rgb(var(--md-color-6));
        }
      }
    }

    .#{$md-prefix}-checkbox.active {
      background-color: rgb(var(--md-color-2));
      ::v-deep .#{$md-prefix}-checkbox__label {
        .icon-you {
          display: block;
          color: rgb(var(--md-color-6));
        }
      }
    }
  }
  .#{$md-prefix}-dialog-right {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;

    &-top {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    &-content {
      flex: 1 1 auto;
      background-color: #f5f5f5;
    }
  }
}
</style>
