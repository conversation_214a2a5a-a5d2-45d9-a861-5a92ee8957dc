<template>
  <div :class="prefixClass('yaoPinBFWZ')">
    <div v-loading="leftloading" :class="prefixClass('yaoPinBFWZ-left')">
      <div :class="prefixClass('yaoPinBFWZ-left-jianSuo')">
        <md-title label="药库房设置"></md-title>
      </div>
      <md-scrollbar :native="false" style="height: 100%">
        <div :class="prefixClass('yaoPinBFWZ-left-tree')">
          <md-tree
            :data="kuFangData"
            highlight-current
            node-key="weiZhiID"
            :default-expand-all="true"
            ref="tree"
            alignment
            :check-strictly="true"
            :props="treeProps"
          >
            <template #default="{ node }">
              <div class="treeItem" @click="handleTreeCurrent(node.data)">
                <div class="treeItem-label">
                  {{
                    node.data.mxTreeList
                      ? node.data.weiZhiDLMC
                      : node.data.weiZhiMC
                  }}
                </div>
              </div>
            </template>
          </md-tree>
        </div>
      </md-scrollbar>
    </div>
    <div :class="prefixClass('yaoPinBFWZ-right')">
      <div
        :class="prefixClass('yaoPinBFWZ-right-main')"
        v-loading="rightLoading"
      >
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>药房发药发送颗粒剂处方</span>
          <md-radio-group
            v-model="sheZhi.query.shiFouFSKLJCF"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药房发药发送颗粒剂处方', 'shiFouFSKLJCF')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM !== '7'">
          <span>开单时库存显示方式</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinKCXSFS"
            border
            class="flex1"
          >
            <md-radio label="0">显示为“*”</md-radio>
            <md-radio label="1">显示数值</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品库存显示方式', 'yaoPinKCXSFS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>是否补进包药机</span>
          <md-radio-group v-model="sheZhi.query.buJinBYJ" border class="flex1">
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('是否补进包药机', 'buJinBYJ')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>是否启用签到机模式</span>
          <md-radio-group
            v-model="sheZhi.query.shiFouKQQDJMS"
            border
            class="flex1"
          >
            <md-radio label="1">启用</md-radio>
            <md-radio label="0">不启用</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('是否启用签到机模式', 'shiFouKQQDJMS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>静配药房过滤标志</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiYFGLBZ"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配药房过滤标志', 'jingPeiYFGLBZ')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>复方药品显示文本</span>
          <md-input
            v-model="sheZhi.query.yaoPinZDFFBZSFXSZW"
            placeholder="请输入"
            class="flex1"
          />
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('复方药品显示文本', 'yaoPinZDFFBZSFXSZW')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>门诊发药默认展示绿通患者处方</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenYFSFZSLTHZCF"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('门诊发药默认展示绿通患者处方', 'menZhenYFSFZSLTHZCF')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>是否启用双控药品</span>
          <md-radio-group
            v-model="sheZhi.query.shuangKongYPYZBZ"
            border
            class="flex1"
          >
            <md-radio label="1">启用</md-radio>
            <md-radio label="0">不启用</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('是否启用双控药品', 'shuangKongYPYZBZ')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>草药默认加价率</span>
          <md-input
            v-model="sheZhi.query.caoYaoMRJJL"
            v-number.float="{ decimal: 0, min: 0 }"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          >
            <template #suffix> % </template>
          </md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药默认加价率', 'caoYaoMRJJL')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>应付款合计金额计算方式</span>
          <md-select v-model="sheZhi.query.yinFuKHJJEJSFS" class="flex1">
            <md-option label="先累加再四舍五入" value="xianLeiJZSSWR">
            </md-option>
            <md-option label="先四舍五入再累加" value="xianSiSWRZLJ">
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('应付款合计金额计算方式', 'yinFuKHJJEJSFS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>西成药药品包装规格默认开关规则</span>
          <md-select v-model="sheZhi.query.xiChengYYPBZGGMRKGGZ" class="flex1">
            <md-option
              label="默认:门诊/急诊大规格全开小规格全关，住院大小规格全开"
              value="0"
            >
            </md-option>
            <md-option
              label="门急住针剂大规格关闭小规格开启，除针剂外大规格开启小规格关闭"
              value="1"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('西成药药品包装规格默认开关规则', 'xiChengYYPBZGGMRKGGZ')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <div class="zidongfy">
            <span>药库采购对接两定平台</span>
            <md-radio-group
              v-model="sheZhi.query.yaoKuCGDJLDPT"
              border
              class="flex1"
            >
              <md-radio label="1">对接</md-radio>
              <md-radio label="0">不对接</md-radio>
            </md-radio-group>
          </div>
          <div v-show="sheZhi.query.yaoKuCGDJLDPT == '0'" class="df flex1">
            <span class="span mar-l-16">药品入库自动带入采购单数量</span>
            <md-radio-group
              v-model="sheZhi.query.yaoPinRKZDDRCGDSL"
              border
              class="flex1"
            >
              <md-radio label="1">申请数量</md-radio>
              <md-radio label="2">消耗量</md-radio>
            </md-radio-group>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药库采购对接两定平台', 'yaoKuCGDJLDPT')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>药品入库功能中待入库标签是否显示</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinRKGNZDRKBQ"
            border
            class="flex1"
          >
            <md-radio label="1">显示</md-radio>
            <md-radio label="0">不显示</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品入库功能中待入库标签是否显示', 'yaoPinRKGNZDRKBQ')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>药品入库选择药品后展示历史进价</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinRKXZYPHZSLSJJ"
            border
            class="flex1"
          >
            <md-radio label="1">显示</md-radio>
            <md-radio label="0">不显示</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品入库选择药品后展示历史进价', 'yaoPinRKXZYPHZSLSJJ')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>药品入库单毒麻精药品是否独立制单</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinRKDDMJYPDLZD"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品入库单毒麻精药品是否独立制单', 'yaoPinRKDDMJYPDLZD')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>药品出库单毒麻精药品是否独立制单</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinCKDDMJYPDLZD"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品出库单毒麻精药品是否独立制单', 'yaoPinCKDDMJYPDLZD')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>药品入库默认入库方式</span>
          <md-select
            v-model="sheZhi.query.yaoPinRKMRRKFS"
            class="flex1"
            filterable
          >
            <md-option
              v-for="item in ruKuFSOptions"
              :key="item.chuRuKFSID"
              :label="item.chuRuKFSMC"
              :value="item.chuRuKFSID"
            />
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品入库默认入库方式', 'yaoPinRKMRRKFS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>药品出库默认出库方式</span>
          <md-select
            v-model="sheZhi.query.yaoPinRKMRCKFS"
            class="flex1"
            filterable
          >
            <md-option
              v-for="item in chuKuFSOptions"
              :key="item.chuRuKFSID"
              :label="item.chuRuKFSMC"
              :value="item.chuRuKFSID"
            />
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品出库默认出库方式', 'yaoPinRKMRCKFS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>虚拟药房处理后自动发药</span>
          <md-radio-group
            v-model="sheZhi.query.chuLiHZDFY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('虚拟药房处理后自动发药', 'chuLiHZDFY')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>默认药品失效天数</span>
          <md-input
            v-model="sheZhi.query.yaoPinSXTS"
            v-number.float="{}"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          ></md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('默认药品失效天数', 'yaoPinSXTS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>默认药品积压天数</span>
          <md-input
            v-model="sheZhi.query.yaoPinJYTS"
            v-number.float="{}"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          ></md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('默认药品积压天数', 'yaoPinJYTS')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>第三方设备对接</span>
          <md-checkbox-group
            v-model="sheZhi.query.diSanFSBDJ"
            border
            class="flex1"
          >
            <md-checkbox
              v-for="item in diSanFSBDJList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</md-checkbox
            >
          </md-checkbox-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('第三方设备对接', 'diSanFSBDJ')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>打印是否按摆放位置排序</span>
          <md-radio-group
            v-model="sheZhi.query.daYinSFABFWZPX"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('打印是否按摆放位置排序', 'daYinSFABFWZPX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>调价后是否展示自动调价单</span>
          <md-radio-group
            v-model="sheZhi.query.shiFouZSZDTJD"
            border
            class="flex1"
          >
            <md-radio label="1">展示</md-radio>
            <md-radio label="0">不展示</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('调价后是否展示自动调价单', 'shiFouZSZDTJD')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>调价是否按供货单位</span>
          <md-radio-group
            v-model="sheZhi.query.tiaoJiaSFAGHDW"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('调价是否按供货单位', 'tiaoJiaSFAGHDW')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>中药饮片入库价格限制</span>
          <md-radio-group
            v-model="sheZhi.query.zhongYaoYPRKJGXZ"
            border
            class="flex1"
          >
            <md-radio label="0">限制</md-radio>
            <md-radio label="1">不限制</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('中药饮片入库价格限制', 'zhongYaoYPRKJGXZ')"
          />
        </div>

        <div
          class="card"
          v-if="
            yiXuanObj.weiZhiDLDM === '7' && sheZhi.query.zhongYaoYPRKJGXZ == '0'
          "
        >
          <div class="tixing flex1">
            <span>零售价:进价</span>
            <div class="tixing" v-for="(item, index) in sheZhi.query.jinJia">
              <md-select
                v-model="item.tiaoJianDM"
                placeholder="选择符合"
                class="selectRight width"
                @change="handleXianZhiTJ($event, index)"
              >
                <md-option
                  v-for="item in tiaoJianLXOptions"
                  :key="item.id"
                  :label="item.tiaoJianMC"
                  :value="item.tiaoJianDM"
                >
                </md-option>
              </md-select>
              <md-input
                v-model="item.tiaoJianZhi"
                v-number.float="{}"
                placeholder="输入比例"
                :clearable="false"
                class="selectLeft width"
              ></md-input>
              <!-- <md-select
              v-model="sheZhi.query.fuHaoLX"
              placeholder="选择符号"
              class="selectRight width"
            >
              <md-option
                v-for="item in fuHaoLXOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
            <md-input
              v-model="sheZhi.query.fuHaoBL"
              v-number.float="{}"
              placeholder="输入比例"
              :clearable="false"
              class="selectLeft width"
            ></md-input> -->
            </div>
          </div>

          <div class="tixing flex1">
            <span class="mr-8">提醒类型</span>
            <md-radio-group
              v-model="sheZhi.query.tiXingLX"
              border
              class="flex1"
              style="background-color: #fff"
            >
              <md-radio label="0">中断</md-radio>
              <md-radio label="1">提醒</md-radio>
            </md-radio-group>
          </div>
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <div class="zidongfy">
            <span class="span">自动发药</span>
            <md-radio-group
              v-model="sheZhi.query.ziDongFY.xiangMuZDM"
              border
              class="flex1"
            >
              <md-radio label="1">是</md-radio>
              <md-radio label="0">否</md-radio>
            </md-radio-group>
          </div>
          <div
            v-show="sheZhi.query.ziDongFY.xiangMuZDM == '1'"
            class="df flex1"
          >
            <span class="span mar-l-16">自动时间间隔</span>
            <md-input
              v-model="sheZhi.query.ziDongFY.xiangMuZMC"
              v-number.float="{}"
              placeholder="请输入"
              style="width: 80%"
            >
              <template #suffix> 分钟 </template>
            </md-input>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('自动发药', 'ziDongFY')"
          />
        </div>
        <div class="caoyaojyfs-content" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <p class="custom-title">草药发药煎药方式</p>
          <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <div class="daijianzh">
              <span class="span">是否允许代煎自煎转换</span>
              <md-radio-group
                v-model="sheZhi.query.shiFouYXDJZJZH.xiangMuZDM"
                border
                class="flex1"
                @change="handlerChangeYF"
              >
                <md-radio label="1">是</md-radio>
                <md-radio label="0">否</md-radio>
              </md-radio-group>
            </div>
            <div class="daijianzh">
              <span class="span">代煎自煎转换是否切换药房</span>
              <md-radio-group
                :disabled="sheZhi.query.shiFouYXDJZJZH.xiangMuZDM === '0'"
                v-model="sheZhi.query.daiJianZJSFQHYF.xiangMuZDM"
                border
                class="flex1"
                @change="handlerChangeYF"
              >
                <md-radio label="1">是</md-radio>
                <md-radio label="0">否</md-radio>
              </md-radio-group>
            </div>
            <div class="df flex1">
              <span class="span mar-l-16">切换药房</span>
              <md-select
                v-model="sheZhi.query.daiJianZJSFQHYF.xiangMuZMC"
                :disabled="sheZhi.query.daiJianZJSFQHYF.xiangMuZDM === '0'"
                style="width: 84%"
              >
                <md-option
                  v-for="item in yaoFangList"
                  :key="item.weiZhiID"
                  :label="item.weiZhiMC"
                  :value="item.weiZhiID"
                >
                </md-option>
              </md-select>
            </div>
            <img
              src="@/assets/images/shuoming.png"
              class="btn"
              alt=""
              @click="chaKanSM('代煎自煎转换是否切换药房', 'daiJianZJSFQHYF')"
            />
          </div>
          <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <span class="span">可以切换的煎药方式</span>
            <md-select
              :disabled="sheZhi.query.shiFouYXDJZJZH.xiangMuZDM === '0'"
              v-model="sheZhi.query.keYiQHDJYFS"
              multiple
              inline
              style="width: 85%"
            >
              <md-option
                v-for="item in caoYaoGYFSOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
            <img
              src="@/assets/images/shuoming.png"
              class="btn"
              alt=""
              @click="chaKanSM('可以切换的煎药方式', 'keYiQHDJYFS')"
            />
          </div>
          <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <span class="span">药房性质</span>
            <md-select
              v-model="sheZhi.query.yaoFangXZ"
              multiple
              style="width: 93%"
              inline
            >
              <md-option
                v-for="item in yaoPinXZList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
            <img
              src="@/assets/images/shuoming.png"
              class="btn"
              alt=""
              @click="chaKanSM('药房性质', 'yaoFangXZ')"
            />
          </div>
          <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <span class="span">不扣减库存的给药方式</span>
            <md-select
              v-model="sheZhi.query.buKouJKCDGYFS"
              multiple
              inline
              style="width: 85%"
            >
              <md-option
                v-for="item in caoYaoGYFSOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
            <img
              src="@/assets/images/shuoming.png"
              class="btn"
              alt=""
              @click="chaKanSM('不扣减库存的给药方式', 'buKouJKCDGYFS')"
            />
          </div>
          <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <span class="span">允许修改处方信息的煎药方式</span>
            <md-select
              v-model="sheZhi.query.yunXuXGCFDJYFS"
              multiple
              inline
              style="width: 85%"
            >
              <md-option
                v-for="item in caoYaoGYFSOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
            <img
              src="@/assets/images/shuoming.png"
              class="btn"
              alt=""
              @click="chaKanSM('允许修改处方信息的煎药方式', 'yunXuXGCFDJYFS')"
            />
          </div>
        </div>

        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药退费是否需要药房同意</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoTFSFXYYFTY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药退费是否需要药房同意', 'caoYaoTFSFXYYFTY')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配是否允许取消接药</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiSFYXQXJS"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配是否允许取消接药', 'jingPeiSFYXQXJS')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiID === '0'">
          <div class="zidongfy">
            <span>外配是否走医保目录</span>
            <md-radio-group
              v-model="sheZhi.query.waiPeiSFZYBML"
              border
              class="flex1"
            >
              <md-radio label="1">是</md-radio>
              <md-radio label="0">否</md-radio>
            </md-radio-group>
          </div>
          <div class="df flex1" v-show="sheZhi.query.waiPeiSFZYBML === '0'">
            <span>外配处方取值</span>
            <md-radio-group
              v-model="sheZhi.query.waiPeiXMZ"
              border
              class="flex1"
            >
              <md-radio label="全部药品字典">全部药品字典</md-radio>
              <md-radio label="外配属性字典">外配属性字典</md-radio>
            </md-radio-group>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('外配是否走医保目录', 'waiPeiSFZYBML')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>按账簿类别管理</span>
          <md-radio-group
            v-model="sheZhi.query.shiFouAZBLBGL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('按账簿类别管理', 'shiFouAZBLBGL')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>备药申请是否批量发送处方</span>
          <md-radio-group
            v-model="sheZhi.query.beiYaoSQSFPLFSCF"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('备药申请是否批量发送处方', 'beiYaoSQSFPLFSCF')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>药库房上线时间</span>
          <div class="zidongfy">
            <md-date-picker
              v-model="sheZhi.query.yaoKuFSXSJ"
              type="datetime"
              placeholder="选择日期"
              format="YYYY-MM-DD HH:ss:mm"
              value-format="YYYY-MM-DD HH:ss:mm"
            ></md-date-picker>
          </div>
          <div class="redText">上线时间影响药品字典更改权限！请谨慎修改！</div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药库房上线时间', 'yaoKuFSXSJ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>护士请领药品是否占药房库存</span>
          <md-radio-group
            v-model="sheZhi.query.huShiQLYPSFZYFKC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('护士请领药品是否占药房库存', 'huShiQLYPSFZYFKC')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>静配接收一次请求组数</span>
          <md-input
            v-number
            v-model="sheZhi.query.jingPeiJSYCQQZS"
            placeholder="请输入"
            class="flex1"
          />
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配接收一次请求组数', 'jingPeiJSYCQQZS')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>草药是否需要限制范围提醒</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoSFXYXZFWTX"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药是否需要限制范围提醒', 'caoYaoSFXYXZFWTX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span class="span">病区发药毒理分类筛选默认值</span>
          <md-select
            v-model="sheZhi.query.bingQuFYDLFLSXMRZ"
            multiple
            inline
            style="width: 85%"
          >
            <md-option
              v-for="item in duLiFLOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药毒理分类筛选默认值', 'bingQuFYDLFLSXMRZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区发药汇总单打印排序</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYHZDDYPX"
            border
            class="flex1"
          >
            <md-radio label="1">按摆放位置顺序</md-radio>
            <md-radio label="2">按药品名称</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药汇总单打印排序', 'bingQuFYHZDDYPX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span class="span">药房药品性质代码</span>
          <md-select
            v-model="sheZhi.query.yaoFangYPXZDM"
            multiple
            inline
            style="width: 89.5%"
          >
            <md-option
              v-for="item in yaoFangYPXZDMList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药房药品性质代码', 'yaoFangYPXZDM')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <div class="zidongfy" v-if="yiXuanObj.weiZhiDLDM === '6'">
            <span>毒麻精发药是否二次审核</span>
            <md-radio-group
              v-model="sheZhi.query.duMaJFYSFECSH"
              border
              class="flex1"
            >
              <md-radio label="1">是</md-radio>
              <md-radio label="0">否</md-radio>
            </md-radio-group>
          </div>
          <div class="df flex1" v-show="sheZhi.query.duMaJFYSFECSH == 1">
            <span class="span mar-l-16">毒理分类</span>
            <md-select
              v-model="sheZhi.query.duMaJFYRCSHFLDM"
              multiple
              inline
              style="width: 85%"
            >
              <md-option
                v-for="item in duLiFList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('毒麻精发药是否二次审核', 'duMaJFYSFECSH')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药待发药列表排序规则</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenFYPXGZ"
            border
            class="flex1"
          >
            <md-radio label="1">按收费时间倒序</md-radio>
            <md-radio label="2">按取药序号升序</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊发药待发药列表排序规则', 'menZhenFYPXGZ')"
          />
        </div>
        <!-- <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药房代煎发药允许修改处方信息</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoFDJFYSFYXXGCF"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('草药房代煎发药允许修改处方信息', 'caoYaoFDJFYSFYXXGCF')
            "
          />
        </div> -->
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配完成分拣默认打印</span>
          <md-checkbox-group
            v-model="sheZhi.query.fenJianDY"
            border
            class="flex1"
          >
            <md-checkbox
              v-for="item in fenJianDYList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</md-checkbox
            >
          </md-checkbox-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('分拣打印_打包码', 'fenJianDY_daBaoMa')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药读卡自动打印</span>
          <md-radio-group
            v-model="sheZhi.query.faYaoDKZDDY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊发药读卡自动打印', 'faYaoDKZDDY')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>TPN输液标签打印批次</span>
          <md-select
            v-model="sheZhi.query.tPNShuYeBQDYPC"
            multiple
            inline
            class="flex1"
          >
            <md-option
              v-for="item in piCiOption"
              :key="item.piCiID"
              :label="item.piCiMC"
              :value="item.piCiID"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('TPN输液标签打印批次', 'tPNShuYeBQDYPC')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiID !== '0'">
          <div class="zidongfy">
            <span>药品进价小数点位数</span>
            <md-input
              v-model="sheZhi.query.jinJiaXSDWS"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            />
          </div>
          <div class="df flex1">
            <span class="span mar-l-16">零售价小数点位数</span>
            <md-input
              v-model="sheZhi.query.lingShouJXSDWS"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            />
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品进价小数点位数', 'jinJiaXSDWS')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiID !== '0'">
          <div class="zidongfy">
            <span>药品进价金额小数点位数</span>
            <md-input
              v-model="sheZhi.query.jinJiaJEXSDWS"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            />
          </div>
          <div class="df flex1">
            <span class="span mar-l-16">零售价金额小数点位数</span>
            <md-input
              v-model="sheZhi.query.lingShouJEXSDWS"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            />
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品零售价小数点位数', 'lingShouJXSDWS')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>退药是否打印</span>
          <md-radio-group
            v-model="sheZhi.query.tuiYaoSFDY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('退药是否打印', 'tuiYaoSFDY')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>打印延迟时间</span>
          <md-input
            v-model="sheZhi.query.daYinYCSJ"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          >
            <template #suffix> 毫秒 </template></md-input
          >
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('打印延迟时间', 'daYinYCSJ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药检索是否弹退药提醒</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenFYJSSFTTYTX"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('门诊发药检索是否弹退药提醒', 'menZhenFYJSSFTTYTX')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>查看全院库存</span>
          <md-radio-group
            v-model="sheZhi.query.kuCunCXSFCKQYKC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('查看全院库存', 'kuCunCXSFCKQYKC')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>是否接收超库存药品请领</span>
          <md-radio-group
            v-model="sheZhi.query.chaoKuCunSFYXQL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('是否接收超库存药品请领', 'chaoKuCunSFYXQL')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>未受理请领是否锁定药库库存</span>
          <md-radio-group
            v-model="sheZhi.query.weiShouLiSFSDKC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('未受理请领是否锁定药库库存', 'weiShouLiSFSDKC')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药取药单是否打印全部给药方式</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoQYDSFDYQBGYFS"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('草药取药单是否打印全部给药方式', 'caoYaoQYDSFDYQBGYFS')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区待发药是否默认全选药品</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuDFYSFMRQXYP"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区待发药是否默认全选药品', 'bingQuDFYSFMRQXYP')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配已接收打印是否录入操作人</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiYJSDYSFLRCZR"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配已接收打印是否录入操作人', 'jingPeiYJSDYSFLRCZR')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配接收打印规则</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiJSDYGZ"
            border
            class="flex1"
          >
            <md-radio label="1">按病区批次药品床号</md-radio>
            <md-radio label="0">按病区床号批次</md-radio>
            <md-radio label="2">静配接收打印规则</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配接收打印规则', 'jingPeiJSDYGZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配待出舱是否需要批量出舱</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiDCCSFXYPLCC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配待出舱是否需要批量出舱', 'jingPeiDCCSFXYPLCC')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配分拣单病区单批次校验强控</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiFJDBQDPCJYQK"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配分拣单病区单批次校验强控', 'jingPeiFJDBQDPCJYQK')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配配送是否默认选择交接单</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiPSSFMRXZJJD"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配配送是否默认选择交接单', 'jingPeiPSSFMRXZJJD')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配待接收展示打印内容</span>
          <md-checkbox-group
            v-model="sheZhi.query.jingPeiDJSZSDYNR"
            border
            class="flex1"
          >
            <md-checkbox
              v-for="item in daiJieShouDYList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</md-checkbox
            >
          </md-checkbox-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配待接收展示打印内容', 'jingPeiDJSZSDYNR')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配已接收是否展示皮试医嘱</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiYJSSFZSPSYZ"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配已接收是否展示皮试医嘱', 'jingPeiYJSSFZSPSYZ')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配皮试医嘱是否只走到接收流程</span>
          <md-radio-group
            v-model="sheZhi.query.jingPeiPSYZSFZZDJSLC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('静配皮试医嘱是否只走到接收流程', 'jingPeiPSYZSFZZDJSLC')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配药品汇总打印规则</span>
          <md-select
            v-model="sheZhi.query.jingPeiYPHZDYGZ"
            inline
            class="flex1"
          >
            <md-option
              v-for="item in yaoPinDYGZList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('静配药品汇总打印规则', 'jingPeiYPHZDYGZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药窗口默认勾选暂停</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenFYCKMRGXZT"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊发药窗口默认勾选暂停', 'menZhenFYCKMRGXZT')"
          />
        </div>
        <!--  -->
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊待发药展示退药记录</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenDFYZSTYJL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊发药窗口默认勾选暂停', 'menZhenDFYZSTYJL')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>允许小规格入库</span>
          <md-radio-group
            v-model="sheZhi.query.ShiFouYXXGGRK"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('允许小规格入库', 'ShiFouYXXGGRK')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区发药申请时间范围默认值</span>
          <md-input
            v-model="sheZhi.query.bingQuFYSQSJFWMRZ"
            v-number.float="{ decimal: 0, min: 0 }"
            placeholder="请输入"
            style="width: 80%"
          >
            <template #suffix>天</template>
          </md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药申请时间范围默认值', 'bingQuFYSQSJFWMRZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区发药不同发药类型退药是否合并</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYBTFYLXTYSFHB"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '病区发药不同发药类型退药是否合并',
                'bingQuFYBTFYLXTYSFHB',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>按采购单入库是否需要选择批号效期</span>
          <md-radio-group
            v-model="sheZhi.query.anCaiGDRKSFXYXZPHXQ"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '按采购单入库是否需要选择批号效期',
                'anCaiGDRKSFXYXZPHXQ',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>库存查询默认查询范围</span>
          <md-radio-group
            v-model="sheZhi.query.kuCunCXMRCXFW"
            border
            class="flex1"
          >
            <md-radio label="1">库存不为0</md-radio>
            <md-radio label="2">库存为0</md-radio>
            <md-radio label="0">所有</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '按采购单入库是否需要选择批号效期',
                'anCaiGDRKSFXYXZPHXQ',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>病区发药按单病区发药</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYADBQFY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药按单病区发药', 'bingQuFYADBQFY')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>药品出库未记账是否打印请领单</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinCKWJZSFDYQLD"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品出库未记账是否打印请领单', 'yaoPinCKWJZSFDYQLD')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>药库入库记账是否更新进价零售价</span>
          <md-radio-group
            v-model="sheZhi.query.yaoKuRKJZSFGXJJLSJ"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药库入库记账是否更新进价零售价', 'yaoKuRKJZSFGXJJLSJ')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>采购计划中的采购数量值</span>
          <md-radio-group
            v-model="sheZhi.query.caiGouJHZDCGSLZ"
            border
            class="flex1"
          >
            <md-radio label="2">空白</md-radio>
            <md-radio label="1">根据消耗量和包装系数计算</md-radio>
            <md-radio label="3">等于消耗量</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('采购计划中的采购数量值', 'caiGouJHZDCGSLZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>请领受理拆单规则</span>
          <md-select
            v-model="sheZhi.query.qingLingSLCDGZ"
            class="flex1"
            filterable
            :clearable="false"
          >
            <md-option
              v-for="item in qingLingSLCDGZOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('请领受理拆单规则', 'qingLingSLCDGZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>受理请领库存不足的是否生成待受理单</span>
          <md-radio-group
            v-model="sheZhi.query.shouLiQLKCBZSFSCDSLD"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="2">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '受理请领库存不足的是否生成待受理单',
                'shouLiQLKCBZSFSCDSLD',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <span>出库是否按总量</span>
          <md-radio-group
            v-model="sheZhi.query.chuKuSFAZL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="2">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('出库是否按总量', 'chuKuSFAZL')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '7'">
          <div class="zidongfy">
            <span>饮片药品入库默认药品效期为入库制单日期</span>
            <md-input
              v-model="sheZhi.query.yinPianYPRKMRYPXQ"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            >
              <template #suffix> 年 </template>
            </md-input>
          </div>
          <div class="df flex1">
            <span class="span mar-l-16"
              >颗粒剂药品入库默认药品效期为入库制单日期</span
            >
            <md-input
              v-model="sheZhi.query.keLiJYPRKMRYPXQ"
              v-number.float="{ decimal: 0, min: 0 }"
              placeholder="请输入"
              :clearable="false"
              class="flex1"
            >
              <template #suffix> 年 </template>
            </md-input>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '饮片药品入库默认药品效期为入库制单日期',
                'yinPianYPRKMRYPXQ',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药处方顺序号生成规则</span>
          <md-select
            v-model="sheZhi.query.caoYaoCFSXHSCGZ"
            inline
            class="flex1"
          >
            <md-option
              v-for="item in caoYaoSCGZOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药处方顺序号生成规则', 'caoYaoCFSXHSCGZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID !== '0'">
          <span>盘存模式</span>
          <md-radio-group v-model="sheZhi.query.panCunMS" border class="flex1">
            <md-radio label="1">总量模式</md-radio>
            <md-radio label="2">按批次盘存</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('盘存模式', 'panCunMS')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <div class="df flex1">
            <span>代煎处方补传草药发送处方方式</span>
            <md-radio-group
              v-model="sheZhi.query.caoYaoFSCFFS"
              border
              class="flex1"
            >
              <md-radio label="2">非标</md-radio>
              <md-radio label="1">标准</md-radio>
            </md-radio-group>
          </div>
          <div class="df flex1" v-if="sheZhi.query.caoYaoFSCFFS == 2">
            <span>草药自煎是否发送处方（非标）</span>
            <md-radio-group
              v-model="sheZhi.query.caoYaoZJFSCFBZ"
              border
              class="flex1"
            >
              <md-radio label="1">是</md-radio>
              <md-radio label="0">否</md-radio>
            </md-radio-group>
          </div>
          <div class="df flex1" v-else>
            <span>草药发送处方煎药方式（标准）</span>
            <md-select
              v-model="sheZhi.query.caoYaoFSCFJYFSBZ"
              class="flex1"
              multiple
              filterable
            >
              <md-option
                v-for="item in jianYaoFSOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('代煎处方补传草药发送处方方式', 'caoYaoFSCFFS')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊备药草药处方打印规则</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenBYCYCFDYGZ"
            border
            class="flex1"
          >
            <md-radio label="1">打印1份</md-radio>
            <md-radio label="2">代煎打印2份，自煎打印1份</md-radio>
          </md-radio-group>

          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊备药草药处方打印规则', 'menZhenBYCYCFDYGZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>出院带药按单人发药</span>
          <md-radio-group
            v-model="sheZhi.query.chuYuanDYADRFY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('出院带药按单人发药', 'chuYuanDYADRFY')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>是否启用药房发药闭环</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYSFQYYFFYBH"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('是否启用药房发药闭环', 'bingQuFYSFQYYFFYBH')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>药品出库打印顺序</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinCKDYSX"
            border
            class="flex1"
          >
            <md-radio label="1">按摆放位置</md-radio>
            <md-radio label="2">按单据排序</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品出库打印顺序', 'yaoPinCKDYSX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>医嘱开立时单字母是否检索药品</span>
          <md-radio-group
            v-model="sheZhi.query.yiZhuKLSDZMSFJSYP"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('医嘱开立时单字母是否检索药品', 'yiZhuKLSDZMSFJSYP')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>药品名称检索时拼接内容</span>
          <md-checkbox-group
            v-model="sheZhi.query.yaoPinMCJSSPJNR"
            border
            class="flex1"
          >
            <md-checkbox
              v-for="item in yaoPinMCJSSPJNRList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</md-checkbox
            >
          </md-checkbox-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品名称检索时拼接内容', 'yaoPinMCJSSPJNR')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>草药开方检索是否需要根据类型过滤</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoKFJSSFXYGJLXGL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '草药开方检索是否需要根据类型过滤',
                'caoYaoKFJSSFXYGJLXGL',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>出院带药开立范围控制</span>
          <md-radio-group
            v-model="sheZhi.query.chuYuanDYKLFWKZ"
            border
            class="flex1"
          >
            <md-radio label="0">按规则</md-radio>
            <md-radio label="1">按规格设置 </md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('出院带药开立范围控制', 'chuYuanDYKLFWKZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>相同药品医嘱显示顺序</span>
          <md-radio-group
            v-model="sheZhi.query.xiangTongYPYZXSSX"
            border
            class="flex1"
          >
            <md-radio label="1">先大规格再小规格</md-radio>
            <md-radio label="2">先小规格再大规格</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('相同药品医嘱显示顺序', 'xiangTongYPYZXSSX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>无追溯码药品上传是否固定拆零</span>
          <md-radio-group
            v-model="sheZhi.query.wuZhuiSMYPSCSFGDCL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('无追溯码药品上传是否固定拆零', 'wuZhuiSMYPSCSFGDCL')
            "
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiID === '0'">
          <span>西成药医嘱检索单价显示位数</span>
          <md-input
            v-model="sheZhi.query.xiChengYYZJSDJXSWS"
            v-number.float="{ decimal: 0, min: 0 }"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          >
            <template #suffix> 位 </template>
          </md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('西成药医嘱检索单价显示位数', 'xiChengYYZJSDJXSWS')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span>药品包装规格是否启用夜间使用标志</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinBZGGSFQYYJSYBZ"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('药品包装规格是否启用夜间使用标志', 'yaoPinBZGGSFQYYJSYBZ')
            "
          />
        </div>
        <div class="col yiyuandz mar-b-8" v-if="yiXuanObj.weiZhiID === '0'">
          <span class="yiyuandz-title">药品快递送医院地址</span>
          <!-- 药品快递送医院地址 -->
          <div>
            <md-row class="yiyuandz-row" :gutter="4" type="flex" align="middle">
              <md-col :span="2">
                <span>收件人姓名</span>
              </md-col>
              <md-col :span="7">
                <md-input
                  v-model="yiYuanDZForm.shouJianRXM"
                  placeholder="请输入内容"
                ></md-input>
              </md-col>
              <md-col :span="2">
                <span>收件人电话</span>
              </md-col>
              <md-col :span="7">
                <md-input
                  v-model="yiYuanDZForm.shouJianRDH"
                  placeholder="请输入内容"
                ></md-input>
              </md-col>
              <md-col :span="6"> </md-col>
            </md-row>
            <md-row class="yiyuandz-row" :gutter="4" type="flex" align="middle">
              <md-col :span="2">
                <span>收件地址</span>
              </md-col>
              <md-col :span="16">
                <AddressSelect
                  v-model="xiangXiDZ"
                  :data="setData"
                  ref="addressSelect"
                  @change="handleCSDChange($event)"
                />
              </md-col>
              <md-col :span="2">
                <span>详细地址</span>
              </md-col>
              <md-col :span="4">
                <md-input
                  v-model="yiYuanDZForm.shouJianDZQTXX"
                  placeholder="请输入内容"
                ></md-input>
              </md-col>
            </md-row>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品快递送医院地址', 'yaoPinKDSYYDZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>入库单中批号效期是否自动带出</span>
          <md-radio-group
            v-model="sheZhi.query.ruKuDZPHXQSFZDDC"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('入库单中批号效期是否自动带出', 'ruKuDZPHXQSFZDDC')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药处方明细打印顺序</span>
          <md-select
            v-model="sheZhi.query.caoYaoCFMXDYSX"
            class="flex1"
            filterable
            :clearable="false"
          >
            <md-option
              v-for="item in caoYaoCFMXDYSXOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药处方明细打印顺序', 'caoYaoCFMXDYSX')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药患者是否显示历史用药</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenFYHZSFXSLSYY"
            border
            class="flex1"
          >
            <md-radio label="1">显示不展开</md-radio>
            <md-radio label="2">显示并展开</md-radio>
            <md-radio label="3">不显示</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('门诊发药患者是否显示历史用药', 'menZhenFYHZSFXSLSYY')
            "
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <div class="zidongfy">
            <span>病区发药配方颗粒贴默认打印数量</span>
            <md-select
              v-model="sheZhi.query.bingQuFYPFKLTMRDYSL"
              class="flex1"
              filterable
            >
              <md-option
                v-for="item in bingQuFYPFKLTMRDYSLOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </div>
          <div class="df flex1">
            <span class="span mar-l-16">病区发药中药处方默认打印数量</span>
            <md-select
              v-model="sheZhi.query.bingQuFYZYCFMRDYSL"
              class="flex1"
              filterable
              :clearable="false"
            >
              <md-option
                v-for="item in bingQuFYZYCFMRDYSLOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('病区发药配方颗粒贴默认打印数量', 'bingQuFYPFKLTMRDYSL')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>药品请领允许重复请领</span>
          <md-radio-group
            v-model="sheZhi.query.yaoPinQLYXCFQL"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品请领允许重复请领', 'yaoPinQLYXCFQL')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>静配退药待确认自定义默认查询时间范围</span>
          <md-input
            v-model="sheZhi.query.jingPeiTYDQRMRCXSJFW"
            v-number.float="{ decimal: 0, min: 0 }"
            placeholder="请输入"
            :clearable="false"
            class="flex1"
          >
            <template #suffix> 天 </template>
          </md-input>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM(
                '静配退药待确认自定义默认查询时间范围',
                'jingPeiTYDQRMRCXSJFW',
              )
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药贴是否只打印一份</span>
          <md-radio-group
            v-model="sheZhi.query.caoYaoTSFZDYYF"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('草药贴是否只打印一份', 'caoYaoTSFZDYYF')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>药品养护理由默认值</span>
          <md-select
            v-model="sheZhi.query.yaoPinYHLYMRZ"
            class="flex1"
            filterable
            :clearable="false"
          >
            <md-option
              v-for="item in yaoPinYHLYMRZOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </md-select>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('药品养护理由默认值', 'yaoPinYHLYMRZ')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>草药门诊发药患者展示代煎标识</span>
          <md-radio-group
            v-model="sheZhi.query.CaoYaoMZFYHZZSDJBS"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="
              chaKanSM('草药门诊发药患者展示代煎标识', 'CaoYaoMZFYHZZSDJBS')
            "
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区发药草药未发药是否打印</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYCYWFYSFDY"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药草药未发药是否打印', 'bingQuFYCYWFYSFDY')"
          />
        </div>
        <md-title
          label="追溯码设置"
          v-if="yiXuanObj.weiZhiDLDM === '6'"
          class="mar-b-8"
        ></md-title>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>是否启用第三方</span>
          <md-radio-group
            v-model="sheZhi.query.zhuiSuMa_shiFouQYDSF"
            border
            class="flex1"
          >
            <md-radio label="1">启用</md-radio>
            <md-radio label="0">不启用</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('追溯码_是否启用第三方', 'zhuiSuMa_shiFouQYDSF')"
          />
        </div>
        <div class="col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>追溯码录入节点</span>
          <md-checkbox-group
            v-model="sheZhi.query.zhuiSuMLRJD"
            border
            class="flex1"
          >
            <md-checkbox
              v-for="item in zhuiSuMLRJDList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</md-checkbox
            >
          </md-checkbox-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('追溯码录入节点', 'zhuiSuMLRJD')"
          />
        </div>
        <div class="df mar-b-8 col" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <div class="df flex1">
            <span class="span">门诊录入范围</span>
            <md-checkbox-group
              v-model="sheZhi.query.zhuiSuMa_menJiZLRFW"
              @change="handlerChangeMZAndZY($event, 'zhuiSuMa_menJiZLRFW')"
              border
              class="flex1"
            >
              <md-checkbox
                v-for="item in menZhenAndZYList"
                :key="item.value"
                :label="item.value"
                >{{ item.label }}</md-checkbox
              >
            </md-checkbox-group>
          </div>
          <div class="df flex1">
            <span class="span mar-l-16">住院录入范围</span>
            <md-checkbox-group
              v-model="sheZhi.query.zhuiSuMa_zhuYuanLRFW"
              @change="handlerChangeMZAndZY($event, 'zhuiSuMa_zhuYuanLRFW')"
              border
              class="flex1"
            >
              <md-checkbox
                v-for="item in zhuYuanList"
                :key="item.value"
                :label="item.value"
                >{{ item.label }}</md-checkbox
              >
            </md-checkbox-group>
          </div>
          <div class="df flex1">
            <span class="span mar-l-16">住院录入方式</span>
            <md-radio-group
              v-model="sheZhi.query.zhuiSuMa_zhuYuanLRFS"
              border
              class="flex1"
            >
              <md-radio label="1">以药品为单位</md-radio>
              <md-radio label="2">以人为单位</md-radio>
            </md-radio-group>
          </div>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊录入范围', 'zhuiSuMa_menJiZLRFW')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>病区发药时是否录入追溯码</span>
          <md-radio-group
            v-model="sheZhi.query.bingQuFYSSFLRZSM"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('病区发药时是否录入追溯码', 'bingQuFYSSFLRZSM')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊发药追溯码是否强控</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenFYZSMSFQK"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊发药追溯码是否强控', 'menZhenFYZSMSFQK')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>过期发药追溯码是否强控</span>
          <md-radio-group
            v-model="sheZhi.query.guoQiFYZSMSFQK"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('过期发药追溯码是否强控', 'guoQiFYZSMSFQK')"
          />
        </div>
        <div class="col mar-b-8" v-if="yiXuanObj.weiZhiDLDM === '6'">
          <span>门诊退药追溯码是否强控</span>
          <md-radio-group
            v-model="sheZhi.query.menZhenTYZSMSFQK"
            border
            class="flex1"
          >
            <md-radio label="1">是</md-radio>
            <md-radio label="0">否</md-radio>
          </md-radio-group>
          <img
            src="@/assets/images/shuoming.png"
            class="btn"
            alt=""
            @click="chaKanSM('门诊退药追溯码是否强控', 'menZhenTYZSMSFQK')"
          />
        </div>
      </div>
      <div :class="prefixClass('yaoPinBFWZ-right-fotter')">
        <md-button
          type="primary"
          :class="prefixClass('right-12 buttons')"
          plain
          @click="handleReset"
          >重置</md-button
        >
        <md-button
          type="primary"
          :class="prefixClass('right-12 buttons')"
          @click="handleSave"
          >保存</md-button
        >
      </div>
    </div>
    <chaKanSMDrawer ref="Drawer" />
  </div>
</template>
<script lang="ts" setup>
import { logger } from '@/service/log';
import { getGeiYaoFSSel } from '@/service/yaoPin/geiYaoFS';
import { GetYaoFangListByJGID } from '@/service/yaoPin/yaoPinZD';
import {
  GetCaoYaoTieGYFSList,
  GetJingPeiPCGZSelectList,
  GetYaoKuFSZWZList,
  SaveKuFangSZ,
  getKuFangSZ,
} from '@/service/yaoPin/YaoPinZDJCSJ';
import { getShengShiQList } from '@/service/yaoPinYK/gongHuoDW';

import AddressSelect from '@/components/address-select/index.vue';
import chaKanSMDrawer from '@/components/peiZhiSM/chaKanSMDrawer.vue';
import {
  getShuJuYZYList,
  getYaoPinShuJuYZYList,
} from '@/service/yaoPin/yeWuZD';
import { GetChuRuKFSByFXDM_1 } from '@/service/yaoPinYK/chuRuKFS';
import { getYiZhuSJYZYListByLBID } from '@/service/yiZhu/yiZhuYWZD';
import { getJiGouID } from '@/system/utils/local-cache';
import { MdMessage, MdMessageBox, useNamespace } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
import { nextTick, reactive, ref } from 'vue';

const ns = useNamespace('YaoKuFSZ');
const formData = {
  bingQuFYCYWFYSFDY: '',
  CaoYaoMZFYHZZSDJBS: '',
  caoYaoTSFZDYYF: '',
  jingPeiTYDQRMRCXSJFW: '',
  jingPeiYJSSFZSPSYZ: '1',
  jingPeiDJSZSDYNR: ['shuYeBQ', 'yaoPinHZ'],
  jingPeiYPHZDYGZ: '1',
  menZhenFYZSMSFQK: '0', // 门诊发药追溯码是否强控
  guoQiFYZSMSFQK: '0', //   过期发药追溯码是否强控
  menZhenTYZSMSFQK: '0', // 门诊退药追溯码是否强控
  qingLingSLCDGZ: '',
  bingQuFYPFKLTMRDYSL: '',
  bingQuFYZYCFMRDYSL: '',
  menZhenFYHZSFXSLSYY: '',
  caoYaoCFMXDYSX: '',
  yaoPinYHLYMRZ: '2',
  ruKuDZPHXQSFZDDC: '',
  yaoPinQLYXCFQL: '1',
  bingQuFYSFQYYFFYBH: '',
  yaoPinCKDYSX: '',
  jingPeiPSYZSFZZDJSLC: '',
  menZhenBYCYCFDYGZ: '',
  chuYuanDYADRFY: '',
  caoYaoFSCFJYFSBZ: [],
  caoYaoFSCFFS: '',
  bingQuFYSSFLRZSM: '0',
  caoYaoCFSXHSCGZ: '',
  yaoPinCKWJZSFDYQLD: '',
  yaoKuRKJZSFGXJJLSJ: '',
  caiGouJHCGSLJSGZ: '',
  caiGouJHZDCGSLZ: '1',
  shouLiQLKCBZSFSCDSLD: '1',
  chuKuSFAZL: '',
  panCunMS: '1',
  bingQuFYADBQFY: '',
  kuCunCXMRCXFW: '',
  zhuiSuMa_zhuYuanLRFS: '',
  anCaiGDRKSFXYXZPHXQ: '',
  bingQuFYBTFYLXTYSFHB: '',
  bingQuFYSQSJFWMRZ: 30,
  ShiFouYXXGGRK: '',
  menZhenDFYZSTYJL: '',
  menZhenFYCKMRGXZT: '',
  jingPeiPSSFMRXZJJD: '',
  jingPeiFJDBQDPCJYQK: '',
  jingPeiDCCSFXYPLCC: '',
  jingPeiJSDYGZ: '',
  jingPeiYJSDYSFLRCZR: '',
  bingQuDFYSFMRQXYP: '',
  menZhenFYJSSFTTYTX: '',
  kuCunCXSFCKQYKC: '',
  chaoKuCunSFYXQL: '',
  weiShouLiSFSDKC: '',
  tuiYaoSFDY: '',
  daYinYCSJ: '',
  caoYaoQYDSFDYQBGYFS: '', // 草药取药单是否打印全部给药方式  0 null：否，只打印代煎和自煎，1：是，打印代煎自煎免煎等，默认否
  lingShouJEXSDWS: 2,
  jinJiaJEXSDWS: 2,
  jinJiaXSDWS: 3,
  lingShouJXSDWS: 3,
  caoYaoFDJFYSFYXXGCF: '', // 草药放代煎发药允许修改处方信息
  duMaJFYSFECSH: '', //毒麻精二次审核
  duMaJFYRCSHFLDM: [],
  menZhenFYPXGZ: '', // 门诊发药排序规则
  faYaoDKZDDY: '', //门诊读卡自动打印
  tPNShuYeBQDYPC: [],
  yinFuKHJJEJSFS: '',
  xiChengYYPBZGGMRKGGZ: '',
  shiFouZSZDTJD: '', //展示调价单
  waiPeiSFZYBML: '', //外配
  waiPeiXMZ: '',
  shiFouAZBLBGL: '',
  beiYaoSQSFPLFSCF: '',
  huShiQLYPSFZYFKC: '',
  caoYaoSFXYXZFWTX: '',
  yaoKuFSXSJ: '',
  yiZhuKLSDZMSFJSYP: '',
  caoYaoKFJSSFXYGJLXGL: '',
  wuZhuiSMYPSCSFGDCL: '0',
  yaoPinBZGGSFQYYJSYBZ:'',
  chuYuanDYKLFWKZ: '0',
  xiangTongYPYZXSSX: '',
  yaoPinKDSYYDZ: '',
  daYinSFABFWZPX: '',
  caoYaoMRJJL: '',
  xiChengYYZJSDJXSWS:'',
  shiFouKQQDJMS: '', //启用签到机
  shiFouFSKLJCF: '', //发送颗粒剂处方
  caoYaoZJFSCFBZ: '', //自煎掉接口
  caoYaoTFSFXYYFTY: '', //退费限制
  jingPeiSFYXQXJS: null,
  buJinBYJ: '',
  yaoPinZDFFBZSFXSZW: '',
  jingPeiJSYCQQZS: '',
  menZhenYFSFZSLTHZCF: '',
  shuangKongYPYZBZ: '',
  zhuiSuMa_shiFouQYDSF: '',
  zhuiSuMLRJD: [],
  fenJianDY: [],
  zhuiSuMa_menJiZLRFW: [],
  zhuiSuMa_zhuYuanLRFW: [],
  jingPeiYFGLBZ: '',
  yaoPinRKXZYPHZSLSJJ: '',
  chuLiHZDFY: '',
  yaoPinKCXSFS: '', //药品显示方式
  yaoKuCGDJLDPT: '',
  yaoPinRKZDDRCGDSL: '', // 药品入库自动带入采购单数量 1：申请数量，2：消耗量，null：申请数量
  yaoPinRKDDMJYPDLZD: '',
  yaoPinCKDDMJYPDLZD: '',
  yaoPinRKGNZDRKBQ: '', //入库标签
  zhongYaoYPRKJGXZ: '', //入库价格限制
  tiaoJiaSFAGHDW: '', //调价是否按供货单位 默认否
  yaoPinSXTS: '',
  yaoPinJYTS: '',
  tiaoJianLX: '',
  jinJia: [
    {
      tiaoJianDM: '',
      tiaoJianMC: '',
      tiaoJianZhi: null,
      shunXuHao: 1,
    },
    {
      tiaoJianDM: '',
      tiaoJianMC: '',
      tiaoJianZhi: null,
      shunXuHao: 2,
    },
  ],
  tiXingLX: '',
  diSanFSBDJ: [],
  yaoPinMCJSSPJNR: [],
  ziDongFY: {
    xiangMuZDM: '',
    xiangMuZMC: '',
  },
  shiFouYXDJZJZH: {
    xiangMuZDM: '',
    xiangMuZMC: '',
  },
  daiJianZJSFQHYF: {
    xiangMuZDM: '',
    xiangMuZMC: '',
  },
  yaoFangXZ: [],
  keYiQHDJYFS: [],
  buKouJKCDGYFS: [],
  yunXuXGCFDJYFS: [],
  yaoFangYPXZDM: [],
  bingQuFYDLFLSXMRZ: [],
  yaoPinRKMRRKFS: '',
  yaoPinRKMRCKFS: '',
  bingQuFYHZDDYPX: null,
  yinPianYPRKMRYPXQ: null,
  keLiJYPRKMRYPXQ: null,
};
type DataType = typeof formData;

interface SheZhiType {
  query: DataType;
  xiangMuDMList: XiangMuDMType[];
}
//左侧树属性
interface KuFangItem {
  weiZhiDLMC: string;
  weiZhiDLDM: string;
  weiZhiID: string | number;
  weiZhiMC: string;
  mxTreeList: Array<any>;
  completelyDisabled?: boolean;
}
interface XiangMuDMType {
  biaoZhunDM: string;
  biaoZhunMC: string;
}

interface XiangMuSaveItem {
  xiangMuDM: keyof DataType;
  xiangMuMC: string;
  xiangMuZDM: DataType[keyof DataType];
  xiangMuZMC: string;
}
interface KuFangSZItem extends XiangMuSaveItem {}

interface KuCunFSType {
  chuRuKFSID: string;
  chuRuKFSMC: string;
  kuCunZJDM: string;
  danWeiBMDM: string;
  danWeiBMMC: string;
}
interface yaoFangItem {
  weiZhiMC: string;
  weiZhiID: string;
}
interface jianYaoFSItem {
  geiYaoFSID: string;
  geiYaoFSMC: string;
}
const ruKuFSOptions = ref<KuCunFSType[]>([]);
const chuKuFSOptions = ref<KuCunFSType[]>([]);
const Drawer = ref();
const kuFangData = ref<KuFangItem[]>([]);
const yaoPinMCJSSPJNRList = reactive([
  {
    label: '商品名',
    value: '1',
  },
  {
    label: '描述',
    value: '2',
  },
]);
let xiangXiDZ = reactive([]);
let setData = ref<any>();
let yiYuanDZForm = reactive({
  shouJianRXM: '',
  shouJianRDH: '',
  shouJianDZQTXX: '',
  shouJianDZSFDM: '',
  shouJianDZSFMC: '',
  shouJianDZSDQDM: '',
  shouJianDZSDQMC: '',
  shouJianDZXQDM: '',
  shouJianDZXQMC: '',
});

const yaoPinDYGZList = reactive([
  {
    label: '摆放位置',
    value: '1',
  },
  {
    label: '先大输液，再摆放位置',
    value: '2',
  },
]);
const daiJieShouDYList = reactive([
  {
    label: '药品汇总',
    value: 'yaoPinHZ',
  },
  {
    label: '接收明细',
    value: 'bingQuKSHZ',
  },
  {
    label: '输液标签',
    value: 'shuYeBQ',
  },
]);
const diSanFSBDJList = reactive([
  {
    label: '包药机',
    value: '0',
  },
  {
    label: '备药机',
    value: '1',
  },
  {
    label: '发药机',
    value: '2',
  },
]);
const fenJianDYList = [
  {
    label: '打印打包码',
    value: '1',
  },
  {
    label: '打印分拣明细',
    value: '2',
  },
];
const zhuiSuMLRJDList = reactive([
  {
    label: '门诊发药',
    value: '1',
  },
  {
    label: '住院发药',
    value: '2',
  },
  {
    label: '过期发药',
    value: '3',
  },
  {
    label: '门诊退药',
    value: '4',
  },
]);
const menZhenAndZYList = reactive([
  {
    label: '全部',
    value: '0',
  },
  {
    label: '大规格药品',
    value: '1',
  },
  {
    label: '小规格转大规格',
    value: '2',
  },
]);
const zhuYuanList = reactive([
  {
    label: '全部',
    value: '0',
  },
  {
    label: '大规格出院带药',
    value: '1',
  },
]);
const tiaoJianLXOptions = reactive([
  {
    id: 1,
    tiaoJianMC: '大于',
    tiaoJianDM: '>',
  },
  {
    id: 2,
    tiaoJianMC: '小于',
    tiaoJianDM: '<',
  },
  {
    id: 3,
    tiaoJianMC: '等于',
    tiaoJianDM: '=',
  },
  {
    id: 4,
    tiaoJianMC: '大于等于',
    tiaoJianDM: '>=',
  },
  {
    id: 5,
    tiaoJianMC: '小于等于',
    tiaoJianDM: '<=',
  },
]);
const yaoFangList = ref<yaoFangItem[]>([]);
const jianYaoFSOptions = ref<jianYaoFSItem[]>([]);
const caoYaoCFMXDYSXOptions = ref<{ value: string; label: string }[]>([
  { label: '处方明细顺序', value: '1' },
  { label: '库位字典顺序', value: '2' },
]);
const bingQuFYPFKLTMRDYSLOptions = ref<{ value: string; label: string }[]>([
  { label: '贴数*2', value: '1' },
]);
const bingQuFYZYCFMRDYSLOptions = ref<{ value: string; label: string }[]>([
  { label: '1', value: '1' },
  { label: '2', value: '2' },
]);
let yaoFangYPXZDMList = reactive<{ value: string; label: string }[]>([]);
const yaoPinXZList = reactive([
  {
    label: '自煎药房',
    value: '1',
  },
  {
    label: '代煎药房',
    value: '2',
  },
]);
const caoYaoSCGZOptions = reactive([
  {
    label: '处方结算生成后：月份+日期+自煎/代煎/临方/免煎首字母+5位处方序号',
    value: '1',
  },
  {
    label: '发药调用第三方接口时按照药房位置生成：每天从1开始递增',
    value: '2',
  },
  {
    label: '门诊代煎处方结算后生成：年份后2位+日期+3位序号',
    value: '3',
  },
]);
let duLiFLOptions = reactive<{ value: string; label: string }[]>([]);
let yaoPinYHLYMRZOptions = reactive<{ value: string; label: string }[]>([]);
const qingLingSLCDGZOptions = reactive<{ value: string; label: string }[]>([
  {
    label: '不拆单',
    value: '1',
  },
  {
    label: '按西药（普通）、冷藏药品、成药、毒麻精自动拆单',
    value: '2',
  },
]);
let duLiFList = reactive<{ value: string; label: string }[]>([]);
const lastYaoPinXSWB = ref();
const treeProps = reactive({ label: 'weiZhiMC', children: 'mxTreeList' });
let yiXuanObj = reactive<Record<string, any>>({});
const caoYaoGYFSOptions = ref<{ value: string; label: string }[]>([]);
const piCiOption = ref([]);
//初始化药库房设置内容
function initData() {
  return cloneDeep(formData);
}
function getLabelByValue(list, value) {
  const item = list.find((item) => item.value === String(value));
  return item?.label || '';
}
//库房配置
const sheZhi = reactive<SheZhiType>({
  query: initData(),
  xiangMuDMList: [],
});
let leftloading = ref<boolean>(false);
let rightLoading = ref(false);
const tree = ref<{
  setCurrentKey: (weiZhiDLDM: string) => void;
}>();
async function GetShengShiQList() {
  setData.value = await getShengShiQList();
}
//获取左侧树
function handleDiZhiData(list: any) {
  let data = {
    diZhiSFDM: '',
    diZhiSFMC: '',
    diZhiSDQDM: '',
    diZhiSDQMC: '',
    diZhiXQDM: '',
    diZhiXQMC: '',
  };
  if (list && list.length > 0) {
    let diZhiMCList = list[0];
    let diZhiDMList = list[1];
    data.diZhiSFDM = diZhiDMList[0] ?? ''; //省份代码
    data.diZhiSFMC = diZhiMCList[0] ?? ''; //省份名称
    data.diZhiSDQDM = diZhiDMList[1] ?? ''; //市地区代码
    data.diZhiSDQMC = diZhiMCList[1] ?? ''; //市地区名称
    data.diZhiXQDM = diZhiDMList[2] ?? ''; //县区代码
    data.diZhiXQMC = diZhiMCList[2] ?? ''; //县区名称
  }
  return data;
}
function handleCSDChange(val: any) {
  let diZhiXX = handleDiZhiData(val);
  let data = {
    shouJianDZSFDM: '',
    shouJianDZSFMC: '',
    shouJianDZSDQDM: '',
    shouJianDZSDQMC: '',
    shouJianDZXQDM: '',
    shouJianDZXQMC: '',
  };
  data.shouJianDZSFDM = diZhiXX.diZhiSFDM; //户口地址省份代码
  data.shouJianDZSFMC = diZhiXX.diZhiSFMC; //户口地址省份名称
  data.shouJianDZSDQDM = diZhiXX.diZhiSDQDM; //户口地址市地区代码
  data.shouJianDZSDQMC = diZhiXX.diZhiSDQMC; //户口地址市地区名称
  data.shouJianDZXQDM = diZhiXX.diZhiXQDM; //户口地址县区代码
  data.shouJianDZXQMC = diZhiXX.diZhiXQMC; //户口地址县区名称
  Object.assign(yiYuanDZForm, data);
}
async function getBaiFangWZ() {
  try {
    leftloading.value = true;
    kuFangData.value = (await GetYaoKuFSZWZList()) as KuFangItem[];
    kuFangData.value.unshift({
      weiZhiDLMC: '通用',
      weiZhiDLDM: '0',
      weiZhiID: '0',
      weiZhiMC: '通用',
      mxTreeList: [],
    });
    kuFangData.value.forEach((el, index) => {
      if (el.mxTreeList.length > 0) {
        el.completelyDisabled = true;
        el.weiZhiID = index + 1;
      }
    });
    yiXuanObj = kuFangData.value[0];
    showForm();
    nextTick(() => {
      tree.value?.setCurrentKey(kuFangData.value[0].weiZhiDLDM);
    });
    sheZhi.xiangMuDMList = await getShuJuYZYList(
      {
        pageIndex: 1,
        pageSize: 9999,
        shuJuYLBID: 'YP0106',
      },
      null,
    );
  } catch (error) {
    logger.error(error);
  } finally {
    leftloading.value = false;
  }
}

// 获取药房数据
async function getYaoFangList() {
  yaoFangList.value = await GetYaoFangListByJGID({ zuZhiJGID: getJiGouID() });
  const res = await getGeiYaoFSSel({
    likeQuery: '',
    caoYaoSYBZ: '2',
    menZhenZYBZ: 1,
  });
  jianYaoFSOptions.value = res.map((el: any) => {
    return {
      value: el.geiYaoFSID,
      label: el.geiYaoFSMC,
    };
  });
}
// 获取批次数据
async function getPiCIList() {
  piCiOption.value = await GetJingPeiPCGZSelectList(yiXuanObj.weiZhiID);
}
function handlerChangeYF(val: string) {
  if (val == '0') sheZhi.query.daiJianZJSFQHYF.xiangMuZMC = '';
}
async function getCaoYaoTieGYFSList() {
  const res = await GetCaoYaoTieGYFSList();
  caoYaoGYFSOptions.value = res.map((item) => {
    return {
      label: item.geiYaoFSMC,
      value: item.geiYaoFSID,
    };
  });
}

//     //回显药库房信息
async function showForm() {
  try {
    rightLoading.value = true;

    const res: KuFangSZItem[] = await getKuFangSZ({
      weiZhiID: yiXuanObj.weiZhiID,
    });
    const daijian = caoYaoGYFSOptions.value.find(
      (item) => item.label === '代煎',
    );
    if (daijian) sheZhi.query.yunXuXGCFDJYFS.push(daijian.value);
    res &&
      res.forEach((el) => {
        // el.xiangMuZDM为null时，当前数据则不赋值，不然有些[]改为null,会报错卡死页面
        if (el.xiangMuZDM != null) {
          sheZhi.query[el.xiangMuDM] = el.xiangMuZDM as any;
        }

        //中药进价限制
        if (el.xiangMuDM === 'zhongYaoYPRKJGXZ') {
          const obj = el.xiangMuZMC ? JSON.parse(el.xiangMuZMC) : '';
          if (obj) {
            sheZhi.query.jinJia = obj.jinJia;
            sheZhi.query.tiXingLX = obj.tiXingLX.tiXingLXDM;
          }
        }
        //第三方设备对接
        if (
          el.xiangMuDM === 'diSanFSBDJ' ||
          el.xiangMuDM === 'zhuiSuMa_zhuYuanLRFW' ||
          el.xiangMuDM === 'zhuiSuMLRJD' ||
          el.xiangMuDM === 'zhuiSuMa_menJiZLRFW' ||
          el.xiangMuDM === 'yaoPinMCJSSPJNR' ||
          el.xiangMuDM === 'jingPeiDJSZSDYNR'
        ) {
          if (typeof el.xiangMuZDM === 'string') {
            sheZhi.query[el.xiangMuDM] = el.xiangMuZDM.split(
              ',',
            ) as DataType[el.xiangMuDM];
          }
        }
        if (el.xiangMuDM === 'ziDongFY') {
          sheZhi.query.ziDongFY = {
            xiangMuZDM: el.xiangMuZDM ? el.xiangMuZDM : '1',
            xiangMuZMC: el.xiangMuZDM === '1' ? el.xiangMuZMC : '',
          } as DataType['ziDongFY'];
        }
        if (el.xiangMuDM === 'shiFouYXDJZJZH') {
          sheZhi.query.shiFouYXDJZJZH = {
            xiangMuZDM: el.xiangMuZDM ? el.xiangMuZDM : '1',
            xiangMuZMC: el.xiangMuZDM === '0' ? el.xiangMuZMC : '是',
          } as DataType['shiFouYXDJZJZH'];
        }
        //代煎自煎转换是否切换药房
        if (el.xiangMuDM === 'daiJianZJSFQHYF') {
          sheZhi.query.daiJianZJSFQHYF = {
            xiangMuZDM: el.xiangMuZDM ? el.xiangMuZDM : '0',
            xiangMuZMC: el.xiangMuZDM === '1' ? el.xiangMuZMC : '',
          } as DataType['daiJianZJSFQHYF'];
        }
        if (el.xiangMuDM === 'fenJianDY_daBaoMa') {
          if (el.xiangMuZDM == '1') {
            sheZhi.query.fenJianDY.push('1');
          }
        }
        if (el.xiangMuDM === 'fenJianDY_fenJianMX') {
          if (el.xiangMuZDM == '1') {
            sheZhi.query.fenJianDY.push('2');
          }
        }
        if (el.xiangMuDM === 'caoYaoFSCFJYFSBZ') {
          if (el.xiangMuZDM) {
            sheZhi.query.caoYaoFSCFJYFSBZ = el.xiangMuZDM.split(',');
          } else {
            sheZhi.query.caoYaoFSCFJYFSBZ = [];
          }
        }
        if (el.xiangMuDM === 'yaoFangXZ') {
          sheZhi.query.yaoFangXZ =
            el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
        }
        if (el.xiangMuDM === 'buKouJKCDGYFS') {
          sheZhi.query.buKouJKCDGYFS =
            el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
        }
        if (el.xiangMuDM === 'yunXuXGCFDJYFS') {
          if (el.xiangMuZDM?.length > 0) {
            sheZhi.query.yunXuXGCFDJYFS = el.xiangMuZDM.split('|');
          }
        }
        if (el.xiangMuDM === 'keYiQHDJYFS') {
          sheZhi.query.keYiQHDJYFS =
            el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
        }
        if (el.xiangMuDM === 'yaoFangYPXZDM') {
          sheZhi.query.yaoFangYPXZDM =
            el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
        }
        if (el.xiangMuDM === 'bingQuFYDLFLSXMRZ') {
          if (el.xiangMuZDM) {
            sheZhi.query.bingQuFYDLFLSXMRZ =
              el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
          } else {
            sheZhi.query.bingQuFYDLFLSXMRZ = [];
          }
        }
        if (el.xiangMuDM === 'duMaJFYRCSHFLDM') {
          sheZhi.query.duMaJFYRCSHFLDM =
            el.xiangMuZDM?.length > 0 ? el.xiangMuZDM.split('|') : [];
        }
        if (el.xiangMuDM === 'tPNShuYeBQDYPC') {
          if (el.xiangMuZDM) {
            sheZhi.query.tPNShuYeBQDYPC = el.xiangMuZDM?.split(',');
          } else {
            sheZhi.query.tPNShuYeBQDYPC = [];
          }
        }
        // 储存上一次的复方文本
        if (el.xiangMuDM === 'yaoPinZDFFBZSFXSZW') {
          lastYaoPinXSWB.value = el.xiangMuZDM;
        }
        if (el.xiangMuDM === 'waiPeiSFZYBML') {
          sheZhi.query.waiPeiSFZYBML = el.xiangMuZMC.length > 1 ? '0' : '1';
          sheZhi.query.waiPeiXMZ = el.xiangMuZMC;
        }
        if (el.xiangMuDM === 'yaoPinKDSYYDZ') {
          const temp = JSON.parse(el.xiangMuZMC);
          yiYuanDZForm.shouJianRXM = temp.shouJianRXM;
          yiYuanDZForm.shouJianRDH = temp.shouJianRDH;
          yiYuanDZForm.shouJianDZQTXX = temp.shouJianDZQTXX;
          yiYuanDZForm.shouJianDZSFDM = temp.shouJianDZSFDM;
          yiYuanDZForm.shouJianDZSFMC = temp.shouJianDZSFMC;
          yiYuanDZForm.shouJianDZSDQDM = temp.shouJianDZSDQDM;
          yiYuanDZForm.shouJianDZSDQMC = temp.shouJianDZSDQMC;
          yiYuanDZForm.shouJianDZXQDM = temp.shouJianDZXQDM;
          yiYuanDZForm.shouJianDZXQMC = temp.shouJianDZXQMC;
          xiangXiDZ = [
            temp.shouJianDZSFMC,
            temp.shouJianDZSDQMC,
            temp.shouJianDZXQMC,
          ];
        }
      });
    // 如果是药房
    if (yiXuanObj.weiZhiDLDM === '6') {
      sheZhi.query.kuCunCXSFCKQYKC = sheZhi.query.kuCunCXSFCKQYKC
        ? sheZhi.query.kuCunCXSFCKQYKC
        : '0';
    } else {
      sheZhi.query.kuCunCXSFCKQYKC = sheZhi.query.kuCunCXSFCKQYKC
        ? sheZhi.query.kuCunCXSFCKQYKC
        : '1';
    }
  } catch (error) {
    console.error(error);
    logger.error(error);
  } finally {
    rightLoading.value = false;
  }
}
/**
 * @description: 获取药库房出入库方式options
 * @return {*}
 */
function getChuRuKOptions() {
  const weiZhiID = yiXuanObj.weiZhiID;
  GetChuRuKFSByFXDM_1({
    chuRuKFXDM: '2',
    shiFouBHQB: false,
    weiZhiID: weiZhiID,
  }).then((res: any) => {
    let optionData: KuCunFSType[] = [];
    res.forEach((item: any) => {
      optionData.push({
        chuRuKFSMC: item.fangShiMC,
        chuRuKFSID: item.fangShiID,
        kuCunZJDM: item.kuCunZJDM,
        danWeiBMDM: item.danWeiBMDM,
        danWeiBMMC: item.danWeiBMMC,
      });
    });
    chuKuFSOptions.value = optionData;
  });
  GetChuRuKFSByFXDM_1({
    chuRuKFXDM: '1',
    shiFouBHQB: false,
    weiZhiID: weiZhiID,
  }).then((res: any) => {
    let optionData: KuCunFSType[] = [];
    res.forEach((item: any) => {
      optionData.push({
        chuRuKFSMC: item.fangShiMC,
        chuRuKFSID: item.fangShiID,
        kuCunZJDM: item.kuCunZJDM,
        danWeiBMDM: item.danWeiBMDM,
        danWeiBMMC: item.danWeiBMMC,
      });
    });
    ruKuFSOptions.value = optionData;
  });
}
//点击药库房节点
function handleTreeCurrent(node: { mxTreeList: any[] }) {
  if (node.mxTreeList && node.mxTreeList.length > 0) return;
  sheZhi.query = initData();
  yiXuanObj = node;
  showForm();
  getChuRuKOptions();
  getPiCIList();
}

//填写限制条件
function handleXianZhiTJ(item: string, index: number) {
  const mingCheng = tiaoJianLXOptions.find((fl) => fl.tiaoJianDM == item);
  if (mingCheng) {
    sheZhi.query.jinJia[index].tiaoJianMC = mingCheng.tiaoJianMC;
  }
}

//保存
async function handleSave() {
  try {
    rightLoading.value = true;
    let params = {};
    if (yiXuanObj.weiZhiID === '0') {
      if (sheZhi.query.waiPeiSFZYBML == 0 && !sheZhi.query.waiPeiXMZ) {
        MdMessage.warning('外配处方取值不能为空！');
        rightLoading.value = false;
        return;
      }
      const jiSuanFSMC =
        sheZhi.query.yinFuKHJJEJSFS == 'xianLeiJZSSWR'
          ? '先累加再四舍五入'
          : '先四舍五入再累加';
      params = {
        weiZhiID: yiXuanObj.weiZhiID,
        weiZhiMC: yiXuanObj.weiZhiMC,
        weiZhiDLDM: yiXuanObj.weiZhiDLDM,
        weiZhiDLMC: yiXuanObj.weiZhiDLMC,
        kuFangSZXXDto: [
          {
            xiangMuDM: 'jingPeiYFGLBZ',
            xiangMuMC: '静配药房过滤标志',
            xiangMuZDM: sheZhi.query.jingPeiYFGLBZ,
            xiangMuZMC: sheZhi.query.jingPeiYFGLBZ
              ? sheZhi.query.jingPeiYFGLBZ == '0'
                ? '否'
                : '是'
              : '',
          },
          {
            xiangMuDM: 'yaoPinZDFFBZSFXSZW',
            xiangMuMC: '复方药品显示文本',
            xiangMuZDM: sheZhi.query.yaoPinZDFFBZSFXSZW,
            xiangMuZMC: lastYaoPinXSWB.value,
          },
          {
            xiangMuDM: 'jingPeiJSYCQQZS',
            xiangMuMC: '静配接收一次请求组数',
            xiangMuZDM: sheZhi.query.jingPeiJSYCQQZS,
            xiangMuZMC: lastYaoPinXSWB.value,
          },
          {
            xiangMuDM: 'menZhenYFSFZSLTHZCF',
            xiangMuMC: '门诊发药默认展示绿通患者处方',
            xiangMuZDM: sheZhi.query.menZhenYFSFZSLTHZCF,
            xiangMuZMC: sheZhi.query.menZhenYFSFZSLTHZCF
              ? sheZhi.query.menZhenYFSFZSLTHZCF == '0'
                ? '否'
                : '是'
              : '',
          },
          {
            xiangMuDM: 'shuangKongYPYZBZ',
            xiangMuMC: '双控药品验证标志',
            xiangMuZDM: sheZhi.query.shuangKongYPYZBZ,
            xiangMuZMC: sheZhi.query.shuangKongYPYZBZ
              ? sheZhi.query.shuangKongYPYZBZ == '0'
                ? '不启用'
                : '启用'
              : '',
          },
          {
            xiangMuDM: 'caoYaoMRJJL',
            xiangMuMC: '草药默认加价率',
            xiangMuZDM: sheZhi.query.caoYaoMRJJL,
            xiangMuZMC: sheZhi.query.caoYaoMRJJL + '%',
          },
          {
            xiangMuDM: 'yinFuKHJJEJSFS',
            xiangMuMC: '应付款合计金额计算方式',
            xiangMuZDM: sheZhi.query.yinFuKHJJEJSFS,
            xiangMuZMC: sheZhi.query.yinFuKHJJEJSFS ? jiSuanFSMC : '',
          },
          {
            xiangMuDM: 'waiPeiSFZYBML',
            xiangMuMC: '外配是否走医保目录',
            xiangMuZDM:
              sheZhi.query.waiPeiSFZYBML == 1
                ? sheZhi.query.waiPeiSFZYBML
                : sheZhi.query.waiPeiXMZ,
            xiangMuZMC:
              sheZhi.query.waiPeiSFZYBML == 1 ? '是' : sheZhi.query.waiPeiXMZ,
          },
          {
            xiangMuDM: 'shiFouAZBLBGL',
            xiangMuMC: '按账簿类别管理',
            xiangMuZDM: sheZhi.query.shiFouAZBLBGL,
            xiangMuZMC: sheZhi.query.shiFouAZBLBGL == 1 ? '是' : '否',
          },
          {
            xiangMuDM: 'beiYaoSQSFPLFSCF',
            xiangMuMC: '备药申请是否批量发送处方',
            xiangMuZDM: sheZhi.query.beiYaoSQSFPLFSCF,
            xiangMuZMC: sheZhi.query.beiYaoSQSFPLFSCF == 1 ? '是' : '否',
          },
          {
            xiangMuDM: 'huShiQLYPSFZYFKC',
            xiangMuMC: '护士请领药品是否占药房库存',
            xiangMuZDM: sheZhi.query.huShiQLYPSFZYFKC,
            xiangMuZMC: sheZhi.query.huShiQLYPSFZYFKC == 1 ? '是' : '否',
          },
          {
            xiangMuDM: 'caoYaoSFXYXZFWTX',
            xiangMuMC: '草药是否需要限制范围提醒',
            xiangMuZDM: sheZhi.query.caoYaoSFXYXZFWTX,
            xiangMuZMC: sheZhi.query.caoYaoSFXYXZFWTX == 1 ? '是' : '否',
          },
          {
            xiangMuDM: 'xiChengYYPBZGGMRKGGZ',
            xiangMuMC: '西成药药品包装规格默认开关规则',
            xiangMuZDM: sheZhi.query.xiChengYYPBZGGMRKGGZ,
            xiangMuZMC:
              sheZhi.query.xiChengYYPBZGGMRKGGZ == 1
                ? '门急住针剂大规格关闭小规格开启，除针剂外大规格开启小规格关闭'
                : '默认:门诊/急诊大规格全开小规格全关，住院大小规格全开',
          },
          {
            xiangMuDM: 'yaoKuFSXSJ',
            xiangMuMC: '药库房上线时间',
            xiangMuZDM: sheZhi.query.yaoKuFSXSJ,
            xiangMuZMC: sheZhi.query.yaoKuFSXSJ,
          },
          {
            xiangMuDM: 'yaoPinKCXSFS',
            xiangMuMC: '开单时库存显示方式',
            xiangMuZDM: sheZhi.query.yaoPinKCXSFS,
            xiangMuZMC:
              sheZhi.query.yaoPinKCXSFS == '0' ? '显示为‘*’' : '显示数值',
          },
          {
            xiangMuDM: 'yiZhuKLSDZMSFJSYP',
            xiangMuMC: '医嘱开立时单字母是否检索药品',
            xiangMuZDM: sheZhi.query.yiZhuKLSDZMSFJSYP,
            xiangMuZMC: sheZhi.query.yiZhuKLSDZMSFJSYP == '1' ? '是' : '否',
          },
          {
            xiangMuDM: 'yaoPinMCJSSPJNR',
            xiangMuMC: '药品名称检索时拼接内容',
            xiangMuZDM: sheZhi.query.yaoPinMCJSSPJNR.join(','),
            xiangMuZMC: sheZhi.query.yaoPinMCJSSPJNR
              .map((value) => getLabelByValue(yaoPinMCJSSPJNRList, value))
              .join(','),
          },
          {
            xiangMuDM: 'caoYaoKFJSSFXYGJLXGL',
            xiangMuMC: '草药开方检索是否需要根据类型过滤',
            xiangMuZDM: sheZhi.query.caoYaoKFJSSFXYGJLXGL,
            xiangMuZMC: sheZhi.query.caoYaoKFJSSFXYGJLXGL == '1' ? '是' : '否',
          },
          {
            xiangMuDM: 'chuYuanDYKLFWKZ',
            xiangMuMC: '出院带药开立范围控制',
            xiangMuZDM: sheZhi.query.chuYuanDYKLFWKZ,
            xiangMuZMC:
              sheZhi.query.chuYuanDYKLFWKZ == '1' ? '按规格设置' : '按规则',
          },
          {
            xiangMuDM: 'xiangTongYPYZXSSX',
            xiangMuMC: '相同药品医嘱显示顺序',
            xiangMuZDM: sheZhi.query.xiangTongYPYZXSSX,
            xiangMuZMC:
              sheZhi.query.xiangTongYPYZXSSX == null ||
              sheZhi.query.xiangTongYPYZXSSX === ''
                ? ''
                : sheZhi.query.xiangTongYPYZXSSX == '1'
                  ? '先大规格再小规格'
                  : '先小规格再大规格',
          },
          {
            xiangMuDM: 'yaoPinKDSYYDZ',
            xiangMuMC: '药品快递送医院地址',
            xiangMuZDM: '',
            xiangMuZMC: JSON.stringify(yiYuanDZForm),
          },
          {
            xiangMuDM: 'wuZhuiSMYPSCSFGDCL',
            xiangMuMC: '无追溯码药品上传是否固定拆零',
            xiangMuZDM: sheZhi.query.wuZhuiSMYPSCSFGDCL,
            xiangMuZMC:
              sheZhi.query.wuZhuiSMYPSCSFGDCL === '1'
                ? '是'
                : sheZhi.query.wuZhuiSMYPSCSFGDCL === '0'
                  ? '否'
                  : '',
          },
           {
            xiangMuDM: 'xiChengYYZJSDJXSWS',
            xiangMuMC: '西成药医嘱检索单价显示位数',
            xiangMuZDM: sheZhi.query.xiChengYYZJSDJXSWS,
            xiangMuZMC: sheZhi.query.xiChengYYZJSDJXSWS + '%',
          },
          {
            xiangMuDM: 'yaoPinBZGGSFQYYJSYBZ',
            xiangMuMC: '无追溯码药品上传是否固定拆零',
            xiangMuZDM: sheZhi.query.yaoPinBZGGSFQYYJSYBZ,
            xiangMuZMC:
              sheZhi.query.yaoPinBZGGSFQYYJSYBZ === '1'
                ? '是'
                : sheZhi.query.yaoPinBZGGSFQYYJSYBZ === '0'
                  ? '否'
                  : '',
          },
        ],
      };
    } else {
      if (
        sheZhi.query.ziDongFY.xiangMuZDM == '1' &&
        !Number(sheZhi.query.ziDongFY.xiangMuZMC) &&
        yiXuanObj.weiZhiDLDM === '6'
      ) {
        MdMessage.warning('自动发药时间不能为空！');
        rightLoading.value = false;
        return;
      }
      let arr: XiangMuSaveItem[] = [];
      sheZhi.xiangMuDMList.forEach((el) => {
        let mingCheng: number | string = '';
        if (
          el.biaoZhunDM === 'yaoPinJYTS' ||
          el.biaoZhunDM === 'yaoPinSXTS' ||
          el.biaoZhunDM === 'bingQuFYZYCFMRDYSL' ||
          el.biaoZhunDM === 'yinPianYPRKMRYPXQ' ||
          el.biaoZhunDM === 'keLiJYPRKMRYPXQ'
        ) {
          mingCheng = sheZhi.query[el.biaoZhunDM];
        } else if (el.biaoZhunDM === 'diSanFSBDJ') {
          sheZhi.query[el.biaoZhunDM]?.forEach((el) => {
            const label = diSanFSBDJList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'yaoPinMCJSSPJNR') {
          sheZhi.query[el.biaoZhunDM]?.forEach((el) => {
            const label = diSanFSBDJList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'yaoPinRKGNZDRKBQ') {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '显示' : '不显示';
        } else if (el.biaoZhunDM === 'yaoKuCGDJLDPT') {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '对接' : '不对接';
        } else if (el.biaoZhunDM === 'yaoPinRKZDDRCGDSL') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '2' ? '消耗量' : '申请数量';
        } else if (el.biaoZhunDM === 'zhongYaoYPRKJGXZ') {
          //限制条件
          mingCheng =
            sheZhi.query[el.biaoZhunDM] === '1'
              ? ''
              : JSON.stringify({
                  jinJia: sheZhi.query.jinJia,
                  tiXingLX: {
                    tiXingLXDM: sheZhi.query.tiXingLX,
                    tiaoJianMC: sheZhi.query.tiXingLX == '1' ? '提醒' : '中断',
                  },
                });
        } else if (el.biaoZhunDM === 'ziDongFY') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM].xiangMuZDM == '1'
              ? Number(sheZhi.query[el.biaoZhunDM].xiangMuZMC)
              : '';
        } else if (el.biaoZhunDM === 'shiFouYXDJZJZH') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM].xiangMuZDM == '1'
              ? sheZhi.query[el.biaoZhunDM].xiangMuZMC
              : '';
        } else if (el.biaoZhunDM === 'daiJianZJSFQHYF') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM].xiangMuZDM == '1'
              ? sheZhi.query[el.biaoZhunDM].xiangMuZMC
              : '';
        } else if (el.biaoZhunDM === 'yaoPinRKMRRKFS') {
          const matchedItem = ruKuFSOptions.value.find(
            (item) => item.chuRuKFSID === sheZhi.query[el.biaoZhunDM],
          );
          mingCheng = matchedItem ? matchedItem.chuRuKFSMC : '';
        } else if (el.biaoZhunDM === 'yaoPinRKMRCKFS') {
          const matchedItem = chuKuFSOptions.value.find(
            (item) => item.chuRuKFSID === sheZhi.query[el.biaoZhunDM],
          );
          mingCheng = matchedItem ? matchedItem.chuRuKFSMC : '';
        } else if (
          el.biaoZhunDM === 'zhuiSuMa_shiFouQYDSF' ||
          el.biaoZhunDM == 'shiFouKQQDJMS'
        ) {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '启用' : '不启用';
        } else if (el.biaoZhunDM === 'zhuiSuMLRJD') {
          sheZhi.query[el.biaoZhunDM]?.forEach((el) => {
            const label = zhuiSuMLRJDList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'zhuiSuMa_menJiZLRFW') {
          sheZhi.query[el.biaoZhunDM]?.forEach((el) => {
            const label = menZhenAndZYList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'zhuiSuMa_zhuYuanLRFW') {
          sheZhi.query[el.biaoZhunDM]?.forEach((el) => {
            const label = zhuYuanList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'yaoFangXZ') {
          sheZhi.query['yaoFangXZ'].forEach((el) => {
            const label = yaoPinXZList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'buKouJKCDGYFS') {
          sheZhi.query['buKouJKCDGYFS'].forEach((el) => {
            const label = caoYaoGYFSOptions.value.find(
              (fl) => fl.value == el,
            )?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'yunXuXGCFDJYFS') {
          sheZhi.query['yunXuXGCFDJYFS'].forEach((el) => {
            const label = caoYaoGYFSOptions.value.find(
              (fl) => fl.value == el,
            )?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'keYiQHDJYFS') {
          sheZhi.query['keYiQHDJYFS'].forEach((el) => {
            const label = caoYaoGYFSOptions.value.find(
              (fl) => fl.value == el,
            )?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'yaoFangYPXZDM') {
          sheZhi.query['yaoFangYPXZDM'].forEach((el) => {
            const label = yaoFangYPXZDMList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'bingQuFYDLFLSXMRZ') {
          sheZhi.query['bingQuFYDLFLSXMRZ'].forEach((el) => {
            const label = duLiFLOptions.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'duMaJFYRCSHFLDM') {
          sheZhi.query['duMaJFYRCSHFLDM'].forEach((el) => {
            const label = duLiFList.find((fl) => fl.value == el)?.label;
            if (label) mingCheng += label + '|';
          });
        } else if (el.biaoZhunDM === 'tPNShuYeBQDYPC') {
          sheZhi.query['tPNShuYeBQDYPC'].forEach((el) => {
            const label = piCiOption.value.find(
              (fl) => fl.piCiID == el,
            )?.piCiMC;
            if (label) mingCheng += label + ',';
          });
        } else if (el.biaoZhunDM === 'yaoPinKCXSFS') {
          mingCheng =
            sheZhi.query.yaoPinKCXSFS == '0' ? '显示为‘*’' : '显示数值';
        } else if (
          el.biaoZhunDM === 'buJinBYJ' ||
          el.biaoZhunDM === 'caoYaoTFSFXYYFTY' ||
          el.biaoZhunDM === 'jingPeiSFYXQXJS' ||
          el.biaoZhunDM === 'caoYaoZJFSCFBZ' ||
          el.biaoZhunDM === 'shiFouFSKLJCF' ||
          el.biaoZhunDM === 'duMaJFYSFECSH' ||
          el.biaoZhunDM === 'caoYaoFDJFYSFYXXGCF' ||
          el.biaoZhunDM === 'daYinSFABFWZPX' ||
          el.biaoZhunDM === 'faYaoDKZDDY' ||
          el.biaoZhunDM === 'tiaoJiaSFAGHDW' ||
          el.biaoZhunDM === 'tuiYaoSFDY' ||
          el.biaoZhunDM === 'menZhenFYJSSFTTYTX' ||
          el.biaoZhunDM === 'kuCunCXSFCKQYKC' ||
          el.biaoZhunDM === 'caoYaoQYDSFDYQBGYFS' ||
          el.biaoZhunDM === 'chaoKuCunSFYXQL' ||
          el.biaoZhunDM === 'weiShouLiSFSDKC' ||
          el.biaoZhunDM === 'bingQuDFYSFMRQXYP' ||
          el.biaoZhunDM === 'jingPeiYJSDYSFLRCZR' ||
          el.biaoZhunDM === 'jingPeiPSSFMRXZJJD' ||
          el.biaoZhunDM === 'jingPeiFJDBQDPCJYQK' ||
          el.biaoZhunDM === 'jingPeiDCCSFXYPLCC' ||
          el.biaoZhunDM === 'menZhenFYCKMRGXZT' ||
          el.biaoZhunDM === 'menZhenDFYZSTYJL' ||
          el.biaoZhunDM === 'bingQuFYBTFYLXTYSFHB' ||
          el.biaoZhunDM === 'anCaiGDRKSFXYXZPHXQ' ||
          el.biaoZhunDM === 'bingQuFYADBQFY' ||
          el.biaoZhunDM === 'yaoPinCKWJZSFDYQLD' ||
          el.biaoZhunDM === 'yaoKuRKJZSFGXJJLSJ' ||
          el.biaoZhunDM === 'bingQuFYSSFLRZSM' ||
          el.biaoZhunDM === 'chuYuanDYADRFY' ||
          el.biaoZhunDM === 'jingPeiPSYZSFZZDJSLC' ||
          el.biaoZhunDM === 'bingQuFYSFQYYFFYBH' ||
          el.biaoZhunDM === 'ruKuDZPHXQSFZDDC' ||
          el.biaoZhunDM === 'yaoPinQLYXCFQL' ||
          el.biaoZhunDM === 'menZhenFYZSMSFQK' ||
          el.biaoZhunDM === 'guoQiFYZSMSFQK' ||
          el.biaoZhunDM === 'menZhenTYZSMSFQK' ||
          el.biaoZhunDM === 'jingPeiYJSSFZSPSYZ' ||
          el.biaoZhunDM === 'shouLiQLKCBZSFSCDSLD' ||
          el.biaoZhunDM === 'chuKuSFAZL' ||
          el.biaoZhunDM === 'CaoYaoMZFYHZZSDJBS' ||
          el.biaoZhunDM === 'bingQuFYCYWFYSFDY'
        ) {
          mingCheng = sheZhi.query[el.biaoZhunDM] == 1 ? '是' : '否';
        } else if (el.biaoZhunDM === 'panCunMS') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == 2 ? '按批次盘存' : '总量模式';
        } else if (el.biaoZhunDM === 'caiGouJHCGSLJSGZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == 1 ? '等于消耗量' : '通用规则';
        } else if (el.biaoZhunDM === 'caiGouJHZDCGSLZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == 2
              ? '空白'
              : sheZhi.query[el.biaoZhunDM] == 3
                ? '等于消耗量'
                : '根据消耗量和包装系数计算';
        } else if (el.biaoZhunDM === 'shiFouZSZDTJD') {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '展示' : '不展示';
        } else if (el.biaoZhunDM === 'yaoPinCKDYSX') {
          if (sheZhi.query[el.biaoZhunDM] == '2') {
            mingCheng = '按单据顺序';
          } else {
            mingCheng = '按摆放位置';
          }
        } else if (el.biaoZhunDM === 'bingQuFYHZDDYPX') {
          if (sheZhi.query[el.biaoZhunDM] == '1') {
            mingCheng = '按摆放位置顺序';
          } else if (sheZhi.query[el.biaoZhunDM] == '2') {
            mingCheng = '按药品名称';
          } else {
            mingCheng = '未知排序方式';
          }
        } else if (el.biaoZhunDM === 'menZhenFYPXGZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '按收费时间正序'
              : '按取药序号升序';
        } else if (el.biaoZhunDM === 'fenJianDY_daBaoMa') {
          mingCheng = sheZhi.query.fenJianDY.includes('1') ? '选中' : '未选中';
        } else if (el.biaoZhunDM === 'fenJianDY_fenJianMX') {
          mingCheng = sheZhi.query.fenJianDY.includes('2') ? '选中' : '未选中';
        } else if (
          el.biaoZhunDM === 'daYinYCSJ' ||
          el.biaoZhunDM === 'jingPeiTYDQRMRCXSJFW'
        ) {
          mingCheng = sheZhi.query[el.biaoZhunDM];
        } else if (el.biaoZhunDM === 'jingPeiJSDYGZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '按病区批次药品床号'
              : sheZhi.query[el.biaoZhunDM] == '2'?
              '静配接收打印规则':
              '按病区床号批次';
        } else if (el.biaoZhunDM === 'bingQuFYSQSJFWMRZ') {
          mingCheng = '天';
        } else if (el.biaoZhunDM === 'zhuiSuMa_zhuYuanLRFS') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1' ? '以药品为单位' : '以人为单位';
        } else if (el.biaoZhunDM === 'kuCunCXMRCXFW') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '库存不为0'
              : sheZhi.query[el.biaoZhunDM] == '2'
                ? '库存为0'
                : '所有';
        } else if (el.biaoZhunDM === 'caoYaoCFSXHSCGZ') {
          mingCheng = caoYaoSCGZOptions.find(
            (fl) => fl.value == sheZhi.query[el.biaoZhunDM],
          )?.label;
        } else if (el.biaoZhunDM === 'caoYaoFSCFFS') {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '标准' : '非标';
        } else if (el.biaoZhunDM === 'caoYaoFSCFJYFSBZ') {
          for (let item of sheZhi.query[el.biaoZhunDM]) {
            mingCheng += getLabelByValue(jianYaoFSOptions.value, item) + ',';
          }
          // mingCheng = jianYaoFSOptions.value.find(
          //   (fl) => fl.geiYaoFSID == sheZhi.query[el.biaoZhunDM],
          // )?.geiYaoFSMC;
        } else if (el.biaoZhunDM === 'menZhenBYCYCFDYGZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '打印1份'
              : '代煎打印2份，自煎打印1份';
        } else if (el.biaoZhunDM === 'caoYaoCFMXDYSX') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '处方明细顺序'
              : '库位字典顺序';
        } else if (el.biaoZhunDM === 'yaoPinYHLYMRZ') {
          mingCheng = yaoPinYHLYMRZOptions.find(
            (item) => item.value === sheZhi.query[el.biaoZhunDM],
          )?.label;
        } else if (el.biaoZhunDM === 'menZhenFYHZSFXSLSYY') {
          switch (sheZhi.query[el.biaoZhunDM]) {
            case '1':
              mingCheng = '显示不展开';
              break;
            case '2':
              mingCheng = '显示并展开';
              break;
            case '3':
              mingCheng = '不显示';
              break;
          }
        } else if (el.biaoZhunDM === 'bingQuFYPFKLTMRDYSL') {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '贴数*2' : '';
        } else if (el.biaoZhunDM === 'qingLingSLCDGZ') {
          const matchedItem = qingLingSLCDGZOptions.find(
            (item) => item.value === sheZhi.query[el.biaoZhunDM],
          );
          mingCheng = matchedItem ? matchedItem.label : '';
        } else if (el.biaoZhunDM === 'jingPeiYPHZDYGZ') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '摆放位置'
              : '先大输液，再摆放位置';
        } else if (el.biaoZhunDM === 'jingPeiDJSZSDYNR') {
          for (let item of sheZhi.query[el.biaoZhunDM]) {
            mingCheng += getLabelByValue(daiJieShouDYList, item) + ',';
          }
        } else if (el.biaoZhunDM === 'caoYaoTSFZDYYF') {
          mingCheng =
            sheZhi.query[el.biaoZhunDM] == '1'
              ? '打印1份'
              : '自煎打印3份，代煎打印5份';
        } else {
          mingCheng = sheZhi.query[el.biaoZhunDM] == '1' ? '显示' : '不显示';
        }
        //药库
        if (
          yiXuanObj.weiZhiDLDM === '7' &&
          (el.biaoZhunDM == 'yaoKuCGDJLDPT' ||
            el.biaoZhunDM == 'yaoPinRKGNZDRKBQ' ||
            el.biaoZhunDM == 'yaoPinRKDDMJYPDLZD' ||
            el.biaoZhunDM == 'yaoPinCKDDMJYPDLZD' ||
            el.biaoZhunDM == 'zhongYaoYPRKJGXZ' ||
            el.biaoZhunDM == 'yaoPinJYTS' ||
            el.biaoZhunDM == 'yaoPinSXTS' ||
            el.biaoZhunDM == 'yaoPinRKXZYPHZSLSJJ' ||
            el.biaoZhunDM == 'yaoPinRKMRCKFS' ||
            el.biaoZhunDM == 'yaoPinRKMRRKFS' ||
            el.biaoZhunDM == 'daYinSFABFWZPX' ||
            el.biaoZhunDM == 'shiFouZSZDTJD' ||
            el.biaoZhunDM == 'tiaoJiaSFAGHDW' ||
            el.biaoZhunDM == 'lingShouJXSDWS' ||
            el.biaoZhunDM == 'jinJiaXSDWS' ||
            el.biaoZhunDM == 'jinJiaJEXSDWS' ||
            el.biaoZhunDM == 'lingShouJEXSDWS' ||
            el.biaoZhunDM === 'kuCunCXSFCKQYKC' ||
            el.biaoZhunDM === 'chaoKuCunSFYXQL' ||
            el.biaoZhunDM === 'weiShouLiSFSDKC' ||
            el.biaoZhunDM === 'ShiFouYXXGGRK' ||
            el.biaoZhunDM === 'yaoPinRKZDDRCGDSL' ||
            el.biaoZhunDM === 'anCaiGDRKSFXYXZPHXQ' ||
            el.biaoZhunDM === 'kuCunCXMRCXFW' ||
            el.biaoZhunDM === 'bingQuFYADBQFY' ||
            el.biaoZhunDM === 'yaoPinCKWJZSFDYQLD' ||
            el.biaoZhunDM === 'yaoKuRKJZSFGXJJLSJ' ||
            el.biaoZhunDM === 'chuYuanDYADRFY' ||
            el.biaoZhunDM === 'caiGouJHCGSLJSGZ' ||
            el.biaoZhunDM === 'qingLingSLCDGZ' ||
            el.biaoZhunDM === 'caiGouJHZDCGSLZ' ||
            el.biaoZhunDM === 'shouLiQLKCBZSFSCDSLD' ||
            el.biaoZhunDM === 'chuKuSFAZL' ||
            el.biaoZhunDM === 'keLiJYPRKMRYPXQ' ||
            el.biaoZhunDM === 'yinPianYPRKMRYPXQ' ||
            el.biaoZhunDM === 'panCunMS')
        ) {
          arr.push({
            xiangMuDM: el.biaoZhunDM,
            xiangMuMC: el.biaoZhunMC,
            xiangMuZDM: sheZhi.query[el.biaoZhunDM],
            xiangMuZMC: (sheZhi.query[el.biaoZhunDM]
              ? mingCheng
              : '') as string,
          });
        }
        //药房
        if (
          yiXuanObj.weiZhiDLDM === '6' &&
          (el.biaoZhunDM == 'diSanFSBDJ' ||
            el.biaoZhunDM == 'yaoPinJYTS' ||
            el.biaoZhunDM == 'yaoPinSXTS' ||
            el.biaoZhunDM == 'yaoPinRKDDMJYPDLZD' ||
            el.biaoZhunDM == 'yaoPinCKDDMJYPDLZD' ||
            el.biaoZhunDM == 'ziDongFY' ||
            el.biaoZhunDM == 'chuLiHZDFY' ||
            el.biaoZhunDM == 'yaoPinRKMRCKFS' ||
            el.biaoZhunDM == 'yaoPinRKMRRKFS' ||
            el.biaoZhunDM == 'zhuiSuMa_shiFouQYDSF' ||
            el.biaoZhunDM == 'zhuiSuMLRJD' ||
            el.biaoZhunDM == 'zhuiSuMa_menJiZLRFW' ||
            el.biaoZhunDM == 'zhuiSuMa_zhuYuanLRFW' ||
            el.biaoZhunDM == 'shiFouYXDJZJZH' ||
            el.biaoZhunDM == 'daiJianZJSFQHYF' ||
            el.biaoZhunDM == 'yaoFangXZ' ||
            el.biaoZhunDM == 'yaoFangYPXZDM' ||
            el.biaoZhunDM == 'yaoPinKCXSFS' ||
            el.biaoZhunDM == 'buJinBYJ' ||
            el.biaoZhunDM == 'caoYaoTFSFXYYFTY' ||
            el.biaoZhunDM == 'jingPeiSFYXQXJS' ||
            el.biaoZhunDM == 'caoYaoZJFSCFBZ' ||
            el.biaoZhunDM == 'shiFouFSKLJCF' ||
            el.biaoZhunDM == 'shiFouKQQDJMS' ||
            el.biaoZhunDM == 'shiFouZSZDTJD' ||
            el.biaoZhunDM == 'bingQuFYDLFLSXMRZ' ||
            el.biaoZhunDM == 'bingQuFYHZDDYPX' ||
            el.biaoZhunDM == 'duMaJFYSFECSH' ||
            el.biaoZhunDM == 'menZhenFYPXGZ' ||
            el.biaoZhunDM == 'caoYaoFDJFYSFYXXGCF' ||
            el.biaoZhunDM == 'faYaoDKZDDY' ||
            el.biaoZhunDM == 'fenJianDY_daBaoMa' ||
            el.biaoZhunDM == 'fenJianDY_fenJianMX' ||
            el.biaoZhunDM == 'tPNShuYeBQDYPC' ||
            el.biaoZhunDM == 'duMaJFYRCSHFLDM' ||
            el.biaoZhunDM === 'tuiYaoSFDY' ||
            el.biaoZhunDM === 'daYinYCSJ' ||
            el.biaoZhunDM === 'caoYaoQYDSFDYQBGYFS' ||
            el.biaoZhunDM === 'menZhenFYJSSFTTYTX' ||
            el.biaoZhunDM === 'kuCunCXSFCKQYKC' ||
            el.biaoZhunDM == 'lingShouJXSDWS' ||
            el.biaoZhunDM == 'jinJiaXSDWS' ||
            el.biaoZhunDM == 'jinJiaJEXSDWS' ||
            el.biaoZhunDM == 'lingShouJEXSDWS' ||
            el.biaoZhunDM == 'bingQuDFYSFMRQXYP' ||
            el.biaoZhunDM === 'jingPeiYJSDYSFLRCZR' ||
            el.biaoZhunDM === 'jingPeiPSSFMRXZJJD' ||
            el.biaoZhunDM === 'jingPeiFJDBQDPCJYQK' ||
            el.biaoZhunDM === 'jingPeiDCCSFXYPLCC' ||
            el.biaoZhunDM === 'jingPeiJSDYGZ' ||
            el.biaoZhunDM === 'menZhenFYCKMRGXZT' ||
            el.biaoZhunDM === 'menZhenDFYZSTYJL' ||
            el.biaoZhunDM === 'ShiFouYXXGGRK' ||
            el.biaoZhunDM === 'bingQuFYSQSJFWMRZ' ||
            el.biaoZhunDM === 'bingQuFYBTFYLXTYSFHB' ||
            el.biaoZhunDM === 'zhuiSuMa_zhuYuanLRFS' ||
            el.biaoZhunDM === 'kuCunCXMRCXFW' ||
            el.biaoZhunDM === 'bingQuFYADBQFY' ||
            el.biaoZhunDM === 'caoYaoCFSXHSCGZ' ||
            el.biaoZhunDM === 'bingQuFYSSFLRZSM' ||
            el.biaoZhunDM === 'caoYaoFSCFFS' ||
            el.biaoZhunDM === 'caoYaoFSCFJYFSBZ' ||
            el.biaoZhunDM === 'menZhenBYCYCFDYGZ' ||
            el.biaoZhunDM === 'chuYuanDYADRFY' ||
            el.biaoZhunDM === 'jingPeiPSYZSFZZDJSLC' ||
            el.biaoZhunDM === 'bingQuFYSFQYYFFYBH' ||
            el.biaoZhunDM === 'ruKuDZPHXQSFZDDC' ||
            el.biaoZhunDM === 'yaoPinCKDYSX' ||
            el.biaoZhunDM === 'caoYaoCFMXDYSX' ||
            el.biaoZhunDM === 'menZhenFYHZSFXSLSYY' ||
            el.biaoZhunDM === 'bingQuFYPFKLTMRDYSL' ||
            el.biaoZhunDM === 'bingQuFYZYCFMRDYSL' ||
            el.biaoZhunDM === 'yaoPinQLYXCFQL' ||
            el.biaoZhunDM === 'menZhenFYZSMSFQK' ||
            el.biaoZhunDM === 'guoQiFYZSMSFQK' ||
            el.biaoZhunDM === 'menZhenTYZSMSFQK' ||
            el.biaoZhunDM === 'jingPeiYJSSFZSPSYZ' ||
            el.biaoZhunDM === 'jingPeiYPHZDYGZ' ||
            el.biaoZhunDM === 'jingPeiDJSZSDYNR' ||
            el.biaoZhunDM === 'buKouJKCDGYFS' ||
            el.biaoZhunDM === 'keYiQHDJYFS' ||
            el.biaoZhunDM === 'yunXuXGCFDJYFS' ||
            el.biaoZhunDM === 'panCunMS' ||
            el.biaoZhunDM === 'jingPeiTYDQRMRCXSJFW' ||
            el.biaoZhunDM === 'caoYaoTSFZDYYF' ||
            el.biaoZhunDM === 'yaoPinYHLYMRZ' ||
            el.biaoZhunDM === 'CaoYaoMZFYHZZSDJBS' ||
            el.biaoZhunDM === 'bingQuFYCYWFYSFDY')
        ) {
          const xiangMuZMC =
            sheZhi.query[el.biaoZhunDM] ||
            sheZhi.query['diSanFSBDJ']?.join(',')?.length > 0
              ? mingCheng
              : '';
          const yaoFangXZDM =
            el.biaoZhunDM === 'yaoFangXZ'
              ? sheZhi.query['yaoFangXZ'].join('|')
              : sheZhi.query[el.biaoZhunDM];
          const yaoFangYPXZDMDM =
            el.biaoZhunDM === 'yaoFangYPXZDM'
              ? sheZhi.query['yaoFangYPXZDM'].join('|')
              : sheZhi.query[el.biaoZhunDM];
          const duLiFLDMDM =
            el.biaoZhunDM === 'bingQuFYDLFLSXMRZ'
              ? sheZhi.query['bingQuFYDLFLSXMRZ'].join('|')
              : sheZhi.query[el.biaoZhunDM];
          const duLiFLDMSH =
            el.biaoZhunDM === 'duMaJFYRCSHFLDM'
              ? sheZhi.query['duMaJFYRCSHFLDM'].join('|')
              : sheZhi.query[el.biaoZhunDM];
          const piCiXMDM =
            el.biaoZhunDM == 'tPNShuYeBQDYPC'
              ? sheZhi.query['tPNShuYeBQDYPC'].join(',')
              : sheZhi.query[el.biaoZhunDM];
          const xiangMuZDM =
            el.biaoZhunDM === 'diSanFSBDJ' ||
            el.biaoZhunDM == 'zhuiSuMLRJD' ||
            el.biaoZhunDM == 'zhuiSuMa_menJiZLRFW' ||
            el.biaoZhunDM == 'zhuiSuMa_zhuYuanLRFW' ||
            el.biaoZhunDM == 'jingPeiDJSZSDYNR'
              ? sheZhi.query[el.biaoZhunDM].join(',')
              : el.biaoZhunDM === 'bingQuFYDLFLSXMRZ'
                ? duLiFLDMDM
                : el.biaoZhunDM === 'yaoFangXZ'
                  ? yaoFangXZDM
                  : el.biaoZhunDM == 'tPNShuYeBQDYPC'
                    ? piCiXMDM
                    : el.biaoZhunDM === 'duMaJFYRCSHFLDM'
                      ? duLiFLDMSH
                      : yaoFangYPXZDMDM;

          if (el.biaoZhunDM == 'shiFouYXDJZJZH') {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: (sheZhi.query[el.biaoZhunDM].xiangMuZDM
                ? sheZhi.query[el.biaoZhunDM].xiangMuZDM
                : '1') as string,
              xiangMuZMC: (sheZhi.query[el.biaoZhunDM].xiangMuZDM == '0'
                ? '否'
                : '是') as string,
            });
          } else if (el.biaoZhunDM == 'buKouJKCDGYFS') {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: sheZhi.query['buKouJKCDGYFS'].join('|'),
              xiangMuZMC: xiangMuZMC as string,
            });
          } else if (el.biaoZhunDM == 'yunXuXGCFDJYFS') {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: sheZhi.query['yunXuXGCFDJYFS'].join('|'),
              xiangMuZMC: xiangMuZMC as string,
            });
          } else if (el.biaoZhunDM == 'keYiQHDJYFS') {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: sheZhi.query['keYiQHDJYFS'].join('|'),
              xiangMuZMC: xiangMuZMC as string,
            });
          } else if (
            el.biaoZhunDM === 'ziDongFY' ||
            el.biaoZhunDM == 'daiJianZJSFQHYF'
          ) {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: (el.biaoZhunDM === 'ziDongFY' ||
              el.biaoZhunDM == 'daiJianZJSFQHYF'
                ? sheZhi.query[el.biaoZhunDM].xiangMuZDM
                : xiangMuZDM) as string,
              xiangMuZMC: (el.biaoZhunDM === 'ziDongFY'
                ? mingCheng
                : xiangMuZMC) as string,
            });
          } else if (
            el.biaoZhunDM === 'fenJianDY_daBaoMa' ||
            el.biaoZhunDM == 'fenJianDY_fenJianMX'
          ) {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM:
                el.biaoZhunDM === 'fenJianDY_daBaoMa'
                  ? sheZhi.query.fenJianDY.includes('1')
                    ? '1'
                    : '0'
                  : sheZhi.query.fenJianDY.includes('2')
                    ? '1'
                    : '0',
              xiangMuZMC: mingCheng as string,
            });
          } else if (el.biaoZhunDM == 'caoYaoFSCFJYFSBZ') {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: (sheZhi.query[el.biaoZhunDM]
                ? sheZhi.query[el.biaoZhunDM].join(',')
                : '') as string,
              xiangMuZMC: mingCheng as string,
            });
          } else {
            arr.push({
              xiangMuDM: el.biaoZhunDM,
              xiangMuMC: el.biaoZhunMC,
              xiangMuZDM: xiangMuZDM as string,
              xiangMuZMC: xiangMuZMC as string,
            });
          }
        }
      });
      params = {
        weiZhiID: yiXuanObj.weiZhiID,
        weiZhiMC: yiXuanObj.weiZhiMC,
        weiZhiDLDM: yiXuanObj.weiZhiDLDM,
        weiZhiDLMC: yiXuanObj.weiZhiDLMC,
        kuFangSZXXDto: arr,
      };
    }
    await SaveKuFangSZ(params);
    MdMessage.success('保存成功！');
    showForm();
  } catch (error) {
    console.error(error);
    logger.error(error);
  } finally {
    leftloading.value = false;
  }
}
//充值
function handleReset() {
  MdMessageBox.confirm('确认要重置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    sheZhi.query = initData();
    handleSave();
  });
}
//门诊住院全部和大规格互斥
function handlerChangeMZAndZY(val: string, type: string) {
  //默认选择了全部
  sheZhi.query[type] = [val[val.length - 1]];
}
// TODO
function handleRuKuChange(val: any, key: any) {}
async function getZiDianSJ() {
  let daiMaList = ['YP0006'];
  let yangHuLYList = ['YP0016'];
  getYaoPinShuJuYZYList(yangHuLYList).then((data: any) => {
    yaoPinYHLYMRZOptions = data[0].zhiYuList.map((item: any) => ({
      label: item.biaoZhunMC,
      value: item.biaoZhunDM,
    }));
  });
  getYaoPinShuJuYZYList(daiMaList).then((res: any) => {
    duLiFLOptions = res[0].zhiYuList.map((item: any) => ({
      label: item.biaoZhunMC,
      value: item.biaoZhunDM,
    }));
    duLiFList = res[0].zhiYuList.map((item: any) => ({
      label: item.biaoZhunMC,
      value: item.biaoZhunDM,
    }));
    duLiFList.splice(0, 1);
  });
  const res = await getYiZhuSJYZYListByLBID('YZ0005');
  yaoFangYPXZDMList = res.zhiYuList.map((item: any) => {
    return {
      value: item.biaoZhunDM,
      label: item.biaoZhunMC,
    };
  });
  await getCaoYaoTieGYFSList();
}
function chaKanSM(title: any, biaoZhunDM: any) {
  let peiZhiXiangList = [];
  switch (biaoZhunDM) {
    case 'zhuiSuMa_menJiZLRFW':
      peiZhiXiangList = [
        { biaoZhunDM: 'zhuiSuMa_menJiZLRFW', biaoZhunMC: '门诊录入范围' },
        { biaoZhunDM: 'zhuiSuMa_zhuYuanLRFW', biaoZhunMC: '住院录入范围' },
      ];
      break;
    default:
      peiZhiXiangList = [{ biaoZhunDM, biaoZhunMC: title }];
      break;
  }
  Drawer.value.showModel({
    gongNengMC: '药库房设置',
    biaoZhunDM,
    title,
    currentPeiZhi: yiXuanObj,
    peiZhiXiangList,
  });
}
getZiDianSJ();
getBaiFangWZ();
GetShengShiQList();
getYaoFangList();
getChuRuKOptions();
</script>
<style lang="scss" scoped>
.#{$md-prefix}-yaoPinBFWZ {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;

  &-left {
    padding: 8px 0;
    width: 224px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;

    &-jianSuo {
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
    }

    &-tree {
      min-height: 0;
      flex: 1;

      .treeItem {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 8px;

        .treeItem-icon {
          display: none;
        }

        &:hover {
          .treeItem-icon {
            display: block;
          }
        }
      }
    }
  }

  &-right {
    flex: 1;
    min-width: 0;
    border: 8px solid #f0f2f5;
    border-bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &-main {
      .daijianzh {
        width: 35%;
        display: flex;
        margin-right: 8px;
        align-items: center;
      }
      .zidongfy {
        width: 50%;
        display: flex;
        margin-right: 8px;
        align-items: center;
      }
      .redText {
        display: inline-block;
        color: #f12933 !important;
        /* width: 80%; */
      }

      .df {
        display: flex;
        align-items: center;
      }

      .span {
        flex-shrink: 0;
        margin-right: var(--md-spacing-3);
      }

      .mar-l-16 {
        margin-left: var(--md-spacing-5);
      }

      .mar-b-8 {
        margin-bottom: var(--md-spacing-3);
      }

      flex: 1;
      min-height: 0;
      padding: 8px 12px;
      box-sizing: border-box;
      overflow: auto;

      .col {
        display: flex;
        align-items: center;
        margin-bottom: var(--md-spacing-3);
        padding: var(--md-spacing-3);
        position: relative;
        padding-right: 3%;
        span {
          max-width: 300px;
          padding-right: 8px;
          color: #333333;
          font-size: 14px;
        }
      }
      .col:after {
        content: '';
        width: 98%;
        position: absolute;
        top: 0;
        left: 0%;
        bottom: 0;
        background-color: #f5f5f5;
        z-index: -1;
      }

      .flex1 {
        flex: 1;
      }

      .mr-8 {
        margin-right: var(--md-spacing-3);
      }

      .card {
        height: 46px;
        background: #e6f3ff;
        border-radius: 4px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding-left: var(--md-spacing-3);
        padding-right: var(--md-spacing-3);

        .tixing {
          display: flex;
          align-items: center;

          span {
            max-width: 300px;
            color: #333333;
            font-size: 14px;
            display: inline-block;
            width: 120px;
            text-align: right;
          }
        }

        .width {
          width: 94px;
        }
      }
      .caoyaojyfs-content {
        margin-bottom: 20px;
      }
      .custom-title {
        margin-top: 8px;
        color: #666666;
      }
      .yiyuandz {
        display: block;
        .yiyuandz-title {
          color: #666666;
          font-size: 14px;
        }
        .yiyuandz-row {
          margin: 5px 0;
        }
      }
    }

    &-fotter {
      width: 100%;
      height: 46px;
      background: #e6f3ff;
      border-radius: 0px 0px 4px 4px;
      padding: var(--md-spacing-3) 0;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-end;
    }
  }

  .#{$md-prefix}-right-12 {
    margin-right: 12px;
  }

  .#{$md-prefix}-buttons {
    width: 64px;
  }
}

.selectRight {
  margin-left: 8px;
}

.selectRight ::v-deep .#{$md-prefix}-input__wrapper {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.selectLeft ::v-deep .#{$md-prefix}-input__wrapper {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.col:hover .btn {
  display: block;
}
.btn {
  display: none;
  color: #1385f0;
  cursor: pointer;
  margin-right: 10px;
  position: absolute;
  right: -0.5%;
  width: 19px;
}
</style>
