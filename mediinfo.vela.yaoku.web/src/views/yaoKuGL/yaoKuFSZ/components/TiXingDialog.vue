<template>
  <md-dialog
    title="提醒"
    size="large"
    v-model="dialogVisible"
    :custom-class="prefixClass('tixing-dialog')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    @close="handleClose"
  >
    <div :class="prefixClass('content')">
      <md-table
        :data="tableData"
        :columns="columns"
        height="100%"
        autoFill="true"
      >
        <template #tiXing="{ row, $index }">
          <span class="textColor">{{ row.tiXing }}</span>
        </template>

        <template #caoZuo="{ row, $index }">
          <md-radio-group v-model="row.luRuBZ">
            <md-radio label="0">录入</md-radio>
            <md-radio label="1">不录入</md-radio>
          </md-radio-group>
        </template>
      </md-table>
    </div>
    <template #footer>
      <md-button
        type="primary"
        v-if="showShanChu !== '2'"
        :class="prefixClass('right-12')"
        plain
        @click="handleLuRu('1')"
        >全部不录入</md-button
      >
      <md-button
        type="primary"
        v-if="showShanChu !== '2'"
        :class="prefixClass('right-12')"
        @click="handleLuRu('0')"
        >全部录入</md-button
      >
      <md-button
        type="primary"
        v-if="showShanChu !== '2'"
        :class="prefixClass('right-12 buttons')"
        @click="handleSave(1)"
        >确定</md-button
      >
      <md-button
        type="primary"
        v-if="showShanChu === '2'"
        :class="prefixClass('right-12 buttons')"
        @click="handleSave(2)"
        >删除药品</md-button
      >
    </template>
  </md-dialog>
</template>
<script setup lang="ts">
import { defineEmits, ref } from 'vue';

interface tableItem {
  luRuBZ: String | Number;
}

const tableData = ref<tableItem[]>([]);
const dialogVisible = ref(false);
const columns = ref([
  {
    label: '药品名称与规格',
    prop: 'yaoPinMC',
    minwidth: 220,
    type: 'text',
  },
  {
    label: '产地',
    prop: 'chanDiMC',
    type: 'text',
    minwidth: 100,
  },
  {
    label: '生产企业',
    prop: 'shengChanQY',
    minwidth: 200,
  },
  {
    label: '提醒',
    prop: 'tiXing',
    slot: 'tiXing',
  },
  {
    label: '操作',
    slot: 'caoZuo',
    hidden: false,
  },
]);
const resolved = ref();
const rejected = ref();
const showShanChu = ref();
//传方法的
const emit = defineEmits(['']);

//open dialig
async function showDialog(arr: [], xianZhiTJ: { xianZhiTXLX: Number }) {
  dialogVisible.value = true;
  tableData.value = arr;
  showShanChu.value = xianZhiTJ.xianZhiTXLX;
  columns.value[4].hidden = showShanChu.value === '2' ? true : false;
  return new Promise((resolve, reject) => {
    resolved.value = resolve;
    rejected.value = reject;
  });
}
function handleClose() {
  dialogVisible.value = false;
}
//录入 把录入的数据带入入库单
function handleLuRu(type: any) {
  tableData.value.forEach((el) => {
    el.luRuBZ = type == '0' ? '0' : '1';
  });
  const arr = tableData.value.filter((fl) => fl.luRuBZ == '0');
  resolved.value(arr);
  handleClose();
}
//保存 把录入的数据带入入库单
async function handleSave(type: Number) {
  const arr = tableData.value.filter((fl) => fl.luRuBZ == '0');
  handleClose();
  if (type === 1) {
    resolved.value(arr);
  } else {
    resolved.value([]);
  }
}
//去掉单元格padding
function cellStyle() {
  return { padding: 0 };
}
//抛出方法，ref可调用到
defineExpose({
  showDialog,
});
</script>
<style lang="scss" scoped>
.#{$md-prefix}-content {
  height: 100%;

  span {
    padding-right: 8px;
  }

  .textColor {
    color: #ff8600;
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-tixing-dialog {
  .mediinfo-vela-yaoku-web-dialog__footer {
    height: 46px;
    background: #e6f3ff !important;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .mediinfo-vela-yaoku-web-scrollbar__view {
    height: 100%;

    .mediinfo-vela-yaoku-web-dialog__inner-wrapper {
      height: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
  }
}
</style>
