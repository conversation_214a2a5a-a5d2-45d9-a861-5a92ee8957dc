<template>
  <div :class="prefixClass('page-wrap')">
    <div :class="prefixClass('yishouli-wrap')">
      <div :class="prefixClass('search-bar')">
        <div :class="prefixClass('search-bar__left')">
          <span>请领日期</span>
          <md-date-picker
            v-model="timeObj"
            type="daterange"
            :teleported="false"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 210px; margin: 0 8px"
            @change="handleSearch"
          />
          <md-select
            v-model="qingLingFS"
            placeholder="请选择"
            :class="prefixClass('qinglingfs-select space-8')"
            @change="handleChangeQingLingFS"
          >
            <md-option
              v-for="item in qingLingFSOptionsNew"
              :key="item.fangShiID"
              :label="item.fangShiMC"
              :value="item.fangShiID"
            >
            </md-option>
          </md-select>
          <md-select
            v-if="$ShouLiQL.showZhangBLB == 1"
            v-model="zhangBuLBID"
            @change="handleSearch"
            placeholder=""
            style="margin-right: 8px; width: 140px"
          >
            <md-option value="" label="全部账簿类别"></md-option>
            <md-option
              v-for="item in zhangBuLBOptions"
              :value="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :key="item.biaoZhunDM"
            ></md-option>
          </md-select>
          <md-select
            v-model="qingLingWZ"
            filterable
            :disabled="!qingLingFS"
            default-first-option
            placeholder="输入选择请领位置"
            :class="prefixClass('qinglingwz-select space-8')"
            @change="handleSearch"
          >
            <md-option
              v-for="item in qingLingWZOptions"
              :key="item.weiZhiID"
              :label="
                item.zuZhiJGID === zuZhiJGID
                  ? item.weiZhiMC
                  : item.zuZhiJGMC + '-' + item.weiZhiMC
              "
              :value="item.weiZhiID"
            >
            </md-option>
          </md-select>
          <md-checkbox
            v-model="shiFouJY"
            label="急用"
            :class="prefixClass('space-8')"
            @change="handleSearch"
          >
          </md-checkbox>
        </div>
        <div :class="prefixClass('search-bar__right')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
            style="margin-left: auto"
          >
            刷新</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('container___alias')">
        <md-table-pro
          :columns="yiShouLiColumns"
          height="100%"
          ref="yiShouLiTable"
          :onFetch="handleFetch"
        >
          <template v-slot:qingLingDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('qinglingdantip')"
            >
              <!-- <template #reference> -->
              <span
                :class="prefixClass('qinglingdh')"
                @click="handleClickQingLingDan($event, row)"
                >{{ row.qingLingDH }}</span
              >
              <!-- </template> -->
              <template #content>
                <div @click="copy(row.qingLingDH)">复制</div>
              </template>
            </md-tooltip>
            <span v-if="row.jiYongBZ == 1" :class="prefixClass('jiyongtag')"
              >急</span
            >
          </template>
          <template v-slot:yaoPinMX="{ row }">
            <biz-taglist
              :list="row.yaoPinMC"
              @clickMore="({ event }) => handleClickQingLingDan(event, row)"
            ></biz-taglist>
          </template>
          <template v-slot:danJuZTDM="{ row }">
            <span
              :class="
                prefixClass([
                  'zhuangtaitag',
                  {
                    jujue: row.danJuZTDM == '5',
                    shouli: row.danJuZTDM == '4',
                  },
                ])
              "
              >{{ row.danJuZTDM !== '5' ? '已受理' : '已拒绝' }}</span
            >
          </template>
        </md-table-pro>
      </div>
    </div>
  </div>
</template>

<script>
import BizTagList from '@/components/BizTagList';
import { MdMessageBox } from '@mdfe/medi-ui';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { GetChuRuKFSList } from '@/service/yaoPinYK/chuRuKFSNew';
import {
  GetQingLingWZList,
  GetShouLiQLDCount,
  GetShouLiQLDList,
} from '@/service/yaoPinYK/shouLiQL';
import { getJiGouID } from '@/system/utils/local-cache';

import useClipboard from 'vue-clipboard3';
import tableData from './tableData';
import dayjs from 'dayjs';
const ziDian = {
  YP0050: 'qingLingFSOptions',
};

export default {
  name: 'yiShouLi',
  inject: ['$ShouLiQL'],
  data() {
    return {
      timeObj: [
        dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      zhangBuLBID: '',
      zhangBuLBOptions: [],
      zuZhiJGID: getJiGouID(),
      qingLingFS: '', // 请领方式
      qingLingWZ: '', // 请领位置
      shiFouJY: false, // 是否急用
      table: false,
      qingLingFSOptions: [],
      qingLingWZOptions: [],
      qingLingFSOptionsNew: [],
      yiShouLiColumns: tableData.yiShouLiColumns,
    };
  },
  created() {
    this.initData();
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    async initData() {
      //字典初始化
      getYaoPinShuJuYZYList(Object.keys(ziDian)).then((res) => {
        res.forEach((item) => {
          const name = ziDian[item.shuJuYLBID];
          this[name] = item.zhiYuList;
          this[name].unshift({
            biaoZhunDM: '',
            biaoZhunMC: '全部',
            shunXuHao: 0,
          });
        });
      });
      getZhangBuQXXQ().then((res) => {
        this.zhangBuLBOptions = res.zhangBuLBXXList || [];
      });
      let result = await GetChuRuKFSList({
        ChuRuKFXDM: 4,
        pageSize: 999999,
        PageIndex: 1,
      });
      this.qingLingFSOptionsNew = result || [];
      this.qingLingFSOptionsNew.unshift({
        fangShiID: '',
        fangShiMC: '全部',
        shunXuHao: 0,
      });
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    // 已受理列表查询
    async handleFetch({ page, pageSize }, config) {
      const zhangBuLBStr =
        this.zhangBuLBOptions &&
        this.zhangBuLBOptions.map((m) => m.zhangBuLBID);
      const params = {
        pageIndex: page,
        pageSize: pageSize,
        shouLiBZ: 1,
        zhangBuLBID:
          this.zhangBuLBID || (zhangBuLBStr && zhangBuLBStr.join(',')),
        qingLingWZID: this.qingLingWZ,
        qingLingLXDM: this.qingLingFS,
        jiYongBZ: this.shiFouJY ? 1 : '',
        zuoFeiBZ: 0,
        likeQuery: '',
        kuaiYuanQBZ: 1,
        kaiShiSJ: this.timeObj?.[0] ? this.timeObj[0] : '',
        jieShuSJ: this.timeObj?.[1] ? this.timeObj[1] : '',
      };
      const [items, total] = await Promise.all([
        GetShouLiQLDList(params, config),
        !this.total && GetShouLiQLDCount(params, config),
      ]);
      this.data = items;
      this.total = Number(total) || this.total;
      return {
        items: items,
        total: this.total,
      };
    },
    // 搜索改变方法
    handleSearch() {
      this.total = 0;
      this.$refs.yiShouLiTable.search({ pageSize: 100 });
    },
    // 请领方式改变获取请领位置
    handleChangeQingLingFS(val) {
      this.qingLingWZ = '';
      GetQingLingWZList({ qingLingLXDM: val }).then((res) => {
        this.qingLingWZOptions = res;
      });
      this.handleSearch();
    },
    // 查看请领单详情
    handleClickQingLingDan(e, row) {
      e.stopPropagation();
      const options = {
        qingLingDID: row.id,
        qingLingDH: row.qingLingDH,
        beiZhu: row.beiZhu,
        shiFouJY: row.jiYongBZ,
        danJuZTDM: row.danJuZTDM,
        shouLiBZ: '1',
        zhiDanRXM: row.zhiDanRXM,
        zhiDanSJ: row.zhiDanSJ,
        weiZhiMC: row.weiZhiMC,
        qingLingLXMC: row.qingLingLXMC,
        juJueYYMS: row.juJueYYMS,
        juJueYYMC: row.juJueYYMC,
        zhangBuLBMC: row.zhangBuLBMC,
      };
      this.$ShouLiQL.$refs.shouLiQLDrawer.openDrawer(options, 2);
    },
    // 复制成功提示
    // handleCopySuccess() {
    //   MdMessage({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000
    //   })
    // },
    // 复制失败提示
    // handleCopyError() {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了',
    //   })
    // }
  },
  components: {
    'biz-taglist': BizTagList,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-qinglingdantip {
  min-width: 30px;
  // color: #1e88e5;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-page-wrap {
  height: 100%;
  width: 100%;

  .#{$md-prefix}-yishouli-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-search-bar {
      display: flex;
      justify-content: space-between;
      height: 30px;

      &__left {
        ::v-deep
          .#{$md-prefix}-input.#{$md-prefix}-input--suffix
          .#{$md-prefix}-input__inner {
          vertical-align: baseline;
        }
        .#{$md-prefix}-space-8 {
          margin-right: 8px;
        }
        .#{$md-prefix}-qinglingfs-select {
          width: 128px;
        }
        .#{$md-prefix}-qinglingwz-select {
          width: 180px;
        }
      }
      &__right {
      }
    }
    .#{$md-prefix}-container___alias {
      flex: 1;
      margin-top: 8px;
      min-height: 0;
      .#{$md-prefix}-qinglingdh {
        cursor: pointer;
        color: rgb(var(--md-color-6));
        &:hover {
          // color: #1e88e5;
          color: rgb(var(--md-color-6));
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-jiyongtag {
        display: inline-block;
        color: #fff;
        font-size: 14px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        // background-color: #f12933;
        background-color: #ff9900;
        border-radius: 2px;
        text-align: center;
        margin: 0 0 0 5px;
      }
      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        &.#{$md-prefix}-shouli {
          background-color: rgb(var(--md-color-1));
          color: rgb(var(--md-color-6));
        }
        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }
      ::v-deep
        .#{$md-prefix}-data-table.isMaxHeight
        .#{$md-prefix}-table
        td
        > .cell {
        max-height: unset;
      }
    }
  }
}
</style>
