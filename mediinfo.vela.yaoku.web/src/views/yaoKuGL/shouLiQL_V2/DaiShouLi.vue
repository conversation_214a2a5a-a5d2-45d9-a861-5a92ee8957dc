<template>
  <div :class="prefixClass('page-wrap')">
    <div v-loading="pageLoading" :class="prefixClass('daishouli-wrap')">
      <div :class="prefixClass('search-bar')">
        <div :class="prefixClass('search-bar__left')">
          <span>请领日期</span>
          <md-date-picker
            v-model="timeObj"
            type="daterange"
            :teleported="false"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 210px; margin: 0 8px"
            @change="handleSearch"
          />
          <md-select
            v-model="qingLingFS"
            placeholder="请选择"
            :class="prefixClass('qinglingfs-select space-8')"
            @change="handleChangeQingLingFS"
          >
            <md-option
              v-for="item in qingLingFSOptionsNew"
              :key="item.fangShiID"
              :label="item.fangShiMC"
              :value="item.fangShiID"
            >
            </md-option>
          </md-select>
          <md-select
            v-if="$ShouLiQL.showZhangBLB == 1"
            @change="handleSearch"
            v-model="zhangBuLBID"
            placeholder=""
            style="margin-right: 8px; width: 140px"
          >
            <md-option value="" label="全部账簿类别"></md-option>
            <md-option
              v-for="item in zhangBuLBOptions"
              :value="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :key="item.zhangBuLBID"
            ></md-option>
          </md-select>
          <md-select
            v-model="qingLingWZ"
            filterable
            :disabled="!qingLingFS"
            default-first-option
            placeholder="输入选择请领位置"
            :class="prefixClass('qinglingwz-select space-8')"
            @change="handleSearch"
          >
            <md-option
              v-for="item in qingLingWZOptions"
              :key="item.weiZhiID"
              :label="
                item.zuZhiJGID === zuZhiJGID
                  ? item.weiZhiMC
                  : item.zuZhiJGMC + '-' + item.weiZhiMC
              "
              :value="item.weiZhiID"
            >
            </md-option>
          </md-select>
          <md-checkbox
            v-model="shiFouJY"
            label="急用"
            :class="prefixClass('space-8')"
            @change="handleSearch"
          >
          </md-checkbox>
        </div>
        <div :class="prefixClass('search-bar__right')">
          <md-button
            type="text"
            :class="prefixClass('space-8')"
            :icon="prefixClass('icon-shuaxin')"
            @click="handleSearch"
          >
            刷新
          </md-button>
          <md-button type="primary" @click="handleShouLiQL">受理请领</md-button>
        </div>
      </div>
      <div :class="prefixClass('container__alias')">
        <md-table-pro
          :columns="daiShouLiColumns"
          ref="daiShouLiTable"
          :class="prefixClass('daishouli-table')"
          height="100%"
          :onFetch="handleFetch"
          :onAfterFetch="handleAfterFetch"
          :cell-class-name="tableCellClassName"
        >
          <template v-slot:qingLingDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('qinglingdantip')"
            >
              <template #content>
                <div @click="copy(row.qingLingDH)">复制</div>
              </template>
              <!-- <template  v-slot:reference> -->
              <span
                :class="prefixClass('qinglingdh')"
                @click="handleClickQingLingDan($event, row)"
                >{{ row.qingLingDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
            <span v-if="row.jiYongBZ == 1" :class="prefixClass('jiyongtag')"
              >急</span
            >
          </template>
          <template v-slot:yaoPinMX="{ row }">
            <biz-taglist
              :list="row.yaoPinMC"
              @clickMore="({ event }) => handleClickQingLingDan(event, row)"
            ></biz-taglist>
          </template>
          <template v-slot:operate="{ row }">
            <div :class="prefixClass('operate')">
              <md-button type="primary" noneBg plain @click="handleShouLi(row)"
                >受理</md-button
              >
              <md-button type="danger" noneBg @click="handleJuJue(row)">
                拒绝
              </md-button>
            </div>
          </template>
        </md-table-pro>
      </div>
    </div>
    <bmis-blue-dialog
      v-model:visible="visibleDialog"
      title="拒绝请领"
      height="240px"
      ref="bizDialog"
      @submit="handleSubmitJuJueQL"
    >
      <md-form
        :model="dialogModel"
        :rules="dialogRules"
        ref="dialogForm"
        label-width="80px"
        use-status-icon
      >
        <md-col :span="24">
          <md-form-item label="拒绝原因" prop="juJueYY">
            <md-select
              v-model="dialogModel.juJueYY"
              placeholder="请选择拒绝原因"
            >
              <md-option
                v-for="item in juJueYYOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="24">
          <md-form-item label="备注">
            <md-input
              type="textarea"
              v-model="dialogModel.beiZhu"
              :rows="2"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
      </md-form>
    </bmis-blue-dialog>

    <shouLi-dialog ref="shouLiDialog"></shouLi-dialog>
  </div>
</template>

<script>
import BizTagList from '@/components/BizTagList';
import BlueDialog from '@/components/blue-dialog/index.vue';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { logger } from '@/service/log';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { GetChuRuKFSList } from '@/service/yaoPinYK/chuRuKFSNew';
import {
  GetQingLingWZList,
  GetShouLiQLDCount,
  GetShouLiQLDList,
  JuJueQL,
  shouLiQLByCRKFS,
  shouLiQLPLByCRKFS,
} from '@/service/yaoPinYK/shouLiQL';
import eventBus from '@/system/utils/eventbus';
import { getJiGouID } from '@/system/utils/local-cache';
import useClipboard from 'vue-clipboard3';
import shouLiDialog from './components/ShouLiDialog.vue';
import tableData from './tableData';
import dayjs from 'dayjs';
const ziDian = {
  YP0050: 'qingLingFSOptions',
  YP0051: 'juJueYYOptions',
};

export default {
  name: 'daiShouLi',
  inject: ['$ShouLiQL'],
  data() {
    return {
      timeObj: [],
      zhangBuLBID: '',
      zhangBuLBOptions: [],
      zuZhiJGID: getJiGouID(),
      qingLingFS: '', // 请领方式
      qingLingWZ: '', // 请领位置
      shiFouJY: false, // 是否急用
      pageLoading: false,
      visibleDialog: false,
      dialogModel: {
        id: '',
        juJueYY: '',
        beiZhu: '',
      },
      dialogRules: {
        juJueYY: { required: true, message: '请选择原因', trigger: 'blur' },
      },
      qingLingFSOptions: [],
      qingLingWZOptions: [],
      qingLingFSOptionsNew: [],
      daiShouLiColumns: tableData.daiShouLiColumns,
      juJueYYOptions: [],
    };
  },
  created() {
    this.initData();
    // 监听受理请领、拒绝请领方法
    eventBus.$on('handleJuJueQL', (v) => {
      this.handleJuJue({ id: v });
    });
    eventBus.$on('handleShouLiQL', (v) => {
      this.handleShouLi({ id: v });
    });
    eventBus.$on('qingLingDrawer', (obj) => {
      this.handleShouLi(obj);
    });
  },
  beforeDestroy() {
    eventBus.$off('qingLingDrawer');
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          MdMessage({
            message: '复制成功',
            type: 'success',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    async initData() {
      //字典初始化
      getYaoPinShuJuYZYList(Object.keys(ziDian)).then((res) => {
        res.forEach((item) => {
          const name = ziDian[item.shuJuYLBID];
          this[name] = item.zhiYuList;
          if (name != 'juJueYYOptions') {
            this[name].unshift({
              biaoZhunDM: '',
              biaoZhunMC: '全部',
              shunXuHao: 0,
            });
          }
        });
      });
      getZhangBuQXXQ().then((res) => {
        this.zhangBuLBOptions = res.zhangBuLBXXList || [];
      });
      let result = await GetChuRuKFSList({
        ChuRuKFXDM: 4,
        pageSize: 999999,
        PageIndex: 1,
      });
      this.qingLingFSOptionsNew = result || [];
      this.qingLingFSOptionsNew.unshift({
        fangShiID: '',
        fangShiMC: '全部',
        shunXuHao: 0,
      });
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    // 选择的行受理
    handleShouLiQL() {
      // 获取选中的行
      const checkedRows = this.$refs.daiShouLiTable
        .getComp('table')
        .getAllCheckedRows();
      const param = checkedRows.map((row) => row.id);
      // 判断是否选中行
      if (param.length <= 0) {
        MdMessage({
          message: '请选择需要受理的请领单！',
          type: 'warning',
        });
        return;
      }
      MdMessageBox.confirm('是否确定受理', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        this.pageLoading = true;
        shouLiQLPLByCRKFS(param)
          .then((res) => {
            MdMessage({
              message: '受理请领成功',
              type: 'success',
            });
            this.handleOpenPage(res);
            this.handleSearch();
          })
          .catch((e) => {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: '受理请领失败：' + e,
              confirmButtonText: '我知道了',
            });
          })
          .finally(() => {
            this.pageLoading = false;
          });
      });
    },
    // 单行受理
    handleShouLi(row) {
      MdMessageBox.confirm('确定受理该单据', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        if (
          (row.qingLingLXDM === '1601' ||
            row.qingLingLXDM === '1602' ||
            row.qingLingLXDM === '1603') &&
          this.$ShouLiQL.qingLingSLCDGZ != '2'
        ) {
          this.$refs.shouLiDialog.showModal(row).then((res) => {
            if (res) {
              this.handleOpenPage(res);
              this.$refs.daiShouLiTable.refresh();
            }
          });
        } else {
          this.pageLoading = true;
          shouLiQLPLByCRKFS([row.id])
            .then((res) => {
              MdMessage({
                message: '受理请领成功',
                type: 'success',
              });
              this.handleOpenPage(res);
              this.$refs.daiShouLiTable.refresh();
            })
            .catch((e) => {
              console.error(e);
              logger.error(error);
            })
            .finally(() => {
              this.pageLoading = false;
            });
        }
      });
    },
    // 打开药库出库单、采购单页面
    handleOpenPage(data) {
      if (Array.isArray(data) && data.length > 0) {
        // 多个请领单受理打开页面问题
        data.forEach((item) => {
          if (item.chuKuDID) {
            //chuKuDID会存在"1481596507873492992|1481596507877687296"这种情况，所以拆成数组跳转第一个chuKuDID
            let chuKuDIDList = item.chuKuDID.split('|');
            this.$router.push({
              name: 'YaoPinCK',
              query: {
                showType: 'second',
                isChongHong: '1',
                id: chuKuDIDList[0],
              },
            });
          }
          if (item.caiGouJHDID) {
            this.$router.push({
              name: 'CaiGouJH',
              query: {
                // showType: 'second',
                // isChongHong: '1',
                // id: item.chuKuDID
              },
            });
          }
        });
      }
    },
    // 打开拒绝请领Dialog
    handleJuJue(row) {
      this.visibleDialog = true;
      this.dialogModel = {
        id: row.id,
        juJueYY: '',
        beiZhu: '',
      };
      this.$nextTick(() => {
        this.$refs.dialogForm.resetFields();
      });
    },
    // 拒绝
    async handleSubmitJuJueQL() {
      try {
        const result = await this.$refs.dialogForm.validate();
        if (!result) return;
        const param = {
          juJueYYDM: this.dialogModel.juJueYY,
          juJueYYMC: this.juJueYYOptions.find(
            (x) => x.biaoZhunDM === this.dialogModel.juJueYY,
          )?.biaoZhunMC,
          juJueYYMS: this.dialogModel.beiZhu,
          id: this.dialogModel.id,
        };
        JuJueQL(param)
          .then((res) => {
            MdMessage({
              message: '拒绝请领成功',
              type: 'success',
            });
            this.visibleDialog = false;
            this.dialogModel.juJueYYDM = '';
            this.dialogModel.juJueYYMS = '';
            this.dialogModel.id = '';
            this.handleSearch();
          })
          .catch((e) => {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: '拒绝请领失败：' + e,
              confirmButtonText: '我知道了',
            });
          });
      } catch (error) {}
    },
    // table刷新
    async handleFetch({ page, pageSize }, config) {
      const zhangBuLBStr =
        this.zhangBuLBOptions &&
        this.zhangBuLBOptions.map((m) => m.zhangBuLBID);
      const params = {
        pageIndex: page,
        pageSize: pageSize,
        shouLiBZ: 0,
        zhangBuLBID:
          this.zhangBuLBID || (zhangBuLBStr && zhangBuLBStr.join(',')),
        qingLingWZID: this.qingLingWZ,
        qingLingLXDM: this.qingLingFS,
        jiYongBZ: this.shiFouJY ? 1 : '',
        zuoFeiBZ: 0,
        likeQuery: '',
        kuaiYuanQBZ: 1,
        kaiShiSJ: this.timeObj?.[0] ? this.timeObj[0] : '',
        jieShuSJ: this.timeObj?.[1] ? this.timeObj[1] : '',
      };
      const [items, total] = await Promise.all([
        GetShouLiQLDList(params, config),
        !this.total && GetShouLiQLDCount(params, config),
      ]);
      this.data = items;
      this.total = Number(total) || this.total;
      return {
        items: items,
        total: this.total,
      };
    },
    // table刷新后全选行
    handleAfterFetch() {
      this.$refs.daiShouLiTable.getComp('table').toggleAllSelection();
    },
    // 查询条件变更搜索
    handleSearch() {
      this.total = 0;
      this.$refs.daiShouLiTable.search({ pageSize: 100 });
    },
    // table样式
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return this.prefixClass('qinglingdan-row');
      }
      return this.prefixClass('qinglingdan-row');
    },
    // 查看请领单详情
    handleClickQingLingDan(e, row) {
      e.stopPropagation();
      const options = {
        qingLingDID: row.id,
        qingLingDH: row.qingLingDH,
        beiZhu: row.beiZhu,
        jiYongBZ: row.jiYongBZ,
        danJuZTDM: row.danJuZTDM,
        shouLiBZ: '0',
        zhiDanRXM: row.zhiDanRXM,
        zhiDanSJ: row.zhiDanSJ,
        weiZhiMC: row.weiZhiMC,
        qingLingLXMC: row.qingLingLXMC,
        juJueYYMS: row.juJueYYMS,
        juJueYYMC: row.juJueYYMC,
        qingLingLXDM: row.qingLingLXDM,
        id: row.id,
        zhangBuLBMC: row.zhangBuLBMC,
      };
      this.$ShouLiQL.$refs.shouLiQLDrawer.openDrawer(options, 1);
    },
    // 请领方式改变获取请领位置
    handleChangeQingLingFS(val) {
      this.qingLingWZ = null;
      GetQingLingWZList({ qingLingLXDM: val }).then((res) => {
        this.qingLingWZOptions = res;
      });
      this.handleSearch();
    },
    // 复制成功提示
    // handleCopySuccess() {
    //   MdMessage({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000
    //   })
    // },
    // 复制失败提示
    // handleCopyError() {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了',
    //   })
    // }
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
    'biz-taglist': BizTagList,
    'shouLi-dialog': shouLiDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-qinglingdantip {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
.#{$md-prefix}-daishouli-table.#{$md-prefix}--data-table
  .#{$md-prefix}-qinglingdan-row {
  color: #1e88e5;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-page-wrap {
  height: 100%;
  width: 100%;

  .#{$md-prefix}-daishouli-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-search-bar {
      display: flex;
      justify-content: space-between;
      height: 30px;
      .#{$md-prefix}-space-8 {
        margin-right: 8px;
      }
      &__left {
        ::v-deep
          .#{$md-prefix}-input.#{$md-prefix}-input--suffix
          .#{$md-prefix}-input__inner {
          vertical-align: baseline;
        }
        .#{$md-prefix}-qinglingfs-select {
          width: 128px;
        }
        .#{$md-prefix}-qinglingwz-select {
          width: 180px;
        }
      }
      &__right {
      }
    }
    .#{$md-prefix}-container__alias {
      flex: 1;
      margin-top: 8px;
      min-height: 0;
      .#{$md-prefix}-qinglingdh {
        cursor: pointer;
        color: rgb(var(--md-color-6));
        &:hover {
          // color: #1e88e5;
          color: rgb(var(--md-color-6));
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-jiyongtag {
        display: inline-block;
        color: #fff;
        font-size: 14px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        // background-color: #f12933;
        background-color: #ff9900;
        border-radius: 2px;
        text-align: center;
        margin: 0 0 0 5px;
      }
      .#{$md-prefix}-operate {
        display: flex;
        justify-content: center;
        align-content: center;
      }
      ::v-deep
        .#{$md-prefix}-data-table.isMaxHeight
        .#{$md-prefix}-table
        td
        > .cell {
        max-height: unset;
      }
    }
  }
}
</style>
