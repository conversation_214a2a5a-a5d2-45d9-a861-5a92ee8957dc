<template>
  <div :class="prefixClass('page-wrap')">
    <div :class="prefixClass('weijizhang-wrap')">
      <div :class="prefixClass('search-bar')">
        <div :class="prefixClass('search-bar__left')">
          <md-date-picker-range-pro
            v-model="searchDate"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :class="prefixClass('procurement-date space-8')"
            @change="handleSearch"
          >
          </md-date-picker-range-pro>
          <md-input
            v-model="baoSunDH"
            placeholder="输入单据号搜索"
            :class="prefixClass('danjuhao-input')"
            @input="handleSearch"
          >
            <template #suffix>
              <i
                @click="handleSearch"
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
              ></i>
            </template>
          </md-input>
        </div>
        <div :class="prefixClass('search-bar__right')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
            style="margin-left: auto"
          >
            刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-xinzeng')"
            :class="prefixClass('kaidan-button')"
            noneBg
            @click="handleAddTiaoJiaDan"
            >开单</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('container__alias')">
        <md-table-pro
          v-loading="tableLoading"
          :columns="daiShouLiColumns"
          ref="daiShouLiTable"
          :class="prefixClass('daishouli-table')"
          height="100%"
          :onFetch="handleFetch"
          :cell-class-name="tableCellClassName"
        >
          <template v-slot:baoSunDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('qinglingdantip')"
            >
              <template #content>
                <div @click="copy(row.baoSunDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template #reference> -->
              <span
                :class="prefixClass('qinglingdh')"
                @click="handleClickBaoSunDan($event, row)"
              >
                {{ row.baoSunDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
          </template>
          <template v-slot:yaoPinMCList="{ row }">
            <biz-taglist
              :list="row.yaoPinMCXSList"
              @clickMore="({ event }) => handleClickBaoSunDan(event, row)"
            ></biz-taglist>
          </template>
          <template v-slot:operate="{ row }">
            <div :class="prefixClass('operate')">
              <md-button type="text-bg" @click="handleEditTiaoJiaDan(row)"
                >编辑</md-button
              >
              <md-button type="text-bg" @click="handleJiZhang(row)">
                记账
              </md-button>
              <md-button type="danger" noneBg @click.stop="handleZuoFei(row)">
                作废
              </md-button>
            </div>
          </template>
        </md-table-pro>
      </div>
    </div>
  </div>
</template>

<script>
import BizTagList from '@/components/BizTagList';
import {
  GetWeiJiZBSDCount,
  GetWeiJiZBSDKSRQ,
  GetWeiJiZBSDList,
  JiZhangBaoSunDan,
  ZuoFeiBaoSunDan,
} from '@/service/yaoPinYK/yaoKuBS';
import eventBus from '@/system/utils/eventbus';
import { MdMessageBox } from '@mdfe/medi-ui';
import { MdDatePickerRangePro, MdTablePro } from '@mdfe/medi-ui-pro';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import tableData from './tableData';

export default {
  name: 'weiJiZhang',
  inject: ['$YaoKuBS'],
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchDate: [
        // dayjs(new Date().setDate(1)).format('YYYY-MM-DD'),
        // dayjs().format('YYYY-MM-DD')
      ],
      baoSunDH: '',
      tableLoading: false,
      qingLingFSOptions: [],
      qingLingWZOptions: [],
      daiShouLiColumns: tableData.weiJiZhangColumns,
    };
  },
  watch: {
    active: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
  },
  created() {
    this.$nextTick(() => {
      this.initData();
    });
    eventBus.$on('handleJiZhangBaoSunDan', (v) => {
      this.handleJiZhang({ id: v });
    });
    eventBus.$on('handleBaoSunFetch', () => {
      this.$nextTick(() => {
        this.handleSearch();
      });
    });
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    async initData() {
      let date = await GetWeiJiZBSDKSRQ();
      this.searchDate = [
        dayjs(date ? date : '').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ];
      this.total = null;
      this.handleSearch();
    },
    handleZuoFei(row) {
      MdMessageBox.confirm('此操作将删除该行数据，是否继续？', '操作提醒！', {
        type: 'warning',
      }).then(async () => {
        this.pageLoading = true;
        try {
          await ZuoFeiBaoSunDan({ baoSunDID: row.id });
          this.$message({
            type: 'success',
            message: '作废成功！',
          });
          this.handleSearch();
        } finally {
          this.pageLoading = false;
        }
      });
    },
    handleEditTiaoJiaDan(row) {
      this.$router.push({
        path: '/BianJiBSD/' + row.id,
        query: {
          title: '报损单-' + row.baoSunDH,
          id: row.id,
        },
      });
    },
    // 记账
    handleJiZhang(row) {
      MdMessageBox.confirm('是否确定记账', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        this.tableLoading = true;
        JiZhangBaoSunDan({ baoSunDID: row.id })
          .then((res) => {
            this.$message({
              message: '记账成功',
              type: 'success',
            });
            this.$emit('go-to-tab');
          })
          .finally(() => {
            this.tableLoading = false;
            // this.handleSearch()
          });
      });
    },
    handleAddTiaoJiaDan() {
      this.$router.push({
        name: 'XinZengBSD',
      });
    },
    // table刷新
    async handleFetch({ page, pageSize }, config) {
      const [kaiShiSJ, jieShuSJ] =
        this.searchDate?.length > 0 ? this.searchDate : ['', ''];
      const params = {
        pageIndex: page,
        pageSize: pageSize,
        kaiShiSJ: kaiShiSJ ? dayjs(kaiShiSJ).format('YYYY-MM-DD') : '',
        jieShuSJ: jieShuSJ ? dayjs(jieShuSJ).format('YYYY-MM-DD') : '',
        baoSunDH: this.baoSunDH,
      };
      const [items, total] = await Promise.all([
        GetWeiJiZBSDList(params, config),
        !this.total && GetWeiJiZBSDCount(params, config),
      ]);

      this.data = items;
      this.total = Number(total) || this.total;
      return {
        items: items,
        total: this.total,
      };
    },
    // 查询条件变更搜索
    handleSearch() {
      this.total = null;
      this.$refs.daiShouLiTable.search({ pageSize: 100 });
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return this.prefixClass('qinglingdan-row');
      }
      return this.prefixClass('qinglingdan-row');
    },
    handleClickBaoSunDan(e, row) {
      e.stopPropagation();
      const options = {
        baoSunDID: row.id,
        danJuHao: row.baoSunDH,
        beiZhu: row.beiZhu,
        zhiDanRXM: row.zhiDanRXM,
        zhiDanSJ: row.zhiDanSJ,
      };
      this.$YaoKuBS.$refs.baoSunDrawer.openDrawer(options, 1);
    },
    // handleCopySuccess () {
    //   this.$message({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000
    //   })
    // },
    // handleCopyError () {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了'
    //   })
    // }
  },
  components: {
    'md-table-pro': MdTablePro,
    'md-date-picker-range-pro': MdDatePickerRangePro,
    'biz-taglist': BizTagList,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-fuzhi {
  // @include md-def('color', 'color-6');
  color: rgb(var(--md-color-6));
}
.#{$md-prefix}-qinglingdantip {
  min-width: 30px;
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
.#{$md-prefix}-daishouli-table.#{$md-prefix}-data-table
  .#{$md-prefix}-qinglingdan-row {
  color: #1e88e5;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-page-wrap {
  height: 100%;
  width: 100%;

  .#{$md-prefix}-weijizhang-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-search-bar {
      display: flex;
      justify-content: space-between;
      height: 30px;

      &__left {
        display: flex;
        .#{$md-prefix}-space-8 {
          margin-right: 8px;
        }
        .#{$md-prefix}-procurement-date {
          width: 250px;
        }
        .#{$md-prefix}-danjuhao-input {
          width: 264px;
        }
      }
      &__right {
        .#{$md-prefix}-kaidan-button {
          margin: 3px 0;
        }
      }
    }
    .#{$md-prefix}-container__alias {
      flex: 1;
      margin-top: 8px;
      min-height: 0;
      .#{$md-prefix}-qinglingdh {
        cursor: pointer;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
        &:hover {
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-jiyongtag {
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        margin: 0 0 2px 5px;
        background-color: #ff9900;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
      }
      .#{$md-prefix}-operate {
        display: flex;
        justify-content: center;
        align-content: center;
      }
    }
  }
}
::v-deep .#{$md-prefix}-data-table__pagination {
  margin-top: 8px !important;
}
</style>
