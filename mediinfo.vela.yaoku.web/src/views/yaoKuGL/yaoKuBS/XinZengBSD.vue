<template>
  <div v-loading="loading" :class="prefixClass('baosundan-page-wrap')">
    <div :class="prefixClass('baosundan-wrap')">
      <div :class="prefixClass('xinZengBSD-title')">
        <div :class="prefixClass('title__left')">
          <div :class="prefixClass('danjumc')">
            <i class="iconfont icondanju"></i>
            报损单
          </div>
          <div :class="prefixClass('yaopindw')">
            <biz-yaopindw
              v-model="kuaiSuDWYP"
              showSuffix
              placeholder="输入药品快速定位"
              :class="prefixClass('danjuhao-input')"
              @change="handleChangeYaoPinDWSR($event)"
            ></biz-yaopindw>
          </div>
        </div>
        <div :class="prefixClass('title__right')">
          <md-button
            v-if="false"
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handlePrint"
            >打印</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('right-12')"
            :icon="prefixClass('icon-shanchuwap')"
            noneBg
            @click="handleDel"
            >删除</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('right-12')"
            plain
            @click="handleSave"
            >保存</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('right-12')"
            @click="handleSaveAndJiZhang"
            >保存并记账</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('search-bar')">
        <md-form label-width="50px" use-status-icon ref="form">
          <md-col :span="24">
            <md-form-item label="备注" prop="username">
              <md-input v-model="beiZhu" placeholder="请输入" />
            </md-form-item>
          </md-col>
        </md-form>
      </div>
      <div :class="prefixClass('container__alias')">
        <md-editable-table-pro
          v-model="data"
          v-table-enter="handleTableEnter"
          :columns="columns"
          id="baoSunTable"
          :class="prefixClass('editable')"
          ref="editable"
          :autoFill="true"
          :hideAddButton="true"
          :show-default-operate="showDefaultOperate"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #yaoPinMCGG="{ row, $index }">
            <biz-yaopindw
              v-model="row.yaoPinXX"
              type="bsd"
              labelKey="yaoPinZC"
              placeholder="请输入"
              @change="handleTableYaoPinDWChange($event, row, $index)"
            ></biz-yaopindw>
          </template>
          <template #baoSunSL="{ row, $index }">
            <md-input
              v-model="row.baoSunSL"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @blur="checkBaoSunSL(row, $index)"
            ></md-input>
          </template>
          <template v-slot:jinJiaJE="{ row }">
            {{ (row.jinJia * row.baoSunSL).toFixed(xiaoShuDianWS) }}
          </template>
          <template v-slot:lingShouJE="{ row }">
            {{ (row.lingShouJia * row.baoSunSL).toFixed(xiaoShuDianWS) }}
          </template>
          <template v-slot:yaoPinXQ="{ row }">
            <md-date-picker
              v-model="row.yaoPinXQ"
              type="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="选择有效期"
            ></md-date-picker>
          </template>
          <template #baoSunYY="{ row, cellRef }">
            <md-select
              v-model="row.baoSunYYDM"
              placeholder="选择报损原因"
              @visible-change="handleVisibleChange($event, cellRef)"
            >
              <md-option
                v-for="item in baoSunYYOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </template>
          <template #baoSunLXDM="{ row, cellRef }">
            <md-select
              v-model="row.baoSunLXDM"
              placeholder="选择报损类型"
              @visible-change="handleVisibleChange($event, cellRef)"
            >
              <md-option
                v-for="item in baoSunLXOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('description')" style="flex-shrink: 0">
        <div :class="prefixClass('description-item')">
          <span :class="prefixClass('description-item__label')">制单:</span>
          <span :class="prefixClass('description-item__content')">{{
            zhiDanRXM
          }}</span>
          <span :class="prefixClass('description-item__content')">{{
            zhiDanSJ
          }}</span>
        </div>
        <div :class="prefixClass('description-item')">
          <span :class="prefixClass('description-item__label')">共计:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ yaoPinZS
            }}<span :class="prefixClass('content-color')">种药品</span></span
          >
          <span :class="prefixClass('description-item__label')"
            >合计 进价金额:</span
          >
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(jinJiaJE).toFixed(xiaoShuDianWS)
            }}<span :class="prefixClass('content-color')">元</span>
          </span>
          <span :class="prefixClass('description-item__label')">零售金额:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(lingShouJE).toFixed(xiaoShuDianWS)
            }}<span :class="prefixClass('content-color')">元</span></span
          >
        </div>
      </div>
    </div>
    <biz-yaopinph model="bs" ref="bizYaoPinPH" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep, findIndex, isEqual } from 'lodash';
import columnMixin from '@/components/mixin/columnMixin';
import eventBus from '@/system/utils/eventbus';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { formatMoney } from '@/system/utils/formatMoney';
import { getWeiZhiMC, getYongHuXM } from '@/system/utils/local-cache';
import { add } from '@/system/utils/mathComputed';

import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinPH from '@/components/YaoKu/BizYaoPinPH';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  AddBaoSunDan,
  GetBaoSunDXQXX,
  JiZhangBaoSunDan,
  UpdateBaoSunDan,
} from '@/service/yaoPinYK/yaoKuBS';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { printByUrl } from '@/system/utils/print';
const ziDian = {
  YP0052: 'baoSunLXOptions',
  YP0056: 'baoSunYYOptions',
};

export default {
  name: 'xinZengBSD',
  inject: ['viewManager'],
  mixins: [columnMixin],
  // props: {
  //   id: ''
  // },
  data() {
    return {
      id: '',
      tableBodyEle: '',
      dingWeiYPIndex: null,
      kuaiSuDWYP: '',
      danJuHao: '',
      beiZhu: '',
      zhiDanRXM: '',
      zhiDanSJ: '',
      yaoPinZS: 0,
      jinJiaJE: 0,
      lingShouJE: 0,
      lingChaJBZ: 0,
      mianFeiYPBZ: 0,
      zengPinBZ: 0,
      xiaoShuDianWS: 3,
      columns: [
        {
          prop: 'xunHao',
          label: '序号',
          width: 70,
          control: true,
          align: 'center',
          formatter: (row, column, cellValue, index) => {
            return index + 1;
          },
          labelClassName: 'set-Icon',
        },
        {
          type: 'selection',
          width: 40,
          showOverflowTooltip: false,
          selectable: (row, index) => {
            return this.data.length !== index + 1;
          },
        },
        {
          prop: 'yaoPinLXMC',
          label: '药品类型',
          type: 'text',
          width: 85,
        },
        {
          slot: 'yaoPinMCGG',
          prop: 'yaoPinXX',
          label: '药品名称与规格',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            if (row.yaoPinMC) {
              return row.yaoPinMC + ' ' + row.yaoPinGG;
            } else {
              return '';
            }
          },
          minWidth: 301,
          endMode: 'custom',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          type: 'text',
          width: 178,
          showOverflowTooltip: true,
          field: true,
        },
        {
          prop: 'baoZhuangDW',
          field: true,
          label: '单位',
          type: 'text',
          width: 60,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          field: true,
          type: 'text',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          slot: 'baoSunSL',
          prop: 'baoSunSL',
          label: '报损数量',
          field: true,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
          startMode: 'click',
          endMode: 'custom',
          width: 100,
        },
        {
          prop: 'jinJia',
          label: '进价',
          type: 'text',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return formatMoney(cellValue, this.isZhongYaoKXS);
          },
          field: true,
          fieldDisabled: true,
        },
        {
          slot: 'jinJiaJE',
          label: '进价金额',
          type: 'text',
          align: 'right',
          width: 100,
          field: true,
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          type: 'text',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(cellValue, this.isZhongYaoKXS);
          },
          field: true,
          fieldDisabled: true,
        },
        {
          slot: 'lingShouJE',
          label: '零售金额',
          type: 'text',
          align: 'right',
          width: 110,
          field: true,
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          type: 'text',
          align: 'right',
          width: 110,
          field: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          type: 'text',
          width: 114,
          formatter: (row, column, cellValue, index) => {
            return yaoKuZDJZTimeShow(cellValue);
          },
          field: true,
        },
        // {
        //   prop: 'lingChaJBZ',
        //   label: '零差/加价',
        //   type: 'text',
        //   width: 110,
        //   formatter: (row, column, cellValue, index) => {
        //     let val = '';
        //     switch (cellValue + '') {
        //       case '0':
        //         val = '普通';
        //         break;
        //       case '1':
        //         val = '零差价';
        //         break;
        //       case '2':
        //         val = '加价';
        //         break;
        //     }
        //     return val;
        //   },
        // },
        // {
        //   prop: 'mianFeiYPBZ',
        //   label: '免',
        //   type: 'text',
        //   width: 50,
        //   align: 'center',
        //   showOverflowTooltip: false,
        //   render: (h, { row }) => {
        //     if (row.mianFeiYPBZ == 1) {
        //       return h('i', {
        //         class: 'iconfont icongou',
        //         style: {
        //           color: '#1e88e5',
        //         },
        //       });
        //     }
        //   },
        // },
        // {
        //   prop: 'zengPinBZ',
        //   label: '赠',
        //   type: 'text',
        //   width: 50,
        //   align: 'center',
        //   showOverflowTooltip: false,
        //   render: (h, { row }) => {
        //     if (row.zengPinBZ == 1) {
        //       return h('i', {
        //         class: 'iconfont icongou',
        //         style: {
        //           color: '#1e88e5',
        //         },
        //       });
        //     }
        //   },
        // },
        {
          slot: 'baoSunYY',
          prop: 'baoSunYYDM',
          label: '报损原因',
          formatter: (row, column, cellValue, index) => {
            if (cellValue) {
              row.baoSunYYMC = this.baoSunYYOptions.find(
                (x) => x.biaoZhunDM === cellValue,
              )?.biaoZhunMC;
            }
            return row.baoSunYYMC;
          },
          startMode: 'click',
          endMode: 'custom',
          width: 110,
          field: true,
        },
        // {
        //   slot: 'baoSunLXDM',
        //   prop: 'baoSunLXDM',
        //   label: '报损类型',
        //   formatter: (row, column, cellValue, index) => {
        //     row.baoSunLXMC = this.baoSunLXOptions.find(
        //       (x) => x.biaoZhunDM === cellValue,
        //     )?.biaoZhunMC;
        //     return row.baoSunLXMC;
        //   },
        //   startMode: 'click',
        //   endMode: 'custom',
        //   width: 110,
        // },
      ],
      data: [],
      resetData: [], // 原始数据
      showDefaultOperate: false,
      loading: false,
      baoSunLXOptions: [],
      baoSunYYOptions: [],
      isZhongYaoKXS: 5,
    };
  },
  watch: {
    data: {
      handler(val) {
        let jinJiaJEHJ = 0;
        let lingShouJEHJ = 0;
        val.forEach((item) => {
          jinJiaJEHJ = add(jinJiaJEHJ, item.jinJiaJE);
          lingShouJEHJ = add(lingShouJEHJ, item.lingShouJE);
        });
        this.jinJiaJE = jinJiaJEHJ;
        this.lingShouJE = lingShouJEHJ;
        this.yaoPinZS = val.length - 1;
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    this.init();
    let xiaoShu = 0;
    try {
      xiaoShu = await GetCanShuZhi({
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '3', //0表示关闭，1表示开启
        gongNengID: '0',
      });
    } finally {
      this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 3 : xiaoShu;
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  computed: {
    editTableDisabled() {
      if (this.data.length === 0) return false;
      else if (this.data[this.data.length - 1].jiaGeID !== '') return false;
      return true;
    },
  },
  methods: {
    async init() {
      this.loading = true;
      try {
        //字典初始化
        await getYaoPinShuJuYZYList(Object.keys(ziDian)).then((res) => {
          res.forEach((item) => {
            const name = ziDian[item.shuJuYLBID];
            this[name] = item.zhiYuList;
          });
        });
        this.id = this.$route.query.id ? this.$route.query.id : '';
        if (this.id) {
          await GetBaoSunDXQXX({ baoSunDID: this.id }).then((res) => {
            this.resetData = cloneDeep(res);
            res.baoSunDMXList.forEach((item) => {
              item.yaoPinXX = {
                guiGeID: item.guiGeID,
                yaoPinMC: item.yaoPinMC,
                yaoPinGG: item.yaoPinGG,
                jiaGeID: item.jiaGeID,
                chanDiMC: item.chanDiMC,
                kuCunSL: item.kuCunSL,
                baoZhuangDW: item.baoZhuangDW,
                danJia: item.danJia,
                yaoPinLXDM: item.yaoPinLXDM,
                yaoPinLXMC: item.yaoPinLXMC,
              };
              item.baoSunLXDM = '0';
              item.baoSunLXMC = '普通';
            });
            this.data = res.baoSunDMXList;
            this.beiZhu = res.beiZhu;
            this.zhiDanRXM = res.zhiDanRXM;
            this.zhiDanSJ = res.zhiDanSJ;
            this.yaoPinZS = res.baoSunDMXList.length;
            this.jinJiaJE = res.baoSunDMXList.reduce(
              (total, item) => total + item.jinJiaJE,
              0,
            );
            this.lingShouJE = res.baoSunDMXList.reduce(
              (total, item) => total + item.lingShouJE,
              0,
            );
          });
        } else {
          this.zhiDanRXM = getYongHuXM();
          this.zhiDanSJ = dayjs().format('YYYY-MM-DD');
        }
        this.addRow();
      } finally {
        this.loading = false;
      }
    },
    async handleTableYaoPinDWChange(data, row, index) {
      if (data) {
        if (data.kuCunSL !== 0) {
          Object.assign(row, data);

          row.yaoPinMC = data.yaoPinMC;
          row.yaoPinGG = data.yaoPinGG;
          row.yaoPinXX = {
            guiGeID: data.guiGeID,
            yaoPinMC: data.yaoPinMC,
            yaoPinGG: data.yaoPinGG,
            jiaGeID: data.jiaGeID,
            chanDiMC: data.chanDiMC,
            kuCunSL: data.kuCunSL,
            baoZhuangDW: data.baoZhuangDW,
            danJia: data.danJia,
            yaoPinLXDM: data.yaoPinLXDM,
            yaoPinLXMC: data.yaoPinLXMC,
          };
          row.yaoPinZC = data.yaoPinMC + ' ' + data.yaoPinGG;
          row.jiaGeID = data.jiaGeID;
          row.guiGeID = data.guiGeID;
          row.danJia = data.danJia;
          row.chanDiMC = data.chanDiMC;
          row.kuCunSL = data.kuCunSL;
          row.baoZhuangDW = data.baoZhuangDW;

          this.$refs.bizYaoPinPH
            .show({
              yaoPinMC: data.yaoPinMC,
              jiaGeID: data.jiaGeID,
            })
            .then((res) => {
              let isRepeat = this.data.some((item, i) => {
                //数据响应，匹配到当前数据不做处理
                return (
                  item.jiaGeID == res.jiaGeID &&
                  item.shengChanPH == res.shengChanPH &&
                  index != i
                );
              });
              if (isRepeat) {
                this.$message({
                  type: 'warning',
                  message: '数据重复，请重新输入！',
                });
                this.resetTableRow(row);
                return;
              }
              Object.assign(row, res);
              if (index === this.data.length - 1) this.addRow();
              this.$refs.editable.toNext(4 * index);
            })
            .catch((e) => {
              this.resetTableRow(row);
            });
        } else {
          this.$message({
            type: 'warning',
            message: '该药品库存数量为空，请重新输入！',
          });
          this.resetTableRow(row);

          this.$nextTick(() => {
            row.yaoPinXX = {};
          });
        }
      }
    },
    handleChangeYaoPinDWSR(data) {
      if (!data) {
        // 清空定位组件时 去掉定位样式
        if (this.tableBodyEle) {
          let dingWeiEle = this.tableBodyEle.querySelectorAll(
            `.mediinfo-vela-yaoku-web-base-table__row`,
          )[this.dingWeiYPIndex];
          dingWeiEle.className = dingWeiEle.className.replace(
            this.prefixClass('dingwei-bg'),
            '',
          );
          this.dingWeiYPIndex = null;
        }
        return;
      }
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(
          `#baoSunTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
        );
      }
      // 去掉上一次定位样式
      if (this.dingWeiYPIndex) {
        let dingWeiEle = this.tableBodyEle.querySelectorAll(
          `.mediinfo-vela-yaoku-web-base-table__row`,
        )[this.dingWeiYPIndex];
        dingWeiEle.className = dingWeiEle.className.replace(
          this.prefixClass('dingwei-bg'),
          '',
        );
        this.dingWeiYPIndex = null;
      }
      if (data) {
        this.$refs.bizYaoPinPH
          .show({
            yaoPinMC: data.yaoPinMC,
            jiaGeID: data.jiaGeID,
          })
          .then((res) => {
            this.dingWeiYPIndex = this.data.findIndex(
              (x) =>
                x.jiaGeID === data.jiaGeID && x.shengChanPH === res.shengChanPH,
            );
            if (this.dingWeiYPIndex == -1) {
              this.$message({
                type: 'warning',
                message: `未找到该药品!`,
              });
              return;
            }
            let dingWeiEle = this.tableBodyEle.querySelectorAll(
              `.mediinfo-vela-yaoku-web-base-table__row`,
            )[this.dingWeiYPIndex];
            let height = this.dingWeiYPIndex * dingWeiEle.clientHeight;
            this.tableBodyEle.scrollTop = height;
            dingWeiEle.className =
              dingWeiEle.className !== ''
                ? dingWeiEle.className + ' ' + this.prefixClass('dingwei-bg')
                : this.prefixClass('dingwei-bg');
          });
      }
    },
    //重置当前row
    resetTableRow(row) {
      for (let key in row) {
        row[key] = null;
      }
      row.kuCunSL = 1;
      row.baoSunSL = 1;
    },
    checkBaoSunSL(row, rowIndex) {
      row.kuCunSL = row.kuCunSL ? Number(row.kuCunSL) : 0;
      row.baoSunSL = row.baoSunSL ? Number(row.baoSunSL) : 0;
      if (row.baoSunSL > row.kuCunSL) {
        row.baoSunSL = 1;
        this.$message({
          type: 'warning',
          message: `报损数量不能大于库存数量！`,
        });
        this.$refs.editable.toNext(4 * rowIndex);
      }
      if (row.baoSunSL <= 0) {
        row.baoSunSL = 1;
        this.$message({
          type: 'warning',
          message: `报损数量必须大于零！`,
        });
        this.$refs.editable.toNext(4 * rowIndex);
      }
      row.jinJiaJE = row.jinJia * row.baoSunSL;
      row.lingShouJE = row.lingShouJia * row.baoSunSL;
    },
    handleTableEnter({ length, activeIndex, callback }) {
      if ((activeIndex + 1) % length == 0 && !this.editTableDisabled) {
        this.addRow();
        callback({});
        return;
      }
      callback();
    },
    addRow() {
      this.data.push({
        yaoPinMCGG: '',
        zuZhiJGID: '',
        zuZhiJGMC: '',
        baoSunDID: '',
        jiaGeID: '',
        yaoPinMC: '',
        yaoPinGG: '',
        baoZhuangDW: '',
        zhangBuLBID: '',
        zhangBuLBMC: '',
        chanDiID: '',
        chanDiMC: '',
        jiXingID: '',
        jiXingMC: '',
        jinJia: '',
        jinJiaJE: 0,
        piFaJia: null,
        piFaJE: null,
        lingShouJia: '',
        lingShouJE: 0,
        shengChanPH: '',
        yaoPinXQ: '',
        kuCunSL: 1,
        baoSunLXDM: '0',
        // this.baoSunLXOptions.length > 0
        //   ? this.baoSunLXOptions[0].biaoZhunDM
        //   : '',
        baoSunLXMC: '普通',
        // this.baoSunLXOptions.length > 0
        //   ? this.baoSunLXOptions[0].biaoZhunMC
        //   : '',
        baoSunSL: 1,
        // baoSunYY: '',
        baoSunYYDM:
          this.baoSunYYOptions.length > 0
            ? this.baoSunYYOptions[0].biaoZhunDM
            : '',
        baoSunYYMC:
          this.baoSunYYOptions.length > 0
            ? this.baoSunYYOptions[0].biaoZhunMC
            : '',
        shunXuHao: 0,
        id: '',
      });
    },
    handleSaveAndJiZhang() {
      let nowData = cloneDeep(this.data);
      nowData.length = nowData.length - 1; // 保存时去掉最后的空行
      if (nowData.length == 0) {
        this.$message({
          type: 'warning',
          message: '报损单列表为空！',
        });
        return;
      }
      //判断必填项空值
      let nullIndex = nowData.findIndex((item) => {
        return !item.jiaGeID || item.baoSunSL <= 0;
      });
      if (nullIndex > -1) {
        this.$message({
          type: 'error',
          message: `第${nullIndex + 1}条,药品名称或报损数量为空`,
        });
        return;
      }
      this.loading = true;
      nowData.forEach((x, index) => {
        delete x.yaoPinXX;
        x.shunXuHao =
          index === 0 ? x.shunXuHao + 1 : nowData[index - 1].shunXuHao + 1;
      });
      // 修改
      if (this.id) {
        const param = {
          beiZhu: this.beiZhu,
          addBaoSunDMXList: [],
          updateBaoSunDMXList: [],
          zuoFeiBaoSunDMXList: [],
          id: this.id,
        };
        //差集
        param.addBaoSunDMXList = [...nowData].filter((x) => x.id === '');
        let resetDataId = this.resetData.baoSunDMXList.map((i) => i.id);
        let nowDataId = new Set(nowData.map((i) => i.id));
        param.zuoFeiBaoSunDMXList = [...resetDataId].filter(
          (x) => !nowDataId.has(x),
        );
        // this.resetData.baoSunDMXList.forEach(x =>
        //   [...nowData].every(y => {
        //     if (y.id && y.id !== x.id) {
        //       param.zuoFeiBaoSunDMXList.push(y.id)
        //     }
        //   })
        // )
        //交集、判断是否修改
        let editData = [...nowData].filter((x) =>
          [...this.resetData.baoSunDMXList].some((y) => y.id === x.id),
        );
        if (editData) {
          editData.forEach((item) => {
            if (
              !isEqual(
                item,
                this.resetData.baoSunDMXList.find((o) => o.id === item.id),
              )
            ) {
              param.updateBaoSunDMXList.push(item);
            }
          });
        }
        let caoZuoZT = 0; // 操作状态
        UpdateBaoSunDan(param)
          .then((res) => {
            caoZuoZT = 1;
            //this.handlePrint(this.id)
            return JiZhangBaoSunDan({ baoSunDID: this.id });
          })
          .then((res) => {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.loading = false;
            this.handleCloseCurrentTab(2);
          })
          .catch((e) => {
            if (caoZuoZT == 1) {
              // 保存成功，记账失败的情况
              this.loading = false;
              this.handleCloseCurrentTab(1);
              // this.$message({
              //   message: '保存成功，记账失败' + e,
              //   type: 'error'
              // })
              // MessageBox({
              //   title: '系统消息',
              //   type: 'error',
              //   message: '保存成功，记账失败' + e,
              //   confirmButtonText: '我知道了',
              // });
            } else {
              // this.$message({
              //   message: '操作失败' + e,
              //   type: 'error'
              // })
              // console.log(e);
              // MessageBox({
              //   title: '系统消息',
              //   type: 'error',
              //   message: '操作失败' + e,
              //   confirmButtonText: '我知道了',
              // });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 新增
        const param = {
          beiZhu: this.beiZhu,
          baoSunDMXList: nowData,
        };
        let caoZuoZT = 0; // 操作状态
        AddBaoSunDan(param)
          .then((res) => {
            caoZuoZT = 1;
            //this.handlePrint(res)
            return JiZhangBaoSunDan({ baoSunDID: res });
          })
          .then((res) => {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.loading = false;
            this.handleCloseCurrentTab(2);
          })
          .catch((e) => {
            if (caoZuoZT == 1) {
              // 保存成功，记账失败的情况
              this.loading = false;
              this.handleCloseCurrentTab(1);
              // this.$message({
              //   message: '保存成功，记账失败' + e,
              //   type: 'error'
              // })
              // MessageBox({
              //   title: '系统消息',
              //   type: 'error',
              //   message: '保存成功，记账失败' + e,
              //   confirmButtonText: '我知道了',
              // });
              // console.log(e);
            } else {
              // this.$message({
              //   message: '操作失败' + e,
              //   type: 'error'
              // })
              // console.log(e);
              // MessageBox({
              //   title: '系统消息',
              //   type: 'error',
              //   message: '操作失败' + e,
              //   confirmButtonText: '我知道了',
              // });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    handleSave() {
      let nowData = cloneDeep(this.data);
      nowData.length = nowData.length - 1; // 保存时去掉最后的空行
      if (nowData.length == 0) {
        this.$message({
          type: 'warning',
          message: '报损单列表为空！',
        });
        return;
      }
      //判断必填项空值
      let nullIndex = nowData.findIndex((item) => {
        return !item.jiaGeID || item.baoSunSL <= 0;
      });
      if (nullIndex > -1) {
        this.$message({
          type: 'error',
          message: `第${nullIndex + 1}条,药品名称或报损数量为空`,
        });
        return;
      }
      this.loading = true;
      nowData.forEach((x, index) => {
        delete x.yaoPinXX;
        x.shunXuHao =
          index === 0 ? x.shunXuHao + 1 : nowData[index - 1].shunXuHao + 1;
      });
      // 修改
      if (this.id) {
        const param = {
          beiZhu: this.beiZhu,
          addBaoSunDMXList: [],
          updateBaoSunDMXList: [],
          zuoFeiBaoSunDMXList: [],
          id: this.id,
        };
        //差集
        param.addBaoSunDMXList = [...nowData].filter((x) => x.id === '');
        let resetDataId = this.resetData.baoSunDMXList.map((i) => i.id);
        let nowDataId = new Set(nowData.map((i) => i.id));
        param.zuoFeiBaoSunDMXList = [...resetDataId].filter(
          (x) => !nowDataId.has(x),
        );
        // this.resetData.baoSunDMXList.forEach(x =>
        //   [...nowData].every(y => {
        //     if (y.id && y.id !== x.id) {
        //       param.zuoFeiBaoSunDMXList.push(y.id)
        //     }
        //   })
        // )
        //交集、判断是否修改
        let editData = [...nowData].filter((x) =>
          [...this.resetData.baoSunDMXList].some((y) => y.id === x.id),
        );
        if (editData) {
          editData.forEach((item) => {
            if (
              !isEqual(
                item,
                this.resetData.baoSunDMXList.find((o) => o.id === item.id),
              )
            ) {
              param.updateBaoSunDMXList.push(item);
            }
          });
        }
        UpdateBaoSunDan(param)
          .then(async (res) => {
            this.$message({
              message: '保存成功',
              type: 'success',
            });
            //await this.handlePrint(this.id)
            this.handleCloseCurrentTab(1);
          })
          .catch((e) => {
            // this.$message({
            //   message: '保存失败' + e,
            //   type: 'error'
            // })
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: '保存失败' + e,
              confirmButtonText: '我知道了',
            });
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 新增
        const param = {
          beiZhu: this.beiZhu,
          baoSunDMXList: nowData,
        };
        AddBaoSunDan(param)
          .then(async (res) => {
            this.$message({
              message: '保存成功',
              type: 'success',
            });
            //await this.handlePrint(res)
            this.handleCloseCurrentTab(1);
          })
          .catch((e) => {
            // this.$message({
            //   message: '保存失败' + e,
            //   type: 'error'
            // })
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: '保存失败' + e,
              confirmButtonText: '我知道了',
            });
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    handleDel() {
      const rows = this.$refs.editable.invokeTableMethod('getAllCheckedRows');
      if (rows.length <= 0) {
        this.$message({
          message: '请勾选需要删除的行',
          type: 'warning',
        });
        return;
      }
      MdMessageBox.confirm('是否确定删除', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        rows.forEach((item) => {
          let rowIndex = findIndex(this.data, item);
          this.$refs.editable.removeRow(rowIndex);
        });
      });
    },
    async handleCloseCurrentTab(tag) {
      eventBus.$emit('handleBaoSunFetch');
      const closeTabKey = cloneDeep(
        this.$route.query.id || this.viewManager.currentPage.name,
      );
      await this.$router.replace({
        path: '/YaoKuBS',
        query: {
          tag: tag,
        },
      });
      this.viewManager.close(closeTabKey);
    },
    async handlePrint(id) {
      try {
        this.loading = true;
        await printByUrl('YKXT002', { id: id });
        MdMessage.success('打印成功！');
      } catch (e) {
        // Message.error(e.message || '打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message || '打印失败！',
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
      }
    },
    handleVisibleChange(val, cellRef) {
      if (!val) {
        setTimeout(() => {
          cellRef.endEdit();
        }, 100);
      }
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
    'biz-yaopinph': BizYaoPinPH,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-description {
  display: flex;
  justify-content: space-between;
  &-item {
    line-height: 20px;
    min-height: 20px;
    font-size: 14px;
    color: #333;
    padding: 5px 0;
    margin-right: 4px;
    &__label {
      color: #aaa;
      margin-left: 8px;
    }
    &__content {
      padding-left: 5px;
      &.#{$md-prefix}-fontWeight {
        font-weight: bold;
      }
      .#{$md-prefix}-content-color {
        color: #aaa;
        font-weight: normal;
      }
    }
  }
}
</style>
<style lang="scss" scope>
.set-Icon {
  .#{$md-prefix}-table-set-icon {
    padding-top: 4px !important;
  }
}
.#{$md-prefix}-baosundan-page-wrap {
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  background-color: #e4e6e9;
  .#{$md-prefix}-baosundan-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-xinZengBSD-title {
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      height: 46px;
      // : #edf6fd;
      // @include md-def('background-color', 'color-1');
      background-color: rgb(var(--md-color-1));
      margin-bottom: 8px;
      .#{$md-prefix}-title__left {
        display: flex;
        align-items: center;
        margin: 4px 8px;
        border-radius: 4px;
        .#{$md-prefix}-danjumc {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 8px;
          line-height: 30px;
          font-size: 16px;
          font-weight: bold;
          // @include md-def('color', 'color-8');
          color: rgb(var(--md-color-8));
          i {
            font-size: 18px;
          }
        }
        /* .#{$md-prefix}-danjuhao-input {
          width: 180px;
        } */
      }
      .#{$md-prefix}-title__right {
        padding: 8px 0;
        .#{$md-prefix}-right-12 {
          margin-right: 12px;
        }
      }
    }
    .#{$md-prefix}-search-bar {
      flex-shrink: 0;
      height: 30px;
      margin-bottom: 8px;
      .#{$md-prefix}-form-item--show-status-icon {
        padding-right: 8px;
      }
    }
    .#{$md-prefix}-container__alias {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      padding: 0 8px;

      ::v-deep .#{$md-prefix}-table-set-icon {
        padding-top: 4px !important;
      }
      .#{$md-prefix}-editable {
        flex: 1;
        min-height: 0;
        ::v-deep .#{$md-prefix}-table-set-wrapper {
          background-color: red;
          .#{$md-prefix}-table-set-icon {
            padding-top: 4px !important;
          }
        }
        .#{$md-prefix}-dingwei-bg {
          td {
            background: #e2efff;
          }
        }
      }
      .#{$md-prefix}-table-addbtn {
        display: block;
        margin: 8px auto;
      }
    }
  }
}

.#{$md-prefix}-input-number {
  ::v-deep .#{$md-prefix}-input {
    &__inner {
      padding-left: 0 !important;
    }
  }
}
</style>
