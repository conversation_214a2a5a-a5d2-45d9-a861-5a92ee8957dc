<template>
  <md-drawer
    direction="rtl"
    v-model="drawer"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    ref="baoSunDanDrawer"
    :modalClass="prefixClass('baosundandrawer')"
    @closed="closeDrawer"
    size="75%"
  >
    <div
      v-loading="loading"
      :loading-text="loadingText"
      :class="prefixClass('drawer')"
    >
      <div :class="prefixClass('bsdtitle')">
        <span :class="prefixClass('title-left')">
          {{ title }}
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <md-button
              v-show="type === 1"
              type="primary"
              noneBg
              @click="handleJiZhang"
              ><i
                class="iconfont iconedit"
                :class="prefixClass('icon-right-4')"
              ></i
              >记账</md-button
            >
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content__alias')">
        <div :class="prefixClass('content-zhuangtai')">
          <md-descriptions
            direction="horizontal"
            label-width="50px"
            label-align="left"
          >
            <md-descriptions-item label="备注：" :column-percent="98">{{
              beiZhu
            }}</md-descriptions-item>
          </md-descriptions>
        </div>
        <md-table
          :columns="baoSunDanColumns"
          :data="baoSunDanData"
          height="100%"
          :class="prefixClass('baosun-table')"
        >
          <template v-slot:yaoPinMCGG="{ row }">
            {{ row.yaoPinMC + ' ' + row.yaoPinGG }}
          </template>
        </md-table>
        <div :class="prefixClass('description')" style="flex-shrink: 0">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">{{
              zhiDanRXM
            }}</span>
            <span :class="prefixClass('description-item__content')">{{
              zhiDanSJ
            }}</span>
            <span
              v-if="type == 2"
              :class="prefixClass('description-item__label')"
              >记账:</span
            >
            <span
              v-if="type == 2"
              :class="prefixClass('description-item__content')"
              >{{ jiZhangRXM + ' ' + jiZhangSJ }}</span
            >
          </div>
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ yaoPinSL
              }}<span :class="prefixClass('content-color')">种药品</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >合计 进价金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ Number(jinJiaJE).toFixed(3)
              }}<span :class="prefixClass('content-color')">元</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >零售金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ Number(lingShouJE).toFixed(3)
              }}<span :class="prefixClass('content-color')">元</span></span
            >
          </div>
        </div>
      </div>

      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT002'"
        :fileName="'药库报损单'"
        :title="'药库报损单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetBaoSunDMXList } from '@/service/yaoPinYK/yaoKuBS';
import eventBus from '@/system/utils/eventbus';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { printByUrl } from '@/system/utils/print';
import { MdMessageBox, MdMessage } from '@mdfe/medi-ui';
import tableData from '../tableData';
export default {
  name: 'BaoSunDrawer',
  data() {
    return {
      drawer: false,
      loading: false,
      params: {},
      loadingText: '正在加载中...',
      title: '',
      type: 1, // 默认1 1-待受理 2-已受理
      baoSunDID: '',
      beiZhu: '',
      zhiDanRXM: '',
      zhiDanSJ: '',
      jiZhangRXM: '',
      jiZhangSJ: '',
      yaoPinSL: 0,
      jinJiaJE: 0,
      lingShouJE: 0,
      baoSunDanColumns: [],
      baoSunDanData: [],
    };
  },
  mounted() {
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    //打开
    openDrawer(options, type) {
      this.loading = true;
      this.title = `详情 - ${options.danJuHao}`;
      this.baoSunDID = options.baoSunDID;
      this.beiZhu = options.beiZhu;
      this.zhiDanRXM = options.zhiDanRXM ? options.zhiDanRXM : '';
      this.zhiDanSJ = yaoKuZDJZTimeShow(options.zhiDanSJ);
      this.jiZhangRXM = options.jiZhangRXM ? options.jiZhangRXM : '';
      this.jiZhangSJ = yaoKuZDJZTimeShow(options.jiZhangSJ);
      this.type = type ? type : 1;
      this.baoSunDanColumns =
        type == 2 ? tableData.yiJiZXQColumns : tableData.weiJiZXQColumns;
      this.drawer = true;

      GetBaoSunDMXList({ baoSunDID: options.baoSunDID })
        .then((res) => {
          this.baoSunDanData = res;
          this.yaoPinSL = this.baoSunDanData.length;
          this.jinJiaJE = this.baoSunDanData.reduce(
            (total, item) => total + item.jinJiaJE,
            0,
          );
          this.lingShouJE = this.baoSunDanData.reduce(
            (total, item) => total + item.lingShouJE,
            0,
          );
        })
        .catch((e) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `获取报损单详情失败！`,
            confirmButtonText: '我知道了',
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    handlerClose() {
      this.$emit('closed');
    },
    handleClickBodyCloseDrawer(e) {
      // if (!this.$refs.baoSunDanDrawer.$el.contains(e.target)) {
      //   this.closeDrawer()
      // }
    },
    // 记账
    handleJiZhang() {
      eventBus.$emit('handleJiZhangBaoSunDan', this.baoSunDID);
      this.drawer = false;
    },

    //预览
    async handleYuLan() {
      const params = {
        id: this.baoSunDID,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handlePrint() {
      try {
        this.loading = true;
        this.loadingText = '正在打印中...';
        await printByUrl('YKXT002', { id: this.baoSunDID });
        MdMessage.success('打印成功！');
      } catch (e) {
        // Message.error(e.message || '打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
        this.loadingText = '正在加载中...';
      }
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-bihuan-popper {
  .#{$md-prefix}-bihuan-content {
    display: flex;

    .#{$md-prefix}-bihuan-item {
      &__title {
        display: flex;
        width: 156px;
        height: 26px;
        color: #333333;

        .#{$md-prefix}-title-name {
          display: inline-block;
          width: 120px;
          height: 26px;
          padding-left: 8px;
          color: #333333;
          font-size: 14px;
          line-height: 26px;
          background:
            linear-gradient(135deg, transparent 0, #ade2ff 0) top left,
            linear-gradient(225deg, transparent 9px, #ade2ff 0) top right,
            linear-gradient(-45deg, transparent 9px, #ade2ff 0) bottom right,
            linear-gradient(45deg, transparent 0, #ade2ff 0) bottom left;
          background-color: #ade2ff;
          background-size: 50% 50%;
          background-repeat: no-repeat;

          &.without {
            background:
              linear-gradient(135deg, transparent 0, #ddd 0) top left,
              linear-gradient(225deg, transparent 9px, #ddd 0) top right,
              linear-gradient(-45deg, transparent 9px, #ddd 0) bottom right,
              linear-gradient(45deg, transparent 0, #ddd 0) bottom left;
            background-color: #ddd;
            background-size: 50% 50%;
            background-repeat: no-repeat;
          }
        }

        .#{$md-prefix}-title-xian {
          width: 36px;
          margin: auto 0;
          border-top: solid #ade2ff 1px;

          &.#{$md-prefix}-without {
            border-top: solid #ddd 1px;
          }
        }
      }

      &__time {
        margin-top: 4px;
        font-weight: 500;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
      }

      .#{$md-prefix}-description-top {
        margin-top: 4px;
        flex-direction: column;

        .#{$md-prefix}-description-item {
          padding: 0 0;
        }
      }
    }
  }
}

.#{$md-prefix}-description {
  display: flex;
  justify-content: space-between;

  &-item {
    line-height: 20px;
    min-height: 20px;
    font-size: 14px;
    color: #333;
    padding: 5px 0;

    &__content {
      padding-left: 5px;

      &.#{$md-prefix}-fontWeight {
        font-weight: bold;
      }

      .#{$md-prefix}-content-color {
        color: #aaa;
        font-weight: normal;
      }
    }
  }
}

.#{$md-prefix}-description {
  .#{$md-prefix}-description-item__label {
    color: #aaa;
    margin-left: 8px;
  }
}
.#{$md-prefix}-baosundandrawer {
  position: initial !important;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-baosundandrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;

  .#{$md-prefix}-drawer {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .#{$md-prefix}-bsdtitle {
      display: flex;
      justify-content: space-between;
      // background: #f0f5fb;
      // @include md-def('background-color', 'color-1');
      background-color: rgb(var(--md-color-1));
      height: 36px;
      line-height: 36px;
      flex-shrink: 0;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-right {
        .title-toolbar {
          margin-right: 8px;
        }

        .#{$md-prefix}-title-close {
          i {
            font-size: 14px;
            float: right;
            margin-right: 12px;
            margin-top: 11px;
            color: #aaaaaa;
            cursor: pointer;
          }
        }

        .#{$md-prefix}-icon-right-4 {
          margin-right: 4px;
        }
      }
    }

    .#{$md-prefix}-content__alias {
      flex: 1;
      display: flex;
      flex-direction: column;
      // margin-top: 10px;
      min-height: 0;
      padding: 0 8px 8px 8px;

      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }

      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }

      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
      }

      .#{$md-prefix}-baosun-table {
        flex: 1;
        height: 100%;
        min-height: 0;
        ::v-deep .#{$md-prefix}-editable-table__container {
          height: 100% !important;
        }
      }
    }
  }
}

.#{$md-prefix}-pos-bottom-direction {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 98%;
  justify-content: space-between;
}

::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}

::v-deep .#{$md-prefix}-descriptions-item {
  color: #222;
  font-size: getCssVar('font-2');
}
</style>
