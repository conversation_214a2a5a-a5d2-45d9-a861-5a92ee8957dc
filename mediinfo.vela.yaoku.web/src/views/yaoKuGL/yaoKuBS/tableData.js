// import { Get<PERSON>an<PERSON>hu<PERSON>hi } from '@/system/utils/canShu';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { formatMoney } from '@/system/utils/formatMoney';
// import { getWeiZhiMC } from '@/system/utils/local-cache';
import { MdInput } from '@mdfe/medi-ui';
// import { EditableTablePro } from '@mdfe/medi-ui-pro';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
// import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';

let jinJiaXSDW = '';
let lingShouXSDW = '';
let jinJiaJEXSDW = 2;
let lingShouJEXSDW = 2;
// let xiaoShuDianWS =2
const canShu = async () => {
  try {
    const res = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  } catch (error) {}
  //如果是中药库
  const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
  // 判断进价零售价是否设置了值，没有则赋默认值
  jinJiaXSDW = jinJiaXSDW ? jinJiaXSDW : xiaoShuDianWS;
  lingShouXSDW = lingShouXSDW ? lingShouXSDW : xiaoShuDianWS;
};
canShu();
export default {
  weiJiZhangColumns: [
    {
      slot: 'baoSunDH',
      label: '报损单',
      width: 118,
    },
    {
      label: '药品数',
      prop: 'yaoPinZS',
      align: 'right',
      width: 66,
    },
    {
      slot: 'yaoPinMCList',
      label: '药品明细',
    },
    {
      label: '制单日期',
      prop: 'zhiDanSJ',
      width: 160,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '制单人',
      prop: 'zhiDanRXM',
      width: 110,
    },
    {
      slot: 'operate',
      label: '操作',
      width: 120,
      fixed: 'right',
    },
  ],
  yiJiZhangColumns: [
    {
      slot: 'baoSunDH',
      label: '报损单',
      prop: 'baoSunDH',
      width: 118,
    },
    {
      label: '药品数',
      prop: 'yaoPinZS',
      align: 'right',
      width: 66,
    },
    {
      slot: 'yaoPinMCList',
      label: '药品明细',
      prop: 'yaoPinMCList',
    },
    {
      label: '制单日期',
      prop: 'zhiDanSJ',
      width: 160,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '制单人',
      prop: 'zhiDanRXM',
      width: 110,
    },
    {
      label: '记账日期',
      prop: 'jiZhangSJ',
      width: 160,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '记账人',
      prop: 'jiZhangRXM',
      width: 110,
    },
    // {
    //   slot: 'operate',
    //   label: '操作',
    //   prop: 'caoZuo',
    //   width: 84
    // }
  ],
  weiJiZXQColumns: [
    {
      prop: 'zhangBuLBMC',
      label: '',
      width: 34,
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.zhangBuLBMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 400,
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      width: 160,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 50,
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'baoSunSL',
      label: '报损数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'jinJia',
      label: '进价',
      align: 'right',
      width: 90,
      formatter(row, column, cellValue) {
        return formatMoney(cellValue, jinJiaXSDW);
      },
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return Number(cellValue).toFixed(jinJiaJEXSDW);
      },
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      align: 'right',
      width: 90,
      formatter(row, column, cellValue) {
        return formatMoney(cellValue, lingShouXSDW);
      },
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return Number(cellValue).toFixed(lingShouJEXSDW);
      },
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      width: 110,
    },
    {
      prop: 'yaoPinXQ',
      label: '药品效期',
      width: 110,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    // {
    //   prop: 'lingChaJBZ',
    //   label: '零差/加价',
    //   width: 110,
    //   formatter: (row, column, cellValue, index) => {
    //     let val = '';
    //     switch (cellValue + '') {
    //       case '0':
    //         val = '普通';
    //         break;
    //       case '1':
    //         val = '零差价';
    //         break;
    //       case '2':
    //         val = '加价';
    //         break;
    //     }
    //     return val;
    //   },
    // },
    // {
    //   prop: 'mianFeiYPBZ',
    //   label: '免',
    //   width: 50,
    //   align: 'center',
    //   showOverflowTooltip: false,
    //   render: (h, { row }) => {
    //     if (row.mianFeiYPBZ == 1) {
    //       return h('i', {
    //         class: 'iconfont icongou',
    //         style: {
    //           color: '#1e88e5',
    //         },
    //       });
    //     }
    //   },
    // },
    // {
    //   prop: 'zengPinBZ',
    //   label: '赠',
    //   width: 50,
    //   align: 'center',
    //   showOverflowTooltip: false,
    //   render: (h, { row }) => {
    //     if (row.zengPinBZ == 1) {
    //       return h('i', {
    //         class: 'iconfont icongou',
    //         style: {
    //           color: '#1e88e5',
    //         },
    //       });
    //     }
    //   },
    // },
    {
      prop: 'baoSunYYMC',
      label: '报损原因',
      width: 110,
    },
    // {
    //   prop: 'baoSunLXMC',
    //   label: '报损类型',
    //   width: 110,
    // },
  ],
  yiJiZXQColumns: [
    {
      prop: 'zhangBuLBMC',
      label: '',
      width: 34,
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.zhangBuLBMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 400,
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      width: 160,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 50,
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'baoSunSL',
      label: '报损数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'jinJia',
      label: '进价',
      align: 'right',
      width: 90,
      formatter(row, column, cellValue) {
        return formatMoney(cellValue, jinJiaXSDW);
      },
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return Number(cellValue).toFixed(jinJiaJEXSDW);
      },
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      align: 'right',
      width: 90,
      formatter(row, column, cellValue) {
        return formatMoney(cellValue, lingShouXSDW);
      },
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return Number(cellValue).toFixed(lingShouJEXSDW);
      },
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      width: 110,
    },
    {
      prop: 'yaoPinXQ',
      label: '药品效期',
      width: 110,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    // {
    //   prop: 'lingChaJBZ',
    //   label: '零差/加价',
    //   width: 110,
    //   formatter: (row, column, cellValue, index) => {
    //     let val = '';
    //     switch (cellValue + '') {
    //       case '0':
    //         val = '普通';
    //         break;
    //       case '1':
    //         val = '零差价';
    //         break;
    //       case '2':
    //         val = '加价';
    //         break;
    //     }
    //     return val;
    //   },
    // },
    // {
    //   prop: 'mianFeiYPBZ',
    //   label: '免',
    //   width: 50,
    //   align: 'center',
    //   showOverflowTooltip: false,
    //   render: (h, { row }) => {
    //     if (row.mianFeiYPBZ == 1) {
    //       return h('i', {
    //         class: 'iconfont icongou',
    //         style: {
    //           color: '#1e88e5',
    //         },
    //       });
    //     }
    //   },
    // },
    // {
    //   prop: 'zengPinBZ',
    //   label: '赠',
    //   width: 50,
    //   align: 'center',
    //   showOverflowTooltip: false,
    //   render: (h, { row }) => {
    //     if (row.zengPinBZ == 1) {
    //       return h('i', {
    //         class: 'iconfont icongou',
    //         style: {
    //           color: '#1e88e5',
    //         },
    //       });
    //     }
    //   },
    // },
    {
      prop: 'baoSunYYMC',
      label: '报损原因',
      width: 110,
    },
    // {
    //   prop: 'baoSunLXMC',
    //   label: '报损类型',
    //   width: 110,
    // },
  ],
  xinZengBSDColumns: [
    {
      type: 'selection',
      width: 50,
      showOverflowTooltip: false,
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      formatter: (row, column, cellValue, index) => {
        if (row.yaoPinMC) {
          return row.yaoPinMC + ' ' + row.yaoPinGG;
        } else {
          return '';
        }
      },
      width: 301,
      endMode: 'custom',
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      width: 178,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
      align: 'center',
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      component: MdInput,
      width: 74,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'baoSunSL',
      label: '报损数量',
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
      align: 'right',
      width: 74,
    },
    {
      prop: 'jinJia',
      label: '进价',
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
      align: 'right',
      width: 100,
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      align: 'right',
      width: 100,
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      align: 'right',
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
      width: 100,
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      align: 'right',
      width: 110,
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
      width: 110,
    },
    {
      slot: 'yaoPinXQ',
      label: '药品效期',
      formatter: (row, column, cellValue, index) => {
        return yaoKuZDJZTimeShow(cellValue);
      },
      width: 110,
    },
    // {
    //   prop: 'tiaoJiaSL',
    //   label: '零差/加价',
    //   component: Input,
    //   formatter: (row, column, cellValue, index) => {
    //     return cellValue;
    //   },
    //   width: 110,
    // },
    // {
    //   prop: 'tiaoJiaSL',
    //   label: '免',
    //   width: 50,
    // },
    // {
    //   prop: 'tiaoJiaSL',
    //   label: '赠',
    //   width: 50,
    // },
    {
      prop: 'baoSunYY',
      label: '报损原因',
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
      width: 110,
    },
    // {
    //   slot: 'baoSunLXDM',
    //   prop: 'baoSunLXDM',
    //   label: '报损类型',
    //   formatter: (row, column, cellValue, index) => {
    //     row.baoSunLXMC = this.baoSunLXOptions.find(
    //       (x) => x.baoSunLXDM === cellValue,
    //     );
    //     return row.baoSunLXMC;
    //   },
    //   startMode: 'click',
    //   endMode: 'custom',
    //   width: 110,
    // },
  ],
};
