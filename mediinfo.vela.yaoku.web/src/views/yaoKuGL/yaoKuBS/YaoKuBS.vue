<template>
  <div :class="prefixClass('shouliql-wrap')">
    <md-tabs v-model="activeName" :class="prefixClass('custom-tab-default')">
      <md-tab-pane
        v-for="tab in tabs"
        :name="tab.gongNengDM"
        :label="tab.mingCheng"
        lazy
        :key="tab.gongNengDM"
      >
        <component
          :is="tab.component"
          :active="activeName === tab.gongNengDM"
          :biaoQian="tab"
          :research="tab.research"
          :moKuaiDM="moKuaiDM"
          @add-extra="onAddExtra(tab, $event)"
          @go-to-tab="handleGoToTab"
        ></component>
      </md-tab-pane>
      <!-- <template #extra>
        <component :is="extra"></component>
      </template> -->
    </md-tabs>
    <baosun-drawer ref="baoSunDrawer"> </baosun-drawer>
  </div>
</template>

<script>
import WeiJiZhangVue from './WeiJiZhang.vue';
import YiJiZhangVue from './YiJiZhang.vue';
import { MdTabs, MdTabPane } from '@mdfe/medi-ui';
import baoSunDrawer from './components/BaoSunDrawer';

export default {
  name: 'YaoKuBS',
  provide() {
    return {
      $YaoKuBS: this,
    };
  },
  data() {
    return {
      activeName: 'WeiJiZhang',
      tabs: [
        {
          url: 'WeiJiZhang',
          gongNengDM: 'WeiJiZhang',
          mingCheng: '未记账',
          component: WeiJiZhangVue,
          extra: null,
        },
        {
          url: 'YiJiZhang',
          gongNengDM: 'YiJiZhang',
          mingCheng: '已记账',
          component: YiJiZhangVue,
          extra: null,
        },
      ],
      extra: null,
      moKuaiDM: 'WeiJiZhang',
    };
  },
  watch: {
    tabs: {
      handler() {
        this.extra = this.current?.extra;
      },
      deep: true,
    },
    activeName(activeName) {
      this.extra = this.current?.extra;
    },
    $route: {
      // 监听路由变化
      handler: function (to, from) {
        if (to.query.tag) {
          if (to.query.tag == 1) {
            this.activeName = 'WeiJiZhang';
          } else {
            this.activeName = 'YiJiZhang';
          }
        }
      },
    },
  },
  created() {
    // this.getTabs()
  },

  methods: {
    handleGoToTab() {
      this.activeName = 'YiJiZhang';
    },
  },
  components: {
    'md-tabs': MdTabs,
    'md-tab-pane': MdTabPane,
    'baosun-drawer': baoSunDrawer,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-shouliql-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0px;
  .#{$md-prefix}-custom-tab-default {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // background: #eaeff3;
    ::v-deep .#{$md-prefix}-tabs__header {
      margin-bottom: 8px;
      background: #fff;
    }
    ::v-deep .#{$md-prefix}-tabs__content {
      flex: 1;
      padding: 0 8px;
      padding-bottom: 8px;
    }
  }
}
::v-deep .#{$md-prefix}-tabs__extra {
  height: 33px;
  line-height: 33px;
}
::v-deep .#{$md-prefix}-data-table__pagination {
  height: 33px;
}
</style>
