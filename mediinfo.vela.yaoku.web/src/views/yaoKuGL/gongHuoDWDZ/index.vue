<template>
  <div :class="prefixClass('GongHuoDWDZ')">
    <div :class="prefixClass('GongHuoDWDZ-header')">
      <md-radio-group v-model="duiZhaoLX" border style="background-color: #fff">
        <md-radio :label="1">全部</md-radio>
        <md-radio :label="2">已对照</md-radio>
        <md-radio :label="3">未对照</md-radio>
      </md-radio-group>
      <span>
        <md-checkbox v-model="shangXiaJ" class="mr-8">上下级</md-checkbox>
        <md-button type="primary" @click="handlePiLiangDZ">批量对照</md-button>
      </span>
    </div>
    <div
      :class="prefixClass('GongHuoDWDZ-container')"
      v-loading="fenLeiLoading"
    >
      <div :class="prefixClass('GongHuoDWDZ-Aside')">
        <div class="Aside-title">
          <span class="Aside-title">
            <md-title label="医院目录"></md-title>
            <span class="shuoming">（单击列表根据单位名称模糊查询）</span>
          </span>

          <md-input
            v-model="danWeiMC"
            placeholder="单位名称"
            :suffix-icon="prefixClass('icon-seach cursor-pointer')"
            :class="prefixClass('procurement-input')"
          >
          </md-input>
        </div>
        <div class="Aside-scrollbar">
          <md-table
            :data="leftData"
            :columns="columnsLeft"
            highlight-current-row
            height="100%"
            ref="mdTablePro"
          >
          </md-table>
        </div>
      </div>
      <div :class="prefixClass('GongHuoDWDZ-main')">
        <div class="Aside-title">
          <span class="Aside-title">
            <md-title label="两定目录"></md-title>
            <span class="shuoming">（双击列表对照）</span>
          </span>
          <span>
            <md-button type="primary" :icon="prefixClass('icon-shuaxin')" noneBg
              >更新</md-button
            >
            <md-input
              v-model="jiGouMC"
              placeholder="机构名称"
              :suffix-icon="prefixClass('icon-seach cursor-pointer')"
              :class="prefixClass('procurement-input')"
            >
            </md-input>
          </span>
        </div>
        <div :class="prefixClass('GongHuoDWDZ-main-content')">
          <md-table
            :data="rightData"
            :columns="columnsRight"
            highlight-current-row
            height="100%"
            ref="mdTableRight"
          >
          </md-table>
        </div>
      </div>
    </div>
    <!-- <fenLeiXX ref="fenLeixx" @change="getTypeData"></fenLeiXX> -->
  </div>
</template>

<script>
import { useParams } from '@/system/utils/useParams';
import {
  MdCheckbox,
  MdInput,
  MdMessage,
  MdMessageBox,
  useNamespace,
} from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
export default {
  name: 'GongHuoDWDZ',
  data() {
    return {
      shangXiaJ: false,
      danWeiMC: '',
      jiGouMC: '',
      leftData: [],
      columnsLeft: [
        {
          label: '单位ID',
          prop: 'danWeiID',
          width: 100,
        },
        {
          label: '单位名称',
          prop: 'danWeiMC',
          width: 120,
        },
        {
          label: '社会统一信用代码',
          prop: 'chanDiMC',
        },
        {
          label: '机构编码',
          prop: 'jiGouDM',
          width: 120,
        },
        {
          label: '机构名称',
          prop: 'jiGouMC',
          width: 120,
        },
      ],
      rightData: [],
      columnsRight: [
        {
          label: '机构编码',
          prop: 'jiGouDM',
          width: 150,
        },
        {
          label: '机构名称',
          prop: 'jiGouMC',
          width: 150,
        },
        {
          label: '社会统一信用代码',
          prop: 'chanDiMC',
        },
      ],
      fenLeiLoading: false,
      duiZhaoLX: 1,
    };
  },
  async created() {},
  methods: {
    handlePiLiangDZ() {
      MdMessageBox.confirm(
        '将根据两定目录进行批量模糊对照，确定要进行操作吗？',
        '操作提醒！',
        {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
        },
      )
        .then(async () => {})
        .catch(() => {});
    },
  },
  components: {},
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-GongHuoDWDZ {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #edf6fd;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;
  }

  &-container {
    flex: 1;
    display: flex;
    min-height: 0;
    justify-content: space-between;
    background-color: #f0f2f5;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;
  }

  &-Aside {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    margin-right: getCssVar('spacing-3');
    overflow: hidden;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;

    .Aside-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .shuoming {
        color: #666;
      }
    }

    .Aside-scrollbar {
      flex: 1;
      min-height: 0;
      margin-top: getCssVar('spacing-3');

      &.empty {
        justify-content: center;
      }

      .type-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin-bottom: getCssVar('spacing-3');
        padding: 0 getCssVar('spacing-3');
        cursor: pointer;

        .name {
          flex: 1;
        }

        .edit {
          display: none;
        }

        &.active {
          background-color: getCssVar('color-2');
        }

        &:hover {
          background-color: getCssVar('color-1');

          .edit {
            display: block;
          }
        }
      }
    }
  }

  .#{$md-prefix}-procurement-input {
    width: 180px;
    margin-left: 4px;
  }

  &-main {
    flex: 1;
    min-height: 0;
    min-width: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;
    .Aside-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .shuoming {
        color: #666;
      }
    }
    &-content {
      flex: 1;
      min-height: 0;
      box-sizing: border-box;
      margin-top: getCssVar('spacing-3');
      ::v-deep .#{$namespace}-form-item--status-icon-inline {
        padding-right: unset;

        .#{$namespace}-form-item__content {
          justify-content: center;
        }
      }

      ::v-deep
        .#{$namespace}-table--edit
        .#{$namespace}-base-table__body
        .#{$namespace}-input__inner {
        border: unset;
      }

      ::v-deep .#{$namespace}-select .#{$namespace}-input__wrapper:hover {
        border-color: transparent;
      }
    }
  }

  &-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: getCssVar('spacing-3');
    background-color: getCssVar('color-1');
  }

  .mr-8 {
    margin-right: getCssVar('spacing-3');
  }
}
</style>
