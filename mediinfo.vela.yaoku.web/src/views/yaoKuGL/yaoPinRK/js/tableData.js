import { GetCanShuZhi } from '@/system/utils/canShu';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import dayjs from 'dayjs';
import { h } from 'vue';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
// import { XiaoShuDian } from './xiaoShuDian'
export default {
  data() {
    return {
      xiaoShuDian: 2,
      columns: [
        {
          slot: 'keyParams',
          label: '采购单号',
          minWidth: 120,
          showOverflowTooltip: false,
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 100,
          formatter: (row) => {
            return yaoKuZDJZTimeShow(row.zhiDanSJ);
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 110,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          width: 65,
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          formatter: (row) => {
            return Number(row.jinJiaJE).toFixed(this.xiaoShuDian);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDian);
          },
        },
        {
          prop: 'beiZhu',
          label: '备注',
          width: 150,
        },
      ],
      anFaPiaoHColumns: [
        {
          type: 'selection',
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          minWidth: 120,
          showOverflowTooltip: false,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          showOverflowTooltip: false,
        },
        {
          prop: 'jinJiaJE',
          label: '发票金额',
          minWidth: 120,
          showOverflowTooltip: false,
        },
        {
          prop: 'zhiDanSJ',
          label: '发票日期',
          minWidth: 120,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
      ],
      rightColumns: [
        {
          slot: 'selection',
          width: 46,
          align: 'center',
        },
        {
          label: '',
          prop: 'yaoPinLXMC',
          width: 35,
          // align: 'center',
          formatter: (row) => {
            if (row.isHeJi) return row.gongHuoDWMC;
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 100,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 200,
          showOverflowTooltip: true,
          formatter: (row) => {
            return row.yaoPinMC + ' ' + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 150,
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
        },
        {
          prop: 'shenQingSL',
          label: '申请数量',
          width: 85,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? Number(cellValue).toFixed(this.xiaoShuDian) : '';
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 90,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            if (cellValue === '合计') return cellValue;
            return Number(formatJiaGe(cellValue)).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 90,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return cellValue || cellValue == 0
              ? Number(formatJiaGe(cellValue)).toFixed(this.lingShouXSDW)
              : '';
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },
        {
          prop: 'kuCunSL',
          label: '药库库存',
          width: 85,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return !row.isHeJi
              ? Number(cellValue).toFixed(this.xiaoShuDian)
              : '';
          },
        },
        // {
        //   prop: 'quanYuanKC',
        //   label: '全院库存',
        //   width: 75,
        //   align: 'right'
        // },
        {
          prop: 'kuCunXX',
          label: '库存下限',
          width: 85,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return !row.isHeJi
              ? Number(cellValue).toFixed(this.xiaoShuDian)
              : '';
          },
        },
        {
          prop: 'kuCunSX',
          label: '库存上限',
          width: 85,
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return !row.isHeJi
              ? Number(cellValue).toFixed(this.xiaoShuDian)
              : '';
          },
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 110,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 110,
        },
      ],
      xinZengRKDColumns: [
        {
          type: 'selection',
          width: 40,
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 32,
          align: 'center',
          type: 'text',
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 120,
          slot: 'faPiaoHM',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 120,
          slot: 'faPiaoRQ',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 100,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          slot: 'yaoPinMCYGG',
          formatter: (row, column, cellValue, index) => {
            if (cellValue.yaoPinMC && cellValue.yaoPinGG)
              return cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG;
            return cellValue;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 48,
          type: 'text',
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 90,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0);
          },
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          width: 90,
          slot: 'ruKuSL',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          // headerAlign: 'center',
          align: 'right',
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          slot: 'jinJia',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.jinJiaXSDW);
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.lingShouXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0).toFixed(
              this.lingShouJEXSDW,
            );
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          slot: 'shengChanPH',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          showOverflowTooltip: false,
          slot: 'yaoPinXQ',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'zhuCeSB',
          label: '注册商标',
          width: 120,
          slot: 'zhuCeSB',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '有' : '无';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'chuChangJYHGD',
          label: '产品合格证',
          width: 120,
          slot: 'chuChangJYHGD',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'baoZhuangQK',
          label: '包装',
          width: 60,
          slot: 'baoZhuangQK',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '完整' : '不完整';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'waiGuanZL',
          label: '外观情况',
          width: 80,
          slot: 'waiGuanZL',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouJL',
          label: '验收结果',
          width: 120,
          slot: 'yanShouJL',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouRXM',
          label: '验收人',
          width: 120,
          type: 'text',
        },
        {
          prop: 'piZhunWH',
          label: '批准文号',
          width: 120,
          slot: 'piZhunWH',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'jinKouYPZH',
          label: '进口药品证号',
          width: 120,
          slot: 'jinKouYPZH',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          slot: 'baiFangWZ',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
      ],
      xinZengCHRKDColumns: [
        {
          label: '序号',
          type: 'index',
        },
        {
          type: 'selection',
          width: 40,
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 32,
          align: 'center',
          type: 'text',
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 100,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 240,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (cellValue.yaoPinMC && cellValue.yaoPinGG)
              return cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG;
            return cellValue;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 48,
          type: 'text',
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 90,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0).toFixed(this.xiaoShuDian);
          },
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          width: 90,
          slot: 'ruKuSL',
          previewSlot: 'ruKuSLYL',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          // headerAlign: 'center',
          align: 'right',
        },
        {
          prop: 'ruKuHKCSL',
          label: '入库后库存数量',
          width: 120,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.jinJiaXSDW);
          },
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          type: 'text',
          render: (h, params) => {
            return (
              <span style="color: #f12933;">
                {Number(params.row.jinJiaJE).toFixed(this.jinJiaJEXSDW)}
              </span>
            );
          },
        },
        {
          prop: 'lingChaJBZ',
          label: '零差/加价',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            switch (cellValue) {
              case 0:
                return '普通';
              case 1:
                return '零差价';
              case 2:
                return '加价';
              default:
                return '';
            }
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.lingShouXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          type: 'text',
          render: (h, params) => {
            return (
              <span style="color: #f12933;">
                {Number(params.row.lingShouJE).toFixed(this.lingShouJEXSDW)}
              </span>
            );
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          showOverflowTooltip: false,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'zhuCeSB',
          label: '注册商标',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '有' : '无';
          },
        },
        {
          prop: 'chuChangJYHGD',
          label: '产品合格证',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'baoZhuangQK',
          label: '包装',
          width: 60,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '完整' : '不完整';
          },
        },
        {
          prop: 'waiGuanZL',
          label: '外观情况',
          width: 80,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'yanShouJL',
          label: '验收结果',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'yanShouRXM',
          label: '验收人',
          width: 120,
          type: 'text',
        },
        {
          prop: 'piZhunWH',
          label: '批准文号',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'jinKouYPZH',
          label: '进口药品证号',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
      ],
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
    };
  },
  created() {
    this.handleXiaoShu();
  },
  methods: {
    async handleXiaoShu() {
      try {
        const res = await getKuFangSZList([
          'jinJiaJEXSDWS',
          'lingShouJEXSDWS',
          'lingShouJXSDWS',
          'jinJiaXSDWS',
        ]);
        if (res.length > 0) {
          res.forEach((el) => {
            if (el.xiangMuDM == 'jinJiaXSDWS') {
              this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
            } else if (el.xiangMuDM == 'lingShouJXSDWS') {
              this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
            } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
              this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
            } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
              this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
            }
          });
        }
      } catch (error) {}
      //如果是中药库
      const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
      // 判断进价零售价是否设置了值，没有则赋默认值
      this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
      this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
    },
  },
};
