<template>
  <md-tabs v-model="activeName" :class="prefixClass('tabs-views')">
    <md-tab-pane
      v-if="isShowRKZD"
      label="待入库"
      name="first"
      :class="prefixClass('content-bg')"
      @go-to-tab="handleGoToTab"
      lazy
    >
      <dairuku-page
        ref="dairuku"
        :duMaJDLZD="duMaJDLZD"
        :showPiHaoDialog="showPiHaoDialog"
      />
    </md-tab-pane>
    <md-tab-pane label="未记账" name="second" lazy>
      <weijizhang-page
        :isChongHong="isChongHong"
        :openId="openId"
        :lingShouJEXSDW="lingShouJEXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        @go-to-tab="handleGoToTab"
        ref="weijizhang"
      />
    </md-tab-pane>
    <md-tab-pane label="已记账" name="third" lazy>
      <yijizhang-page
        :isChongHong="isChongHong"
        :openId="openId"
        :lingShouJEXSDW="lingShouJEXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        ref="yijizhang"
      />
    </md-tab-pane>
  </md-tabs>
</template>

<script>
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import DaiRuKuPage from '@/views/yaoKuGL/yaoPinRK/components/DaiRuKuPage.vue';
import WeijizhangPage from '@/views/yaoKuGL/yaoPinRK/components/WeiJiZhangPage.vue';
import YijizhangPage from '@/views/yaoKuGL/yaoPinRK/components/YiJiZhangPage.vue';
export default {
  name: 'yaopinrk',
  data() {
    return {
      duMaJDLZD: '',
      isShowRKZD: false, //是否显示入库制单，只有中药库显示
      activeName: 'first', // 当前选中导航
      openId: '', // 进入页面时，路由处传入的id （跳转到该页，并显示该id的详情信息。）
      isChongHong: '0', // 是否是其他页面进入
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      showPiHaoDialog: '0', //入库制单是否显示批号弹窗
    };
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val.path === '/YaoPinRK') {
          let query = val.query;
          // 判断是否跳转到某一个导航（second未记账、third已记账）
          if (query && query.showType) {
            if (this.activeName === query.showType) {
              switch (query.showType) {
                case 'second':
                  if (this.$refs.weijizhang)
                    this.$refs.weijizhang.handleSearch();
                  break;
                case 'third':
                  if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch();
                  break;
              }
            }
            this.activeName = query.showType;
          }
          if (query.isChongHong) {
            this.isChongHong = query.isChongHong;
            this.openId = query.id;
          }
        }
      },
    },
    // tab切换时请求数据
    activeName: {
      handler: function (val) {
        switch (val) {
          case 'first':
            if (this.$refs.dairuku) this.$refs.dairuku.handleSearch();
            break;
          case 'second':
            if (this.$refs.weijizhang) this.$refs.weijizhang.handleSearch();
            break;
          case 'third':
            if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch();
            break;
        }
      },
    },
  },
  async mounted() {
    const arr = await getKuFangSZList([
      'yaoPinRKGNZDRKBQ',
      'yaoPinRKDDMJYPDLZD',
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
      'anCaiGDRKSFXYXZPHXQ',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'yaoPinRKGNZDRKBQ') {
          this.isShowRKZD = el.xiangMuZDM == 1 ? true : false;
        } else if (el.xiangMuDM == 'yaoPinRKDDMJYPDLZD') {
          this.duMaJDLZD = el.xiangMuZDM;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'anCaiGDRKSFXYXZPHXQ') {
          this.showPiHaoDialog = el.xiangMuZDM;
        }
      });
    }
    this.activeName = this.isShowRKZD ? 'first' : 'second';
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  // activated() {
  //   if (this.activeName === 'first' && this.$refs.dairuku)
  //     this.$refs.dairuku.handleSearch();
  // },
  methods: {
    //未记账跳转记账页签 yy
    handleGoToTab() {
      this.activeName = 'third';
    },
  },
  components: {
    'yijizhang-page': YijizhangPage,
    'weijizhang-page': WeijizhangPage,
    'dairuku-page': DaiRuKuPage,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  overflow: hidden;
}
::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
}
::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  min-height: 0;
  background: #eaeff3;
}
</style>
