<template>
  <div :class="prefixClass('weijizhang-box')" v-loading="pageLoading">
    <div :class="prefixClass('weijizhang-content-top')">
      <div :class="prefixClass('weijizhang-content-top-filters')">
        <md-date-picker-range-pro
          v-model="timeRange"
          style="margin-right: 8px; width: 250px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        >
        </md-date-picker-range-pro>

        <md-select
          v-model="query.chuRuKFSID"
          filterable
          style="margin-right: 8px; width: 135px"
          @clear="handleClear"
          @change="handleSelectChange($event, 'chuRuKFS')"
        >
          <md-option
            v-for="item in chuRuKFSOptions"
            :key="item.value"
            :label="item.chuRuKFSMC"
            :value="item.chuRuKFSID"
          />
        </md-select>
        <gonghuodw-select
          v-model="query.gongHuoDWID"
          @select-change="handleSearch"
        />
        <md-input
          v-model="query.RuKuDH"
          :class="prefixClass('filter-width')"
          placeholder="输入单据号搜索"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('input__icon icon-seach')"
            slot="suffix"
            @click="handleSearch"
          />
        </md-input>
        <biz-yaopindw
          v-model="yaoPinMCObj"
          placeholder="名称、输入码、别名、规格等关键字进行搜索"
          style="width: 340px; margin-left: 8px"
          @change="handleQueryYaoPinMC"
        >
        </biz-yaopindw>
      </div>
      <div :class="prefixClass('weijizhang-content-top-buttons')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          style="margin-left: auto"
          @click="handleSearch"
          >刷新</md-button
        >
        <md-button
          type="primary"
          :icon="prefixClass('icon-xinzeng')"
          noneBg
          @click="handleKaiDan"
          >开单</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('weijizhang-table-box')">
      <md-table-pro
        :columns="columns"
        height="100%"
        border
        :stripe="false"
        :onFetch="handleFetch"
        ref="table"
        :autoLoad="false"
      >
        <template #yaoPinMX="{ row }">
          <biz-tag-list
            :list="row.yaoPinXSs"
            @clickMore="({ event }) => handleClickRuKuDan(event, row)"
          >
          </biz-tag-list>
        </template>
        <template #ruKuDH="{ row }">
          <div :class="prefixClass('item-inline')">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('rukudantip')"
            >
              <template #content>
                <div @click="copy(row.ruKuDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template v-slot:reference> -->
              <span
                :class="prefixClass('rukudh')"
                @click="handleClickRuKuDan($event, row)"
                >{{ row.ruKuDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
            <div :class="prefixClass('chonghongBZ')" v-if="row.hongDanBZ">
              冲
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <md-button type="text-bg" @click.stop="handleEdit(row)">
            编辑
          </md-button>
          <md-button type="text-bg" @click.stop="handleJiZhang(row)">
            记账
          </md-button>
          <md-button type="danger" noneBg @click.stop="handleZuoFei(row)">
            作废
          </md-button>
        </template>
      </md-table-pro>
    </div>
    <rukudan-detail-drawer
      ref="rukudandetail"
      :jinJiaJEXSDW="jinJiaJEXSDW"
      :lingShouJEXSDW="lingShouJEXSDW"
      @jizhang="handleJiZhang"
      size="75%"
    />
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizTagList from '@/components/BizTagList';
import { logger } from '@/service/log';
import { getZhongYaoYPRKJGXZ } from '@/service/yaoPin/YaoPinZDJCSJ';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import { ChongHongJZRuKuDan } from '@/service/yaoPinYK/danJuCH';
import {
  GetYaoPinRKDCount,
  GetYaoPinRKDList,
  GetYaoPinRKDWJZRQ,
  JiZhangYaoPinRKD,
  ZuoFeiYaoPinRKD,
} from '@/service/yaoPinYK/yaoPinRK';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { getJiGouID, getWeiZhiID } from '@/system/utils/local-cache';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import useClipboard from 'vue-clipboard3';
import RukudanDetailDrawer from './RuKuDanDetailDrawer.vue';
export default {
  name: 'weijizhang-page',
  props: {
    // 冲红标志，
    isChongHong: {
      type: String,
      default: '0',
    },
    // 待打开详情的入库单号的id
    openId: {
      type: String,
      default: '',
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
  },
  data() {
    return {
      zhongYaoYPXZTJ: {}, //中药饮片限制条件
      timeRange: [],
      xiaoShuDianWS: 2,
      yaoPinMCObj: null,
      query: {
        jiaGeID: '',
        kaiShiSJ: '',
        jieShuSJ: dayjs().format('YYYY-MM-DD'),
        chuRuKFSID: '',
        chuRuKFSMC: '',
        gongHuoDWID: '',
        RuKuDH: '',
      },
      pageLoading: false,
      gongHuoDWOptions: [],
      chuRuKFSOptions: [],
      columns: [
        {
          slot: 'ruKuDH',
          label: '入库单',
          width: 135,
        },
        {
          prop: 'chuRuKFSMC',
          label: '入库方式',
          width: 108,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          width: 188,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          width: 65,
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          width: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 || Number(scope.row.jinJiaJE) < 0
                ? 'chonghong-number__color'
                : '';
            return h(
              'span',
              { class: className },
              Number(scope.row.jinJiaJE).toFixed(this.jinJiaJEXSDW),
            );
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          width: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 || Number(scope.row.lingShouJE) < 0
                ? 'chonghong-number__color'
                : '';
            return h(
              'span',
              { class: className },
              Number(scope.row.lingShouJE).toFixed(this.lingShouJEXSDW),
            );
          },
        },
        {
          prop: 'beiZhu',
          label: '备注',
          minWidth: 160,
        },
        {
          slot: 'yaoPinMX',
          label: '药品明细',
          minWidth: 200,
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 108,
          formatter: (row) => {
            return yaoKuZDJZTimeShow(row.zhiDanSJ);
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 110,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 132,
          fixed: 'right',
        },
      ],
    };
  },
  watch: {
    openId: {
      handler: function (val) {
        if (this.isChongHong === '1') {
          this.handleChongHong(val);
        }
      },
      immediate: true,
    },
  },
  async created() {
    await this.getZiDIanSJ();
    let xiaoShu;
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      xiaoShu = await GetCanShuZhi(params);
    } catch (error) {
      logger.error(error);
    }
    this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 2 : xiaoShu;
    this.zhongYaoYPXZTJ = await getZhongYaoYPRKJGXZ();
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    /**
     * 获取字典数据
     * 1. 出入库方式字典数据
     * 2. 第一个未记账数据的制单日期
     */
    async getZiDIanSJ() {
      await Promise.all([
        GetChuRuKFSByFXDM('1').then((res) => {
          let optionData = [];
          res.forEach((item) => {
            optionData.push({
              chuRuKFSMC: item.fangShiMC,
              chuRuKFSID: item.fangShiID,
            });
          });
          optionData.unshift({
            chuRuKFSMC: '所有入库方式',
            chuRuKFSID: '',
          });
          this.chuRuKFSOptions = optionData;
        }),
        GetYaoPinRKDWJZRQ().then((res) => {
          let date = dayjs(res).format('YYYY-MM-DD');
          this.query.kaiShiSJ = date;
          this.timeRange = [date, dayjs().format('YYYY-MM-DD')];
          this.$nextTick(() => {
            // doLoad方法 在不使用内部autoload时第一次调用（目的计算pageSize， pageSize没变化时，不会发送请求）
            this.$refs.table.search({ pageSize: 100 });
          });
        }),
      ]);
    },
    /**
     * 处理其他页面跳转到此时， 自动打开详情抽屉 （冲红跳转、请领跳转）
     * @param id 入库单id
     */
    handleChongHong(id) {
      this.handleClear();
      this.$nextTick(() => {
        this.handleSearch();
        this.$refs.rukudandetail.openDrawer({
          id: id,
          type: 1,
          rowData: row,
        });
      });
    },
    handleZuoFei(row) {
      MdMessageBox.confirm('此操作将删除该行数据，是否继续？', '操作提醒！', {
        type: 'warning',
      }).then(async () => {
        this.pageLoading = true;
        try {
          await ZuoFeiYaoPinRKD(row.id);
          MdMessage({
            type: 'success',
            message: '作废成功！',
          });
          this.handleSearch();
        } finally {
          this.pageLoading = false;
        }
      });
    },
    handleDateRangeChange(val) {
      if (val && val.length > 0) {
        this.query.kaiShiSJ = val[0]
          ? dayjs(val[0]).format('YYYY-MM-DD')
          : null;
        this.query.jieShuSJ = val[1]
          ? dayjs(val[1]).format('YYYY-MM-DD')
          : null;
      } else {
        this.query.kaiShiSJ = '';
        this.query.jieShuSJ = '';
      }
      this.handleSearch();
    },
    handleSelectChange(val, key) {
      let optionsKey = key + 'Options';
      let mingChengKey = key + 'MC';
      let daiMaKey = key + 'ID';
      let data = this[optionsKey].find((item) => item[daiMaKey] === val);
      this.query[mingChengKey] = data ? data[mingChengKey] : '';
      this.handleSearch();
    },
    handleSearch() {
      this.$nextTick(() => {
        this.$refs.table.search({ pageSize: 100 });
      });
    },
    handleKaiDan() {
      let query = {
        type: '0',
      };
      //中药饮片不等于1，1是不限制,0为限制  ;xianZhiTXLX,0是提醒，2为强制。
      if (
        !isEmpty(this.zhongYaoYPXZTJ) &&
        this.zhongYaoYPXZTJ.xiangMuZDM == 0
      ) {
        if (!isEmpty(this.zhongYaoYPXZTJ.jsonDto)) {
          let lingShouJia = this.zhongYaoYPXZTJ.jsonDto?.jinJia[0];
          let jinJia = this.zhongYaoYPXZTJ.jsonDto?.jinJia[1];
          query.beiShuCS = JSON.stringify({
            lingShouJDM: lingShouJia.tiaoJianDM,
            lingShouJTJZ: lingShouJia.tiaoJianZhi,
            jinjiaDM: jinJia.tiaoJianDM,
            jinJiaTJZ: jinJia.tiaoJianZhi,
          });
          query.xianZhiTXLX = this.zhongYaoYPXZTJ.xianZhiTXLX;
        }
      }
      this.$router.push({
        name: 'XinZengRKD',
        query: query,
      });
      sessionStorage.setItem('typeCount', '1');
    },
    handleEdit(row) {
      this.$router.push({
        name: 'XiuGaiRKD',
        query: {
          id: row.id,
          title: '入库单-' + row.ruKuDH,
        },
      });
    },
    /**
     * 记账分为冲红记账，普通入库单记账
     * @param row
     */
    handleJiZhang(row) {
      MdMessageBox.confirm('记账后该入库单无法修改，确定记账？', '操作提醒！', {
        type: 'warning',
      }).then(async () => {
        this.pageLoading = true;
        try {
          if (row.hongDanBZ) {
            await ChongHongJZRuKuDan({ ruKuDID: row.id });
          } else await JiZhangYaoPinRKD({ id: row.id });
          MdMessage({
            type: 'success',
            message: '记账成功',
          });
          // this.handleSearch()
          this.$emit('go-to-tab');
        } finally {
          this.pageLoading = false;
        }
      });
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        pageIndex: page,
        pageSize,
        DanJuZTDM: '1',
        WeiZhiID: getWeiZhiID(),
        ZuZhiJGID: getJiGouID(),
        ...this.query,
      };
      const [items, total] = await Promise.all([
        GetYaoPinRKDList(params, config),
        GetYaoPinRKDCount(params, config),
      ]);
      return {
        items: items,
        total: total.count,
      };
    },
    /**
     * 点击入库单打开抽屉，
     * 1. 阻止点击事件冒泡，防止抽屉关闭
     * @param e
     * @param row
     * @returns {Promise<void>}
     */
    async handleClickRuKuDan(e, row) {
      e.stopPropagation();
      await this.$refs.rukudandetail.openDrawer({
        id: row.id,
        type: 1,
        rowData: row,
      });
      this.handleSearch();
    },
    handleClear() {
      this.query.chuRuKFSID = '';
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.query.jiaGeID = e.jiaGeID || '';
      this.handleSearch();
    },
    // handleCopySuccess() {
    //   MdMessage({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000,
    //   });
    // },
    // handleCopyError() {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了',
    //   });
    // },
  },
  components: {
    'gonghuodw-select': GongHuoDWSelect,
    'rukudan-detail-drawer': RukudanDetailDrawer,
    'biz-tag-list': BizTagList,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-weijizhang-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .#{$md-prefix}-weijizhang-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-filters {
      display: flex;

      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }

  .#{$md-prefix}-weijizhang-table-box {
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
  }

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .#{$md-prefix}-chonghongBZ {
    margin-left: 4px;
    width: 20px;
    height: 20px;
    background-color: #f12933;
    border-radius: 2px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
  }
}

.#{$md-prefix}-rukudh {
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}

::v-deep .chonghong-number__color {
  color: #f12933 !important;
}
</style>
<style lang="scss">
.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
}

.#{$md-prefix}-rukudantip {
  min-width: 30px;
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
