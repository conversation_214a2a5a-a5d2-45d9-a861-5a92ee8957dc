<!--  -->
<template>
  <div class="custom-select">
    <md-select v-bind="$attrs" ref="selectRef"><slot></slot></md-select>
  </div>
</template>

<script setup lang="ts">
import { ref, unref } from 'vue';

import { MdSelect } from '@mdfe/medi-ui';

const selectRef = ref<InstanceType<typeof MdSelect> | null>(null);

function focus() {
  return unref(selectRef)?.focus();
}

defineExpose({ focus });
</script>
