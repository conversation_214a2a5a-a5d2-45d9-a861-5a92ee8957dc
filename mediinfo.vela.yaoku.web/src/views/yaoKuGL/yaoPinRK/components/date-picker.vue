<!--  -->
<template>
  <div class="custom-date-picker" style="width: 100%">
    <md-date-picker
      v-bind="$props"
      ref="datePickerRef"
      @update:modelValue="handleModelValue"
    ></md-date-picker>
  </div>
</template>

<script setup lang="ts">
import { ref, unref } from 'vue';

import { datePickerProps } from '@mdfe/medi-ui';
import type { DatePickerInstance } from '@mdfe/medi-ui';

defineProps(datePickerProps);
const emits = defineEmits(['update:modelValue']);

const datePickerRef = ref<DatePickerInstance | null>(null);

function handleModelValue(value: any) {
  emits('update:modelValue', value);
}

function focus(focusStartInput: boolean) {
  return unref(datePickerRef)?.focus(focusStartInput);
}

defineExpose({ focus });
</script>
