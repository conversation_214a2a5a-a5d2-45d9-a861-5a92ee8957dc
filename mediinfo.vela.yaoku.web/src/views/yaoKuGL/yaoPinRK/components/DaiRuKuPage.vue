<template>
  <md-frameset
    :class="prefixClass('frameset')"
    :gutter="0"
    :frameborder="false"
    padding="0"
    style="display: flex"
  >
    <md-frame-pane style="width: 280px; position: absolute" :inset="false">
      <div :class="prefixClass('frame-pane-box frame-pane-left')">
        <div :class="prefixClass('daiRuKuPage-left-header')">
          <md-radio-group
            v-model="showType"
            :class="prefixClass('radio-box')"
            @change="handleSearch"
          >
            <md-radio label="1">按采购单</md-radio>
            <md-radio label="2">按发票</md-radio>
          </md-radio-group>
          <md-button
            v-show="showType == 1"
            type="primary"
            @click="ClearList"
            :icon="prefixClass('icon-shanchuwap')"
            noneBg
            style="margin-right: 8px"
            >清空列表</md-button
          >
          <md-button
            v-show="showType == 2"
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            style="margin-right: 8px"
            @click="onSearchLeft"
            >两定状态</md-button
          >
        </div>

        <div :class="prefixClass('left-content')">
          <div>
            <md-date-picker-range-pro
              v-model="timeRange"
              :class="prefixClass('query-size')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="/"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleTimeRangeChange"
            >
            </md-date-picker-range-pro>
            <div style="display: flex; align-items: baseline">
              <label v-if="showType == 2" style="width: 90px">供货单位</label>
              <gonghuodw-select
                v-show="showType == 2"
                :value.sync="query.gongHuoDWID"
                @select-change="handleGongHuoLeft"
                :class="prefixClass('query-size')"
              />
            </div>

            <md-input
              v-show="showType == 1"
              v-model="query.caiGouDH"
              :class="prefixClass('query-size')"
              placeholder="输入单据号搜索"
              @keyup.enter.native="handleSearch"
            >
              <i
                :class="prefixClass('input__icon icon-seach')"
                slot="suffix"
                @click="handleSearch"
              ></i>
            </md-input>
            <md-input
              v-show="showType == 2"
              v-model="query.faPiaoHM"
              :class="prefixClass('query-size')"
              placeholder="输入发票号搜索"
              @keyup.enter.native="handleSearch"
            >
              <i
                :class="prefixClass('input__icon icon-seach')"
                slot="suffix"
                @click="handleSearch"
              ></i>
            </md-input>
          </div>
          <!-- :key="showType"为了切换时，columns里的select可以显示 -->
          <yaopingrk-left-table
            :key="showType"
            :columns="showType == 1 ? columns : anFaPiaoHColumns"
            key-params="caiGouDH"
            @delete-row="handleRowDelete"
            @current-change="handleCurrentChange"
            @select="handleSelect"
            :zuoFeiButton="false"
            :get-list-data="getLeftTableData"
            ref="leftTable"
          />
        </div>
      </div>
    </md-frame-pane>
    <md-frame-pane inset style="flex: 1; padding-left: 280px">
      <div
        v-if="!hasData"
        :class="prefixClass('frame-pane-box frame-pane-right')"
      >
        <div :class="prefixClass('nodata')">
          <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
          <span>暂无数据...</span>
        </div>
      </div>
      <div v-else :class="prefixClass('frame-pane-box frame-pane-right')">
        <div :class="prefixClass('content-top')">
          <div :class="prefixClass('content-top-filters')">
            <gonghuodw-select
              v-show="showType == 1"
              :value.sync="rightQuery.gongHuoDWID"
              @select-change="handleGongHuo"
            />
            <biz-yaopindw
              v-model="yaoPinData"
              @change="handleChangeYaoPinDW"
              placeholder="输入药品名称搜索"
              showSuffix
            />
          </div>
          <div :class="prefixClass('content-top-buttons')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-shuaxin')"
              noneBg
              style="margin-right: 8px"
              @click="handleSearch"
              >刷新</md-button
            >
            <md-button
              type="primary"
              plain
              @click="handleJuShou"
              style="margin-right: 8px"
              >拒收</md-button
            >
            <md-button type="primary" @click="handleZhiDan">入库制单</md-button>
          </div>
        </div>
        <div :class="prefixClass('table-box-border')">
          <md-table
            v-loading="tableLoading"
            :columns="rightColumns"
            :data="rightData"
            height="100%"
            border
            resize
            :stripe="false"
            :row-class-name="tableCellClassName"
            :span-method="arraySpanMethod"
            ref="table"
          >
            <template #selection="{ row, $index }">
              <md-checkbox
                v-if="row.isHeJi"
                v-model="row.checkAll"
                :indeterminate="row.isIndeterminate"
                @change="handleCheckAllChange($event, row)"
              ></md-checkbox>
              <md-checkbox
                v-else
                v-model="row.isChecked"
                @change="handleCheckedItemsChange($event, row, $index)"
              ></md-checkbox>
            </template>
          </md-table>
        </div>
      </div>
    </md-frame-pane>

    <tixing-dialog ref="tiXingDialog" />
    <damaj-dialog ref="DuMaJDialog" />
    <pi-hao-x-q-l-y ref="piHaoXQLY" />
  </md-frameset>
</template>

<script>
import dayjs from 'dayjs';
import {
  juShouCGJHDSH,
  UpdateCaiGouJHDListForRKD,
  GetCaiGouJHDListByFPHMCount,
  getCaiGouJHDListByFPHM,
  GetCaiGouJHDMXByFPHM,
} from '@/service/yaoPinYK/caiGouJHSH';
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
import { getZhongYaoYPRKJGXZ } from '@/service/yaoPin/YaoPinZDJCSJ';
import { ZuoFeiCaiGouJHD } from '@/service/yaoPinYK/caiGouJH';
import {
  GetCaiGouJHDCount,
  GetCaiGouJHDList,
  GetCaiGouJHDMXForRKD,
  UpdateCaiGouDanZT,
} from '@/service/yaoPinYK/yaoPinRK';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import YaopingrkLeftTable from '@/views/yaoKuGL/components/YaoPingRKLeftTable';
import PiHaoXQLY from './PiHaoXQLYDIalog';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { isEmpty } from 'lodash';
import TiXingDialog from '../../yaoKuFSZ/components/TiXingDialog.vue';
import DuMaJDialog from './DuMaJDLZDDialog';
import tableData from '../js/tableData.js';
const defaultQuery = () => ({
  ZhiDanSJKS: dayjs().format('YYYY-MM-01'),
  ZhiDanSJJS: dayjs().format('YYYY-MM-DD'),
  caiGouDH: '',
  gongHuoDWID: '',
  faPiaoHM: '',
});
export default {
  name: 'dairuku-page',
  mixins: [tableData],
  props: {
    duMaJDLZD: {
      type: String,
    },
    showPiHaoDialog: {
      type: String,
    },
  },
  data() {
    return {
      zhongYaoYPXZTJ: {}, //中药饮片限制条件
      timeRange: [],
      showType: '1', // 左上radio参数 1. 按采购单入库
      currentRow: null, // 左侧选中的行数据
      selection: [], // 左侧选中的行数据(多选) eg：按发票
      query: defaultQuery(), //左侧筛选条件
      yaoPinData: {}, // 右侧筛选条件——药品绑定数据
      rightQuery: {
        // 左侧筛选条件
        gongHuoDWID: '', // 供货单位
        jiaGeID: '', // 价格ID
      },
      selectedGongSi: '', // 右侧表格当前选中公司ID
      // columns: tableData.columns,
      // rightColumns: tableData.rightColumns,
      rightData: [],
      data: [],
      tableLoading: false,
      hasData: false, //右侧表格是否显示， 左侧没数据时不显示
    };
  },
  async mounted() {
    this.timeRange = [
      dayjs().format('YYYY-MM-01'),
      dayjs().format('YYYY-MM-DD'),
    ];
    this.zhongYaoYPXZTJ = await getZhongYaoYPRKJGXZ();
  },
  methods: {
    async onSearchLeft() {
      await UpdateCaiGouJHDListForRKD({
        ZhiDanSJKS: this.timeRange[0],
        ZhiDanSJJS: this.timeRange[1],
      });
      this.handleSearch();
    },
    async handleSearch() {
      await this.$refs.leftTable.search();
    },
    handleRightSearch() {
      if (this.showType == 1) {
        this.getAnCaiGDList();
      } else this.getAnFaPiaoHList();
    },
    /**
     *  点击制单操作
     *  1.存储选中数据到localStorage，（页面传递数据）
     *  2.记录typeCount， 路由跳转到新增页面时，供刷新页面数据使用
     *  3.跳转到新增页面 （type： 0——新增跳转 其他值——按采购单入库跳转）
     */
    async handleZhiDan() {
      let selectionData = this.rightData.filter(
        (item) => item.isChecked && !item.isHeJi,
      );
      if (selectionData.length === 0) {
        MdMessage({
          type: 'warning',
          message: '请选择一条药品数据！',
        });
        return;
      }
      let items = JSON.stringify(selectionData);
      let type = +sessionStorage.getItem('typeCount') || 1;
      let beiShuCS = {};
      let query = {
        type: type,
        chuRuKFSID: '1001',
        caiGouDH: this.currentRow.caiGouDH,
        faPiaoHM: this.showType == 2 ? this.currentRow.faPiaoHM : '',
      };
      //倍数比例 零售价/进价
      //中药饮片不等于1，为限制 ,0是提醒，2为强制。1是不限制
      if (
        !isEmpty(this.zhongYaoYPXZTJ) &&
        this.zhongYaoYPXZTJ.xiangMuZDM == '0'
      ) {
        if (!isEmpty(this.zhongYaoYPXZTJ.jsonDto)) {
          let lingShouJia = this.zhongYaoYPXZTJ.jsonDto?.jinJia[0];
          let jinJia = this.zhongYaoYPXZTJ.jsonDto?.jinJia[1];
          beiShuCS = {
            lingShouJDM: lingShouJia.tiaoJianDM,
            lingShouJTJZ: lingShouJia.tiaoJianZhi,
            jinjiaDM: jinJia.tiaoJianDM,
            jinJiaTJZ: jinJia.tiaoJianZhi,
          };
          const tiXingInfo =
            this.zhongYaoYPXZTJ.xianZhiTXLX == 1 ? '' : '，无法入库！';
          selectionData.forEach((el) => {
            el.tiXing = '';
            const beiShu = Number(el.lingShouJia) / Number(el.jinJia);
            //进价零售价都设置了条件值
            if (lingShouJia.tiaoJianZhi && jinJia.tiaoJianZhi) {
              if (
                eval(
                  `${beiShu} ${lingShouJia.tiaoJianDM} ${lingShouJia.tiaoJianZhi}`,
                ) ||
                eval(`${beiShu} ${jinJia.tiaoJianDM} ${jinJia.tiaoJianZhi}`)
              ) {
                el.tiXing =
                  el.yaoPinMC +
                  '零售价超出进价的' +
                  beiShu.toFixed(2) +
                  '倍' +
                  tiXingInfo;
              }
            }
            if (
              lingShouJia.tiaoJianZhi &&
              eval(
                `${beiShu} ${lingShouJia.tiaoJianDM} ${lingShouJia.tiaoJianZhi}`,
              )
            ) {
              el.tiXing =
                el.yaoPinMC +
                '零售价超出进价的' +
                beiShu.toFixed(2) +
                '倍' +
                tiXingInfo;
            }
          });
          //只展示符合提醒条件的
          const fuHeTJList = selectionData.filter((fl) => fl.tiXing);
          // 不符合条件的默认带入入库开单
          const moRenRKList = selectionData.filter((fl) => !fl.tiXing);
          let res = [];
          if (fuHeTJList.length > 0) {
            res = await this.$refs.tiXingDialog.showDialog(
              fuHeTJList,
              this.zhongYaoYPXZTJ,
            );
          }
          // 把弹窗里的内容(录入或不录入，录入部分或删除药品的数据和默认带入的药品进行汇总·)
          res = res.concat(moRenRKList);
          items = JSON.stringify(res);
        }
        query.xianZhiTXLX = this.zhongYaoYPXZTJ.xianZhiTXLX;
        if (!isEmpty(beiShuCS)) query.beiShuCS = JSON.stringify(beiShuCS);
      }
      // 开启了毒麻精药品独立制单，删除毒麻精药品
      // 选中的药品里是否含有毒麻精药品
      const isCludeDMJ = JSON.parse(items).some((s) => s.duLiFLDM != 0);
      const isNormal = JSON.parse(items).some((s) => s.duLiFLDM == 0);
      // 同时包含毒麻精和普通药品
      if (this.duMaJDLZD == 1 && isCludeDMJ && isNormal) {
        const res = await this.$refs.DuMaJDialog.showDialog(JSON.parse(items));
        items = JSON.stringify(res);
      }
      // 批号效期弹窗
      if (this.showPiHaoDialog == 1) {
        const obj = await this.$refs.piHaoXQLY.showDialog();
        if (obj) {
          query.piHaoXQLY = obj.piHaoXQLY;
          query.piHaoXQLYWZ = obj.piHaoXQLYWZ;
        }
      }
      //  使用vuex
      sessionStorage.setItem('ruKuDanList', items);
      this.$router.push({
        path: '/XinZengRKD',
        query: query,
      });
      type++;
      sessionStorage.setItem('typeCount', type + '');
    },
    //拒收
    async handleJuShou() {
      let selectionData = this.rightData.filter(
        (item) => item.isChecked && !item.isHeJi,
      );
      if (selectionData.length === 0) {
        MdMessage({
          type: 'warning',
          message: '请选择要拒收的药品数据！',
        });
        return;
      }
      await MdMessageBox.confirm('确定要拒收？', '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      const obj = selectionData.map((m) => m.id);
      await juShouCGJHDSH(obj);
      MdMessage.success('拒收成功！');
      this.handleSearch();
    },
    async handleRowDelete(row) {
      await ZuoFeiCaiGouJHD(row.id);
      MdMessage({
        type: 'success',
        message: '作废成功！',
      });
      await this.handleSearch();
    },
    /**
     *  左侧选中数据改变
     *  1. 记录选中的数据
     *  2. 情况右侧筛选条件
     *  3. 查询数据
     */
    handleCurrentChange(currentRow) {
      this.currentRow = currentRow;
      this.rightQuery = {
        gongHuoDWID: '',
        jiaGeID: '',
      };
      this.yaoPinData = {};
      this.handleRightSearch();
    },
    handleSelect(selection, row) {
      const hasdiff =
        selection.length > 1 &&
        selection.reduce(function (prev, cur) {
          return prev.gongHuoDWID != cur.gongHuoDWID;
        });

      if (hasdiff) {
        MdMessage({
          type: 'warning',
          message: '请选择相同的供货单位！',
        });
        selection = selection.filter((item) => item != row);
        const tableRef = this.$refs.leftTable.getTableComponent();
        tableRef.clearSelection();
        tableRef.toggleRowSelection(selection);
        return;
      }

      this.selection = selection;
      this.rightQuery = {
        gongHuoDWID: '',
        jiaGeID: '',
      };
      this.yaoPinData = {};
      this.handleRightSearch();
    },
    async getLeftTableData(page, pageSize) {
      let params = {
        pageIndex: page,
        pageSize,
        ...this.query,
        daiRuKuBZ: 1,
      };
      if (params.pageSize) {
        let data;
        if (this.showType == 1) {
          data = await Promise.all([
            GetCaiGouJHDList(params),
            GetCaiGouJHDCount(params),
          ]);
        } else {
          delete params.caiGouDH;
          data = await Promise.all([
            getCaiGouJHDListByFPHM(params),
            GetCaiGouJHDListByFPHMCount(params),
          ]);
        }
        const [items, total] = data;
        this.hasData = total > 0 ? true : false;
        return {
          items: items,
          total: total,
        };
      }
    },
    handleChangeYaoPinDW(data) {
      this.rightQuery.jiaGeID = data.jiaGeID;
      this.handleRightSearch();
    },
    handleGongHuoLeft(data) {
      this.query.gongHuoDWID = data.gongHuoDWID;
      this.handleSearch();
    },
    handleGongHuo(data) {
      this.rightQuery.gongHuoDWID = data.gongHuoDWID;
      this.handleRightSearch();
    },
    /**
     * 获取采购单明细列表
     * 1. 清除选中数据
     * 2. 处理请求获取的数据，添加合计列以供货单位id分组
     *    a. 合计列——checkAll: false,isIndeterminate: false
     *    b. 数据列——item1.isChecked = false， 供货单位赋值
     */
    async getAnCaiGDList() {
      this.tableLoading = true;
      const params = {
        caiGouJHDID: this.currentRow.id,
        ...this.rightQuery,
        daiRuKuBZ: 1,
      };
      try {
        this.rightData = [];
        this.clearSelection();
        const data = await GetCaiGouJHDMXForRKD(params);
        let result = [];
        data.forEach((item) => {
          let midData = item.caiGouJHDMXList;
          midData.forEach((item1) => {
            item1.gongHuoDWID = item.gongHuoDWID;
            item1.gongHuoDWMC = item.gongHuoDWMC;
            item1.isChecked = false;
          });
          midData.unshift({
            isHeJi: true,
            jinJia: '合计',
            id: item.gongHuoDWID,
            gongHuoDWID: item.gongHuoDWID,
            gongHuoDWMC: item.gongHuoDWMC,
            jinJiaJE: item.heJiJJJJ,
            lingShouJE: item.heJiLSJJ,
            checkAll: false,
            isIndeterminate: false,
          });
          result.push(...midData);
        });
        this.rightData = result;
      } finally {
        this.tableLoading = false;
      }
    },

    async getAnFaPiaoHList() {
      if (!this.selection.length) {
        this.rightData = [];
        return;
      }
      this.tableLoading = true;
      const faPiaoHMString = this.selection.reduce(function (prev, cur) {
        return `${prev}${cur.faPiaoHM},`;
      }, '');
      const params = {
        faPiaoHM: faPiaoHMString,
        ...this.rightQuery,
        daiRuKuBZ: 1,
      };
      try {
        this.rightData = [];
        this.clearSelection();
        const data = await GetCaiGouJHDMXByFPHM(params);
        let result = [];
        data.forEach((item) => {
          let midData = item.caiGouJHDMXList;
          midData.forEach((item1) => {
            item1.gongHuoDWID = item.gongHuoDWID;
            item1.gongHuoDWMC = item.gongHuoDWMC;
            item1.isChecked = false;
          });
          midData.unshift({
            isHeJi: true,
            jinJia: '合计',
            id: item.gongHuoDWID,
            gongHuoDWID: item.gongHuoDWID,
            gongHuoDWMC: item.gongHuoDWMC,
            jinJiaJE: item.heJiJJJJ,
            lingShouJE: item.heJiLSJJ,
            checkAll: false,
            isIndeterminate: false,
          });
          result.push(...midData);
        });
        this.rightData = result;
      } finally {
        this.tableLoading = false;
      }
    },
    /**
     *  合并第二列到第6列， 供合并列显示供货单位名称
     */
    arraySpanMethod({ row, columnIndex }) {
      if (row.isHeJi) {
        if (columnIndex === 1) {
          return [1, 5];
        } else if (columnIndex > 1 && columnIndex < 6) {
          return [0, 0];
        }
      }
    },
    tableCellClassName({ row }) {
      return row.isHeJi ? this.prefixClass('heji-row') : '';
    },
    // 供货单位全选
    handleCheckAllChange(val, row) {
      if (!this.checkChoosed(row)) {
        //TODO
        // this.$set(row, 'checkAll', true)
        row.checkAll = true;
      }
      this.selectedGongSi = val ? row.gongHuoDWID : '';
      // 选中子项或者取消选中子项
      this.rightData.forEach((item) => {
        if (item.gongHuoDWID === row.gongHuoDWID && !item.isHeJi) {
          item.isChecked = val;
        }
      });
      row.isIndeterminate = false;
    },
    /**
     * 判断选中的单位是否为当前已选中单位， 不是则清除所有选中项
     * @param row 当前点击行
     * @returns {boolean}
     */
    checkChoosed(row) {
      if (this.selectedGongSi !== row.gongHuoDWID) {
        this.clearSelection();
        this.selectedGongSi = row.gongHuoDWID;
        return false;
      }
      return true;
    },
    /**
     * 不是合计行的 checkbox点击处理
     * 1. 同一个供货单位，正常选中， 不是则取消已选中的数据。
     * 2. 更新当前点击节点的父节点选中状态
     */
    handleCheckedItemsChange(val, row) {
      //判断选中的产地是否相同，相同则继续，不相同则取消选中之前的选中， 选中现在点击行
      if (!this.checkChoosed(row)) {
        //TODO
        // this.$set(row, 'isChecked', true)
        row.isChecked = true;
      }
      let allChecked = true; // 是否全部选中
      let allUnchecked = true; // 是否全部未选中
      let parentRow = this.rightData.find(
        (item) => item.gongHuoDWID === row.gongHuoDWID && item.isHeJi,
      );
      this.rightData.forEach((item) => {
        if (item.gongHuoDWID === row.gongHuoDWID && parentRow.id !== item.id) {
          if (!item.isChecked) allChecked = false; // 有没被选择的，则为false
          if (item.isChecked) allUnchecked = false; // 有选择的
        }
      });
      parentRow.checkAll = allChecked;
      parentRow.isIndeterminate = !allUnchecked && !allChecked; // 没有全选 也没有全不选
    },
    async ClearList() {
      MdMessageBox.alert('是否清除列表？', '操作提醒！', {
        type: 'warning',
      })
        .then(async () => {
          let params = {
            ZhiDanSJKS:
              this.query.ZhiDanSJKS == '' ? null : this.query.ZhiDanSJKS,
            ZhiDanSJJS:
              this.query.ZhiDanSJJS == '' ? null : this.query.ZhiDanSJJS,
            CaiGouDH: this.query.caiGouDH,
          };
          await UpdateCaiGouDanZT(params);
          this.getLeftTableData();
          this.handleSearch();
        })
        .catch((error) => {});
    },
    /**
     * 清除所有选中项
     */
    clearSelection() {
      this.rightData.forEach((item) => {
        if (item.isHeJi) {
          item.checkAll = false;
          item.isIndeterminate = false;
        } else item.isChecked = false;
      });
      this.selectedGongSi = null;
    },
    handleTimeRangeChange(time) {
      if (time && time.length > 0) {
        this.query.ZhiDanSJKS = time[0]
          ? dayjs(time[0]).format('YYYY-MM-DD')
          : '';
        this.query.ZhiDanSJJS = time[1]
          ? dayjs(time[1]).format('YYYY-MM-DD')
          : '';
      } else {
        this.query.ZhiDanSJKS = '';
        this.query.ZhiDanSJJS = '';
      }
      this.handleSearch();
    },
  },
  components: {
    'gonghuodw-select': GongHuoDWSelect,
    'biz-yaopindw': BizYaopindw,
    'yaopingrk-left-table': YaopingrkLeftTable,
    'pi-hao-x-q-l-y': PiHaoXQLY,
    'tixing-dialog': TiXingDialog,
    'damaj-dialog': DuMaJDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-daiRuKuPage-left-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgb(var(--md-color-1));
}

.#{$md-prefix}-table-box-border {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

::v-deep .#{$md-prefix}-heji-row {
  background-color: #f2f9f9;

  > td {
    border-right: 0;
    border-left: 0;

    .cell {
      //height: 20px;
      font-weight: 600;
      color: #377777;
      font-size: 14px;
      //line-height: 20px;
    }
  }
}

.#{$md-prefix}-content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &-filters {
    display: flex;

    .#{$md-prefix}-filter-width {
      width: 240px;
    }
  }

  &-buttons {
    display: flex;
  }
}

.#{$md-prefix}-frame-pane-right {
  margin: 8px;
  padding: 8px;
  height: calc(100% - 32px);
}

.#{$md-prefix}-frame-pane-left {
  height: 100%;
}

.#{$md-prefix}-frame-pane-box {
  background: #fff;
  display: flex;
  flex-direction: column;
}

.#{$md-prefix}-radio-box {
  height: 36px;
  display: flex;
  align-items: center;
  // background-color: #edf6fd;
  background-color: rgb(var(--md-color-1));
  padding: 0 8px;
}

.#{$md-prefix}-radio-text {
  height: 20px;
  color: #222222;
  font-size: 14px;
  line-height: 20px;
}

.#{$md-prefix}-left-content {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.#{$md-prefix}-query-size {
  width: 100%;
  margin-bottom: 8px;
}

.#{$md-prefix}-riqi-width-anfapiao {
  width: calc(100% - 38px);
}

.#{$md-prefix}-filter-more {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 30px;
  height: 28px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;

  &:hover {
    background-color: #e6f7ff;
  }
}

.#{$md-prefix}-icon-class {
  font-size: 16px;
}

.#{$md-prefix}-item-inline {
  display: none;
}

.#{$md-prefix}-left-filter {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 999;
  padding: 8px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .#{$md-prefix}-item-inline-label {
      width: 62px;
      height: 20px;
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      text-align: right;
    }

    &-input {
      margin-left: 8px;
      width: calc(100% - 68px);
    }
  }

  .#{$md-prefix}-left-filter-buttons {
    margin-top: 8px;

    .button-class {
      width: 64px;
      height: 30px;
      margin-left: 8px;
    }
  }
}

.#{$md-prefix}-left-filter-more {
  margin-top: 76px;
}

.#{$md-prefix}-right-table-tips {
  height: 20px;
  color: #aaaaaa;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  padding: 8px;

  > span {
    color: #333;
    font-weight: bold;
  }
}
</style>
