<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('shouliqldrawer')"
    :modalClass="prefixClass('shouliqldrawermodal')"
    ref="rukudandrawer"
  >
    <div :class="prefixClass('drawer-1')" v-loading="pageLoading">
      <div :class="prefixClass('rkd-title')">
        <div :class="prefixClass('title-left')">
          <span>{{ title }}</span>
          <span v-if="hongDanBZ" :class="prefixClass('chonghongBZ')">冲</span>
        </div>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <!-- <md-button
              type="primary"
              :icon="prefixClass('icon-daochu')"
              @click="handleDaoChu"
              noneBg
              >导出
            </md-button> -->
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleDaYin"
              noneBg
              >打印
            </md-button> -->
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleDaYinZLYSD"
              noneBg
              >打印质量验收单
            </md-button> -->
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleYuLanZLYSD"
              noneBg
              v-if="isYiJiZhang"
              >预览质量验收单
            </md-button>
            <md-button
              v-if="!isYiJiZhang"
              type="primary"
              @click="handleJiZhang"
              noneBg
            >
              <i
                class="iconfont iconedit"
                style="font-size: 14px; margin-right: 4px"
              />记账
            </md-button>
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <div :class="prefixClass('content-zhuangtai')">
          <!--          <div :class="prefixClass('content-descriptions')">-->
          <div :class="prefixClass('content-description-item')">
            <label>供货单位：</label><span>{{ rowData.gongHuoDWMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>入库方式：</label><span>{{ rowData.chuRuKFSMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>应付款等级：</label><span>{{ rowData.yingFuKDJMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>附单张数：</label><span>{{ rowData.fuDanZS }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>入库日期：</label>
            <span>{{ formatDate(rowData.zhiDanSJ) }}</span>
          </div>
        </div>
        <div
          :class="prefixClass('content-description-item')"
          style="margin-bottom: 12px"
        >
          <label>备注：</label><span>{{ rowData.beiZhu }}</span>
        </div>
        <md-table
          :columns="columns"
          :cell-class-name="handleChongHongSLColor"
          :data="rowData.ruKuDanXQs"
          height="100%"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #caoZuoSZDM="{ row, cellRef }">
            <md-select
              v-model="row.caoZuoSZDM"
              :disabled="row.canShuRu"
              @change="CaoZuoSZChange($event, row, cellRef)"
            >
              <md-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </template>
          <template #lieMingCheng="{ row }">
            <span :class="prefixClass('lieMingCheng')" style="margin-left: 8px">
              {{ row.lieMingCheng }}
            </span>
          </template>
          <template #bieMing="{ row }">
            <md-input v-model="row.bieMing" placeholder="请填写" clearable />
          </template>
        </md-table>
        <div :class="prefixClass('description pos-bottom-direction')">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">
              {{ rowData.zhiDanRXM + ' ' + formatDate(rowData.chuangJianSJ) }}
            </span>
            <span
              v-if="isYiJiZhang"
              :class="prefixClass('description-item__label')"
            >
              记账:
            </span>
            <span
              v-if="isYiJiZhang"
              :class="prefixClass('description-item__content')"
            >
              {{ rowData.jiZhangRXM + ' ' + formatDate(rowData.jiZhangSJ) }}
            </span>
          </div>
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')">
              {{ rowData.yaoPinZS }}
              <span :class="prefixClass('content-color')">种药品</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >合计 进价金额:</span
            >
            <span
              :class="
                prefixClass([
                  'description-item__content fontWeight',
                  hongDanBZ ? 'chonghong-number__color' : '',
                ])
              "
            >
              {{ formatJinE(rowData.jinJiaJEHJ, 'jinjia') }}
              <span :class="prefixClass('content-color')">元</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >零售金额:</span
            >
            <span
              :class="
                prefixClass([
                  'description-item__content fontWeight',
                  hongDanBZ ? 'chonghong-number__color' : '',
                ])
              "
            >
              {{ formatJinE(rowData.lingShouJEHJ, 'lingshou') }}
              <span :class="prefixClass('content-color')">元</span></span
            >
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT004'"
        :fileName="'药品入库单'"
        :title="'药品入库单打印预览'"
      />
      <dayin-zlysddialog
        ref="daYinZLYSDDialog"
        :params="params"
        :id="'YKXT013'"
        :fileName="'质量预览单'"
        :title="'质量预览单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import Big from 'big.js';
import columnMixin from '@/components/mixin/columnMixin';
import {
  default as DaYinDialog,
  default as DaYinZLYSDDialog,
} from '@/components/DaYinDialog.vue';
import { GetYaoPinRKDXQ } from '@/service/yaoPinYK/yaoPinRK';
import { GetCanShuZhi } from '@/system/utils/canShu';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { formatMoney } from '@/system/utils/formatMoney';
import { getKuCunGLLX, getWeiZhiMC } from '@/system/utils/local-cache';
import { add, divide, subtract } from '@/system/utils/mathComputed';
import { printByUrl } from '@/system/utils/print';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { logger } from '@/service/log';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
export default {
  name: 'rukudan-detail-drawer',
  mixins: [columnMixin],
  props: {
    size: { type: [String, Number], default: '75%' },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
  },
  async created() {
    let xiaoShu;
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      xiaoShu = await GetCanShuZhi(params);
    } catch (error) {
      logger.error(error);
    } finally {
      this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 3 : xiaoShu;
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  data() {
    return {
      isZhongYaoKXS: 5,
      weiZhiMC: getWeiZhiMC(),
      drawer: false,
      pageLoading: false,
      params: {},
      title: '',
      xiaoShuDianWS: 3,
      type: 1, // 默认1 1-未记账 2-已记账
      rowData: {
        gongHuoDWMC: '-',
        chuRuKFSMC: '-',
        yingFuKDJMC: '-',
        fuDanZS: '-',
        zhiDanSJ: '-',
        beiZhu: '-',
        ruKuDanXQs: [],
        zhiDanRXM: '',
        jiZhangRXM: '',
        jiZhangSJ: '',
        lingShouJEHJ: '',
        jinJiaJEHJ: '',
        yaoPinZS: '',
        chuangJianSJ: '',
      },
      columns: [
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'yaoPinLX',
          label: '药品类型',
          width: 34,
          field: true,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
          labelClassName: 'yaoPinLXMCHeader',
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 120,
          field: true,
        },
        // {
        //   prop: 'faPiaoRQ',
        //   label: '发票日期',
        //   width: 100,
        //   formatter: (row, column, cellValue) => {
        //     return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
        //   },
        // },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 100,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 200,
          showOverflowTooltip: true,
          field: true,
          formatter: (row) => {
            return row.xianShiXX &&
              row.xianShiXX.tianJiaWZ !== null &&
              row.xianShiXX.tianJiaWZ !== undefined
              ? row.xianShiXX.tianJiaWZ + row.yaoPinMC + ' ' + row.yaoPinGG
              : row.yaoPinMC + ' ' + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          showOverflowTooltip: true,
          minWidth: 120,
          field: true,
        },
        {
          prop: 'ruKuCDMC',
          label: '入库产地',
          minWidth: 120,
          hidden: true,
          field: true,
        },
        {
          prop: 'shengChanQY',
          label: '生产企业',
          minWidth: 150,
          type: 'text',
          showOverflowTooltip: true,
          hidden: false,
          field: true,
        },
         {
          prop: 'canKaoKL',
          label: '参考扣率',
          width: 110,
          type: 'text',
          align: 'right',
          field: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
          field: true,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'ruKuHKCSL',
          label: '入库后库存数量',
          align: 'right',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(3);
          },
        },
        {
          prop: 'jinJia_ZiDian',
          label: '字典进价',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          disabled: true,
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.isZhongYaoKXS);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.jinJiaJEXSDW);
          },
        },
        // {
        //   prop: 'lingChaJBZ',
        //   label: '零差/加价',
        //   width: 120,
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     switch (cellValue) {
        //       case 0:
        //         return '普通';
        //       case 1:
        //         return '零差价';
        //       case 2:
        //         return '加价';
        //       default:
        //         return '';
        //     }
        //   },
        // },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 92,
          align: 'right',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.isZhongYaoKXS);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.lingShouJEXSDW);
          },
        },
        {
          prop: 'kouLv',
          label: '扣率',
          width: 80,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            // return row.lingShouJia && row.jinJia
            //   ? (Number(row.jinJia) / Number(row.lingShouJia)).toFixed(2)
            //   : 0;
            return cellValue;
          },
        },
        {
          prop: 'jiaJiaLv',
          label: '加价率',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          formatter: (row, column, cellValue, index) => {
            if (row.lingShouJia && row.jinJia) {
              const count1 = new Big(row.lingShouJia);
              const xiaoShuWei = Number(this.xiaoShuDianWS);

              const result = count1
                .minus(row.jinJia)
                .div(row.jinJia)
                .toFixed(xiaoShuWei)
                .toString();

              return Number(result).toLocaleString('zh', {
                style: 'percent',
                minimumFractionDigits: 2,
              });
            }
            return '';
          },
          disabled: true,
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          field: true,
        },
        {
          prop: 'shengChanRQ',
          label: '生产日期',
          minWidth: 150,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 120,
          showOverflowTooltip: false,
          field: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          field: true,
          formatter: (row, column, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'jianYanZS',
          label: '检验证书',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '有' : '无';
          },
        },
        {
          prop: 'chuChangJYHGD',
          label: '产品合格证',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'baoZhuangQK',
          label: '包装',
          width: 60,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '完整' : '不完整';
          },
        },
        {
          prop: 'waiGuanZL',
          label: '外观情况',
          width: 80,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'yanShouJL',
          label: '验收结果',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
        },
        {
          prop: 'yanShouRXM',
          label: '验收人',
          width: 120,
          field: true,
        },
        {
          prop: 'piZhunWH',
          label: '批准文号',
          width: 120,
          field: true,
        },
        {
          prop: 'jinKouYPZH',
          label: '进口药品证号',
          width: 120,
          field: true,
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 110,
          field: true,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 110,
          field: true,
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          field: true,
          control: true,
          fieldDisabled: true,
        },
      ],
      data: [],
      controlExtraColumns: [
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 5,
        },
      ],
    };
  },
  computed: {
    isYiJiZhang() {
      return this.type === 2;
    },
    hongDanBZ() {
      return this.rowData.hongDanBZ;
    },
  },
  watch: {
    weiZhiMC: {
      handler: function (val) {
        this.columns[6].hidden =
          decodeURIComponent(val).indexOf('中') > -1 ? false : true;
      },
      immediate: true,
    },
  },
  mounted() {
    //判断是中药库还是西药库，西药库隐藏生产企业
    const isShowSCQY = getKuCunGLLX().indexOf('3') > -1;
    this.columns[5].hidden = !isShowSCQY;
    // window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    // window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    handleChongHongSLColor({ columnIndex, row }) {
      if (this.hongDanBZ) {
        if (columnIndex === 7 || columnIndex === 11 || columnIndex === 14)
          return this.prefixClass('chonghong-number__color');
      }
      if (this.rowData.kuCunZJDM == '2' && columnIndex == 7) {
        return this.prefixClass('chonghong-number__color');
      }
      // if (columnIndex === 5 && row.ruKuSL < 0) {
      //   return this.prefixClass('chonghong-number__color');
      // }
      // return '';
    },
    formatJinE(jinE, type) {
      const count = type === 'jinjia' ? this.jinJiaJEXSDW : this.lingShouJEXSDW;
      return formatJiaGe_2(Math.abs(Number(jinE)), count);
    },
    jiSuanJE(data, rowData = {}) {
      let lingShouJEHJ = 0;
      let jinJiaJEHJ = 0;
      data.forEach((item) => {
        let lingShouJia = Number(item.lingShouJia);
        let jinJia = Number(item.jinJia);
        if (jinJia) item.jinXiaoCJL = subtract(divide(lingShouJia, jinJia), 1);
        else {
          item.jinXiaoCJL = 0;
        }
        lingShouJEHJ = add(
          lingShouJEHJ,
          formatJiaGe_2(item.lingShouJE, this.lingShouJEXSDW),
        );
        jinJiaJEHJ = add(
          jinJiaJEHJ,
          formatJiaGe_2(item.jinJiaJE, this.jinJiaJEXSDW),
        );
      });
      // lingShouJEHJ
      this.rowData.lingShouJEHJ = formatJiaGe_2(
        this.rowData.lingShouJE,
        this.lingShouJEXSDW,
      );
      // jinJiaJEHJ
      this.rowData.jinJiaJEHJ = formatJiaGe_2(
        this.rowData.jinJiaJE,
        this.jinJiaJEXSDW,
      );
      this.rowData.yaoPinZS = data.length;
      if (rowData?.jinJiaJE) {
        this.rowData.jinJiaJEHJ = rowData.jinJiaJE;
      }
      if (rowData?.lingShouJE) {
        this.rowData.lingShouJEHJ = rowData.lingShouJE;
      }
    },
    formatDate(date) {
      return yaoKuZDJZTimeShow(date);
    },
    handleClickBodyCloseDrawer(e) {
      this.closeDrawer();
      // if (!this.$refs.rukudandrawer.$.$el.contains(e.target)) {
      // }
    },
    //打开
    async openDrawer(option) {
      this.pageLoading = true;
      try {
        this.drawer = true;
        this.getColumnInit();
        this.type = option.type ? option.type : 1;
        this.rowData = await GetYaoPinRKDXQ({ id: option.id });
        this.rowData.ruKuDanXQs.forEach((item) => {
          if (this.rowData.kuCunZJDM == '2') {
            item.ruKuHKCSL = item.kuCunSL - item.ruKuSL;
          } else {
            item.ruKuHKCSL = item.kuCunSL + item.ruKuSL;
          }
        });
        this.title = `详情 - ${this.rowData.ruKuDH}`;
        this.jiSuanJE(this.rowData.ruKuDanXQs, option.rowData);
      } finally {
        this.pageLoading = false;
      }
      return new Promise((resolve, reject) => {
        this.finish = resolve;
        this.reject = reject;
      });
    },
    handleDaoChu() {},
    //预览
    async handleYuLan() {
      const params = {
        ruKuDanID: this.rowData.id,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handleDaYin() {
      try {
        this.pageLoading = true;
        const params = {
          ruKuDanID: this.rowData.id,
        };
        await printByUrl('YKXT004', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    async handleYuLanZLYSD() {
      const params = {
        ruKuDanID: this.rowData.id,
      };
      this.params = params;
      this.$refs.daYinZLYSDDialog.showModal();
    },
    async handleDaYinZLYSD() {
      try {
        this.pageLoading = true;
        const params = {
          ruKuDanID: this.rowData.id,
        };
        await printByUrl('YKXT013', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    handleJiZhang() {
      this.$emit('jizhang', { id: this.rowData.id });
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
    'dayin-zlysddialog': DaYinZLYSDDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-shouliqldrawermodal {
  position: initial !important;
}
</style>

<style lang="scss" scoped>
::v-deep .yaoPinLXMCHeader > .cell {
  display: none;
}
.#{$md-prefix}-shouliqldrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;

  .#{$md-prefix}-drawer-1 {
    display: flex;
    flex-direction: column;
    height: 100%;

    .#{$md-prefix}-rkd-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }

        .#{$md-prefix}-chonghongBZ {
          display: inline-block;
          margin-left: 4px;
          width: 20px;
          height: 20px;
          background-color: #f12933;
          border-radius: 2px;
          color: #ffffff;
          font-size: 14px;
          line-height: 20px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-toolbar {
        margin-right: 18px;
      }

      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }

    .#{$md-prefix}-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      // margin-top: 10px;
      padding: 0 8px 8px 8px;
      ::v-deep .caozuo-icon {
        transform: translateY(3px);
      }
      &-description-item {
        margin-top: 12px;

        &:nth-last-child(n + 1) {
          margin-right: 48px;
        }

        > label {
          height: 20px;
          color: #666666;
          font-size: 14px;
          line-height: 20px;
        }

        > span {
          height: 20px;
          color: #222222;
          font-size: 14px;
          line-height: 20px;
        }
      }

      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }

      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }

      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }

      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        margin: 3px 8px 0 0;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;

        &.#{$md-prefix}-shouli {
          background-color: #e2efff;
          color: #1e88e5;
        }

        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }

      .#{$md-prefix}-description {
        display: flex;

        &-item {
          line-height: 20px;
          min-height: 20px;
          font-size: 14px;
          color: #333;
          padding: 5px 0;

          &__label {
            color: #666;
            margin-left: 8px;
          }

          &__content {
            padding-left: 5px;

            &.#{$md-prefix}-fontWeight {
              font-weight: bold;
            }

            .#{$md-prefix}-content-color {
              color: #666;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
}

.#{$md-prefix}-pos-bottom-direction {
  justify-content: space-between;
}

::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}

::v-deep .#{$md-prefix}-description-item {
  color: #222;
}

::v-deep .#{$md-prefix}-description-item {
  font-size: 14px;
}

::v-deep .#{$md-prefix}-drawer__body {
  min-height: 0;
}

::v-deep .#{$md-prefix}-chonghong-number__color .cell {
  color: #f12933 !important;
}
</style>
