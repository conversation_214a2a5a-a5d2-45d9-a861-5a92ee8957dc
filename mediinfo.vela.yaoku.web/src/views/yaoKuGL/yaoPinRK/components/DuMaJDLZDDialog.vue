<template>
  <md-dialog
    title="提醒"
    size="large"
    v-model="dialogVisible"
    :custom-class="prefixClass('tixing-dialog')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    @close="handleClose"
  >
    <div :class="prefixClass('content')">
      <span class="textColor"
        >当前入库单中同时存在普通药品和毒麻精药品，无法入库！以下为毒麻精药品:</span
      >
      <div class="table">
        <md-table
          :data="tableData"
          :columns="columns"
          height="100%"
          autoFill="true"
        >
          <template #tiXing="{ row, $index }">
            <span class="textColor">{{ row.tiXing }}</span>
          </template>
        </md-table>
      </div>
    </div>
    <template #footer>
      <md-button
        type="primary"
        :class="prefixClass('right-12 buttons')"
        @click="handleSave(2)"
        >删除药品</md-button
      >
    </template>
  </md-dialog>
</template>
<script setup lang="ts">
import { defineEmits, ref } from 'vue';

interface tableItem {
  yaoPinMC: String | Number;
}

const tableData = ref<tableItem[]>([]);
const daoChuData = ref<[]>([]);
const dialogVisible = ref(false);
const columns = ref([
  {
    label: '药品名称与规格',
    prop: 'yaoPinMC',
    minwidth: 220,
    type: 'text',
  },
  {
    label: '产地',
    prop: 'chanDiMC',
    type: 'text',
    minwidth: 100,
  },
]);
const resolved = ref();
const rejected = ref();
const showShanChu = ref();
//传方法的
const emit = defineEmits(['']);

//open dialig
async function showDialog(arr: []) {
  dialogVisible.value = true;
  tableData.value = arr.filter((fl) => fl.duLiFLDM != 0);
  daoChuData.value = arr.filter((fl) => fl.duLiFLDM == 0);
  return new Promise((resolve, reject) => {
    resolved.value = resolve;
    rejected.value = reject;
  });
}
function handleClose() {
  dialogVisible.value = false;
}
//保存 把录入的数据带入入库单
async function handleSave() {
  handleClose();
  resolved.value(daoChuData.value);
}
//去掉单元格padding
function cellStyle() {
  return { padding: 0 };
}
//抛出方法，ref可调用到
defineExpose({
  showDialog,
});
</script>
<style lang="scss" scoped>
.#{$md-prefix}-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  span {
    padding-right: 8px;
  }

  .textColor {
    color: #ff8600;
    display: block;
    margin-bottom: 8px;
  }
  .table {
    flex: 1;
    overflow: hidden;
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-tixing-dialog {
  .mediinfo-vela-yaoku-web-dialog__footer {
    height: 46px;
    background: #e6f3ff !important;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .mediinfo-vela-yaoku-web-scrollbar__view {
    height: 100%;

    .mediinfo-vela-yaoku-web-dialog__inner-wrapper {
      height: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
  }
}
</style>
