<template>
  <md-dialog
    v-model="dialogVisible"
    title="批号效期来源"
    size="small"
    :close-on-click-modal="false"
    :class="prefixClass('peiZhiSZDialog')"
  >
    <div class="formClass">
      <span>批号效期来源</span>
      <md-radio-group v-model="piHaoXQLY" border class="flex1">
        <md-radio label="1">药库上一次入库的批号效期</md-radio>
        <md-radio label="2">药房库存最新批号效期</md-radio>
      </md-radio-group>
    </div>
    <div class="formClass" v-show="piHaoXQLY == 2">
      <span>药房</span>
      <md-select v-model="piHaoXQLYWZ" class="flex1">
        <md-option
          v-for="item in yaoFangList"
          :key="item.weiZhiID"
          :label="item.weiZhiMC"
          :value="item.weiZhiID"
        >
        </md-option>
      </md-select>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <md-button @click="handleClose">取消</md-button>
        <md-button type="primary" @click="handleSave"> 确定 </md-button>
      </span>
    </template>
  </md-dialog>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { MdMessage, MdMessageBox, useNamespace } from '@mdfe/medi-ui';
import { GetYaoFangListByJGID } from '@/service/yaoPin/yaoPinZD';
import { getJiGouID } from '@/system/utils/local-cache';
const ns = useNamespace('pihaoxqlydialog');
const dialogVisible = ref(false);
const resolved = ref();
const rejected = ref();
const piHaoXQLY = ref();
const piHaoXQLYWZ = ref();
interface yaoFangItem {
  weiZhiMC: string;
  weiZhiID: string;
}
const yaoFangList = ref<yaoFangItem[]>([]);
//传方法的
const emit = defineEmits(['']);

//open dialog
async function showDialog() {
  dialogVisible.value = true;
  piHaoXQLYWZ.value = '';
  piHaoXQLY.value = '';
  return new Promise((resolve, reject) => {
    resolved.value = resolve;
    rejected.value = reject;
  });
}
// 获取药房数据
async function getYaoFangList() {
  yaoFangList.value = await GetYaoFangListByJGID({ zuZhiJGID: getJiGouID() });
}
function handleClose() {
  dialogVisible.value = false;
}
async function handleSave() {
  if (!piHaoXQLY.value || (piHaoXQLY.value == 2 && !piHaoXQLYWZ.value)) {
    MdMessage.warning('请完成表单选择项！');
    return;
  }
  handleClose();
  resolved.value({
    piHaoXQLYWZ: piHaoXQLYWZ.value,
    piHaoXQLY: piHaoXQLY.value,
  });
}

getYaoFangList();
//抛出方法，ref可调用到
defineExpose({
  showDialog,
});
</script>
<style lang="scss" scoped>
.formClass {
  display: flex;
  align-items: center;
  margin-bottom: var(--md-spacing-3);
  span {
    display: block;
    width: 90px;
    text-align: right;
    margin-right: var(--md-spacing-3);
  }
  .flex1 {
    flex: 1;
  }
}
</style>
