<template>
  <div :class="prefixClass('weijizhang-box')">
    <div :class="prefixClass('weijizhang-content-top')">
      <div :class="prefixClass('weijizhang-content-top-filters')">
        <md-date-picker-range-pro
          v-model="timeRange"
          style="margin-right: 8px; width: 250px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        >
        </md-date-picker-range-pro>

        <md-select
          v-model="query.chuRuKFSID"
          filterable
          style="margin-right: 8px; width: 135px"
          @clear="handleClear"
          @change="handleSelectChange($event, 'chuRuKFS')"
        >
          <md-option
            v-for="item in chuRuKFSOptions"
            :key="item.value"
            :label="item.chuRuKFSMC"
            :value="item.chuRuKFSID"
          />
        </md-select>
        <gonghuodw-select
          v-model="query.gongHuoDWID"
          @select-change="handleSearch"
        />
        <md-input
          v-model="query.RuKuDH"
          :class="prefixClass('filter-width')"
          placeholder="输入单据号搜索"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('input__icon icon-seach')"
            slot="suffix"
            @click="handleSearch"
          />
        </md-input>
        <biz-yaopindw
          v-model="yaoPinMCObj"
          placeholder="名称、输入码、别名、规格等关键字进行搜索"
          style="width: 340px; margin-left: 8px"
          @change="handleQueryYaoPinMC"
        >
        </biz-yaopindw>
      </div>
      <div :class="prefixClass('weijizhang-content-top-buttons')">
        <md-popover
          trigger="click"
          placement="bottom-end"
          width="100px"
          :show-arrow="false"
          popper-style="min-width:90px"
        >
          <template #reference>
            <md-button type="primary" plain class="piLiangDY-btn">
              <span @click="handlerPLDY">批量打印</span>
              <md-icon name="xiajiantou-k" class="xiajiantou-k" />
            </md-button>
          </template>
          <md-checkbox-group v-model="checkedDaYin" :inline="false">
            <md-checkbox
              v-for="item in daYinList"
              :label="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </md-checkbox>
          </md-checkbox-group>
        </md-popover>
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          style="margin-left: auto"
          @click="handleSearch"
          >刷新</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('weijizhang-table-box')">
      <md-table-pro
        :columns="columns"
        height="100%"
        border
        :stripe="false"
        :onFetch="handleFetch"
        ref="table"
        :level="level"
        :controlLevel="controlLevel"
        :customLevels="customLevels"
        :control-loading="controlLoading"
        @select-all="onSelection"
        @select="onSelection"
        :controlColumnLayout="controlColumnLayout"
        @sort-change="handleSortChange"
        @getNewColumn="getNewColumn"
        @recovery-column="recoveryColumn"
        @control-cancel="controlCancel"
        @level-change="levelChange"
      >
        <template #yaoPinMX="{ row }">
          <biz-tag-list
            :list="row.yaoPinXSs"
            @clickMore="({ event }) => handleRowClick(event, row)"
          >
          </biz-tag-list>
        </template>
        <template #ruKuDH="{ row }">
          <div :class="prefixClass('item-inline')">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('rukudantip')"
            >
              <template #content>
                <div @click="copy(row.ruKuDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template v-slot:reference> -->
              <span
                :class="prefixClass('rukudh')"
                @click="handleRowClick($event, row)"
                >{{ row.ruKuDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
            <div :class="prefixClass('chonghongBZ')" v-if="row.hongDanBZ">
              冲
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <md-button type="text-bg" @click="handleDaYin(row)">打印 </md-button>
        </template>
      </md-table-pro>
      <div :class="prefixClass('weijizhang-table-footer')">
        <span>合计</span>
        进价金额： <span :class="prefixClass('number')">{{ jinJiaJE }}</span> 元
        <span style="margin-left: 5px"> 零售金额：</span>
        <span :class="prefixClass('number')">{{ lingShouJJE }}</span> 元
        <span style="margin-left: 5px"> 进零差额：</span>
        <span
          :class="prefixClass(Number(jinLingCE) < 0 ? 'colorRed' : 'number')"
          >{{ jinLingCE }}</span
        >
        元
      </div>
    </div>
    <rukudan-detail-drawer
      ref="rukudandetail"
      size="75%"
      :jinJiaJEXSDW="jinJiaJEXSDW"
      :lingShouJEXSDW="lingShouJEXSDW"
    />
  </div>
</template>

<script>
import columnMixin from '@/components/mixin/columnMixin';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { logger } from '@/service/log';
import BizTagList from '@/components/BizTagList';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import {
  GetYaoPinRKDCount,
  GetYaoPinRKDList,
} from '@/service/yaoPinYK/yaoPinRK';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { yaoKuZDJZTimeShow1 } from '@/system/utils/formatDate';
import { getJiGouID, getWeiZhiID } from '@/system/utils/local-cache';
import { printByUrl, sleep } from '@/system/utils/print';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import useClipboard from 'vue-clipboard3';
import RukudanDetailDrawer from './RuKuDanDetailDrawer';

export default {
  name: 'yijizhang-page',
  mixins: [columnMixin],
  props: {
    isChongHong: {
      type: String,
      default: '0',
    },
    openId: {
      type: String,
      default: '',
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
  },
  data() {
    return {
      selectList: [],
      timeRange: [],
      yaoPinMCObj: null,
      query: {
        jiaGeID: '',
        kaiShiSJ: '',
        jieShuSJ: '',
        chuRuKFSID: '',
        gongHuoDWID: '',
        RuKuDH: '',
      },
      // 表格排序方式
      sortObj: {
        sortField: '', //排序字段
        sortDir: '', //排序方向
      },
      xiaoShuDianWS: 2,
      gongHuoDWOptions: [],
      chuRuKFSOptions: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
          slot: 'ruKuDH',
          prop: 'ruKuDH',
          label: '入库单',
          minWidth: 160,
          field: true,
        },
        {
          prop: 'chuRuKFSMC',
          label: '入库方式',
          minWidth: 108,
          showOverflowTooltip: true,
          field: true,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          minWidth: 188,
          showOverflowTooltip: true,
          field: true,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          minWidth: 65,
          align: 'right',
          field: true,
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          minWidth: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 || Number(scope.row.jinJiaJE) < 0
                ? 'chonghong-number__color'
                : '';
            return h(
              'span',
              { class: className },
              formatJiaGe_2(scope.row.jinJiaJE, this.jinJiaJEXSDW),
            );
          },
          field: true,
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          minWidth: 120,
          align: 'right',
          render: (h, scope) => {
            let className =
              scope.row.hongDanBZ === 1 || Number(scope.row.lingShouJE) < 0
                ? 'chonghong-number__color'
                : '';
            return h(
              'span',
              { class: className },
              formatJiaGe_2(scope.row.lingShouJE, this.lingShouJEXSDW),
            );
          },
          field: true,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          minWidth: 143,
          showOverflowTooltip: true,
          field: true,
        },
        {
          slot: 'yaoPinMX',
          prop: 'yaoPinMX',
          label: '药品明细',
          minWidth: 200,
          field: true,
        },
        {
          prop: 'jiZhangSJ',
          label: '记账日期',
          minWidth: 108,
          formatter: (row) => {
            return yaoKuZDJZTimeShow1(row.jiZhangSJ);
          },
          field: true,
        },
        {
          prop: 'jiZhangRXM',
          label: '记账人',
          minWidth: 110,
          field: true,
        },
        {
          slot: 'operate',
          prop: 'operate',
          label: '操作',
          minWidth: 75,
          fixed: 'right',
          control: true,
          labelClassName: 'set-Icon',
        },
      ],
      jinJiaJE: '',
      lingShouJJE: '',
      jinLingCE: '',
      checkedDaYin: [], // 选择打印
      daYinList: [
        {
          label: '入库单',
          value: '1',
        },
        {
          label: '验收单',
          value: '2',
        },
      ],
    };
  },
  watch: {
    openId: {
      handler: function (val) {
        if (this.isChongHong === '2') {
          this.handleChongHong(val);
        }
      },
      immediate: true,
    },
  },
  async created() {
    await this.getColumnInit();
    await this.getZiDIanSJ();
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      this.xiaoShuDianWS = await GetCanShuZhi(params);
    } catch (error) {
      logger.error(error);
    }
  },
  mounted() {
    this.timeRange = [
      dayjs().format('YYYY-MM') + '-01',
      dayjs().format('YYYY-MM-DD'),
    ];
    this.query.kaiShiSJ = dayjs().format('YYYY-MM') + '-01';
    this.query.jieShuSJ = dayjs().format('YYYY-MM-DD');
  },
  methods: {
    onSelection(val) {
      this.selectList = val;
    },
    async handlerPLDY() {
      try {
        if (this.selectList.length == 0) {
          MdMessage({
            type: 'warning',
            message: '请选择要打印的数据！',
          });
          return;
        }
        if (this.checkedDaYin.length == 0) {
          MdMessage({
            type: 'warning',
            message: '请选择要打印的类型！',
          });
          return;
        }
        this.pageLoading = true;
        const hasRuKuD = this.checkedDaYin.some((i) => i === '1');
        const hasYanShouD = this.checkedDaYin.some((i) => i === '2');
        this.selectList.sort((a, b) => a.index - b.index);
        for (let el of this.selectList) {
          if (hasRuKuD) {
            await printByUrl('YKXT004', { ruKuDanID: el.id });
            await sleep();
          }
          if (hasYanShouD) {
            await printByUrl('YKXT013', { ruKuDanID: el.id });
            await sleep();
          }
        }
      } catch (e) {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    /**
     * 获取出入库方式字典
     * @returns {Promise<void>}
     */
    async getZiDIanSJ() {
      await Promise.all([
        GetChuRuKFSByFXDM('1').then((res) => {
          let optionData = [];
          res.forEach((item) => {
            optionData.push({
              chuRuKFSMC: item.fangShiMC,
              chuRuKFSID: item.fangShiID,
            });
          });
          optionData.unshift({
            chuRuKFSMC: '所有入库方式',
            chuRuKFSID: '',
          });
          this.chuRuKFSOptions = optionData;
        }),
      ]);
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    /**
     * 处理其他页面跳转到此时， 自动打开详情抽屉 （冲红跳转、请领跳转）
     * @param id 入库单id
     */
    handleChongHong(id) {
      this.handleClear();
      this.handleSearch();
      this.$nextTick(() => {
        this.$refs.rukudandetail.openDrawer({
          id: id,
          type: 2,
        });
      });
    },
    handleDateRangeChange(val) {
      if (val && val.length > 0) {
        this.query.kaiShiSJ = val[0]
          ? dayjs(val[0]).format('YYYY-MM-DD')
          : null;
        this.query.jieShuSJ = val[1]
          ? dayjs(val[1]).format('YYYY-MM-DD')
          : null;
      } else {
        this.query.kaiShiSJ = '';
        this.query.jieShuSJ = '';
      }
      this.handleSearch();
    },
    handleSelectChange(val, key) {
      let optionsKey = key + 'Options';
      let mingChengKey = key + 'MC';
      let daiMaKey = key + 'ID';
      let data = this[optionsKey].find((item) => item[daiMaKey] === val);
      this.query[mingChengKey] = data ? data[mingChengKey] : '';
      this.handleSearch();
    },
    handleSearch() {
      this.$refs.table.search({ pageSize: 100 });
    },
    handleCopySuccess() {
      MdMessage({
        message: '复制成功',
        type: 'success',
        duration: 2000,
      });
    },
    handleCopyError() {
      // this.$message({
      //   message: '复制失败',
      //   type: 'error',
      //   duration: 2000
      // })
      MdMessageBox({
        title: '系统消息',
        type: 'error',
        message: `复制失败！`,
        confirmButtonText: '我知道了',
      });
    },
    async handleRowClick(e, row) {
      e.stopPropagation();
      await this.$refs.rukudandetail.openDrawer({
        id: row.id,
        type: 2,
        rowData: row,
      });
    },
    async handleDaYin(row) {
      try {
        this.pageLoading = true;
        const params = {
          ruKuDanID: row.id,
        };
        await printByUrl('YKXT004', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        pageIndex: page,
        pageSize,
        DanJuZTDM: '3',
        WeiZhiID: getWeiZhiID(),
        ZuZhiJGID: getJiGouID(),
        ...this.query,
        ...this.sortObj,
      };
      const [items, total] = await Promise.all([
        GetYaoPinRKDList(params, config),
        GetYaoPinRKDCount(params, config),
      ]);
      const itemsWithIndex = items.map((item, index) => ({
        ...item,
        index: index + 1, // 如果需要从 1 开始，使用 index + 1
      }));
      this.jinJiaJE = formatJiaGe_2(total.jinJiaJE, 2);
      this.lingShouJJE = formatJiaGe_2(total.lingShouJJE, 2);
      this.jinLingCE = formatJiaGe_2(this.lingShouJJE - this.jinJiaJE, 2);
      return {
        items: itemsWithIndex,
        total: total.count,
      };
    },
    handleClear() {
      this.query.chuRuKFSID = '';
      this.query.chuRuKFSMC = '';
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.query.jiaGeID = e.jiaGeID || '';
      this.handleSearch();
    },
    /**
     * 升降序
     * **/
    handleSortChange({ column, prop, order }) {
      if (order) {
        this.sortObj = {
          sortField: prop, //排序字段
          sortDir: order == 'ascending' ? 'asc' : 'desc', //排序方向
        };
      } else {
        this.sortObj = {
          sortField: '', //排序字段
          sortDir: '', //排序方向
        };
      }
      this.handleSearch();
    },
  },
  components: {
    'gonghuodw-select': GongHuoDWSelect,
    'rukudan-detail-drawer': RukudanDetailDrawer,
    'biz-tag-list': BizTagList,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
.daYinList {
  min-width: 96px !important;
}
.piLiangDY-btn {
  padding-right: 0;
  .xiajiantou-k {
    display: inline-block;
    padding-right: var(--md-spacing-3);
    padding-left: var(--md-spacing-3);
  }
}
.#{$md-prefix}-weijizhang-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .#{$md-prefix}-weijizhang-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-filters {
      display: flex;
      width: 100%;

      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }

  .#{$md-prefix}-weijizhang-table-box {
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
  }

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .#{$md-prefix}-chonghongBZ {
    margin-left: 4px;
    width: 20px;
    height: 20px;
    background-color: #f12933;
    border-radius: 2px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
  }
}

.#{$md-prefix}-weijizhang-table-footer {
  position: absolute;
  bottom: 16px;
  color: #aaa;
  font-size: 14px;

  .#{$md-prefix}-number {
    font-weight: 600;
    color: #222222;
  }
  .#{$md-prefix}-colorRed {
    color: #f12933;
  }
}

.#{$md-prefix}-rukudh {
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}

::v-deep .chonghong-number__color {
  color: #f12933 !important;
}
</style>
<style lang="scss">
.set-Icon {
  .#{$md-prefix}-table-set-icon {
    padding-top: 4px !important;
  }
}

.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
}

.#{$md-prefix}-rukudantip {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
