<template>
  <!--//TODO-->
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="332px"
    height="240px"
    title="批量登记发票"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <md-form label-width="80px">
      <md-row>
        <md-col :span="24" style="width: 300px">
          <md-form-item label="发票号码">
            <md-input
              v-model="formModel.faPiaoHM"
              maxlength="20"
              placeholder="请输入"
            ></md-input>
          </md-form-item>
        </md-col>
        <md-col :span="24">
          <md-form-item label="发票日期">
            <md-date-picker
              v-model="formModel.faPiaoRQ"
              :class="prefixClass('datepicker')"
              type="date"
              placeholder="请选择"
              :pickerOptions="pickerOptions"
            />
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
  </bmis-blue-dialog>
</template>

<script>
import dayjs from 'dayjs';
//TODO
import BlueDialog from '@/components/blue-dialog/index';

const formModelInit = () => {
  return {
    faPiaoHM: '',
    faPiaoRQ: '',
  };
};
export default {
  name: 'piliangdjfp-dialog',
  data() {
    return {
      visibleDialog: false,
      formModel: formModelInit(),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  methods: {
    async showModal() {
      this.visibleDialog = true;
      this.formModel = formModelInit();
      return new Promise((resolve, reject) => {
        this.finish = resolve;
        this.reject = reject;
      });
    },
    handleDateChange(val) {
      this.formModel.faPiaoRQ = val ? dayjs(val).format('YYYY-MM-DD') : '';
    },
    handleSubmit() {
      this.finish(this.formModel);
      this.visibleDialog = false;
    },
    handleClose() {
      this.reject();
    },
  },
  components: {
    //TODO
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
.#{$md-prefix}-col-24 {
  max-width: 300px !important;
}
</style>
