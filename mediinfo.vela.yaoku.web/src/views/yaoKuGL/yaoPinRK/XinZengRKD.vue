<template>
  <div :class="prefixClass('inventory-container')">
    <div :class="prefixClass('add-inventory')" v-loading="pageLoading">
      <div :class="prefixClass('add-inventory-header')">
        <div :class="prefixClass('add-inventory-header__action')">
          <div :class="prefixClass('add-inventory-header__action__left')">
            <span :class="prefixClass('rkd-title')"
              ><i class="iconfont icondanju"></i>入库单</span
            >
            <biz-yaopindw
              v-model="query.likeQuery"
              valueKey="yaoPinMC"
              labelKey="yaoPinMC"
              :showXiaoGuiGeYP="showXiaoGuiGeYP"
              @change="handleKuaiSuDW($event, this)"
              @keyup.enter.native="handleEnter"
            >
            </biz-yaopindw>
          </div>
          <div :class="prefixClass('add-inventory-header__action__right')">
            <md-button
              v-if="!isChongHong"
              type="danger"
              :icon="prefixClass('icon-shanchuwap')"
              noneBg
              @click="handleDelete"
              >删除
            </md-button>
            <md-button
              v-if="!isChongHong"
              type="primary"
              :icon="prefixClass('icon-shengcheng')"
              noneBg
              @click="handlePiLiangDJFP"
              >批量登记发票
            </md-button>
            <md-button type="primary" plain @click="handleSave">保存</md-button>
            <md-button type="primary" @click="handleSaveAndJiZhang"
              >保存并记账
            </md-button>
          </div>
        </div>
        <md-form
          :model="params"
          ref="form"
          label-width="90px"
          use-status-tooltip-icon
        >
          <md-row>
            <md-col :span="5">
              <md-form-item label="入库方式" prop="chuRuKFSID" :required="true">
                <CustomSelect
                  v-model="params.chuRuKFSID"
                  placeholder="请选择活动区域"
                  automatic-dropdown
                  :disabled="canEdit"
                  filterable
                  @change="handleSelectChange($event, 'chuRuKFS')"
                >
                  <md-option
                    v-for="item in chuRuKFSOptions"
                    :key="item.chuRuKFSID"
                    :label="item.chuRuKFSMC"
                    :value="item.chuRuKFSID"
                  />
                </CustomSelect>
                <!-- <md-select
                  v-model="params.chuRuKFSID"
                  :disabled="canEdit"
                  filterable
                  @change="handleSelectChange($event, 'chuRuKFS')"
                >
                  <md-option
                    v-for="item in chuRuKFSOptions"
                    :key="item.chuRuKFSID"
                    :label="item.chuRuKFSMC"
                    :value="item.chuRuKFSID"
                  />
                </md-select> -->
              </md-form-item></md-col
            >
            <md-col :span="5">
              <md-form-item label="入库日期" prop="zhiDanSJ" :required="true">
                <!-- <md-date-picker
                  v-model="params.zhiDanSJ"
                  type="date"
                  :disabled="isChongHong"
                  range-separator="/"
                  placeholder="开始日期"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                >
                </md-date-picker> -->
                <CustomDatePicker
                  v-model="params.zhiDanSJ"
                  type="date"
                  :disabled="isChongHong"
                  range-separator="/"
                  placeholder="开始日期"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                ></CustomDatePicker>
              </md-form-item>
            </md-col>
            <md-col :span="9">
              <md-form-item
                label="供货单位"
                prop="gongHuoDWMC"
                :required="true"
              >
                <gonghuodw-select
                  :value.sync="params.gongHuoDWMC"
                  :gongHuoDWList="gongHuoDWList"
                  style="flex: 1; width: 100%"
                  automatic-dropdown
                  placeholder="输入选择供货单位"
                  :disabled="isChongHong"
                  @select-change="handleChangeGongHuoDW"
                /> </md-form-item
            ></md-col>
            <md-col :span="5">
              <md-form-item label="附单张数">
                <md-input
                  v-model="params.fuDanZS"
                  v-number
                  :disabled="isChongHong"
                  placeholder="请输入"
                />
              </md-form-item>
            </md-col>
            <md-col :span="5" v-if="showZhangBLB == 1">
              <md-form-item
                label="账簿类别"
                prop="zhangBuLBID"
                :required="true"
              >
                <CustomSelect
                  v-model="params.zhangBuLBID"
                  placeholder="请选择活动区域"
                  automatic-dropdown
                  filterable
                  :clearable="false"
                  @change="handleChangeZB"
                  @focus="handlerFocusZB"
                >
                  <md-option
                    v-for="item in zhangBuOptions"
                    :key="item.zhangBuLBID"
                    :label="item.zhangBuLBMC"
                    :value="item.zhangBuLBID"
                  />
                </CustomSelect> </md-form-item
            ></md-col>

            <md-col :span="5" v-if="isShowLX">
              <md-form-item label="类型" prop="beiZhu">
                <md-select
                  v-model="yinPianKLBZ"
                  placeholder="请选择"
                  style="width: 300px"
                >
                  <md-option
                    v-for="item in yinPianKLBZList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </md-option>
                </md-select>
              </md-form-item>
            </md-col>
            <md-col :span="beiZhuColLength">
              <md-form-item label="备注" prop="beiZhu">
                <md-input
                  v-model="params.beiZhu"
                  :disabled="isChongHong"
                  placeholder="请输入"
                  @keyup.enter.native="hanldeBeiZhu"
                />
              </md-form-item>
            </md-col>
          </md-row>
        </md-form>
      </div>
      <div v-show="!isChongHong" :class="prefixClass('add-inventory-body')">
        <md-editable-table-pro
          v-model="tableData"
          v-table-enter="(data) => handleTableEnterForRK(data)"
          height="100%"
          :autoFill="true"
          :columns="columns"
          :showDefaultOperate="false"
          :hide-add-button="true"
          :cell-style="cellStyle"
          :cell-class-name="cellClassName"
          :close-control-on-confirm="false"
          :close-control-on-recovery="false"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          id="editTable"
          ref="editTable"
          :key="tableKey"
          :maxLength="9999"
          @sort-change="handleSortChange"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #caoZuoSZDM="{ row, cellRef }">
            <md-select
              v-model="row.caoZuoSZDM"
              :disabled="row.canShuRu"
              @change="CaoZuoSZChange($event, row, cellRef)"
            >
              <md-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </template>
          <template #bieMing="{ row, cellRef }">
            <md-input v-model="row.bieMing" placeholder="请填写" clearable />
          </template>
          <template #yaoPinMCYGG="{ row, cellRef, $index }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              labelKey="yaoPinZC"
              :isRuKuPage="true"
              :showXiaoGuiGeYP="showXiaoGuiGeYP"
              :zhangBuLBID="params.zhangBuLBID == '0' ? '' : params.zhangBuLBID"
              :yinPianKLBZ="yinPianKLBZ"
              @change="handleTableYaoPinDWChange($event, row, $index, cellRef)"
              :class="prefixClass('yaopin-select')"
            >
            </biz-yaopindw>
          </template>
          <template #faPiaoHM="{ row }">
            <md-input
              v-model="row.faPiaoHM"
              placeholder="请填写"
              @blur="handleFaPiaoHMBlur(row)"
            />
          </template>
          <template #faPiaoRQ="{ row, cellRef }">
            <md-date-picker
              v-model="row.faPiaoRQ"
              placeholder="请选择"
              :editable="true"
              type="date"
              format="YYYYMMDD"
              value-format="YYYY-MM-DD"
              no-default-selected
              @change="handleFaPiaoRQChange($event, row)"
              :pickerOptions="pickerOptions"
              @blur="cellRef.endEdit()"
            />
          </template>
          <template #ruKuCDMC="{ row, $index }">
            <md-select
              v-model="row.ruKuCDMC"
              filterable
              remote
              :remote-method="($event) => filterMethod($event, 'ruKuList')"
              placeholder="请选择"
              automatic-dropdown
              :clearable="false"
            >
              <!-- @change="handleChanDiChange($event, $index)" -->
              <md-option
                v-for="item in ruKuList"
                :key="item.chanDiID"
                :label="item.chanDiMC"
                :value="item.chanDiMC"
              ></md-option>
            </md-select>
            <!-- <md-input v-model="row.ruKuCDMC" maxlength="30"></md-input> -->
          </template>
          <template #shengChanQY="{ row }">
            <md-select
              v-model="row.shengChanQY"
              filterable
              remote
              :remote-method="($event) => filterMethod($event, 'shengChanList')"
              placeholder="请选择"
              automatic-dropdown
              :clearable="false"
            >
              <md-option
                v-for="item in shengChanList"
                :key="item.chanDiID"
                :label="item.chanDiMC"
                :value="item.chanDiMC"
              ></md-option>
            </md-select>
            <!-- <md-input v-model="row.shengChanQY" maxlength="30"></md-input> -->
          </template>
          <template #ruKuSL="{ row }">
            <md-input
              v-model="row.ruKuSL"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @blur="CheckRuKuSL(row)"
            />
          </template>
          <template #jinJia="{ row, $index }">
            <md-input
              v-model="row.jinJia"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @input="jiSuanLSJByJJ($event, row)"
              @change="handlerJinJia($event, row, $index)"
            />
          </template>
          <template #jinJiaJE="{ row, $index }">
            <md-input
              v-model="row.jinJiaJE"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @change="handleChangeJJJE($event, row, $index)"
            />
          </template>
          <template #kouLv="{ row, $index }">
            <md-input
              v-model="row.kouLv"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @change="handlerKouLv($event, row, $index)"
            />
          </template>
          <template #lingShouJia="{ row, $index }">
            <md-input
              v-model="row.lingShouJia"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @change="handlerJinJia(row, $index)"
            />
          </template>
          <template #shengChanPH="{ row, $index }">
            <md-input-picker-pro
              v-model="tableData[$index].shengChanPH"
              :options="tableData[$index].shengChanPHList"
              placeholder="请填写"
              @blur="handlePiHaoBlur(row, $index)"
            />
          </template>
          <template #yaoPinXQ="{ row, $index, cellRef }">
            <md-date-picker
              :key="timestamp"
              v-model="row.yaoPinXQ"
              placeholder="请选择"
              :editable="true"
              type="date"
              format="YYYYMMDD"
              value-format="YYYY-MM-DD"
              no-default-selected
              @change="handelYaoPinXQ($event, row, $index, cellRef)"
              @update:modelValue="handelYaoPinXQ($event, row, $index, cellRef)"
              :shortcuts="row.shortcuts"
            />
          </template>
          <template #shengChanRQ="{ row, $index, cellRef }">
            <md-date-picker
              v-model="row.shengChanRQ"
              placeholder="请选择"
              :editable="true"
              type="date"
              format="YYYYMMDD"
              value-format="YYYY-MM-DD"
              no-default-selected
            />
          </template>
          <template #jianYanZS="{ row, cellRef }">
            <md-select
              v-model="row.jianYanZS"
              placeholder="请选择"
              :clearable="false"
              automatic-dropdown
              ref="selectRef"
              @visible-change="selectChange($event, cellRef)"
              @blur="handleBlur"
            >
              <md-option label="无" :value="0"></md-option>
              <md-option label="有" :value="1"></md-option>
            </md-select>
          </template>
          <template #chuChangJYHGD="{ row, cellRef }">
            <md-select
              v-model="row.chuChangJYHGD"
              placeholder="请选择"
              automatic-dropdown
              @visible-change="selectChange($event, cellRef)"
              :clearable="false"
            >
              <md-option label="不合格" :value="0"></md-option>
              <md-option label="合格" :value="1"></md-option>
            </md-select>
          </template>
          <template #baoZhuangQK="{ row, cellRef }">
            <md-select
              v-model="row.baoZhuangQK"
              placeholder="请选择"
              automatic-dropdown
              @visible-change="selectChange($event, cellRef)"
              :clearable="false"
            >
              <md-option label="不完整" :value="0"></md-option>
              <md-option label="完整" :value="1"></md-option>
            </md-select>
          </template>
          <template #waiGuanZL="{ row, cellRef }">
            <md-select
              v-model="row.waiGuanZL"
              placeholder="请选择"
              automatic-dropdown
              @visible-change="selectChange($event, cellRef)"
              :clearable="false"
            >
              <md-option label="不合格" :value="0"></md-option>
              <md-option label="合格" :value="1"></md-option>
            </md-select>
          </template>
          <template #yanShouJL="{ row, cellRef }">
            <md-select
              v-model="row.yanShouJL"
              placeholder="请选择"
              automatic-dropdown
              @visible-change="selectChange($event, cellRef)"
              :clearable="false"
            >
              <md-option label="不合格" :value="0"></md-option>
              <md-option label="合格" :value="1"></md-option>
            </md-select>
          </template>
          <template #yanShouRenXX="{ row, $index, cellRef }">
            <md-select-table
              v-model="row.yanShouRenXX"
              :fetchData="fetchData"
              :columns="yanShouRemColums"
              labelKey="yongHuXM"
              valueKey="yongHuID"
              filterable
              @visible-change="handleVisibleChange($event, cellRef)"
              @change="handleYSR($event, row, $index)"
            />
            <!-- <md-input v-model="row.yanShouRXM" placeholder="请填写" /> -->
          </template>
          <template #piZhunWH="{ row }">
            <md-input v-model="row.piZhunWH" placeholder="请填写" />
          </template>
          <template #jinKouYPZH="{ row }">
            <md-input v-model="row.jinKouYPZH" placeholder="请填写" />
          </template>
          <template #baiFangWZ="{ row, cellRef }">
            <md-select
              v-model="row.baiFangWZ"
              @visible-change="handleVisibleChange($event, cellRef)"
            >
              <md-option
                v-for="(baiFangWZ, index) in row.baiFangWZList"
                :label="baiFangWZ"
                :value="baiFangWZ"
                :key="index"
              >
              </md-option>
            </md-select>
          </template>
        </md-editable-table-pro>
      </div>
      <div v-show="isChongHong" :class="prefixClass('add-inventory-body')">
        <md-editable-table-pro
          v-model="tableData"
          v-table-enter
          row-key="id"
          height="100%"
          :autoFill="true"
          :columns="xinZengCHRKDColumns"
          hideAddButton
          :showDefaultOperate="false"
          id="editTable1"
          ref="editTable1"
          :maxLength="9999"
        >
          <template #ruKuSL="{ row, $index }">
            <md-input
              v-model="row.ruKuSL"
              placeholder="请填写"
              :clearable="false"
              @blur="CheckRuKuSL(row, $index)"
            />
          </template>
          <template #ruKuSLYL="{ row }">
            <span style="color: #f12933">{{
              Number(row.ruKuSL).toFixed(3)
            }}</span>
          </template>
          <template #lingShouJE="{ row }">
            <span style="color: #f12933">{{
              Number(row.lingShouJE).toFixed(4)
            }}</span>
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('add-inventory-footer')">
        <div :class="prefixClass('add-inventory-footer__info left')">
          <span>制单：</span>
          <span :class="prefixClass('info__name color-222')">{{
            rowData.zhiDanRXM
          }}</span>
          <span :class="prefixClass('info__time color-222')">{{
            rowData.chuangJianSJ
              ? formatDate(rowData.chuangJianSJ)
              : rowData.zhiDanRQ
          }}</span>
        </div>
        <div :class="prefixClass('add-inventory-footer__info right')">
          <div :class="prefixClass('margin-right-12')">
            <span>共计：</span>
            <span :class="prefixClass('font-bold')">{{
              zongShuJEData.yaoPinZS
            }}</span>
            <span class="">种药品</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span style="margin-right: 8px">合计</span>
            <span>进价金额：</span>
            <span :class="prefixClass('font-bold')">{{
              Number(zongShuJEData.jinJiaJE).toFixed(this.jinJiaJEXSDW)
            }}</span>
            <span class="">元</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span>零售金额：</span>
            <span :class="prefixClass('font-bold')">{{
              Number(zongShuJEData.lingShouJE).toFixed(this.lingShouJEXSDW)
            }}</span>
            <span class="">元</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span>进销差额：</span>
            <span :class="prefixClass('font-bold')">{{
              (
                Number(zongShuJEData.lingShouJE) -
                Number(zongShuJEData.jinJiaJE)
              ).toFixed(this.lingShouJEXSDW)
            }}</span>
            <span class="">元</span>
          </div>
        </div>
      </div>
    </div>
    <biz-jiesuants ref="bizInfo" @handleYPXQMEthod="handleYPXQMEthod" />
    <biz-yaopinlsjj ref="yaopinlsjj" :chuRuKFSID="params.chuRuKFSID" />
    <piliangdjfp-dialog ref="piliangdjfp" />
  </div>
</template>

<script>
import Big from 'big.js';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { GetJinRuKYPXQPHListByJGID } from '@/service/yaoPinYF/XiaoQiGL';

import { MdMessage, MdMessageBox, useKeyboard } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import { ref } from 'vue';

import { createPrompt } from '@mdfe/stark-app';

//TODO
// import { SelectNormal } from '@mdfe/bmis-ui'
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinLSJJ from '@/components/YaoKu/BizYaoPinLSJJ';
import { getYongHuListGongYong } from '@/service/gongYong/diZuo';
import { getZhongYaoYPRKJGXZ } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import { ChongHongJZRuKuDan } from '@/service/yaoPinYK/danJuCH';
import {
  AddYaoPinRKD,
  CheckFaPiaoSFCF,
  GetChanDiXXSelectList,
  GetYaoPinCDJGByJGID,
  GetYaoPinCDJGListByJGIDList,
  GetYaoPinRKDXQ,
  JiZhangYaoPinRKD,
  UpdateYaoPinRKD,
  ZuoFeiYaoPinRKD,
  GetYaoPinPHXQByJGID,
} from '@/service/yaoPinYK/yaoPinRK';

import { GetCanShuZhi } from '@/system/utils/canShu';
import commonData from '@/system/utils/commonData';
import { focusEditTableDom } from '@/system/utils/focusEditTable';
import { sortBy } from 'lodash-es';
// import formatJiaGe from '@/system/utils/formatJiaGe';
import BizJieSuanTSDialog from '@/components/BizJieSuanTSDialog';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { formatMoney } from '@/system/utils/formatMoney';
import {
  getJiGouID,
  getKuCunGLLX,
  getWeiZhiID,
  getWeiZhiMC,
  getYongHuID,
  getYongHuXM,
} from '@/system/utils/local-cache';
import { add, multiply } from '@/system/utils/mathComputed';
import { makePY } from '@/system/utils/wubi-pinyin.js';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import MdSelectTable from '@mdfe/material.select-table';
import { h } from 'vue';
import { mapGetters } from 'vuex';

import columnMixin from '@/components/mixin/columnMixin';
import PiliangdjfpDialog from './components/PiLiangDJFPDialog';
import CustomDatePicker from './components/date-picker.vue';
import CustomSelect from './components/select.vue';
import commonList from './js/tableData';
const initData = () => {
  return {
    yaoPinZC: '',
    yaoPinLX: '',
    yaoPinGG: '',
    yaoPinMC: '',
    yaoPinMCYGG: {},
    chanDiMC: '',
    faPiaoHM: '',
    faPiaoRQ: '',
    baoZhuangDW: '',
    kuCunSL: '',
    ruKuHKCSL: '',
    ruKuSL: '',
    jinJia: '',
    jiaGeID: '',
    jinJiaJE: '',
    lingChaJBZ: '',
    lingShouJia: '',
    lingShouJE: '',
    shengChanPH: '',
    shengChanPHList: [],
    zuiJinRKSCPH: '',
    shengChanRQ: '',
    caiGouBZ: '',
    shengChanQY: '',
    yaoPinXQ: null,
    shortcuts: [],
    jianYanZS: 1,
    chuChangJYHGD: 1,
    baoZhuangQK: 1,
    waiGuanZL: 1,
    ruKuCDMC: '',
    yanShouJL: 1,
    yanShouRXM: getYongHuXM(),
    yanShouRID: getYongHuID(),
    yanShouRenXX: {
      yongHuXM: getYongHuXM(),
      yongHuID: getYongHuID(),
    },
    yanShouRQ: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    yanShouBZ: 1,
    piZhunWH: '',
    jinKouYPZH: '',
    baiFangWZ: '',
    baiFangWZList: [],
  };
};

export default {
  name: 'xinzengrkd',
  inject: ['viewManager'],
  mixins: [commonList, columnMixin],
  data() {
    this.stopPrompt = null;
    return {
      errorIndex: 0,
      yanShouRenXX: null, //储存验收人信息
      closeTimeOut: null,
      query: {
        likeQuery: {}, // 药品定位数据绑定
      },
      yaoPinXQIndex: 0, //药品效期索引
      shiFouXGLSJ: false, //零售价是否根据进价改变
      routerType: null, // 记录路由传入的type，判断是否要更新页面数据， 新增入库时使用
      isCaiGouRK: false, // 按采购入库标志
      pageLoading: false,
      xiaoShuDianWS: 3,
      zhangBuOptions: [],
      tableKey: 0,
      params: {
        caiGouDIDList: [],
        zhangBuLBID: '', //账簿类别
        zhangBuLBMC: '',
        zuZhiJGID: getJiGouID(), // 组织机构id
        xinZengFS: '1', // 0 为按采购单入库， 1  普通新增
        chuRuKFSMC: '', // 出入库方式名称
        chuRuKFSID: '',
        zhiDanSJ: dayjs().format('YYYY-MM-DD'), //制单时间 （页面显示为入库日期）
        gongHuoDWID: '', // 供货单位
        gongHuoDWMC: '',
        yingFuKDJMC: '', // 应付款等级
        yingFuKDJDM: '',
        fuDanZS: '', // 附单张数
        beiZhu: '', // 备注
        yaoPinZS: 0, // 药品总数
        hongDanBZ: 0, // 红单标志
        jinJiaJE: 0, // 进价金额
        lingShouJE: 0, // 零售金额
        weiZhiID: getWeiZhiID(),
        weiZhiMC: getWeiZhiMC(),
      },
      yinPianKLBZ: '', //类型
      yinPianKLBZList: [
        { value: 1, label: '颗粒' },
        { value: 2, label: '饮片' },
      ],
      gongHuoDWList: {
        gongHuoDWID: '',
        gongHuoDWMC: '',
      },
      chuRuKFSOptions: [],
      yingFuKDJOptions: [],
      columns: [
        {
          prop: 'xunHao',
          label: '序号',
          width: 70,
          control: true,
          align: 'center',
          formatter: (row, column, cellValue, index) => {
            return index + 1;
          },
          // labelClassName: 'sheZhiHeader',
          // renderHeader: ({ column, _self }) => {
          // return [ h('div', {  }, column.label),
          //     h(MdIcon, {
          //       name: "shezhi",
          //       onClick: (event) => {
          //         this.showTableSet()
          //       },
          //     })
          //   ]
          // },
        },
        {
          type: 'selection',
          width: 40,
          selectable: (row, index) => {
            return this.tableData.length !== index + 1;
          },
        },
        {
          prop: 'yaoPinLXMC',
          label: '药品类型',
          width: 32,
          align: 'center',
          type: 'text',
          field: true,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
          labelClassName: 'yaoPinLXMCHeader',
          className: 'yaoPinLXMC',
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 120,
          slot: 'faPiaoHM',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 120,
          slot: 'faPiaoRQ',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          slot: 'yaoPinMCYGG',
          field: true,
          formatter: (row, column, cellValue, index) => {
            if (cellValue.yaoPinMC && cellValue.yaoPinGG)
              return (
                row.tianJiaWZ + cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG
              );
            return row.yaoPinZC;
          },
          endMode: 'custom',
          fieldDisabled: true,
          labelClassName: 'requireHeader',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          sortable: false,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
          fieldDisabled: true,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 200,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
        },
        {
          prop: 'ruKuCDMC',
          label: '入库产地',
          minWidth: 200,
          slot: 'ruKuCDMC',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          // hidden: true,
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'shengChanQY',
          label: '生产企业',
          minWidth: 200,
          slot: 'shengChanQY',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          hidden: false,
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'canKaoKL',
          label: '参考扣率',
          width: 110,
          type: 'text',
          align: 'right',
          field: true,
          hidden: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 70,
          field: true,
          type: 'text',
          fieldDisabled: true,
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          width: 100,
          field: true,
          type: 'text',
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 110,
          type: 'text',
          align: 'right',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0);
          },
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          width: 140,
          slot: 'ruKuSL',
          field: true,
          sortable: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          align: 'right',
        },
        // {
        //   prop: 'ruKuHKCSL',
        //   label: '入库后库存数量',
        //   width: 120,
        //   type: 'text',
        //   align: 'right',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue ? cellValue : 0);
        //   },
        // },
        {
          // 从jinJia和jinJia_ZiDian复制出来的
          prop: 'ziDianJinJia',
          label: '字典进价',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          disabled: true,
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          slot: 'jinJia',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.jinJiaXSDW);
          },
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          fieldDisabled: true,
          align: 'right',
          sortable: true,
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          slot: 'jinJiaJE',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        // {
        //   prop: 'lingChaJBZ',
        //   label: '零差/加价',
        //   width: 120,
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     switch (cellValue) {
        //       case 0:
        //         return '普通';
        //       case 1:
        //         return '零差价';
        //       case 2:
        //         return '加价';
        //       default:
        //         return '';
        //     }
        //   },
        // },
        {
          // slot: 'lingShouJia',
          prop: 'lingShouJia',
          label: '零售价',
          type: 'text',
          width: 100,
          align: 'right',
          field: true,
          fieldDisabled: true,
          // disabled: ({ row }) => {
          //   return !this.canEditLSJ(row.lingChaJBZ)
          // },
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.lingShouXSDW);
          },
          disabled: true,
          // disabled: ({ $index }) => {
          //   if ($index == this.tableData.length - 1) {
          //     return true;
          //   } else if (this.params.chuRuKFSID == '9003') {
          //     return true;
          //   }
          // },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0).toFixed(
              this.lingShouJEXSDW,
            );
          },
        },
        {
          prop: 'kouLv',
          label: '扣率',
          width: 120,
          align: 'right',
          slot: 'kouLv',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? cellValue : 0;
          },
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        {
          prop: 'jiaJiaLv',
          label: '加价率',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          formatter: (row, column, cellValue, index) => {
            if (row.lingShouJia && row.jinJia && row.jinJia != 0) {
              const count1 = new Big(row.lingShouJia);
              const xiaoShuWei = Number(this.jinJiaXSDW);

              const result = count1
                .minus(row.jinJia)
                .div(row.jinJia)
                .toFixed(xiaoShuWei)
                .toString();

              return Number(result).toLocaleString('zh', {
                style: 'percent',
                minimumFractionDigits: 2,
              });
            }
            return '';
          },
          disabled: true,
        },
        {
          prop: 'shengChanPH',
          label: '批号',
          width: 120,
          slot: 'shengChanPH',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          endMode: 'custom',
          labelClassName: 'requireHeader',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'zuiJinRKSCPH',
          label: '最近入库批号',
          width: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'shengChanRQ',
          label: '生产日期',
          width: 120,
          showOverflowTooltip: false,
          slot: 'shengChanRQ',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
          hidden: false,
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 120,
          showOverflowTooltip: false,
          field: true,
          type: 'text',
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          showOverflowTooltip: false,
          slot: 'yaoPinXQ',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'jianYanZS',
          label: '检验证书',
          width: 120,
          slot: 'jianYanZS',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '有' : '无';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'chuChangJYHGD',
          label: '产品合格证',
          width: 120,
          slot: 'chuChangJYHGD',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'baoZhuangQK',
          label: '包装',
          width: 60,
          slot: 'baoZhuangQK',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '完整' : '不完整';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'waiGuanZL',
          label: '外观情况',
          width: 80,
          slot: 'waiGuanZL',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouJL',
          label: '验收结果',
          width: 120,
          slot: 'yanShouJL',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouRenXX',
          slot: 'yanShouRenXX',
          label: '验收人',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            if (row.yanShouRXM) {
              return row.yanShouRXM;
            }
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'piZhunWH',
          label: '批准文号',
          width: 120,
          slot: 'piZhunWH',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'jinKouYPZH',
          label: '进口药品证号',
          width: 120,
          slot: 'jinKouYPZH',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
      ],
      xinZengCHRKDColumns: [
        {
          prop: 'xunHao',
          label: '序号',
          width: 70,
          align: 'center',
          formatter: (row, column, cellValue, index) => {
            return index + 1;
          },
        },
        {
          type: 'selection',
          width: 40,
          selectable: (row, index) => {
            return this.tableData.length !== index + 1;
          },
        },
        {
          prop: 'yaoPinLXMC',
          label: '药品类型',
          width: 32,
          align: 'center',
          type: 'text',
          field: true,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
          labelClassName: 'yaoPinLXMCHeader',
          className: 'yaoPinLXMC',
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 120,
          slot: 'faPiaoHM',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 120,
          slot: 'faPiaoRQ',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 200,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          slot: 'yaoPinMCYGG',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (cellValue.yaoPinMC && cellValue.yaoPinGG)
              return (
                row.tianJiaWZ + cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG
              );
            return row.yaoPinZC;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
          fieldDisabled: true,
        },
        {
          prop: 'ruKuCDMC',
          label: '入库产地',
          minWidth: 200,
          slot: 'ruKuCDMC',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'shengChanQY',
          label: '生产企业',
          minWidth: 200,
          slot: 'shengChanQY',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          hidden: false,
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'canKaoKL',
          label: '参考扣率',
          width: 110,
          type: 'text',
          align: 'right',
          field: true,
          hidden: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 70,
          field: true,
          type: 'text',
          fieldDisabled: true,
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          width: 100,
          field: true,
          type: 'text',
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 110,
          type: 'text',
          align: 'right',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0);
          },
        },
        {
          prop: 'ruKuSL',
          label: '入库数量',
          width: 140,
          slot: 'ruKuSL',
          field: true,
          sortable: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          align: 'right',
        },
        // {
        //   prop: 'ruKuHKCSL',
        //   label: '入库后库存数量',
        //   width: 120,
        //   type: 'text',
        //   align: 'right',
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue ? cellValue : 0);
        //   },
        // },
        {
          // 从jinJia和jinJia_ZiDian复制出来的
          prop: 'ziDianJinJia',
          label: '字典进价',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          disabled: true,
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          slot: 'jinJia',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.jinJiaXSDW);
          },
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          fieldDisabled: true,
          align: 'right',
          sortable: true,
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          slot: 'jinJiaJE',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        // {
        //   prop: 'lingChaJBZ',
        //   label: '零差/加价',
        //   width: 120,
        //   type: 'text',
        //   formatter: (row, column, cellValue, index) => {
        //     switch (cellValue) {
        //       case 0:
        //         return '普通';
        //       case 1:
        //         return '零差价';
        //       case 2:
        //         return '加价';
        //       default:
        //         return '';
        //     }
        //   },
        // },
        {
          // slot: 'lingShouJia',
          prop: 'lingShouJia',
          label: '零售价',
          type: 'text',
          width: 100,
          align: 'right',
          field: true,
          fieldDisabled: true,
          // disabled: ({ row }) => {
          //   return !this.canEditLSJ(row.lingChaJBZ)
          // },
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.lingShouXSDW);
          },
          disabled: true,
          // disabled: ({ $index }) => {
          //   if ($index == this.tableData.length - 1) {
          //     return true;
          //   } else if (this.params.chuRuKFSID == '9003') {
          //     return true;
          //   }
          // },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue ? cellValue : 0).toFixed(
              this.lingShouJEXSDW,
            );
          },
        },
        {
          prop: 'kouLv',
          label: '扣率',
          width: 120,
          align: 'right',
          slot: 'kouLv',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? cellValue : 0;
          },
          disabled: ({ $index }) => {
            if ($index == this.tableData.length - 1) {
              return false;
            } else if (this.params.chuRuKFSID == '9003') {
              return true;
            }
          },
        },
        {
          prop: 'jiaJiaLv',
          label: '加价率',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          hidden: true,
          formatter: (row, column, cellValue, index) => {
            if (row.lingShouJia && row.jinJia && row.jinJia != 0) {
              const count1 = new Big(row.lingShouJia);
              const xiaoShuWei = Number(this.jinJiaXSDW);

              const result = count1
                .minus(row.jinJia)
                .div(row.jinJia)
                .toFixed(xiaoShuWei)
                .toString();

              return Number(result).toLocaleString('zh', {
                style: 'percent',
                minimumFractionDigits: 2,
              });
            }
            return '';
          },
          disabled: true,
        },
        {
          prop: 'shengChanPH',
          label: '批号',
          width: 120,
          slot: 'shengChanPH',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          labelClassName: 'requireHeader',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
        },
        {
          prop: 'zuiJinRKSCPH',
          label: '最近入库批号',
          width: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'shengChanRQ',
          label: '生产日期',
          width: 120,
          showOverflowTooltip: false,
          slot: 'shengChanRQ',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
          hidden: false,
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 120,
          showOverflowTooltip: false,
          field: true,
          type: 'text',
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          showOverflowTooltip: false,
          slot: 'yaoPinXQ',
          field: true,
          fieldDisabled: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'jianYanZS',
          label: '检验证书',
          width: 120,
          slot: 'jianYanZS',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '有' : '无';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'chuChangJYHGD',
          label: '产品合格证',
          width: 120,
          slot: 'chuChangJYHGD',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'baoZhuangQK',
          label: '包装',
          width: 60,
          slot: 'baoZhuangQK',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '完整' : '不完整';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'waiGuanZL',
          label: '外观情况',
          width: 80,
          slot: 'waiGuanZL',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouJL',
          label: '验收结果',
          width: 120,
          slot: 'yanShouJL',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '合格' : '不合格';
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'yanShouRenXX',
          slot: 'yanShouRenXX',
          label: '验收人',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            if (row.yanShouRXM) {
              return row.yanShouRXM;
            }
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'piZhunWH',
          label: '批准文号',
          width: 120,
          slot: 'piZhunWH',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'jinKouYPZH',
          label: '进口药品证号',
          width: 120,
          slot: 'jinKouYPZH',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          startMode: 'click',
          endMode: 'custom',
        },
      ],
      tableData: [],
      tableDataOrigin: [],
      rowData: {
        zhiDanRQ: dayjs().format('YYYY-MM-DD'),
        zhiDanRID: getYongHuID(),
        zhiDanRXM: getYongHuXM(),
      },
      oldList: [], // 源数据，编辑时区分数据（修改、新增、删除）
      dangQingDWYPIndex: null, //药品定位， 当前聚焦的index
      dingWeiYPIndexArr: [], //药品定位， 搜索到的所有药品index
      tableBodyEle: '', // 存储table节点
      pickerOptions: {
        // 发票日期禁用设置（今天之前）
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      pickerOptionsypxq: {
        // 药品效期禁用设置（今天之后的）
        disabledDate(time) {
          return (
            time.getTime() < new Date().getTime() - 1 * 24 * 60 * 60 * 1000
          );
        },
      },
      cssPrefix: process.env.VUE_APP_NAMESPACE,
      yanShouRemColums: [
        {
          prop: 'yongHuID',
          label: '工号',
          width: 100,
        },
        {
          prop: 'yongHuXM',
          label: '姓名',
        },
      ],
      tempYaoPinLX: true, // 1西药 2成药 3草药 4 卫材
      isZhongYaoKXS: 5,
      ruKuList: [],
      ruKuListOriginOptions: [],
      isShowSCQY: false,
      shengChanList: [],
      shengChanListOriginOptions: [],
      beiShuBox: false,
      isRuKuShowLSJJ: '',
      showZhangBLB: '',
      duMaJDLZD: '', //毒麻经是否独立制单
      beforeZhangBuChange: '',
      changeJinJiaJE: false,
      controlExtraColumns: [
        {
          slot: 'caoZuoSZDM',
          label: '键盘操作设置',
          cIndex: 5,
        },
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 6,
        },
      ],
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      showXiaoGuiGeYP: false,
      kuCunZJDM: null,
      yaoKuCGDJLDPT: null,
      yaoPinRKZDDRCGDSL: null,
      jinEJSFS: 'xianLeiJZSSWR', //先累加在四舍五入
      timestamp: null,
      ruKuDZPHXQSFZDDC: false, //入库单中批号效期是否自动带出
    };
  },
  setup() {
    const form = ref(null);
    // useKeyboard(form);
    const { focusNext } = useKeyboard(form, {
      cssSelector: [
        'custom-date-picker',
        'custom-select',
        'gonghuodw-select',
        'gong-huo-d-w-select',
      ],
    });
    return {
      form,
      focusNext,
    };
  },
  computed: {
    // TODO 暂时不修改，不需要冲红单编辑
    isChongHong() {
      // 是否冲红， 编辑时 区分冲红单 冲红时，只能修改入库数量
      return this.params.hongDanBZ == '1';
    },
    canEdit() {
      // 能否编辑入库方式，  修改时，不能修改，冲红时不能修改
      return this.$route.path === '/XiuGaiRKD' || this.isChongHong;
    },
    zongShuJEData() {
      let lingShouJEHJ = 0;
      let jinJiaJEHJ = 0;
      this.tableData.forEach((item) => {
        let ruKuSL = Number(item.ruKuSL) ? Number(item.ruKuSL) : 0;
        if (item.ruKuSL || item.ruKuSL == 0) {
          if (this.params.chuRuKFSID == '9003') {
            item.ruKuHKCSL = item.kuCunSL - ruKuSL;
          } else {
            item.ruKuHKCSL = ruKuSL + item.kuCunSL;
          }
          // 如果编辑了进价金额，那么进价金额就不会在根据金价和入库数量计算
          if (!this.changeJinJiaJE) {
            item.jinJiaJE = multiply(this.stringToNumber(item.jinJia), ruKuSL);
          }
          item.lingShouJE = multiply(
            this.stringToNumber(item.lingShouJia),
            ruKuSL,
          );
          if (!item.lingShouJia) item.lingShouJia = 0;
        } else {
          item.jinJiaJE = 0;
          item.lingShouJE = 0;
        }
        jinJiaJEHJ =
          this.jinEJSFS !== 'xianSiSWRZLJ'
            ? add(jinJiaJEHJ, item.jinJiaJE)
            : add(jinJiaJEHJ, formatMoney(item.jinJiaJE, 2));
        lingShouJEHJ =
          this.jinEJSFS !== 'xianSiSWRZLJ'
            ? add(lingShouJEHJ, item.lingShouJE)
            : add(lingShouJEHJ, formatMoney(item.lingShouJE, 2));
      });
      return {
        lingShouJE: lingShouJEHJ,
        jinJiaJE: jinJiaJEHJ,
        yaoPinZS: !this.isChongHong
          ? this.tableData.length - 1
          : this.tableData.length,
      };
    },
    // 是否显示类型
    isShowLX() {
      return getKuCunGLLX().indexOf('3') > -1;
    },
    // 备注的col长度
    beiZhuColLength() {
      let length = 24;
      if (this.showZhangBLB == 1) {
        length -= 5;
      }
      if (this.isShowLX) {
        length -= 5;
      }

      return Math.max(length, 5); //最少保留5
    },
    ...mapGetters({
      tuiYaoList: 'yaoku/getXiaoQiList',
    }),
  },
  watch: {
    '$route.query.chuRuKFSID': {
      handler: function (val) {
        //新增入库单时， 判断是否采购入库， 并给出入库方式赋值
        if (this.$route.path === '/XinZengRKD') {
          if (val) {
            this.isCaiGouRK = true;
            this.params.chuRuKFSID = val;
          } else {
            this.isCaiGouRK = false;
          }
        }
      },
      immediate: true,
    },
    'params.weiZhiMC': {
      handler: function (val) {
        // this.columns[6].hidden =
        //   decodeURIComponent(val).indexOf('草') > -1 ||
        //     decodeURIComponent(val).indexOf('中') > -1
        //     ? false
        //     : true;
      },
      immediate: true,
    },
    'tableData.length': {
      handler: function (val) {
        //新增入库单时， 判断是否采购入库， 并给出入库方式赋值
        if (val > 0) {
          const faPiaoHM = this.tableData[0].faPiaoHM || '';
          this.tableData.forEach((el) => {
            if (!el.faPiaoHM) el.faPiaoHM = faPiaoHM;
          });
        }
      },
      immediate: true,
    },
  },
  /**
   * 处理标签页切换时，或跳转时，更新页面数据
   */
  async activated() {
    let val = this.$route.query.type;
    await this.initKuFangSZ(); //获取库房设置参数
    if (this.$route.path === '/XinZengRKD' && val !== this.routerType) {
      this.routerType = val;
      await this.checkType(val);
      if (this.isCaiGouRK) {
        this.isLoad = true;
        this.tableData.push(initData());
      }
    }
    this.tableDataOrigin = cloneDeep(this.tableData);
  },
  /**
   * 1. 获取字典数据，
   * 2. 获取详情数据、 或初始化页面数据
   */
  async created() {
    let xiaoShu = null;
    this.pageLoading = true;
    try {
      await this.getColumnInit();
      //药品入库是否展示历史进价
      await this.initKuFangSZ(); //获取库房设置参数
      //如果是中药库
      const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
      // 判断进价零售价是否设置了值，没有则赋默认值
      this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
      this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
      const query = this.$route.query;
      const selectId = query.id;
      const type = query.type;
      //判断是中药库还是西药库，西药库隐藏生产企业
      const isShowSCQY = getKuCunGLLX().indexOf('3') > -1;
      // this.columns[7].hidden = !isShowSCQY;
      this.isShowSCQY = isShowSCQY;
      const moRenCHKFS = await getZhongYaoYPRKJGXZ({
        xiangMuDM: 'yaoPinRKMRRKFS',
      });
      // JVADT74039 【嵊州人民】西药库新增出库的出库方式默认为药房领用
      if (moRenCHKFS && moRenCHKFS.xiangMuZDM) {
        this.params.chuRuKFSID = moRenCHKFS.xiangMuZDM;
        this.params.chuRuKFSMC = moRenCHKFS.xiangMuZMC;
      }
      if (selectId) {
        await Promise.all([this.getZiDianSJ(), this.getDetail(selectId)]);
      } else if (query.type) {
        await this.getZiDianSJ();
        this.handleSelectChange(this.params.chuRuKFSID, 'chuRuKFS');
      }
      if (!this.isChongHong && !this.isLoad) {
        this.tableData.push(initData());
      }
      if (type === 'yaokuTY') {
        this.viewManager.setTitle('XinZengRKD', '新增退库请领单');
        this.params.chuRuKFSID = '9003';
        this.params.chuRuKFSMC = '退还医药公司';

        this.tableData = cloneDeep(this.tuiYaoList);
        if (!this.tableData) {
          this.tableData = [];
          this.$message.warning('请重新选择要退库的药');
        }
        this.params.gongHuoDWMC = this.tuiYaoList[0].gongHuoDWMC;
        this.params.gongHuoDWID = this.tuiYaoList[0].gongHuoDWID;
        this.tableData.forEach((item) => {
          item.yaoPinMCYGG = {
            yaoPinLXMC: item.yaoPinLXMC,
            yaoPinLXDM: item.yaoPinLXDM,
            yaoPinMC: item.yaoPinMC,
            yaoPinGG: item.yaoPinGG,
            baoZhuangDW: item.baoZhuangDW,
            chanDiMC: item.chanDiMC,
            kuCunSL: item.kuCunSL,
          };
          item.chuChangJYHGD = 1;
          item.baoZhuangQK = 1;
          item.waiGuanZL = 1;
          item.yanShouJL = 1;
          item.yanShouRXM = getYongHuXM();
          item.yanShouRID = getYongHuID();
          item.yanShouRenXX = {
            yongHuXM: getYongHuXM(),
            yongHuID: getYongHuID(),
          };
        });
        this.tableData.push(initData());
      }
      //如果是中药库的入库制单进来的新增,药品排序为自定义
      if (type !== '0' && isShowSCQY) {
        // this.columns[4].sortable = 'custom';
      }
      //获取参数
      var canShuResult = await GetCanShuZhi({
        canShuMC: '药品入库_草药零售价是否随进价变化',
        canShuMRZ: '0', // 0 否  1 是
      });
      if (canShuResult == '1') {
        this.shiFouXGLSJ = true;
      } else {
        this.shiFouXGLSJ = false;
      }
      // //获取小数参数要是没有值，就默认等于2
      // xiaoShu = await GetCanShuZhi({
      //   canShuMC: '库房管理_小数点位数',
      //   canShuMRZ: '2', //0表示关闭，1表示开启
      //   gongNengID: '0',
      // });
    } finally {
      this.pageLoading = false;
      // this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 2 : xiaoShu;
      // this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (getKuCunGLLX().indexOf('3') > -1) {
        this.getYaoPinCD();
      }
      const res = await getZhangBuQXXQ();
      this.zhangBuOptions = res.zhangBuLBXXList || [];
      // this.zhangBuOptions.unshift({
      //   zhangBuLBMC: '全部',
      //   zhangBuLBID: '0',
      // });
    }
    this.tableDataOrigin = cloneDeep(this.tableData);
    await this.jiaoYanWeiBaoCun();
  },
  beforeUnmount() {
    sessionStorage.setItem('ruKuDanList', '');
    this.closeTimeOut = null;
    this.stopPrompt();
    this.stopPrompt = null;
  },
  methods: {
    /**
     * 校验数据是否未保存
     * **/
    jiaoYanWeiBaoCun() {
      this.stopPrompt = createPrompt(async (page) => {
        if (
          page.name !== 'xinzengrkd' &&
          page.path?.indexOf('/mediinfo-vela-yaoku-web/#/XiuGaiRKD') < 0
        ) {
          return;
        }

        if (!isEqual(this.tableData, this.tableDataOrigin)) {
          await MdMessageBox.confirm('有未保存的数据，确定要离开？', '提示', {
            confirmButtonText: '离开',
            cancelButtonText: '取消',
            type: 'warning',
          });
        }

        // 阻止默认提示弹窗
        return {
          healess: true,
        };
      });
    },
    /**
     * 获取库房设置参数
     * **/
    async initKuFangSZ() {
      const arr = await getKuFangSZList([
        'yaoPinRKXZYPHZSLSJJ',
        'shiFouAZBLBGL',
        'yaoPinRKDDMJYPDLZD',
        'jinJiaJEXSDWS',
        'lingShouJEXSDWS',
        'lingShouJXSDWS',
        'jinJiaXSDWS',
        'ShiFouYXXGGRK',
        'yaoKuCGDJLDPT',
        'yaoPinRKZDDRCGDSL',
        'yinFuKHJJEJSFS',
        'ruKuDZPHXQSFZDDC',
      ]);
      if (arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          const el = arr[i];

          if (el.xiangMuDM === 'yaoPinRKXZYPHZSLSJJ') {
            this.isRuKuShowLSJJ = el.xiangMuZDM;
          } else if (el.xiangMuDM === 'shiFouAZBLBGL') {
            this.showZhangBLB = el.xiangMuZDM;
          } else if (el.xiangMuDM === 'yaoPinRKDDMJYPDLZD') {
            this.duMaJDLZD = el.xiangMuZDM;
          } else if (el.xiangMuDM === 'jinJiaXSDWS') {
            this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
          } else if (el.xiangMuDM === 'lingShouJXSDWS') {
            this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
          } else if (el.xiangMuDM === 'jinJiaJEXSDWS') {
            this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
          } else if (el.xiangMuDM === 'lingShouJEXSDWS') {
            this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
          } else if (el.xiangMuDM === 'ShiFouYXXGGRK') {
            this.showXiaoGuiGeYP = el.xiangMuZDM == 1;
          } else if (el.xiangMuDM === 'yaoKuCGDJLDPT') {
            this.yaoKuCGDJLDPT = el.xiangMuZDM == '0' ? true : false;
          } else if (el.xiangMuDM === 'yaoPinRKZDDRCGDSL') {
            this.yaoPinRKZDDRCGDSL = el.xiangMuZDM == '2';
          } else if (el.xiangMuDM === 'yinFuKHJJEJSFS') {
            this.jinEJSFS = el.xiangMuZDM;
          } else if (el.xiangMuDM === 'ruKuDZPHXQSFZDDC') {
            this.ruKuDZPHXQSFZDDC = el.xiangMuZDM == '1';
          }
        }
      }
    },
    //备注回车跳转到表格第一行的发票列上
    hanldeBeiZhu() {
      focusEditTableDom({
        rowIndex: 0,
        columns: this.columns,
        key: 'faPiaoHM',
      });
    },
    //升降序
    handleSortChange({ column, prop, order }) {
      if (order) {
        //删除最后一个空白行
        if (!this.tableData[this.tableData.length - 1].jiaGeID)
          this.tableData.splice(this.tableData.length - 1, 1);
        this.tableData = sortBy(this.tableData, (item) => {
          if (prop === 'yaoPinMCYGG') {
            const pinyin = makePY(item.yaoPinMC);
            if (pinyin) return pinyin.toLocaleUpperCase();
          }
          if (prop === 'baiFangWZ') {
            return item.baiFangWZSXH;
          } else {
            return item[prop];
          }
        });
        if (order === 'descending') {
          this.tableData.reverse();
        }
        this.tableData.push(initData());
      }
    },
    //获取产地
    async getYaoPinCD() {
      try {
        const res = await GetChanDiXXSelectList();
        this.ruKuListOriginOptions = cloneDeep(res);
        this.shengChanListOriginOptions = cloneDeep(res);
        // this.ruKuList = res;
      } catch (e) {}
    },
    // 根据拼音码筛选
    filterMethod(query, mingCheng) {
      if (query !== '') {
        this[mingCheng] = this[mingCheng + 'OriginOptions'].filter((item) => {
          return (
            item.chanDiMC.toLowerCase().indexOf(query.toLowerCase()) > -1 ||
            (item.shuRuMa1 &&
              item.shuRuMa1.toLowerCase().indexOf(query.toLowerCase()) > -1) ||
            (item.shuRuMa2 &&
              item.shuRuMa2.toLowerCase().indexOf(query.toLowerCase()) > -1)
          );
        });
      } else {
        this[mingCheng] = this[mingCheng + 'OriginOptions'];
      }
    },
    //产地筛选
    handleChanDiChange(val, index) {
      if (val) {
        const mingCheng = this.ruKuListOriginOptions.find(
          (fl) => fl == val,
        ).chanDiMC;
        this.tableData[index].ruKuCDMC = mingCheng;
      } else {
        this.tableData[index].ruKuCDMC = '';
      }
    },
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      const params = {
        zuZhiJGID: getJiGouID(),
        likeQuery: inputValue,
        pageSize,
        pageIndex: page,
      };
      return await getYongHuListGongYong(params);
    },
    handleTableEnterForRK({ activeIndex, callback }) {
      // const tuiHuoIndex = this.params.chuRuKFSID == '9003' ? 1 : 0;
      // //药品类型1.2.4为true 一行15个 草药 一行16
      // const yaoPinLX = this.tempYaoPinLX ? 15 - tuiHuoIndex : 17 - tuiHuoIndex;
      // const num =
      //   yaoPinLX == 15 - tuiHuoIndex ? 5 - tuiHuoIndex : 7 - tuiHuoIndex;
      // let a =
      //   (activeIndex - num) / yaoPinLX > Math.floor(activeIndex / yaoPinLX);
      // if ((activeIndex < yaoPinLX && activeIndex > num) || a) {
      //   const count = Math.ceil(activeIndex / yaoPinLX) * yaoPinLX - 1;
      //   this.$refs.editTable.toNext(count);
      // } else {
      //   this.handleTableEnter({ activeIndex, callback });
      // }
      this.handleTableEnter({ activeIndex, callback });
    },
    //验收人
    handleYSR(val, row, index) {
      this.yanShouRenXX = val;
      Object.assign(row, {
        yanShouRXM: val.yongHuXM,
        yanShouRID: val.yongHuID,
        yanShouRenXX: {
          yongHuXM: val.yongHuXM,
          yongHuID: val.yongHuID,
        },
      });
      if (index !== this.tableData.length - 1) {
        this.tableData.forEach((t, tindex) => {
          if (tindex > index) {
            t.yanShouRXM = val.yongHuXM;
            t.yanShouRID = val.yongHuID;
            t.yanShouRenXX = {
              yongHuXM: val.yongHuXM,
              yongHuID: val.yongHuID,
            };
          }
        });
      }
    },
    /**
     * 判断是否能修改零售价
     * @param lingChaJBZ
     * @returns {boolean}
     */
    canEditLSJ(lingChaJBZ) {
      return lingChaJBZ !== 1 || this.params.chuRuKFSID == '1002';
    },
    /**
     * 零售价列，添加不可更改样式
     * @param row
     * @param column
     * @returns {{backgroundColor: string}}
     */
    cellStyle({ row, column }) {
      if (
        !this.canEditLSJ(row.lingChaJBZ) &&
        column.property === 'lingShouJia'
      ) {
        return { backgroundColor: '#f5f5f5' };
      }
    },
    cellClassName({ columnIndex }) {
      if (this.params.chuRuKFSID == '9003' && columnIndex == 9) {
        return this.prefixClass('chonghong-number__color');
      }
    },
    //根据进价计算零售价
    jiSuanLSJByJJ(val, row) {
      //参数控制是否修改零售价
      // 零售价直接取GetYaoPinCDJGByJGID的零售价字段值，不随差价和加价率变化
      if (this.shiFouXGLSJ && this.params.chuRuKFSID != '1002') {
        //LingChaJBZ（零差价标志）= 2 并且 JiaJiaLv（加价率） 不等于零和不等于空，那么零售价=进价+进价*加价率
        if (row.lingChaJBZ == 2 && row.jiaJiaLv) {
          row.lingShouJia = add(val, multiply(val, row.jiaJiaLv));
        }
        if (row.lingChaJBZ == 1) row.lingShouJia = val;
      }
    },
    /**
     * 延时关闭上一节点， 供下拉选择时，回车事件获取表格输入定位。
     * @param value
     * @param cellRef
     */
    selectChange(value, cellRef) {
      if (!value) {
        setTimeout(() => {
          cellRef.endEdit();
        }, 200);
      }
    },
    //进价金额联动进价，改动金额/数量=进价
    handleChangeJJJE(val, row, index) {
      if (val) {
        this.changeJinJiaJE = true;
        row.jinJia =
          Number(row.ruKuSL) > 0
            ? (Number(val) / Number(row.ruKuSL)).toFixed(6)
            : 0;
        row.kouLv = (Number(row.jinJia) / Number(row.liCiJJ)).toFixed(2);
        // 改动进价的同时，要判断是否有加价率和零差价标志来联动零售价
        this.jiSuanLSJByJJ(row.jinJia, row);
        if (
          row.jinJia &&
          row.lingShouJia &&
          Number(row.jinJia) > Number(row.lingShouJia)
        ) {
          MdMessageBox.confirm('当前药品进价高于零售价，请修改', '', {
            showCancelButton: false,
            confirmButtonText: '好的',
            type: 'warning',
          }).then((res) => {
            focusEditTableDom({
              rowIndex: index,
              columns: this.columns,
              key: 'jinJiaJE',
            });
          });
        }
      }
    },
    //扣率联动进价
    handlerKouLv(val, row, index) {
      this.changeJinJiaJE = false;
      row.jinJia = multiply(this.stringToNumber(row.liCiJJ), Number(val));
      this.jiSuanLSJByJJ(row.jinJia, row);
    },
    //进价change
    handlerJinJia(val, row, index) {
      //如果进价大于零售价，提示他
      if (
        row.jinJia &&
        row.lingShouJia &&
        Number(row.jinJia) > Number(row.lingShouJia)
      ) {
        MdMessageBox.confirm('当前药品进价高于零售价，请修改', '', {
          showCancelButton: false,
          confirmButtonText: '好的',
          type: 'warning',
        }).then((res) => {
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: 'jinJia',
          });
        });
      }

      if (val) {
        this.changeJinJiaJE = false;
        row.kouLv = (Number(val) / Number(row.liCiJJ)).toFixed(2);
      }

      //2是强控 -删除药品 1提醒
      if (row.jinJia && row.lingShouJia) {
        const beiShu = (Number(row.lingShouJia) / Number(row.jinJia)).toFixed(
          1,
        );
        const isBeiShu = this.lingShouJTX(beiShu, row, index);
        if (this.$route.query.xianZhiTXLX === '2' && isBeiShu) {
          MdMessageBox.confirm(
            `${row.yaoPinMC}零售价超出进价的${beiShu}倍，无法入库！`,
            '',
            {
              showCancelButton: false,
              confirmButtonText: '删除药品',
              closeOnClickModal: false,
              type: 'warning',
            },
          ).then((res) => {
            this.tableData[index] = initData();
            focusEditTableDom({
              rowIndex: index,
              columns: this.columns,
              key: 'yaoPinMCYGG',
            });
            this.$refs.editTable.toNext(15 * index);
          });
        }
        if (this.$route.query.xianZhiTXLX === '1' && isBeiShu) {
          MdMessageBox.confirm(
            `${row.yaoPinMC}零售价超出进价的${beiShu}倍`,
            '',
            {
              showCancelButton: true,
              closeOnClickModal: false,
              cancelButtonText: '删除药品',
              confirmButtonText: '继续录入',
              type: 'warning',
            },
          )
            .then((res) => {
              focusEditTableDom({
                rowIndex: index,
                columns: this.columns,
                key: 'shengChanPH',
              });
            })
            .catch((e) => {
              this.tableData[index] = initData();
              focusEditTableDom({
                rowIndex: index,
                columns: this.columns,
                key: 'yaoPinMCYGG',
              });
              this.$refs.editTable.toNext(15 * index);
            });
        }
      }
    },
    handleBeiShuBox(row, index, beiShu, isBeiShu) {
      try {
        if (this.$route.query.xianZhiTXLX === '2' && isBeiShu) {
          this.beiShuBox = false;
          MdMessageBox.confirm(
            `${row.yaoPinMC}零售价超出进价的${beiShu}倍，无法入库！`,
            '',
            {
              showCancelButton: false,
              closeOnClickModal: false,
              confirmButtonText: '删除药品',
              type: 'warning',
            },
          ).then((res) => {
            this.tableData[index] = initData();
            focusEditTableDom({
              rowIndex: index,
              columns: this.columns,
              key: 'yaoPinMCYGG',
            });
          });
        }
        if (this.$route.query.xianZhiTXLX === '1' && isBeiShu) {
          MdMessageBox.confirm(
            `${row.yaoPinMC}零售价超出进价的${beiShu}倍，确定入库！`,
            '',
            {
              showCancelButton: true,
              closeOnClickModal: false,
              cancelButtonText: '删除药品',
              confirmButtonText: '继续录入',
              type: 'warning',
            },
          )
            .then((res) => {
              this.beiShuBox = true;
              this.$refs.editTable.actionEnter();
            })
            .catch((e) => {
              this.beiShuBox = false;
              this.tableData[index] = initData();
              focusEditTableDom({
                rowIndex: index,
                columns: this.columns,
                key: 'yaoPinMCYGG',
              });
            });
        }
      } catch (e) {}
    },
    //处理倍数
    lingShouJTX(beiShu) {
      const beiShuCS = JSON.parse(this.$route.query.beiShuCS);
      //2是强控 -删除药品
      if (beiShuCS.lingShouJTJZ && beiShuCS.jinJiaTJZ) {
        return (
          eval(`${beiShu} ${beiShuCS.lingShouJDM} ${beiShuCS.lingShouJTJZ}`) ||
          eval(`${beiShu} ${beiShuCS.jinjiaDM} ${beiShuCS.jinJiaTJZ}`)
        );
      } else if (beiShuCS.lingShouJTJZ) {
        return eval(
          `${beiShu} ${beiShuCS.lingShouJDM} ${beiShuCS.lingShouJTJZ}`,
        );
      } else {
        return eval(`${beiShu} ${beiShuCS.jinjiaDM} ${beiShuCS.jinJiaTJZ}`);
      }
    },
    // 去掉定位样式
    clearDingWeiClass(tableBodyEle, index) {
      if (tableBodyEle && index >= 0) {
        let dingWeiEle = tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__row`,
        )[index];
        dingWeiEle.className = dingWeiEle.className.replace(
          this.prefixClass('dingwei-bg'),
          '',
        );
      }
    },
    //设置定位样式
    setDingWeiClass(tableBodyEle, index, dingWeiBZ = false) {
      let dingWeiEle = tableBodyEle.querySelectorAll(
        `.${this.cssPrefix}-base-table__row`,
      )[index];
      dingWeiEle.className =
        dingWeiEle.className !== ''
          ? dingWeiEle.className + ' ' + this.prefixClass('dingwei-bg')
          : this.prefixClass('dingwei-bg');
      if (dingWeiBZ) this.setDingWeiScroll(tableBodyEle, dingWeiEle, index);
    },
    // 定位滚动
    setDingWeiScroll(tableBodyEle, dingWeiEle, index) {
      tableBodyEle.scrollTop = index * dingWeiEle.clientHeight;
      this.dangQingDWYPIndex = index;
    },
    // 定位切换
    handleEnter() {
      let length = this.dingWeiYPIndexArr.length;
      if (this.query.likeQuery && length > 0) {
        let index = this.dingWeiYPIndexArr.findIndex(
          (item) => item === this.dangQingDWYPIndex,
        );
        if (index === length - 1) {
          index = 0;
        } else index++;
        let dingWeiEle = this.tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__row`,
        )[index];
        this.setDingWeiScroll(
          this.tableBodyEle,
          dingWeiEle,
          this.dingWeiYPIndexArr[index],
        );
      }
    },
    // 药品快速定位处理
    handleKuaiSuDW(data, el) {
      //清空
      if (!data) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
        return;
      }
      // 获取doom
      if (!this.tableBodyEle) {
        if (!this.isChongHong) {
          this.tableBodyEle = document.querySelector(
            `#editTable .${this.cssPrefix}-base-table__body-wrapper`,
          );
        } else {
          this.tableBodyEle = document.querySelector(
            `#editTable1 .${this.cssPrefix}-base-table__body-wrapper`,
          );
        }
      }
      // 去掉上一次定位样式
      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
      }
      //寻找index
      this.dingWeiYPIndexArr = this.tableData.reduce((pre, item, index) => {
        if (item.jiaGeID === data.jiaGeID) {
          pre.push(index);
        }
        return pre;
      }, []);
      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item, index) => {
          this.setDingWeiClass(this.tableBodyEle, item, index === 0);
        });
      } else {
        MdMessage({
          type: 'warning',
          message: '未找到该药品！',
        });
      }
    },
    /**
     * 获取出入库方式、 应付款等级
     * @returns {Promise<void>}
     */
    async getZiDianSJ() {
      await Promise.all([
        getYaoPinShuJuYZYList(['YP0004']).then((res) => {
          this.yingFuKDJOptions = res[0].zhiYuList.reduce((pre, item) => {
            pre.push({
              yingFuKDJMC: item.biaoZhunMC,
              yingFuKDJDM: item.biaoZhunDM,
            });
            return pre;
          }, []);
        }),
        GetChuRuKFSByFXDM('1').then((res) => {
          let optionData = [];
          res.forEach((item) => {
            optionData.push({
              chuRuKFSMC: item.fangShiMC,
              chuRuKFSID: item.fangShiID,
              danWeiBMDM: item.danWeiBMDM,
              kuCunZJDM: item.kuCunZJDM,
            });
          });
          this.chuRuKFSOptions = optionData;
        }),
      ]);
    },
    /**
     * 限制输入入库数量，
     * @param item
     * @param index
     * @constructor
     */
    CheckRuKuSL(item, index) {
      this.changeJinJiaJE = false;
      // 新增库存增减方向“库存不增不减”需要输入负数
      if (this.kuCunZJDM !== '3') {
        item.ruKuSL = item.ruKuSL.replace(/-/g, '');
        if (item.ruKuSL <= 0) {
          item.ruKuSL = 1;
          MdMessage({
            type: 'warning',
            message: `入库数量应该大于0`,
          });
          this.$refs.editTable._prev();
        }
      }

      if (item.ruKuSL > item.kuCunSL && this.params.chuRuKFSID == '9003') {
        item.ruKuSL = item.kuCunSL;
        MdMessage({
          type: 'warning',
          message: `入库数量应该小于库存数量`,
        });
        this.$refs.editTable._prev();
      }
    },
    async handleFaPiaoHMBlur(row) {
      // const reg = /^[A-Za-z0-9]+$/;
      // if (row.faPiaoHM && !reg.test(row.faPiaoHM)) {
      //   MdMessage({
      //     type: 'warning',
      //     message: '发票号码只能有数字和字母',
      //   });
      //   row.faPiaoHM = '';
      // } else
      if (row.faPiaoHM) {
        let res = await CheckFaPiaoSFCF({ faPiaoHM: row.faPiaoHM });
        if (res) {
          MdMessage({
            type: 'warning',
            message: '发票号码有重复',
          });
        }
      } else if (isNaN(row.faPiaoHM)) {
        row.faPiaoHM = '';
      }
    },
    handleFaPiaoRQChange(val, row) {
      if (!val) {
        row.faPiaoRQ = '';
      }
    },
    /**
     * 处理保存的字段， 转为数字
     *
     *@param list
     */
    handleParseSaveData(list) {
      list.forEach((item, index) => {
        if (!item.faPiaoRQ) item.faPiaoRQ = null;
        item.jinJia = this.stringToNumber(item.jinJia);
        item.jinJiaJE = this.isChongHong
          ? -this.stringToNumber(item.jinJiaJE)
          : this.stringToNumber(item.jinJiaJE);
        item.lingShouJia = this.stringToNumber(item.lingShouJia);
        item.lingShouJE = this.isChongHong
          ? -this.stringToNumber(item.lingShouJE)
          : this.stringToNumber(item.lingShouJE);
        item.ruKuSL = this.isChongHong
          ? -this.stringToNumber(item.ruKuSL)
          : this.stringToNumber(item.ruKuSL);
        item.kuCunSL = this.stringToNumber(item.kuCunSL);
        item.shunXuHao = index;
        if (item.weiZhiID) {
          item.weiZhiID = getWeiZhiID();
          item.weiZhiMC = getWeiZhiMC();
        }
      });
    },
    stringToNumber(str, type = 0) {
      if (type === 0) return str ? Number.parseFloat(str) : 0;
      else return str ? Number.parseInt(str) : 0;
    },
    /**
     * 根据药品价格id， 获取药品具体信息
     *
     * @param val
     * @param row
     * @param index
     * @param cellRef
     * @returns {Promise<void>}
     */
    async handleTableYaoPinDWChange(val, row, index, cellRef) {
      this.tempYaoPinLX = val.yaoPinLXDM != '3';
      let isBeiShu = false; //默认是false，true是符合限制条件
      try {
        this.$refs.editTable.stopEnter();
        if (val) {
          let yaopinphdata = row;
          //退库时的历史进价
          if (this.params.chuRuKFSID == '9003') {
            this.errorIndex = index;
            // 调用批次选择处理， 只有一条直接赋值， 多条则弹框
            yaopinphdata = await this.$refs.yaopinlsjj.show({
              jiaGeID: val.jiaGeID,
              yaoPinMC: val.yaoPinMC,
              model: 'rk',
            });
            // val.shengChanPH = yaopinphdata.shengChanPH;
            // row.shengChanPH = yaopinphdata.shengChanPH;
            // val.yaoPinXQ = yaopinphdata.yaoPinXQ;
            // row.yaoPinXQ = yaopinphdata.yaoPinXQ;
            val.kuCunSL = yaopinphdata.kuCunZL;
            row.kuCunSL = yaopinphdata.kuCunZL;
          }
          //采购入库时的历史进价
          if (this.params.chuRuKFSID === '1001' && this.isRuKuShowLSJJ == 1) {
            this.errorIndex = index;
            // 调用批次选择处理， 只有一条直接赋值， 多条则弹框
            const jinJiaRow = await this.$refs.yaopinlsjj.show({
              jiaGeID: val.jiaGeID,
              yaoPinMC: val.yaoPinMC,
              model: 'rk',
            });
            yaopinphdata = !isEmpty(jinJiaRow) ? jinJiaRow : {};
          }
          //判断药品名称重复，只提示
          let findIndex = this.tableData.findIndex(
            (item, index1) => item.jiaGeID === val.jiaGeID && index1 !== index,
          );
          if (findIndex !== -1) {
            MdMessage({
              type: 'warning',
              message: `第${findIndex + 1}行已有相同药品(${val.yaoPinMC})！`,
            });
          }
          this.pageLoading = true;
          let data = await GetYaoPinCDJGByJGID({
            jiaGeID: val.jiaGeID,
            zuZhiJGID: getJiGouID(),
          });
          data.faPiaoHM = row.faPiaoHM;
          // 寻找表格中是否有毒麻精
          const isNormal =
            this.tableData &&
            this.tableData.some((fl) => fl.jiaGeID && fl.duLiFLDM == 0);
          const isDuMaJ =
            this.tableData &&
            this.tableData.some((fl) => fl.jiaGeID && fl.duLiFLDM != 0);
          // 毒麻经独立制单且是西药库时
          // 有普通药品但是当前选择药品是毒麻精；表格没有普通药品但当前选择行是普通药品
          if (
            this.tableData.length > 1 &&
            this.duMaJDLZD == 1 &&
            ((isNormal && data.duLiFLDM != 0) ||
              (isDuMaJ && data.duLiFLDM == 0))
          ) {
            await MdMessageBox.confirm(
              `当前入库单中同时存在普通药品和毒麻精药品，无法入库`,
              '',
              {
                showCancelButton: false,
                closeOnClickModal: false,
                confirmButtonText: '删除药品',
                type: 'warning',
              },
            );
            isBeiShu = true;
            this.tableData[index] = initData();
            focusEditTableDom({
              rowIndex: index,
              columns: this.columns,
              key: 'yaoPinMCYGG',
            });
            return;
          }
          //计算零售价和进价的差价倍数
          let beiShu = (Number(data.lingShouJia) / Number(data.jinJia)).toFixed(
            1,
          );
          //判断倍数是否在设置的倍数大小区间
          //判断限制提醒类型是不是1和2，没有则不判断
          if (this.$route.query.xianZhiTXLX) {
            isBeiShu = await this.lingShouJTX(beiShu);
            await this.handleBeiShuBox(row, index, beiShu, isBeiShu);
          }
          //非中药库下赋值生产批号
          if (!this.isShowSCQY) {
            data.shengChanPH = yaopinphdata.shengChanPH;
            data.shengChanRQ = yaopinphdata.shengChanRQ;
            data.yaoPinXQ = yaopinphdata.yaoPinXQ;
          }
          //历次进价实际上储存的是字典进价
          data.liCiJJ = cloneDeep(data.jinJia);
          data.kouLv = 1;
          data.ziDianJinJia = cloneDeep(data.jinJia);

          //如果是退库
          if (this.params.chuRuKFSID == '9003') {
            data.jinJia = yaopinphdata.liCiJJ;
            // data.lingShouJia = yaopinphdata.lingShouJia;
            data.kouLv = (
              Number(yaopinphdata.liCiJJ) / Number(data.liCiJJ)
            ).toFixed(2);
          }
          //采购入库
          if (this.params.chuRuKFSID === '1001' && this.isRuKuShowLSJJ == 1) {
            // data.liCiJJ = data.jinJia; //历次进价实际上储存的是字典进价
            data.jinJia = yaopinphdata.liCiJJ
              ? yaopinphdata.liCiJJ
              : data.jinJia;
            data.lingShouJia = yaopinphdata.lingShouJia
              ? yaopinphdata.lingShouJia
              : data.lingShouJia;
            data.kouLv = yaopinphdata.kouLv ? yaopinphdata.kouLv : data.kouLv;
          }
          // if (!isBeiShu) this.$refs.editTable.actionEnter()

          // 入库单中批号效期是否自动带出
          if (this.ruKuDZPHXQSFZDDC) {
            const piHaoRes = await GetJinRuKYPXQPHListByJGID({
              jiaGeID: row.jiaGeID,
            });

            data.shengChanPH = piHaoRes?.[0]?.shengChanPH || '';
            data.yaoPinXQ = piHaoRes?.[0]?.yaoPinXQ || '';
          }

          Object.assign(row, data);
          // 获取选中药品的药品批号和效期
          const res = await GetYaoPinPHXQByJGID({
            jiaGeID: val.jiaGeID,
          });
          let list = {
            shengChanPHList: res.shengChanPHList
              .filter((item) => !!item)
              .map((item) => ({
                label: item,
                value: item,
                aliasLabel: '',
              })),
            shortcuts: res.yaoPinXQList
              .filter((item) => !!item)
              .map((item) => ({
                text: dayjs(item).format('YYYY-MM-DD'),
                value: dayjs(item).format('YYYY-MM-DD'),
              })),
          };
          this.timestamp = new Date();
          Object.assign(row, list);
          row.kuCunSL = val.kuCunSL;
          var chuRuK = this.params.chuRuKFSID;
          if (chuRuK == '1002') {
            row.jinJia = 0;
          }
          //自动新增一行处理
          if (index === this.tableData.length - 1) {
            let obj = initData();
            obj.faPiaoHM = row.faPiaoHM;
            obj.faPiaoRQ = row.faPiaoRQ;
            this.tableData.push(obj);
          }

          // this.$refs.editTable.toNext(16 * index + 1);
        } else {
          let obj = initData();
          delete obj.faPiaoHM;
          delete obj.faPiaoRQ;
          Object.assign(row, obj);
        }
        row.ruKuCDMC = row.ruKuCDMC ? row.ruKuCDMC : row.chanDiMC;
        row.tianJiaWZ =
          val.xianShiXX && val.xianShiXX.tianJiaWZ !== undefined
            ? val.xianShiXX.tianJiaWZ
            : '';
        row.yaoPinZC = row.yaoPinMC + ' ' + row.yaoPinGG;
        row.yaoPinMCYGG = val;
        if (this.yanShouRenXX) {
          this.tableData[index + 1].yanShouRXM = this.yanShouRenXX.yongHuXM;
          this.tableData[index + 1].yanShouRID = this.yanShouRenXX.yongHuID;
          this.tableData[index + 1].yanShouRenXX = {
            yongHuXM: this.yanShouRenXX.yongHuXM,
            yongHuID: this.yanShouRenXX.yongHuID,
          };
        }
        //如果是中药库的话，掉筛选方法
        if (this.isShowSCQY) {
          this.ruKuList = this.ruKuListOriginOptions.filter((item) => {
            return item.chanDiMC.trim() == row.ruKuCDMC;
          });
          this.shengChanList = this.shengChanListOriginOptions.filter(
            (item) => {
              return item.chanDiMC.trim() == row.shengChanQY;
            },
          );
          // this.filterMethod(row.ruKuCDMC);
        }
      } catch (e) {
        console.error(e);
        Object.assign(row, initData());
        // this.tableData[index] = initData();
      } finally {
        this.pageLoading = false;
        this.changeJinJiaJE = false;
        //如果是退库且不符合进价限制条件
        if (!isBeiShu) this.$refs.editTable.actionEnter();
      }
    },
    handleBlur() {
      this.$refs.selectRef.visible = false;
    },
    handelYaoPinXQ1(value, row, index, cellRef) {
      console.log('panel-change', value, row, index, cellRef);
    },
    //药品效期输入完直接跳转下一行的药品输入框
    async handelYaoPinXQ(value, row, index, cellRef) {
      //清空药品效期或者退库状态下，不用校验药品效期
      if (!value) return;
      if (this.params.chuRuKFSID == '9003') return;
      const daoQiSJ = dayjs(value);
      const currentSJ = dayjs();
      //当药品效期小于等于6个月
      const monthsDiff = daoQiSJ.diff(currentSJ, 'months');
      if (monthsDiff <= 6) {
        this.yaoPinXQIndex = index;
        await this.$refs.bizInfo.showDialog(cellRef);
        return;
      }
      focusEditTableDom({
        rowIndex: index + 1,
        columns: this.columns,
        key: 'faPiaoHM',
      });
    },
    //药品效期小于六个月操作处理
    handleYPXQMEthod(type, cellRef) {
      //取消操作
      if (type === 'cancel') {
        this.tableData[this.yaoPinXQIndex].yaoPinXQ = '';
      } else if (type == 'del') {
        focusEditTableDom({
          rowIndex: this.yaoPinXQIndex,
          columns: this.columns,
          key: 'faPiaoHM',
        });
        this.tableData[this.yaoPinXQIndex] = initData();
      } else {
        //确定
        focusEditTableDom({
          rowIndex: this.yaoPinXQIndex + 1,
          columns: this.columns,
          key: 'faPiaoHM',
        });
      }
    },
    handleJiZhang(id) {
      return this.isChongHong
        ? ChongHongJZRuKuDan({ ruKuDID: id })
        : JiZhangYaoPinRKD({ id: id });
    },
    /**
     * 判断是否为新增， 0： 新增、 其他为， 按采购单入库制单
     * @param val
     * @returns {Promise<void>}
     */
    async checkType(val) {
      this.tableData = [];
      if (val == '0') {
        this.params = {
          zhangBuLBID: '',
          zhangBuLBMC: '',
          zuZhiJGID: getJiGouID(),
          xinZengFS: '1',
          chuRuKFSMC: '',
          chuRuKFSID: '',
          zhiDanSJ: dayjs().format('YYYY-MM-DD'),
          gongHuoDWID: '',
          gongHuoDWMC: '',
          yingFuKDJMC: '',
          yingFuKDJDM: '',
          fuDanZS: '',
          beiZhu: '',
          yaoPinZS: 0,
          hongDanBZ: 0,
          jinJiaJE: 0,
          lingShouJE: 0,
          weiZhiID: getWeiZhiID(),
          weiZhiMC: getWeiZhiMC(),
        };
        return Promise.resolve();
      } else await this.getDataFromLocalStorage();
    },
    async getDataFromLocalStorage() {
      let list = JSON.parse(sessionStorage.getItem('ruKuDanList'));
      if (this.isCaiGouRK) {
        this.params.xinZengFS = '0';
        this.params.caiGouDID = list[0].caiGouDID;
        const caiGouIds = list.map((m) => m.caiGouDID);
        const arr = new Set(caiGouIds);
        this.params.caiGouDIDList = Array.from(arr);
        this.params.gongHuoDWID = list[0].gongHuoDWID;
        this.params.gongHuoDWMC = list[0].gongHuoDWMC;
        this.params.beiZhu = `采购单-${this.$route.query.caiGouDH}`;
        this.gongHuoDWList = {
          gongHuoDWID: list[0].gongHuoDWID,
          gongHuoDWMC: list[0].gongHuoDWMC,
        };
      }
      this.handleSelectChange(this.params.chuRuKFSID, 'chuRuKFS');
      let midList = [];
      if (list) {
        const { jiaGeIDs, yaoPinXX, ids } = list.reduce(
          (pre, item) => {
            pre.jiaGeIDs.push(item.jiaGeID);
            const piPeiGZ = item.shengChanPH
              ? item.jiaGeID + '' + item.shengChanPH
              : item.jiaGeID;
            pre.yaoPinXX.set(piPeiGZ, item);
            pre.ids.push(item.id);
            return pre;
          },
          { jiaGeIDs: [], yaoPinXX: new Map(), ids: [] },
        );
        let data = await GetYaoPinCDJGListByJGIDList({
          JiaGeIDList: jiaGeIDs,
          zuZhiJGID: getJiGouID(),
          caiGouJHDMXIDList: ids,
          piHaoXQLY: this.$route.query.piHaoXQLY || '',
          piHaoXQLYWZ: this.$route.query.piHaoXQLYWZ || '',
        });
        data.forEach((yaoPinItem) => {
          // 根据参数判断中药库根据采购单生成的入库单需求自动带出数量，数量是采购单的消耗量
          if (this.yaoKuCGDJLDPT && this.yaoPinRKZDDRCGDSL) {
            yaoPinItem.ruKuSL = yaoPinItem.caiGouJHDXHL;
          }
          const piPeiGZ = yaoPinItem.shengChanPH
            ? yaoPinItem.jiaGeID + '' + yaoPinItem.shengChanPH
            : yaoPinItem.jiaGeID;
          let item = yaoPinXX.get(piPeiGZ);
          if (!item) {
            item = cloneDeep(yaoPinItem);
          }
          item.tianJiaWZ = '';
          const tianJiaWZ =
            item.xianShiXX &&
            item.xianShiXX.tianJiaWZ !== undefined &&
            item.xianShiXX.tianJiaWZ !== null
              ? item.xianShiXX.tianJiaWZ
              : '';
          item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
          yaoPinItem.ruKuCDMC = yaoPinItem.ruKuCDMC
            ? yaoPinItem.ruKuCDMC
            : yaoPinItem.chanDiMC;
          item.yaoPinMCYGG = {
            baoZhuangDW: item.baoZhuangDW,
            chanDiMC: item.chanDiMC,
            danJia: item.danJia,
            guiGeID: item.guiGeID,
            jiaGeID: item.jiaGeID,
            jinJia: item.jinJia,
            kuCunSL: item.kuCunSL,
            yaoPinGG: item.yaoPinGG,
            yaoPinLXDM: item.yaoPinLXDM,
            yaoPinLXMC: item.yaoPinLXMC,
            yaoPinMC: tianJiaWZ + item.yaoPinMC,
          };
          item.liCiJJ = cloneDeep(item.jinJia);
          item.ziDianJinJia = cloneDeep(item.jinJia);
          // item.ruKuSL = item.shenQingSL;
          yaoPinItem.faPiaoHM = this.$route.query.faPiaoHM || '';
          Object.assign(item, yaoPinItem);
          item.id = null;
          item = Object.assign(initData(), item);
          midList.push(item);
        });
        midList.sort((a, b) => {
          return a.shunXuHao - b.shunXuHao;
        });
        this.tableData = midList;
      }
    },
    //编辑的时候获取table内容
    async getDetail(id) {
      const data = await GetYaoPinRKDXQ({ id: id });
      this.oldList = cloneDeep(data.ruKuDanXQs);
      if (data.kuCunZJDM) {
        this.kuCunZJDM = data.kuCunZJDM;
      }
      data.ruKuDanXQs.forEach((item) => {
        if (data.chuRuKFSID == '9003') {
          item.ruKuHKCSL = item.kuCunSL - item.ruKuSL;
        } else {
          item.ruKuHKCSL = item.kuCunSL + item.ruKuSL;
        }
        item.tianJiaWZ = '';
        const tianJiaWZ =
          item.xianShiXX &&
          item.xianShiXX.tianJiaWZ !== undefined &&
          item.xianShiXX.tianJiaWZ !== null
            ? item.xianShiXX.tianJiaWZ
            : '';
        item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
        item.yanShouRenXX = {
          yongHuXM: item.yanShouRXM,
          yongHuID: item.yanShouRID,
        };
        // 编辑时获取药品的字典进价
        item.liCiJJ = cloneDeep(item.jinJia_ZiDian);
        item.ziDianJinJia = cloneDeep(item.jinJia_ZiDian);
        item.yaoPinMCYGG = {
          baoZhuangDW: item.baoZhuangDW,
          chanDiMC: item.chanDiMC,
          danJia: item.danJia,
          guiGeID: item.guiGeID,
          jiaGeID: item.jiaGeID,
          jinJia: item.jinJia,
          kuCunSL: item.kuCunSL,
          yaoPinGG: item.yaoPinGG,
          yaoPinLXDM: item.yaoPinLXDM,
          yaoPinLXMC: item.yaoPinLXMC,
          yaoPinMC: tianJiaWZ + item.yaoPinMC,
        };
        // item.ruKuCDMC = item.chanDiMC;
      });
      Object.assign(this.params, {
        chuRuKFSMC: data.chuRuKFSMC,
        chuRuKFSID: data.chuRuKFSID,
        hongDanBZ: data.hongDanBZ,
        gongHuoDWID: data.gongHuoDWID,
        gongHuoDWMC: data.gongHuoDWMC,
        yingFuKDJMC: data.yingFuKDJMC,
        yingFuKDJDM: data.yingFuKDJDM,
        fuDanZS: data.fuDanZS,
        beiZhu: data.beiZhu,
        zhiDanSJ: data.zhiDanSJ,
        // ruKuRQ: data.beiZhu,
        id: data.id,
        zhangBuLBID: data.zhangBuLBID,
        zhangBuLBMC: data.zhangBuLBMC,
      });
      Object.assign(this.rowData, data);
      if (this.isChongHong) {
        data.ruKuDanXQs.forEach((item) => {
          item.ruKuSL = Math.abs(item.ruKuSL);
        });
      }
      this.tableData = data.ruKuDanXQs;
    },
    closeTab() {
      let closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      this.closeTimeOut = setTimeout(() => {
        this.viewManager.close(closeTabKey);
      }, 500);
    },
    /**
     *  判断行数据必填项
     */
    checkRequired(row, requiredKeyObj, index) {
      let flag = true;
      // 如库存增减代码是3，不校验批号和效期
      if(this.kuCunZJDM == '3') return flag
      for (let key in requiredKeyObj) {
        if (!row[key]) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品'${row.yaoPinMC}'的${
              requiredKeyObj[key]
            }不能为空！`,
          });
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: key,
          });
          flag = false;
          return false;
        }
      }
      return flag;
    },
    handlePiHaoBlur(row, rowIndex) {
      if (row.shengChanPH === '') return;
      let index = this.tableData.findIndex(
        (item, index1) =>
          item.jiaGeID === row.jiaGeID &&
          item.shengChanPH === row.shengChanPH &&
          index1 !== rowIndex,
      );
      // if (index !== -1) {
      //   MdMessage({
      //     type: 'warning',
      //     message: `第${index + 1}行已有药品'${row.yaoPinMC}'的相同生产批号(${
      //       row.shengChanPH
      //     })！`,
      //   });
      //   row.shengChanPH = '';
      // }
    },
    /**
     * 判断必填字段
     * @param data
     * @returns {boolean}
     */
    canSave(data) {
      if (data.length === 0 || (data.length === 1 && !data[0].jiaGeID)) {
        MdMessage({
          type: 'warning',
          message: '入库单列表为空！',
        });
        return;
      }
      let requiredKeyArr = {
        shengChanPH: '批号',
        yaoPinXQ: '药品效期',
      };

      if (this.params.chuRuKFSID == '9003') {
        requiredKeyArr = {
          shengChanPH: '批号',
        };
      }

      if (data[0].yaoPinLXDM && data[0].yaoPinLXDM == 3) {
        requiredKeyArr = {
          shengChanPH: '批号',
        };
      }
      if (!this.params.chuRuKFSID) {
        MdMessage({
          type: 'warning',
          message: `入库单据的出入库方式不能为空！`,
        });
        return false;
      }
      if (!this.params.zhiDanSJ) {
        MdMessage({
          type: 'warning',
          message: `入库单据的入库日期不能为空！`,
        });
        return false;
      }
      if (!this.params.zhangBuLBID && this.showZhangBLB == 1) {
        MdMessage({
          type: 'warning',
          message: `入库单据的账簿类别不能为空！`,
        });
        return false;
      }
      if (!this.params.gongHuoDWID) {
        //药房退还不录入供货单位
        //danWeiBMDM = 1 不需要录入
        let chuRuKFSXX = this.chuRuKFSOptions.find(
          (r) => r.chuRuKFSID == this.params.chuRuKFSID,
        );
        if (chuRuKFSXX.danWeiBMDM != '1') {
          MdMessage({
            type: 'warning',
            message: `入库单据的供货单位不能为空！`,
          });
          return false;
        }
      }
      let flag = true;
      data.every((item, index) => {
        if (index === data.length - 1 && !data.jiaGeID) {
          return true;
        }
        if (!item.jiaGeID) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品名称与规格不能为空！`,
          });
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: 'yaoPinMCYGG',
          });
          flag = false;
          return false;
        }
        if (item.jinJia < 0 && this.params.chuRuKFSID != '1002') {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品'${item.yaoPinMC}'的进价应该大于0`,
          });
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: 'jinJia',
          });
          flag = false;
          return false;
        }
        // 新增库存增减方向“库存不增不减”需要输入负数
        if (this.kuCunZJDM !== '3') {
          if (item.ruKuSL <= 0) {
            MdMessage({
              type: 'warning',
              message: `第${index + 1}行药品'${item.yaoPinMC}'的入库数量应该大于0`,
            });
            focusEditTableDom({
              rowIndex: index,
              columns: this.columns,
              key: 'ruKuSL',
            });
            flag = false;
            return false;
          }
        }
        if (item.ruKuSL > item.kuCunSL && this.params.chuRuKFSID == '9003') {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品'${
              item.yaoPinMC
            }'的入库数量小于库存数量`,
          });
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: 'ruKuSL',
          });
          flag = false;
          return false;
        }
        if (
          (decodeURIComponent(this.params.weiZhiMC).indexOf('草') > -1 ||
            decodeURIComponent(this.params.weiZhiMC).indexOf('中') > -1) &&
          !item.ruKuCDMC
        ) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品的入库产地名称不能为空！`,
          });
          focusEditTableDom({
            rowIndex: index,
            columns: this.columns,
            key: 'ruKuCDMC',
          });
          flag = false;
          return false;
        }
        flag = this.checkRequired(item, requiredKeyArr, index);
        if (!flag) return false;
        return true;
      });
      return flag;
    },
    /**
     * 分类保存数据，（新增，修改，删除）
     * @param data
     * @param params
     */
    fengLeiMXData(data, params) {
      let updateRuKuDMXList = [];
      let addRuKuDMXList = [];
      let deleteRuKuDMXList = [];
      data.forEach((item) => {
        if (item.id) {
          let index = this.oldList.findIndex((item1) => item1.id === item.id);
          if (index !== -1) {
            updateRuKuDMXList.push({
              ruKuCDMC: item.ruKuCDMC,
              jiaGeID: item.jiaGeID,
              yaoPinMC: item.yaoPinMC,
              yaoPinGG: item.yaoPinGG,
              gongHuoDWID: item.gongHuoDWID,
              gongHuoDWMC: item.gongHuoDWMC,
              jinJia: item.jinJia,
              jinJiaJE: item.jinJiaJE,
              piFaJia: item.piFaJia,
              piFaJE: item.piFaJE,
              lingShouJia: item.lingShouJia,
              lingShouJE: item.lingShouJE,
              shengChanPH: item.shengChanPH,
              zuiJinRKSCPH: item.zuiJinRKSCPH,
              shengChanRQ: item.shengChanRQ,
              shengChanQY: item.shengChanQY,
              yaoPinXQ: item.yaoPinXQ,
              piZhunWH: item.piZhunWH,
              jinKouYPZH: item.jinKouYPZH,
              baiFangWZ: item.baiFangWZ,
              guoJiaYBDM: item.guoJiaYBDM,
              guoJiaYBMC: item.guoJiaYBMC,
              ruKuSL: item.ruKuSL,
              kuCunSL: item.kuCunSL,
              jianYanZS: item.jianYanZS,
              chuChangJYHGD: item.chuChangJYHGD,
              yanShouJL: item.yanShouJL,
              baoZhuangQK: item.baoZhuangQK,
              waiGuanZL: item.waiGuanZL,
              jianYanBGSBH: item.jianYanBGSBH,
              zhuCeZH: item.zhuCeZH,
              kouAnJYBG: item.kouAnJYBG,
              chengMingDJCJG: item.chengMingDJCJG,
              daoDaWD: item.daoDaWD,
              piQianFHGZBH: item.piQianFHGZBH,
              chuLiQK: item.chuLiQK,
              yanShouBZ: item.yanShouBZ,
              faPiaoHM: item.faPiaoHM,
              faPiaoRQ: item.faPiaoRQ,
              faPiaoDM: item.jiaGeID,
              dianZiJGM: item.jiaGeID,
              yanShouRXM: item.yanShouRXM,
              yanShouRID: item.yanShouRID,
              id: item.id,
              kouLv: item.kouLv,
              shengPingTBM: item.shengPingTBM,
            });
          }
        } else {
          addRuKuDMXList.push({
            jiaGeID: item.jiaGeID,
            yaoPinMC: item.yaoPinMC,
            ruKuCDMC: item.ruKuCDMC,
            yaoPinGG: item.yaoPinGG,
            gongHuoDWID: item.gongHuoDWID,
            gongHuoDWMC: item.gongHuoDWMC,
            jinJia: item.jinJia,
            jinJiaJE: item.jinJiaJE,
            piFaJia: item.piFaJia,
            piFaJE: item.piFaJE,
            lingShouJia: item.lingShouJia,
            lingShouJE: item.lingShouJE,
            shengChanPH: item.shengChanPH,
            zuiJinRKSCPH: item.zuiJinRKSCPH,
            shengChanRQ: item.shengChanRQ,
            shengChanQY: item.shengChanQY,
            yaoPinXQ: item.yaoPinXQ,
            piZhunWH: item.piZhunWH,
            jinKouYPZH: item.jinKouYPZH,
            baiFangWZ: item.baiFangWZ,
            guoJiaYBDM: item.guoJiaYBDM,
            guoJiaYBMC: item.guoJiaYBMC,
            ruKuSL: item.ruKuSL,
            kuCunSL: item.kuCunSL,
            jianYanZS: item.jianYanZS,
            chuChangJYHGD: item.chuChangJYHGD,
            yanShouJL: item.yanShouJL,
            baoZhuangQK: item.baoZhuangQK,
            waiGuanZL: item.waiGuanZL,
            jianYanBGSBH: item.jianYanBGSBH,
            zhuCeZH: item.zhuCeZH,
            kouAnJYBG: item.kouAnJYBG,
            chengMingDJCJG: item.chengMingDJCJG,
            daoDaWD: item.daoDaWD,
            piQianFHGZBH: item.piQianFHGZBH,
            chuLiQK: item.chuLiQK,
            yanShouBZ: item.yanShouBZ,
            yanShouRQ: item.yanShouRQ,
            yanShouRID: item.yanShouRID,
            yanShouRXM: item.yanShouRXM,
            faPiaoHM: item.faPiaoHM,
            faPiaoRQ: item.faPiaoRQ,
            faPiaoDM: item.faPiaoDM,
            dianZiJGM: item.dianZiJGM,
            shunXuHao: item.shunXuHao,
            kouLv: item.kouLv,
            shengPingTBM: item.shengPingTBM,
          });
        }
      });
      let idList = data.reduce((pre, item) => {
        if (item.id) {
          pre.push(item.id);
        }
        return pre;
      }, []);
      this.oldList.forEach((item) => {
        if (!idList.includes(item.id)) {
          deleteRuKuDMXList.push(item.id);
        }
      });
      params.updateRuKuDMXList = updateRuKuDMXList;
      params.addRuKuDMXList = addRuKuDMXList;
      params.deleteRuKuDMXList = deleteRuKuDMXList;
    },
    /**
     * 1. 判断必填
     * 2. 数字型数据处理
     * 3. 新增时， 直接请求， 编辑时， 分类数据后请求
     * @returns {Promise<*>}
     */
    async onSave() {
      let paramsData = cloneDeep(this.tableData);
      if (!this.canSave(paramsData)) return Promise.reject();
      this.handleParseSaveData(paramsData);
      Object.assign(this.params, this.zongShuJEData);
      //附单张数 为空时传null值
      if (!this.params.fuDanZS && this.params.fuDanZS !== 0) {
        this.params.fuDanZS = null;
      }
      let params = { ...this.params };

      params.zhiDanSJ = `${params.zhiDanSJ}`;
      let result = null;
      if (!paramsData[paramsData.length - 1].jiaGeID) {
        paramsData.splice(paramsData.length - 1, 1);
      }
      if (this.$route.query.id) {
        params.ruKuDID = this.$route.query.id;
        params.id = this.$route.query.id;
        this.fengLeiMXData(paramsData, params);
        params.weiZhiID = getWeiZhiID();
        params.weiZhiMC = getWeiZhiMC();

        let newParams = {
          id: params.id,
          ruKuKSID: params.ruKuKSID,
          ruKuKSMC: params.ruKuKSMC,
          gongHuoDWID: params.gongHuoDWID,
          gongHuoDWMC: params.gongHuoDWMC,
          chuRuKFSID: params.chuRuKFSID,
          chuRuKFSMC: params.chuRuKFSMC,
          yingFuKDJDM: params.yingFuKDJDM,
          yingFuKDJMC: params.yingFuKDJMC,
          fuDanZS: params.fuDanZS,
          beiZhu: params.beiZhu,
          yaoPinZS: params.yaoPinZS,
          zhiDanSJ: params.zhiDanSJ,
          hongDanBZ: params.hongDanBZ,
          addRuKuDMXList: params.addRuKuDMXList,
          updateRuKuDMXList: params.updateRuKuDMXList,
          deleteRuKuDMXList: params.deleteRuKuDMXList,
          zhangBuLBID: params.zhangBuLBID == '0' ? '' : params.zhangBuLBID,
          zhangBuLBMC: params.zhangBuLBID == '0' ? '' : params.zhangBuLBMC,
        };
        result = await UpdateYaoPinRKD(newParams);
      } else {
        params.ruKuDMXList = [];
        paramsData.forEach((item) => {
          params.ruKuDMXList.push({
            ruKuCDMC: item.ruKuCDMC,
            jiaGeID: item.jiaGeID,
            yaoPinMC: item.yaoPinMC,
            yaoPinGG: item.yaoPinGG,
            gongHuoDWID: item.gongHuoDWID,
            gongHuoDWMC: item.gongHuoDWMC,
            jinJia: item.jinJia,
            jinJiaJE: item.jinJiaJE,
            piFaJia: item.piFaJia,
            piFaJE: item.piFaJE,
            lingShouJia: item.lingShouJia,
            lingShouJE: item.lingShouJE,
            shengChanPH: item.shengChanPH,
            zuiJinRKSCPH: item.zuiJinRKSCPH,
            shengChanRQ: item.shengChanRQ,
            shengChanQY: item.shengChanQY,
            yaoPinXQ: item.yaoPinXQ,
            piZhunWH: item.piZhunWH,
            jinKouYPZH: item.jinKouYPZH,
            baiFangWZ: item.baiFangWZ,
            guoJiaYBDM: item.guoJiaYBDM,
            guoJiaYBMC: item.guoJiaYBMC,
            ruKuSL: item.ruKuSL,
            kuCunSL: item.kuCunSL,
            jianYanZS: item.jianYanZS,
            chuChangJYHGD: item.chuChangJYHGD,
            yanShouJL: item.yanShouJL,
            baoZhuangQK: item.baoZhuangQK,
            waiGuanZL: item.waiGuanZL,
            jianYanBGSBH: item.jianYanBGSBH,
            zhuCeZH: item.zhuCeZH,
            kouAnJYBG: item.kouAnJYBG,
            chengMingDJCJG: item.chengMingDJCJG,
            daoDaWD: item.daoDaWD,
            piQianFHGZBH: item.piQianFHGZBH,
            chuLiQK: item.chuLiQK,
            yanShouBZ: item.yanShouBZ,
            yanShouRQ: item.yanShouRQ,
            yanShouRID: item.yanShouRID,
            yanShouRXM: item.yanShouRXM,
            faPiaoHM: item.faPiaoHM,
            faPiaoRQ: item.faPiaoRQ,
            faPiaoDM: item.faPiaoDM,
            dianZiJGM: item.dianZiJGM,
            shunXuHao: item.shunXuHao,
            kouLv: item.kouLv,
            shengPingTBM: item.shengPingTBM,
          });
        });
        let newParams = {
          ruKuKSID: params.ruKuKSID,
          ruKuKSMC: params.ruKuKSMC,
          gongHuoDWID: params.gongHuoDWID,
          gongHuoDWMC: params.gongHuoDWMC,
          chuRuKFSID: params.chuRuKFSID,
          chuRuKFSMC: params.chuRuKFSMC,
          yingFuKDJDM: params.yingFuKDJDM,
          yingFuKDJMC: params.yingFuKDJMC,
          fuDanZS: params.fuDanZS,
          beiZhu: params.beiZhu,
          yaoPinZS: params.yaoPinZS,
          zhiDanSJ: params.zhiDanSJ,
          caiGouDID: params.caiGouDID,
          caiGouDIDList: params.caiGouDIDList,
          xinZengFS: params.xinZengFS,
          ruKuDMXList: params.ruKuDMXList,
          zhangBuLBID: params.zhangBuLBID == '0' ? '' : params.zhangBuLBID,
          zhangBuLBMC: params.zhangBuLBID == '0' ? '' : params.zhangBuLBMC,
          zuiJinRKSCPH: params.zuiJinRKSCPH,
          kuCunZJDM:this.kuCunZJDM,
        };
        result = await AddYaoPinRKD(newParams);
      }
      if (result) {
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
      }
      return Promise.resolve(result);
    },
    /**
     * 点击保存，
     * 1. 调用保存方法
     * 2. 关闭弹框并跳转未记账页面
     * @returns {Promise<void>}
     */
    async handleSave() {
      this.pageLoading = true;
      try {
        await this.onSave();
        this.closeTab();
        await this.$router.push({
          path: '/YaoPinRK',
          query: {
            showType: 'second',
          },
        });
      } finally {
        this.pageLoading = false;
      }
    },
    /**
     * 点击保存并记账
     * 1. 调用保存方法
     * 2. 调用记账方法
     * 3. 成功记账 关闭弹框并跳转记账页面， 记账失败  跳转未记账页面
     * @returns {Promise<void>}
     */
    handleSaveAndJiZhang() {
      this.pageLoading = true;
      this.onSave()
        .then((id) => {
          this.handleJiZhang(id)
            .then((_) => {
              MdMessage({
                type: 'success',
                message: '记账成功',
              });
              this.closeTab();
              this.$router.push({
                path: '/YaoPinRK',
                query: {
                  showType: 'third',
                },
              });
            })
            .catch((_) => {
              this.closeTab();
              this.$router.push({
                path: '/YaoPinRK',
                query: {
                  showType: 'second',
                },
              });
            });
        })
        .catch((e) => {
          this.pageLoading = false;
        });
    },
    formatDate(date) {
      return yaoKuZDJZTimeShow(date);
    },
    handleDaoChu() {},
    handleDelete() {
      if (this.getTableComp().getAllCheckedRows().length == 0) {
        MdMessageBox.confirm('至少选择一条数据！', '操作提醒！', {
          showCancelButton: false,
          confirmButtonText: '好的',
          type: 'warning',
        });
        return;
      }
      if (this.$route.query.id) {
        let tableRow = this.tableData.length - 1;
        let tableSelection = this.getTableComp().getAllCheckedRows().length;

        if (tableRow - tableSelection < 1) {
          MdMessageBox.confirm('是否要作废此单据？', '操作提醒！', {
            cancelButtonText: '否',
            confirmButtonText: '是',
            type: 'warning',
          })
            .then(async () => {
              await ZuoFeiYaoPinRKD(this.$route.query.id);
              this.viewManager.close(this.$route.query.id);
              this.$router.push({
                path: '/YaoPinRK',
                query: {
                  showType: 'second',
                },
              });
            })
            .catch(async () => {
              let tableComp = this.getTableComp();
              let tableSelection = tableComp.getAllCheckedRows();
              tableSelection.forEach((item) => {
                let index = this.tableData.findIndex(
                  (item1) =>
                    item1.yaoPinMCYGG === item.yaoPinMCYGG &&
                    item1.chanDiMC === item.chanDiMC,
                );
                this.$refs[
                  this.isChongHong ? 'editTable1' : 'editTable'
                ].removeRow(index);
              }, []);
              tableComp.clearSelection();
            });
        } else {
          let tableComp = this.getTableComp();
          let tableSelection = tableComp.getAllCheckedRows();
          tableSelection.forEach((item) => {
            let index = this.tableData.findIndex(
              (item1) =>
                item1.yaoPinMCYGG === item.yaoPinMCYGG &&
                item1.chanDiMC === item.chanDiMC,
            );
            this.$refs[this.isChongHong ? 'editTable1' : 'editTable'].removeRow(
              index,
            );
          }, []);
          tableComp.clearSelection();
        }
      } else {
        let tableComp = this.getTableComp();
        let tableSelection = tableComp.getAllCheckedRows();
        tableSelection.forEach((item) => {
          let index = this.tableData.findIndex(
            (item1) =>
              item1.yaoPinMCYGG === item.yaoPinMCYGG &&
              item1.chanDiMC === item.chanDiMC,
          );
          this.$refs[this.isChongHong ? 'editTable1' : 'editTable'].removeRow(
            index,
          );
        }, []);
        tableComp.clearSelection();
      }
    },
    handleVisibleChange(val, cellRef) {
      if (!val) {
        setTimeout(() => {
          cellRef.endEdit();
        }, 100);
      }
    },
    handleChangeGongHuoDW(data) {
      this.params.gongHuoDWMC = data.gongHuoDWMC;
      this.params.gongHuoDWID = data.gongHuoDWID;
    },
    //账簿类别focus
    handlerFocusZB() {
      this.beforeZhangBuChange = cloneDeep(this.params.zhangBuLBID);
    },
    //切换账簿提醒
    async handleChangeZB(val) {
      let obj = this.zhangBuOptions.find((item) => item.zhangBuLBID == val);
      this.params.zhangBuLBMC = obj.zhangBuLBMC;
      if (this.tableData.length === 1) return;
      try {
        await MdMessageBox.confirm(
          '切换账簿类别会清空表格数据，确认切换？',
          '操作提醒！',
          {
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          },
        );
        this.tableData = [];
        this.tableData.push(initData());
      } catch (e) {
        this.params.zhangBuLBID = this.beforeZhangBuChange;
        let obj = this.zhangBuOptions.find(
          (item) => item.zhangBuLBID == this.beforeZhangBuChange,
        );
        this.params.zhangBuLBMC = obj.zhangBuLBMC;
      }
    },
    handleSelectChange(val, key) {
      if (this.params.chuRuKFSID == '1002') {
        this.tableData.forEach((item) => {
          item.jinJia = 0;
        });
      }
      // this.columns[17].hidden = false;
      if (this.params.chuRuKFSID == '9003') {
        // this.columns[17].hidden = true;
      }
      let optionsKey = key + 'Options';
      let mingChengKey = key + 'MC';
      let daiMaKey =
        key + (key === 'gongHuoDW' || key === 'chuRuKFS' ? 'ID' : 'DM');
      let data = this[optionsKey].find((item) => item[daiMaKey] === val);
      if (data) this.params[mingChengKey] = data ? data[mingChengKey] : '';
      // 出入库方式修改后，增加kuCunZJDM字段
      if (data && key === 'chuRuKFS') {
        this.kuCunZJDM = data.kuCunZJDM;
      }
    },
    getTableComp() {
      return this.$refs[this.isChongHong ? 'editTable1' : 'editTable'].$refs
        .tableRef;
    },

    /**
     * 批量登记发票处理
     * @returns {Promise<void>}
     */
    async handlePiLiangDJFP() {
      let tableComp = this.getTableComp();
      let tableSelection = tableComp.getAllCheckedRows();
      if (!tableSelection || tableSelection.length === 0) {
        MdMessageBox.confirm('至少选择一条数据！', '操作提醒！', {
          showCancelButton: false,
          confirmButtonText: '好的',
          type: 'warning',
        });
        return;
      }
      try {
        let data = await this.$refs.piliangdjfp.showModal();
        tableSelection.forEach((item) => {
          Object.assign(item, data);
        });
        tableComp.clearSelection();
      } catch (e) {}
    },
  },
  components: {
    'biz-yaopindw': BizYaopindw,
    // 'biz-yaopinph': BizYaopinph,
    'biz-yaopinlsjj': BizYaoPinLSJJ,
    'gonghuodw-select': GongHuoDWSelect,
    'piliangdjfp-dialog': PiliangdjfpDialog,
    'biz-jiesuants': BizJieSuanTSDialog,
    'md-select-table': MdSelectTable,
    CustomDatePicker,
    CustomSelect,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
@import '../../../components/table-column-set/tableSet.scss';

.#{$md-prefix}-yaopin-select {
  &::v-deep .biz-input {
    height: 33px;
    border-color: transparent;

    &.focus {
      border-radius: 0;
    }
  }
}

.header-parent {
  display: flex;
}

.#{$md-prefix}-inventory-container {
  height: 100%;
  box-sizing: border-box;
  min-height: 0;
  background-color: #eaeff3;
  padding: 8px;
}

.#{$md-prefix}-add-inventory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;

  &-header {
    flex-shrink: 0;

    &__action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 46px;
      background-color: getCssVar('color-1');
      padding: 0 8px;
      margin-bottom: 8px;

      &__left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        border-radius: 4px;

        .#{$md-prefix}-rkd-title {
          margin-right: 8px;
          line-height: 30px;
          color: getCssVar('color-8');
          font-size: 16px;
          font-weight: bold;

          i {
            font-size: 18px;
          }
        }

        ::v-deep .biz-select .biz-input__inner {
          background-color: #fff;
        }
      }

      &__right {
        .#{$md-prefix}-button {
          margin-left: 8px;

          &.md-button--text {
            margin-right: 4px;
          }
        }
      }
    }

    > form {
      padding: 0 8px;
    }
  }

  &-body {
    flex: 1;
    min-height: 0;
    padding: 0 8px;
    display: flex;
    flex-direction: column;

    ::v-deep .#{$md-prefix}-table-set-icon {
      padding-top: 4px;
    }

    ::v-deep .#{$md-prefix}-tooltip {
      min-width: 40px;
    }

    ::v-deep .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .#{$md-prefix}-dingwei-bg {
        td {
          background: #e2efff;
        }
      }

      .#{$md-prefix}-table.#{$md-prefix}-table--edit {
        min-height: 0;
        display: flex;
        flex-direction: column;
      }

      .#{$md-prefix}-base-table__header-wrapper {
        flex-shrink: 0;

        td.td-text.is-center {
          padding-left: 0px;
        }
      }

      .#{$md-prefix}-base-table__body-wrapper {
        overflow: auto;
        flex: 1;

        .#{$md-prefix}-base-table__row.heightLight {
          > td {
            background: #e2efff;
          }
        }
      }

      .cell {
        .#{$md-prefix}-date-editor {
          width: 100%;
        }
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    flex-shrink: 0;
    line-height: 20px;
    font-size: 14px;

    &__info {
      display: flex;

      .#{$md-prefix}-info__name {
        margin-right: 8px;
      }

      .#{$md-prefix}-margin-right-12 {
        margin-right: 12px;
      }

      &.#{$md-prefix}-right {
        span {
          color: #aaa;
        }
      }
    }

    span {
      color: #666666;

      &.#{$md-prefix}-color-222 {
        color: #222222;
      }

      &.#{$md-prefix}-font-bold {
        font-weight: 600;
        color: #222222;
      }
    }
  }
}

.#{$md-prefix}-lieMingCheng {
  padding: var(--md-spacing-3);
}

::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}

::v-deep .#{$md-prefix}-chonghong-number__color .cell {
  color: #f12933 !important;
}

::v-deep .#{$md-prefix}-select {
  width: 210px;
}

::v-deep .yaoPinLXMC > .cell {
  text-align: left;
}

::v-deep .yaoPinLXMCHeader > .cell {
  display: none;
}

::v-deep .#{$md-prefix}-base-table-column--selection {
  text-align: center;

  .cell {
    justify-content: center;
  }
}
</style>
