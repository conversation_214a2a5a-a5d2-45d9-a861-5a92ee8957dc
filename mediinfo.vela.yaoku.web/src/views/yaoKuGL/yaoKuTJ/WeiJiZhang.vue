<template>
  <div :class="prefixClass('page-wrap')">
    <div :class="prefixClass('weijizhang-wrap')">
      <div :class="prefixClass('search-bar')">
        <div :class="prefixClass('search-bar__left')">
          <md-date-picker-range-pro
            v-model="searchDate"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            :class="prefixClass('procurement-date space-8')"
            @change="handleSearch"
          >
          </md-date-picker-range-pro>
          <md-input
            v-model="danJuHao"
            placeholder="输入单据号搜索"
            :class="prefixClass('danjuhao-input')"
            @input="handleSearch"
          >
            <template #suffix>
              <i
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
                @click="handleSearch"
              ></i>
            </template>
          </md-input>
          <biz-yaopindw
            v-model="yaoPinMCObj"
            placeholder="名称、输入码、别名、规格等关键字进行搜索"
            style="width: 340px; margin-left: 8px"
            @change="handleQueryYaoPinMC"
          >
          </biz-yaopindw>
        </div>
        <div :class="prefixClass('search-bar__right')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
          >
            刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            :class="prefixClass('kaidan-button')"
            noneBg
            @click="handleYuLan"
            >打印</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-xinzeng')"
            :class="prefixClass('kaidan-button')"
            noneBg
            @click="handleAddTiaoJiaDan"
            >开单</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('container__alias')">
        <md-table-pro
          :columns="weiJiZhangColumns"
          :pagination="pagination"
          ref="weiJiZhangTable"
          :class="prefixClass('weijizhang-table')"
          height="100%"
          :onFetch="handleFetch"
          @selection-change="handleSelectionChange"
          :cell-class-name="tableCellClassName"
        >
          <template v-slot:tiaoJiaDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('qinglingdantip')"
            >
              <template #content>
                <div @click="copy(row.tiaoJiaDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template #reference> -->
              <span
                :class="prefixClass('qinglingdh')"
                @click="handleClickQingLingDan($event, row)"
                >{{ row.tiaoJiaDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
          </template>
          <template v-slot:yaoPinShu="{ row }">
            {{ row.yaoPinZS }}
          </template>
          <template v-slot:yaoPinMX="{ row }">
            <biz-taglist
              :list="row.yaoPinMXYPMCList"
              @clickMore="({ event }) => handleClickQingLingDan(event, row)"
            ></biz-taglist>
          </template>
          <template v-slot:operate="{ row }">
            <div :class="prefixClass('operate')">
              <md-button
                v-access:BianJi
                type="text-bg"
                @click="handleEditTiaoJiaDan(row)"
                >编辑</md-button
              >
              <md-button
                v-if="row.jiHuaTJSJ"
                disabled="false"
                type="text-bg"
                @click="handleJiZhang(row)"
              >
                待调价
              </md-button>
              <md-button v-else type="text-bg" @click="handleJiZhang(row)">
                记账
              </md-button>
              <md-button type="danger" noneBg @click="handleZuoFei(row)">
                作废
              </md-button>
            </div>
          </template>
        </md-table-pro>
      </div>
    </div>

    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT015'"
      :fileName="'药库调价单'"
      :title="'调价单打印预览'"
    />
    <tiaojiabcd-dialog ref="TiaoJiaBCD"></tiaojiabcd-dialog>
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import DaYinDialog from '@/components/DaYinDialog.vue';
import {
  GetTiaoJiaDCount,
  GetTiaoJiaDList,
  JiZhangTJD,
  ZuoFeiTJD,
} from '@/service/yaoPinYK/yaoKuTJ';
import eventBus from '@/system/utils/eventbus';
import BizTagList from '@/views/yaoKuGL/yaoKuTJ/components/BizTagList.vue';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import TiaoJiaBCD from './components/TiaoJiaBCDDialog.vue';
import tableData from './tableData';
import { logger } from '@/service/log';
export default {
  name: 'weiJiZhang',
  inject: ['$YaoKuTJ'],
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchDate: [],
      danJuHao: '', // 单据号
      tableLoading: false,
      pagination: {
        pageSize: 1000, //默认1000
        pageSizes: [1000, 500, 100, 50, 40, 30, 20, 10],
        layout: 'total, sizes, prev, pager, next, jumper',
      },
      weiJiZhangColumns: tableData.weiJiZhangColumns,
      selectedList: null, //选中调价单
      yaoPinMCObj: null,
      params: {},
    };
  },
  created() {
    this.$nextTick((_) => {
      this.$refs.weiJiZhangTable.search({ pageSize: 100 });
    });
    eventBus.$on('handleJiZhangTJD', (v) => {
      this.handleJiZhang({ id: v });
    });
    eventBus.$on('handleTiaoJiaFetch', () => {
      this.handleSearch();
    });
  },
  watch: {
    active: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    //勾选调价单
    handleSelectionChange(value) {
      this.selectedList = value;
    },
    //打印选中的调价单
    handleYuLan() {
      if (!this.selectedList || this.selectedList.length === 0) {
        MdMessage.warning('请勾选要打印的调价单!');
        return;
      }

      let tiaoJiaDanID = '';
      this.selectedList.forEach((r) => {
        tiaoJiaDanID = tiaoJiaDanID + '|' + r.id;
      });
      let params = {
        tiaoJiaDanID: tiaoJiaDanID,
      };

      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    // 编辑调价单
    handleEditTiaoJiaDan(row) {
      this.$router.push({
        name: 'BianJiTJD',
        query: {
          title: '调价单-' + row.tiaoJiaDH,
          id: row.id,
        },
      });
    },
    async handleTiaoJiaBCD() {
      if (!this.selectedList || this.selectedList.length === 0) {
        MdMessage.warning('请勾选要打印的调价单!');
        return;
      }
      const tiaoJiaDanIDs = [];
      this.selectedList.forEach((x) => {
        tiaoJiaDanIDs.push(x.id);
      });
      const res = await this.$refs.TiaoJiaBCD.showDialog({
        tiaoJiaDanIDs,
      });
    },
    // 新增调价单
    handleAddTiaoJiaDan() {
      this.$router.push({
        name: 'XinZengTJD',
      });
    },
    // 记账
    handleJiZhang(row) {
      MdMessageBox.confirm('是否确定记账', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        this.tableLoading = true;
        JiZhangTJD({ tiaoJiaDID: row.id })
          .then((res) => {
            this.$message({
              message: '记账成功',
              type: 'success',
            });
            this.$emit('go-to-tab');
          })
          .catch((e) => {
            // MessageBox({
            //   title: '系统消息',
            //   type: 'error',
            //   message: '记账失败' + e,
            //   confirmButtonText: '我知道了',
            // })
            logger.error(e);
          })
          .finally(() => {
            this.tableLoading = false;
          });
      });
    },
    // 作废
    async handleZuoFei(row) {
      await MdMessageBox.confirm('是否确定作废', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      });
      await ZuoFeiTJD({ tiaoJiaDID: row.id });
      this.$message({
        message: '操作成功',
        type: 'success',
      });
      this.handleSearch();
      //   .then(async () => {
      //   this.tableLoading = true;
      //   ZuoFeiTJD({ tiaoJiaDID: row.id })
      //     .then(async (res) => {
      //       this.$message({
      //         message: '操作成功',
      //         type: 'success',
      //       });
      //       await this.handleSearch();
      //       this.tableLoading = false;
      //     })
      //     .catch((e) => {
      //       MdMessageBox({
      //         title: '系统消息',
      //         type: 'error',
      //         message: '操作失败' + e,
      //         confirmButtonText: '我知道了',
      //       });
      //     })
      //     .finally(() => {
      //       this.tableLoading = false;
      //     });
      // });
    },
    // table刷新
    async handleFetch({ page, pageSize = 1000 }, config) {
      try {
        let kaiShiSJ = null,
          jieShuSJ = null;
        if (this.searchDate?.length > 0) {
          kaiShiSJ = this.searchDate[0]
            ? dayjs(this.searchDate[0]).format('YYYY-MM-DD')
            : null;
          jieShuSJ = this.searchDate[1]
            ? dayjs(this.searchDate[1]).format('YYYY-MM-DD')
            : null;
        }
        const params = {
          pageIndex: page,
          pageSize: pageSize,
          kaiShiSJ: kaiShiSJ,
          jieShuSJ: jieShuSJ,
          danJuZTDM: '1',
          zuoFeiBZ: 0,
          likeQuery: this.danJuHao,
          jiaGeID: this.yaoPinMCObj?.jiaGeID || '',
        };
        const [items, total] = await Promise.all([
          GetTiaoJiaDList(params, config),
          !this.total && GetTiaoJiaDCount(params, config),
        ]);
        this.data = items;
        if (this.searchDate?.length === 0 && this.data?.length > 0) {
          this.searchDate.push(
            dayjs(this.data[0].zhiDanSJ).format('YYYY-MM-DD'),
          );
          this.searchDate.push(dayjs().format('YYYY-MM-DD'));
        }
        this.total = Number(total) || this.total;
        return {
          items: items,
          total: this.total,
        };
      } catch (error) {
        logger.error(error);
      }
    },
    // 查询条件变更搜索
    handleSearch() {
      this.total = null;
      if (this.$refs.weiJiZhangTable) {
        this.$refs.weiJiZhangTable.search({ pageSize: 100 });
      } else {
        this.$nextTick(() => {
          this.$refs.weiJiZhangTable.search({ pageSize: 100 });
        });
      }
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return this.prefixClass('qinglingdan-row');
      }
      return this.prefixClass('qinglingdan-row');
    },
    // 点击打开侧滑详情
    handleClickQingLingDan(e, row) {
      e.stopPropagation();
      const options = {
        tiaoJiaDH: row.tiaoJiaDH,
        id: row.id,
      };
      this.$YaoKuTJ.$refs.tiaoJiaDrawer.openDrawer(options, 1);
    },
    handleCopySuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 2000,
      });
    },
    handleCopyError() {
      MdMessageBox({
        title: '系统消息',
        type: 'error',
        message: `复制失败！`,
        confirmButtonText: '我知道了',
      });
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.yaoPinMCObj = {
        ...e,
      };

      this.handleSearch();
    },
  },
  components: {
    'biz-taglist': BizTagList,
    'dayin-dialog': DaYinDialog,
    'tiaojiabcd-dialog': TiaoJiaBCD,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
  // @include md-def('color', 'color-6');
}

.#{$md-prefix}-qinglingdantip {
  min-width: 30px;
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
.#{$md-prefix}-daishouli-table.#{$md-prefix}-data-table
  .#{$md-prefix}-qinglingdan-row {
  color: #1e88e5;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-page-wrap {
  height: 100%;
  width: 100%;

  .#{$md-prefix}-weijizhang-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-search-bar {
      display: flex;
      justify-content: space-between;
      height: 30px;

      &__left {
        display: flex;
        .#{$md-prefix}-space-8 {
          margin-right: 8px;
        }
        .#{$md-prefix}-procurement-date {
          width: 250px;
        }
        .#{$md-prefix}-danjuhao-input {
          width: 264px;
        }
      }
      &__right {
        .#{$md-prefix}-kaidan-button {
          margin: 3px 0;
        }
      }
    }
    .#{$md-prefix}-container__alias {
      flex: 1;
      margin-top: 8px;
      min-height: 0;

      .#{$md-prefix}-qinglingdh {
        cursor: pointer;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
        &:hover {
          color: rgb(var(--md-color-6));
          // @include md-def('color', 'color-6');
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-jiyongtag {
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        margin: 0 0 2px 5px;
        background-color: #ff9900;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
      }
      .#{$md-prefix}-operate {
        display: flex;
        justify-content: center;
        align-content: center;
      }
    }
  }
}

::v-deep .#{$md-prefix}-data-table__pagination {
  margin-top: 8px !important;
}

// ::v-deep .#{$md-prefix}-web-base-table-column--selection .cell {
//   padding: 0 10px !important;
// }
</style>
