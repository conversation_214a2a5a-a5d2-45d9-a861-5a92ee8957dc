<template>
  <bmis-blue-dialog
    v-model:visible="visible"
    width="560px"
    height="250px"
    title="按库存下限生成"
    appendToBody
    @submit="handleSave"
    :closeOnClickModal="false"
    @close="handleClose"
  >
    <p>
      <span>默认库存下限(小规格)</span>
      <md-input
        type="number"
        v-model="xiaXian"
        placeholder="请输入"
        style="width: 300px"
      ></md-input>
    </p>
  </bmis-blue-dialog>
</template>

<script>
import { MdMessage } from '@mdfe/medi-ui';
import BlueDialog from '@/components/blue-dialog/index.vue';
export default {
  name: 'anxiaohaol-dialog',
  data() {
    return {
      visible: false,
      xiaXian: '',
    };
  },
  methods: {
    handleSave() {
      const args = {
        KuCunXX: this.xiaXian + '',
      };
      if (!args.KuCunXX) {
        MdMessage.warning('请输入库存下限!');
        return;
      }
      this.resolve(args);
      this.visible = false;
    },
    handleClose() {
      this.reject();
    },
    showModel() {
      this.visible = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style scoped lang="scss">
p {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  margin-top: 14px;
  span {
    margin-left: 80px;
    margin-right: 8px;
    flex-shrink: 0;
  }
}
p:nth-child(1) {
  margin-top: 37px;
}
</style>
