<template>
  <md-dialog
    v-show="dialogVisible"
    :title="title + '调价补差单'"
    width="1100px"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    height="500px"
    ref="tk"
    @closed="closeDialog"
    append-to-body
    top="5vh"
  >
    <md-table
      :columns="columns"
      height="100%"
      ref="tiaoJiaBCD"
      :autoFill="true"
      :data="tiaoJiaDanData"
      @selection-change="handleSelectionChange"
    >
      <template v-slot:yaoPinMCGG="{ row }">
        {{ row.yaoPinMC + ' ' + row.yaoPinGG }}
      </template>
    </md-table>
    <template #footer>
      <md-button
        :class="prefixClass('normal__button')"
        type="primary"
        @click="handelDaYin"
        >打 印</md-button
      >
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT017'"
        :fileName="'药库补差单'"
        :title="'药库补差单打印预览'"
      />
    </template>
  </md-dialog>
</template>

<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetTiaoJiaDByIds } from '@/service/yaoPinYK/yaoKuTJ';
import commonData from '@/system/utils/commonData';
import { MdMessage } from '@mdfe/medi-ui';
export default {
  name: 'ChuRuKFS-dialog',
  data() {
    const refs = this.$refs;
    return {
      dialogVisible: false,
      selectedList: null, //选中调价单
      params: {},
      title: '',
      columns: [
        {
          type: 'selection',
          width: 35,
        },
        {
          prop: 'zhangBuLBMC',
          label: '',
          width: 34,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.zhangBuLBMC,
            );
            return data ? data.tag : '';
          },
        },
        {
          prop: 'tiaoJiaWH',
          label: '调价文号',
          'min-width': 100,
        },
        {
          slot: 'yaoPinMCGG',
          prop: 'yaoPinMCGG',
          label: '药品名称与规格',
          'min-width': 301,
        },
        { prop: 'chanDiMC', label: '产地名称', 'min-width': 110 },
        {
          prop: 'caiGouDW',
          label: '采购公司',
          'min-width': 102,
        },
        { prop: 'baoZhuangDW', label: '单位', 'min-width': 88 },
        { prop: 'tiaoJiaSL', label: '调价数量', 'min-width': 80 },
        { prop: 'yuanJinJia', label: '原进价', 'min-width': 88 },
        { prop: 'xianJinJia', label: '现进价', 'min-width': 92 },
        { prop: 'yuanLingSJ', label: '原零售价', 'min-width': 88 },
        { prop: 'xianLingSJ', label: '现零售价', 'min-width': 92 },
      ],
      tiaoJiaDanData: [],
    };
  },
  created() {},
  methods: {
    closeDialog() {
      this.dialogVisible = false;
    },
    async cancel() {
      this.dialogVisible = false;
    },
    //勾选调价单
    handleSelectionChange(value) {
      this.selectedList = value;
    },
    async handleAfterFetch() {
      this.$nextTick(() => {
        this.$refs.tiaoJiaBCD.toggleAllSelection();
      });
    },
    async showDialog(options) {
      const res = await GetTiaoJiaDByIds({
        tiaoJiaDIDs: options.tiaoJiaDanIDs.join('|'),
      });
      this.tiaoJiaDanData = res;
      // this.selectedList = this.tiaoJiaDanData
      this.dialogVisible = true;
      this.handleAfterFetch();
    },
    handelDaYin() {
      if (!this.selectedList || this.selectedList.length === 0) {
        MdMessage.warning('请勾选要打印的调价单!');
        return;
      }
      const tiaoJiaDanMXIDs = [];
      this.selectedList.forEach((x) => {
        tiaoJiaDanMXIDs.push(x.id);
      });
      const params = {
        tiaoJiaDMXIDs: tiaoJiaDanMXIDs.join('|'),
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
.#{$md-prefix}-checkBox {
  padding-right: 30px;
  margin-bottom: 8px;
}
</style>
