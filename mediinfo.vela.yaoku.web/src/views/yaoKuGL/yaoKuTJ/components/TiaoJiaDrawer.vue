<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="customSize"
    ref="tiaoJiaDanDrawer"
    :modalClass="prefixClass('tiaojiadandrawer')"
    height="100%"
    @closed="closeDrawer"
  >
    <div
      v-loading="loading"
      :loading-text="loadingText"
      :class="prefixClass('drawer__alias')"
    >
      <div :class="prefixClass('tiaoJiaDan-title')">
        <span :class="prefixClass('title-left')">
          {{ title }}
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <!-- <md-button type="primary" :icon="prefixClass('icon-daochu')" noneBg
              >导出</md-button
            > -->
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handlePrint"
              >打印</md-button
            > -->
            <md-button
              v-show="type === 1 && !jiHuaTJSJ"
              type="primary"
              noneBg
              @click="handleJiZhang"
              ><i
                class="iconfont iconedit"
                :class="prefixClass('icon-right-4')"
              ></i
              >记账</md-button
            >
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content__alias')">
        <div :class="prefixClass('content-zhuangtai')">
          <md-descriptions direction="horizontal" label-align="left">
            <md-description-item label="调价方式" :column-percent="16">{{
              tiaoJiaFSMC || '-'
            }}</md-description-item>
            <!-- <md-description-item label="调价文号" :column-percent="18">{{
              tiaoJiaWH || '-'
            }}</md-description-item> -->
            <md-description-item label="计划调价时间" :column-percent="24">{{
              jiHuaTJSJ || '-'
            }}</md-description-item>
            <md-description-item label="备注" :column-percent="40">{{
              beiZhu || '-'
            }}</md-description-item>
          </md-descriptions>
        </div>
        <md-table
          :columns="tiaoJiaDanColumns"
          :data="tiaoJiaDanData"
          height="100%"
          :key="tableKey"
          :class="prefixClass('tiaojia-table')"
          ref="tiaoJiaTable"
        >
          <template v-slot:yaoPinMCGG="{ row }">
            {{ row.yaoPinMC + ' ' + row.yaoPinGG }}
          </template>
          <template v-slot:jinJiaSJJE="{ row }">
            {{
              (
                Number(row.xianJinJia) -
                Number(row.yuanJinJia ? row.yuanJinJia : 0)
              ).toFixed(3)
            }}
          </template>
          <template v-slot:lingShouSJJE="{ row }">
            {{
              (
                Number(row.xianLingSJ) -
                Number(row.yuanLingSJ ? row.yuanLingSJ : 0)
              ).toFixed(3)
            }}
          </template>
        </md-table>
        <div :class="prefixClass('description')" style="flex-shrink: 0">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">{{
              zhiDanRXM + ' ' + zhiDanSJ
            }}</span>
            <span
              v-if="type == 2"
              :class="prefixClass('description-item__label')"
              >记账:</span
            >
            <span
              v-if="type == 2"
              :class="prefixClass('description-item__content')"
              >{{ jiZhangRXM + ' ' + jiZhangSJ }}</span
            >
          </div>
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ tiaoJiaDanData.length
              }}<span :class="prefixClass('content-color')">种药品</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >合计 进价差额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ Number(jinJiaJE).toFixed(3)
              }}<span :class="prefixClass('content-color')">元</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >零售差额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ Number(lingShouJE).toFixed(3)
              }}<span :class="prefixClass('content-color')">元</span></span
            >
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT006'"
        :fileName="'药库调价单'"
        :title="'药库调价单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetTiaoJiaDById } from '@/service/yaoPinYK/yaoKuTJ';
import eventBus from '@/system/utils/eventbus';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { printByUrl } from '@/system/utils/print';
import tableData from '../tableData';

export default {
  name: 'tiaoJiaDrawer',
  props: {
    size: { type: String || Number, default: '75%' },
  },
  data() {
    return {
      customSize: null,
      loadingText: '正在加载中...',
      drawer: false,
      loading: false,
      params: {},
      title: '',
      type: 1, // 默认1 1-待受理 2-已受理
      id: '',
      tiaoJiaFSMC: '', // 调价方式名称
      tiaoJiaWH: '', // 调价文号
      jiHuaTJSJ: null, // 计划调价时间
      beiZhu: '',
      zhiDanRXM: '',
      zhiDanSJ: '',
      jiZhangRXM: '',
      jiZhangSJ: '',
      jinJiaJE: 0,
      lingShouJE: 0,
      tiaoJiaDanColumns: [],
      tiaoJiaDanData: [],
      tableKey: 0,
      hiddenGongHuoDW: true,
    };
  },
  async mounted() {
    if (this.size) {
      this.customSize = this.size;
    }
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    //打开
    async openDrawer(options, type) {
      this.loading = true;
      const res = await getKuFangSZList(['tiaoJiaSFAGHDW']);
      if (res.length > 0) {
        res.forEach((el) => {
          if (el.xiangMuDM === 'tiaoJiaSFAGHDW') {
            this.hiddenGongHuoDW = el.xiangMuZDM == 1 ? false : true;
          }
        });
      }
      this.title = `详情 - ${options.tiaoJiaDH}`;
      this.type = type ? type : 1;
      tableData.yiJiZXQColumns[3].hidden = this.hiddenGongHuoDW;
      tableData.weiJiZXQColumns[3].hidden = this.hiddenGongHuoDW;
      this.tiaoJiaDanColumns =
        type == 2 ? tableData.yiJiZXQColumns : tableData.weiJiZXQColumns;
      this.tableKey++;
      this.id = options.id;
      this.drawer = true;
      GetTiaoJiaDById({ tiaoJiaDID: options.id })
        .then((res) => {
          this.tiaoJiaDanData = res.mingXiYPList;
          this.tiaoJiaFSMC = res.tiaoJiaFSMC;
          this.tiaoJiaWH = res.tiaoJiaWH;
          this.jiHuaTJSJ = res.jiHuaTJSJ;
          this.beiZhu = res.beiZhu;
          this.jinJiaJE = res.mingXiYPList.reduce(
            (total, element) =>
              (element.xianJinJia - element.yuanJinJia) * element.tiaoJiaSL +
              total,
            0,
          );
          this.lingShouJE = res.mingXiYPList.reduce(
            (total, element) =>
              (element.xianLingSJ - element.yuanLingSJ) * element.tiaoJiaSL +
              total,
            0,
          );
          this.zhiDanRXM = res.zhiDanRXM;
          this.zhiDanSJ = yaoKuZDJZTimeShow(res.zhiDanSJ);
          this.jiZhangRXM = res.jiZhangRXM;
          this.jiZhangSJ = yaoKuZDJZTimeShow(res.jiZhangSJ);
          // 自适应drawer宽度
          this.$nextTick(() => {
            // 仅在药品调价中，应用以下
            if ([1, 2].includes(this.type)) {
              let size = 16;
              this.$refs.tiaoJiaTable.columns.forEach((item) => {
                size += item.width;
              });
              this.customSize = size;
            }
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    handlerClose() {
      this.$emit('closed');
    },
    handleClickBodyCloseDrawer(e) {
      this.closeDrawer();
      // if (!this.$refs.tiaoJiaDanDrawer.$el.contains(e.target)) {
      // }
    },
    handleJiZhang() {
      eventBus.$emit('handleJiZhangTJD', this.id);
      this.drawer = false;
    },
    //预览
    async handleYuLan() {
      const params = {
        tiaoJiaDanID: this.id,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handlePrint() {
      try {
        this.loading = true;
        this.loadingText = '正在打印中...';
        await printByUrl('YKXT006', { tiaoJiaDanID: this.id });
        MdMessage.success('打印成功！');
      } catch (e) {
        // Message.error(e.message || '打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
        this.loadingText = '正在加载中...';
      }
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-bihuan-popper {
  .#{$md-prefix}-bihuan-content {
    display: flex;

    .#{$md-prefix}-bihuan-item {
      &__title {
        display: flex;
        width: 156px;
        height: 26px;
        color: #333333;

        .#{$md-prefix}-title-name {
          display: inline-block;
          width: 120px;
          height: 26px;
          padding-left: 8px;
          color: #333333;
          font-size: 14px;
          line-height: 26px;
          background:
            linear-gradient(135deg, transparent 0, #ade2ff 0) top left,
            linear-gradient(225deg, transparent 9px, #ade2ff 0) top right,
            linear-gradient(-45deg, transparent 9px, #ade2ff 0) bottom right,
            linear-gradient(45deg, transparent 0, #ade2ff 0) bottom left;
          background-color: #ade2ff;
          background-size: 50% 50%;
          background-repeat: no-repeat;

          &.without {
            background:
              linear-gradient(135deg, transparent 0, #ddd 0) top left,
              linear-gradient(225deg, transparent 9px, #ddd 0) top right,
              linear-gradient(-45deg, transparent 9px, #ddd 0) bottom right,
              linear-gradient(45deg, transparent 0, #ddd 0) bottom left;
            background-color: #ddd;
            background-size: 50% 50%;
            background-repeat: no-repeat;
          }
        }

        .#{$md-prefix}-title-xian {
          width: 36px;
          margin: auto 0;
          border-top: solid #ade2ff 1px;

          &.#{$md-prefix}-without {
            border-top: solid #ddd 1px;
          }
        }
      }

      &__time {
        margin-top: 4px;
        font-weight: 500;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
      }

      .#{$md-prefix}-description-top {
        margin-top: 4px;
        flex-direction: column;

        .#{$md-prefix}-description-item {
          padding: 0 0;
        }
      }
    }
  }
}

.#{$md-prefix}-description {
  display: flex;
  justify-content: space-between;

  &-item {
    line-height: 20px;
    min-height: 20px;
    font-size: 14px;
    color: #333;
    padding: 5px 0;

    &__content {
      padding-left: 5px;

      &.#{$md-prefix}-fontWeight {
        font-weight: bold;
      }

      .#{$md-prefix}-content-color {
        color: #aaa;
        font-weight: normal;
      }
    }
  }
}

.#{$md-prefix}-description {
  .#{$md-prefix}-description-item__label {
    color: #aaa;
    margin-left: 8px;
  }
}
.#{$md-prefix}-tiaojiadandrawer {
  position: initial !important;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-tiaojiadandrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;

  .#{$md-prefix}-drawer__alias {
    display: flex;
    flex-direction: column;
    height: 100%;

    .#{$md-prefix}-tiaoJiaDan-title {
      display: flex;
      justify-content: space-between;
      // @include md-def('background-color', 'color-1');
      background-color: rgb(var(--md-color-1));
      height: 36px;
      line-height: 36px;
      flex-shrink: 0;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-right {
        .#{$md-prefix}-title-toolbar {
          margin-right: 8px;
        }

        .#{$md-prefix}-title-close {
          i {
            font-size: 14px;
            float: right;
            margin-right: 12px;
            margin-top: 11px;
            color: #aaaaaa;
            cursor: pointer;
          }
        }

        .#{$md-prefix}-icon-right-4 {
          margin-right: 4px;
        }
      }
    }

    .#{$md-prefix}-content__alias {
      flex: 1;
      display: flex;
      flex-direction: column;
      // margin-top: 10px;
      min-height: 0;
      padding: 0 8px 8px 8px;

      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }

      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }

      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
        height: 30px;
        flex-shrink: 0;
      }

      .#{$md-prefix}-tiaojia-table {
        flex: 1;
        min-height: 0;
        width: 100%;
      }
    }
  }
}

.#{$md-prefix}-pos-bottom-direction {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 98%;
  justify-content: space-between;
}

::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;

  .#{$md-prefix}-description-item__label {
    margin-left: 0;
  }
}

::v-deep .#{$md-prefix}-description-item {
  color: #222;
}

::v-deep .#{$md-prefix}-description-item {
  // font-size: 14px;
  // @include md-def('font-size', 'font-2');
  font-size: getCssVar('font-2');
}
</style>
