<template>
  <div v-loading="loading" :class="prefixClass('tiaojiadan-page-wrap')">
    <div :class="prefixClass('tiaojiadan-wrap')">
      <div :class="prefixClass('tiaojiadan-wrap-title')">
        <div :class="prefixClass('title__left')">
          <div :class="prefixClass('danjumc')">
            <i class="iconfont icondanju"></i>
            调价单
          </div>
          <div :class="prefixClass('yaopindw')">
            <biz-yaopindw
              v-model="kuaiSuDWYP"
              showSuffix
              placeholder="输入药品快速定位"
              :class="prefixClass('danjuhao-input')"
              @change="handleChangeYaoPinDWSR($event, this)"
              @keyup.enter.native="handleEnter"
            ></biz-yaopindw>
          </div>
          <!-- 草药才有按下限生成 -->
          <div
            :class="prefixClass('procurement-left-rt')"
            placement="top"
            width="160"
            v-show="kuCunGLLX == 3"
          >
            <span slot="reference" @click="hanldeAnKunCSC">按库存下限生成</span>
          </div>
        </div>
        <div :class="prefixClass('title__right')">
          <md-button
            v-if="false"
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            :class="prefixClass('right-8')"
            noneBg
            @click="handlePrint"
            >打印</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-shanchuwap')"
            :class="prefixClass('right-8')"
            noneBg
            @click="handleDel"
            >删除</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('right-12')"
            plain
            @click="handleSave"
            >保存</md-button
          >
          <md-button
            v-show="!formData.jiHuaTJSJ"
            type="primary"
            :class="prefixClass('right-12')"
            @click="handleSaveAndJiZhang"
            >保存并记账</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('search-bar')">
        <md-form :model="formData" ref="form" inline>
          <md-form-item label="调价方式" prop="tiaoJiaFSDM" label-width="auto">
            <md-select
              v-model="formData.tiaoJiaFSDM"
              placeholder="请选择"
              style="width: 130px"
            >
              <md-option
                v-for="item in tiaoJiaFSOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              >
              </md-option>
            </md-select>
          </md-form-item>
          <!-- <md-col :span="6">
              <md-form-item label="调价文号">
                <md-input
                  v-model="formData.tiaoJiaWH"
                  placeholder="请输入"
                /> </md-form-item
            ></md-col> -->
          <md-form-item
            label="计划调价时间"
            prop="jiHuaTJSJ"
            label-width="auto"
          >
            <md-date-picker
              v-model="formData.jiHuaTJSJ"
              type="datetime"
              placeholder="选择日期时间"
              @change="handleChangeJiHuaTJSJ"
            >
            </md-date-picker>
          </md-form-item>
          <md-form-item label="备注" prop="beiZhu" label-width="auto">
            <md-input
              v-model="formData.beiZhu"
              placeholder="请输入"
              width="340px"
            />
          </md-form-item>
        </md-form>
      </div>

      <div :class="prefixClass('container__alias')">
        <md-editable-table-pro
          v-model="tableData"
          v-table-enter="handleTableEnter"
          :columns="columns"
          id="tiaoJiaTable"
          :class="prefixClass('editable')"
          ref="editable"
          :hideAddButton="true"
          :autoFill="true"
          :key="tableKey"
          :show-default-operate="showDefaultOperate"
        >
          <template #yaoPinMCYGG="{ row, $index }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              automatic-dropdown
              labelKey="yaoPinZC"
              @change="handleTableYaoPinDWChange($event, row, $index)"
              @visible-change="handleVisibleChange($event, row, $index)"
              @getFenBuSL="getFenBuSL(row)"
            ></biz-yaopindw>
          </template>
          <template #xianJinJia="{ row, $index }">
            <md-input
              v-model="row.xianJinJia"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @input="(val) => handleInputChange(val, $index, '')"
            ></md-input>
          </template>
          <!-- <template v-slot:jinJiaSJJE="{ row }">
            {{
 (
                (Number(row.xianJinJia) -
                  Number(row.yuanJinJia ? row.yuanJinJia : 0)) *
                Number(row.tiaoJiaSL)
              ).toFixed(2)
            }}
          </template> -->
          <template #xianLingSJ="{ row, $index }">
            <md-input
              v-model="row.xianLingSJ"
              v-number.float="{}"
              placeholder="请填写"
              :clearable="false"
              @input="(val) => handleInputChange(val, $index, 'xianLingSJ')"
            ></md-input>
            <!-- @input="handleLSJInputChange($index)" -->
          </template>
          <!-- <template v-slot:lingShouSJJE="{ row }">
            {{
              (
                (Number(row.xianLingSJ) -
                  Number(row.yuanLingSJ ? row.yuanLingSJ : 0)) *
                Number(row.tiaoJiaSL)
              ).toFixed(2)
            }}
          </template> -->
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('description')" style="flex-shrink: 0">
        <div :class="prefixClass('description-item')">
          <span :class="prefixClass('description-item__label')">制单:</span>
          <span :class="prefixClass('description-item__content')">{{
            zhiDanRXM + ' ' + zhiDanSJ
          }}</span>
        </div>
        <div :class="prefixClass('description-item')">
          <span :class="prefixClass('description-item__label')">共计:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ yaoPinZS
            }}<span :class="prefixClass('content-color')">种药品</span></span
          >
          <span :class="prefixClass('description-item__label')"
            >合计 进价金额:</span
          >
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(jinJiaJE).toFixed(3)
            }}<span :class="prefixClass('content-color')">元</span>
          </span>
          <span :class="prefixClass('description-item__label')">零售金额:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(lingShouJE).toFixed(3)
            }}<span :class="prefixClass('content-color')">元</span></span
          >
          <span :class="prefixClass('description-item__label')">进价差额:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(jinJiaCE).toFixed(3)
            }}<span :class="prefixClass('content-color')">元</span></span
          >
          <span :class="prefixClass('description-item__label')">零售差额:</span>
          <span :class="prefixClass('description-item__content fontWeight')"
            >{{ Number(lingShouCE).toFixed(3)
            }}<span :class="prefixClass('content-color')">元</span></span
          >
        </div>
      </div>
    </div>
    <!-- :class="prefixClass('tiaojiadan-right')" -->
    <div
      v-loading="fenBuLoading"
      :class="[
        prefixClass(['tiaojiadan-right']),
        yaoPinKCFBVisible ? 'on' : '',
      ]"
    >
      <a
        href="javascript:;"
        :class="[prefixClass(['btn-visible']), yaoPinKCFBVisible ? 'on' : '']"
        @click="handleYaoPinKCFBVisible"
        ref="jianTouBtn"
      >
        <md-icon name="zuojiantou-k" />
      </a>
      <p :class="prefixClass('tiaojiadan-title')">药品库存分布</p>
      <div v-if="!fenBuData.jiaGeID" :class="prefixClass('nodata')">
        <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
        <span>请选择药品</span>
      </div>
      <div :class="prefixClass('tiaojiadan-data')">
        <div class="bggray">
          <div class="name">{{ fenBuData.yaoPinMC }}</div>
          <div class="info">
            <md-descriptions>
              <md-description-item label="产地名称">{{
                fenBuData.chanDiMC
              }}</md-description-item>
              <md-description-item label="单位">{{
                fenBuData.baoZhuangDW
              }}</md-description-item>
              <md-description-item label="进价">{{
                fenBuData.jinJia
              }}</md-description-item>
              <md-description-item label="零售价">{{
                fenBuData.lingShouJia
              }}</md-description-item>
            </md-descriptions>
          </div>
        </div>
        <div class="table">
          <md-table
            :columns="fenBucolumns"
            :data="fenBuData.fenBuMXList"
            class="demo-table"
          >
            <template v-slot:append>
              <div class="footer">
                <span> 合计：</span>
                <span> {{ fenBuData.zongShuLiang }} </span>
              </div>
            </template>
          </md-table>
        </div>
      </div>
    </div>
    <ankucxx-dialog ref="anKuCunXXSCDialog" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep, findIndex, isEqual } from 'lodash';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';

import eventBus from '@/system/utils/eventbus';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2.js';
import { getKuCunGLLX, getYongHuXM } from '@/system/utils/local-cache';
import { add } from '@/system/utils/mathComputed';
import { logger } from '@/service/log';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  AddTiaoJiaDan,
  AnKuCunXXSC,
  GetTiaoJiaDById,
  GetTiaoJiaYPKCFB,
  GetTiaoJiaYPPCKCList,
  JiZhangTJD,
  UpdateTiaoJiaDan,
} from '@/service/yaoPinYK/yaoKuTJ';
import { printByUrl } from '@/system/utils/print';
import AnKuCunXXSCDialog from './components/AnKuCunXXSCDialog.vue';
const formatJiaGekong = (num, xiaoShu = 3) => {
  if (!num) return '';
  num = +num + '';
  let numLength = num.split('.');
  if (numLength.length === 1) {
    return Number(num).toFixed(xiaoShu);
  }
  numLength = numLength[1];
  if (numLength.length < 2) {
    return Number(num).toFixed(xiaoShu);
  }
  return num;
};
const initRow = () => ({
  // tiaoJiaWH: '',
  tiaoJiaDID: '',
  jiaGeID: '',
  yaoPinMC: '',
  yaoPinGG: '',
  baoZhuangDW: '',
  zhangBuLBID: '',
  zhangBuLBMC: '',
  chanDiID: '',
  chanDiMC: '',
  // caiGouDW: '',
  tiaoJiaSL: 0,
  yuanJinJia: 0,
  yuanJinJJE: 0,
  yuanPiFJ: 0,
  yuanLingSJ: 0,
  yuanPiFJE: 0,
  yuanLingSJE: 0,
  shiJiJJ: 0,
  xianJinJia: '',
  xianJinJJE: 0,
  xianPiFJ: 0,
  xianLingSJ: '',
  xianPiFJE: 0,
  xianLingSJE: 0,
  yuanJiaJL: 0,
  xianJiaJL: 0,
  shunXuHao: 0,
  id: null,
});
export default {
  name: 'xinZengTJD',
  inject: ['viewManager'],
  // props: {
  //   id: { type: String, default: '' }
  // },
  data() {
    return {
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      tableKey: 0,
      id: '',
      tableBodyEle: '',
      dangQingDWYPIndex: null, //药品定位， 当前聚焦的index
      dingWeiYPIndexArr: [], //药品定位， 搜索到的所有药品index
      cssPrefix: process.env.VUE_APP_NAMESPACE,
      formData: {
        tiaoJiaFSDM: '2', // 调价方式代码 默认政策调价
        tiaoJiaFSMC: '', // 调价方式名称
        // tiaoJiaWH: '', // 调价文号
        jiHuaTJSJ: null, // 计划调价时间
        beiZhu: '',
      },
      kuCunGLLX: '', // 库存管理类型
      kuaiSuDWYP: '', // 快速定位药品
      columns: [
        {
          type: 'selection',
          width: 40,
          showOverflowTooltip: false,
          selectable: (row, index) => {
            return this.tableData.length !== index + 1;
          },
        },
        {
          prop: 'yaoPinLX',
          label: '',
          formatter: (row, column, cellValue, index) => {
            if (row.zhangBuLBMC) {
              return row.zhangBuLBMC.substr(0, 1);
            } else {
              return '';
            }
          },
          type: 'text',
          width: 34,
        },
        // {
        //   prop: 'tiaoJiaWH',
        //   label: '调价文号',
        //   width: 135,
        //   type: 'text',
        // },

        {
          slot: 'yaoPinMCYGG',
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          formatter: (row, column, cellValue, index) => {
            if (row.yaoPinMC && row.yaoPinGG)
              return row.yaoPinMC + ' ' + row.yaoPinGG;
          },
          minWidth: 301,
          endMode: 'custom',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          type: 'text',
          width: 178,
          showOverflowTooltip: true,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          width: 160,
          hidden: true,
          type: 'text',
        },
        // {
        //   prop: 'caiGouDW',
        //   label: '采购公司',
        //   type: 'text',
        //   width: 178,
        // },
        {
          prop: 'baoZhuangDW',
          type: 'text',
          label: '单位',
          align: 'center',
          width: 60,
        },
        {
          prop: 'tiaoJiaSL',
          label: '调价数量',
          type: 'text',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(3);
          },
        },
        {
          prop: 'yuanJinJia',
          label: '原进价',
          type: 'text',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.jinJiaXSDW);
          },
        },
        {
          slot: 'xianJinJia',
          prop: 'xianJinJia',
          label: '现进价',
          align: 'right',
          disabled: ({ row }) => {
            // return row.yuanJinJia == 0 && this.id;
          },
          formatter: (row, column, cellValue, index) => {
            row.xianJinJJE = cellValue * row.tiaoJiaSL;
            return formatJiaGekong(cellValue, this.jinJiaXSDW);
          },
          width: 100,
        },
        {
          prop: 'yuanLingSJ',
          label: '原零售价',
          type: 'text',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.lingShouXSDW);
          },
        },
        {
          slot: 'xianLingSJ',
          prop: 'xianLingSJ',
          label: '现零售价',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            row.xianLingSJE = cellValue * row.tiaoJiaSL;
            return formatJiaGekong(cellValue, this.lingShouXSDW);
          },
          width: 100,
        },
        {
          prop: 'jinJiaSJJE',
          label: '进价升降金额',
          align: 'right',
          type: 'text',
          width: 115,
          formatter: (row, column, cellValue, index) => {
            return (
              (Number(row.xianJinJia) -
                Number(row.yuanJinJia ? row.yuanJinJia : 0)) *
              Number(row.tiaoJiaSL)
            ).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          // slot: 'lingShouSJJE',
          prop: 'lingShouSJJE',
          type: 'text',
          label: '零售升降金额',
          align: 'right',
          width: 115,
          formatter: (row, column, cellValue, index) => {
            return (
              (Number(row.xianLingSJ) -
                Number(row.yuanLingSJ ? row.yuanLingSJ : 0)) *
              Number(row.tiaoJiaSL)
            ).toFixed(this.lingShouJEXSDW);
          },
        },
      ],
      tableData: [],
      showDefaultOperate: false,
      loading: false,
      tiaoJiaFSOptions: [], // 调价方式
      zhiDanRXM: '',
      zhiDanSJ: '',
      yaoPinZS: 0,
      jinJiaJE: 0,
      lingShouJE: 0,
      jinJiaCE: 0,
      lingShouCE: 0,
      fenBuData: {},
      fenBuLoading: false,
      fenBucolumns: [
        {
          prop: 'jiGouMC',
          label: '机构名称',
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoKuFangMC',
          label: '药库房名称',
          showOverflowTooltip: true,
        },
        {
          prop: 'shuLiang',
          label: '库存数量',
          showOverflowTooltip: true,
        },
      ],
      yaoPinKCFBVisible: true,
    };
  },
  computed: {
    editTableDisabled() {
      let data = this.tableData;
      if (data.length === 0) return false;
      else if (data[data.length - 1].jiaGeID !== '') return false;
      return true;
    },
  },
  watch: {
    // change的时候计算价格
    tableData: {
      handler(val) {
        let jinJiaJEHJ = 0;
        let lingShouJEHJ = 0;
        let jinJiaCEHJ = 0;
        let lingShouCEHJ = 0;
        val.forEach((item) => {
          jinJiaJEHJ = add(jinJiaJEHJ, item.xianJinJJE);
          lingShouJEHJ = add(lingShouJEHJ, item.xianLingSJE);
          jinJiaCEHJ = add(
            jinJiaCEHJ,
            (item.xianJinJia - item.yuanJinJia) * item.tiaoJiaSL,
          );
          lingShouCEHJ = add(
            lingShouCEHJ,
            (item.xianLingSJ - item.yuanLingSJ) * item.tiaoJiaSL,
          );
        });
        this.jinJiaJE = jinJiaJEHJ;
        this.lingShouJE = lingShouJEHJ;
        this.jinJiaCE = jinJiaCEHJ;
        this.lingShouCE = lingShouCEHJ;
        this.yaoPinZS = val.length - 1;
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    this.initData();
    const res = await getKuFangSZList([
      'tiaoJiaSFAGHDW',
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM === 'tiaoJiaSFAGHDW') {
          this.columns[4].hidden = el.xiangMuZDM == 1 ? false : true;
          this.tableKey++;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  methods: {
    async initData() {
      this.loading = true;
      try {
        var yaoPinLXList = getKuCunGLLX().split('|');
        if (yaoPinLXList.findIndex((r) => r == 3) > -1) {
          this.kuCunGLLX = 3;
        }
        //字典初始化
        getYaoPinShuJuYZYList(['YP0046']).then((res) => {
          this.tiaoJiaFSOptions = res[0]?.zhiYuList;
        });
        // 如果ID不为空，则是编辑 获取调价单的数据
        this.id = this.$route.query.id ? this.$route.query.id : '';
        if (this.id) {
          await GetTiaoJiaDById({ tiaoJiaDID: this.id }).then((res) => {
            this.resetData = cloneDeep(res);
            res.mingXiYPList.forEach((item) => {
              item.yaoPinXX = {
                guiGeID: item.guiGeID,
                yaoPinMC: item.yaoPinMC,
                yaoPinGG: item.yaoPinGG,
                jiaGeID: item.jiaGeID,
                chanDiMC: item.chanDiMC,
                // caiGouDW: item.caiGouDW,
                kuCunSL: item.kuCunSL || 0,
                baoZhuangDW: item.baoZhuangDW,
                danJia: item.danJia,
                yaoPinLXDM: item.yaoPinLXDM,
                yaoPinLXMC: item.yaoPinLXMC,
              };
              item.yaoPinMCYGG = item.yaoPinXX;
            });
            this.tableData = res.mingXiYPList;
            // this.formData.tiaoJiaWH = res.tiaoJiaWH;
            this.formData.beiZhu = res.beiZhu;
            this.formData.jiHuaTJSJ = res.jiHuaTJSJ;
            this.formData.tiaoJiaFSDM = res.tiaoJiaFSDM;
            this.zhiDanRXM = res.zhiDanRXM;
            this.zhiDanSJ = res.zhiDanSJ;
            this.yaoPinZS = res.mingXiYPList.length;
            this.jinJiaJE = res.mingXiYPList.reduce(
              (total, item) => total + item.xianJinJJE,
              0,
            );
            this.lingShouJE = res.mingXiYPList.reduce(
              (total, item) => total + item.xianLingSJE,
              0,
            );
            this.jinJiaCE = res.mingXiYPList.reduce(
              (total, element) =>
                (element.xianJinJia - element.yuanJinJia) * element.tiaoJiaSL +
                total,
              0,
            );
            this.lingShouCE = res.mingXiYPList.reduce(
              (total, element) =>
                (element.xianLingSJ - element.yuanLingSJ) * element.tiaoJiaSL +
                total,
              0,
            );
          });
        } else {
          this.zhiDanRXM = getYongHuXM();
          this.zhiDanSJ = dayjs().format('YYYY-MM-DD');
        }
        this.addRow();
      } finally {
        this.loading = false;
      }
    },
    //列表选中
    handleSelect(selection) {
      if (selection.length > 0) {
        this.getFenBuSL(selection[selection.length - 1]);
      } else {
        this.fenBuData = {};
      }
    },
    //按库存下限生成
    hanldeAnKunCSC() {
      this.$refs.anKuCunXXSCDialog.showModel().then((res) => {
        this.loading = true;
        const params = {
          ...res,
          yaoPinLX: getKuCunGLLX(), //草药
        };
        AnKuCunXXSC(params)
          .then((res) => {
            this.tableData = [];
            //this.resetData = cloneDeep(res)
            let index = 0;
            if (res && res.length > 0) {
              res.forEach((item) => {
                item.shunXuHao = index++;
                item.tiaoJiaSL = item.kuCunSL;
                item.yuanJinJia = item.jinJia;
                item.yuanLingSJ = item.danJia;
                item.xianJinJia = item.jinJia;
                item.xianLingSJ = item.danJia;
                item.yuanPiFJ = 0;
                item.yuanPiFJE = 0;
                item.shiJiJJ = item.jinJia;
                item.xianPiFJ = 0;
                item.xianPiFJE = 0;
                item.yuanJiaJL = 0;
                item.xianJiaJL = 0;
                item.yaoPinXX = {
                  guiGeID: item.guiGeID,
                  yaoPinMC: item.yaoPinMC,
                  yaoPinGG: item.yaoPinGG,
                  jiaGeID: item.jiaGeID,
                  chanDiMC: item.chanDiMC,
                  // caiGouDW: item.caiGouDW,
                  kuCunSL: item.kuCunSL,
                  baoZhuangDW: item.baoZhuangDW,
                  danJia: item.danJia,
                  yaoPinLXDM: item.yaoPinLXDM,
                  yaoPinLXMC: item.yaoPinLXMC,
                };
                item.yaoPinMCYGG = item.yaoPinXX;
              });
              this.tableData = res;
              // this.formData.tiaoJiaWH = res.tiaoJiaWH;
              this.formData.beiZhu = res.beiZhu;
              this.formData.jiHuaTJSJ = res.jiHuaTJSJ;
              this.formData.tiaoJiaFSDM = res.tiaoJiaFSDM;
              this.yaoPinZS = res.length;
              this.jinJiaJE = res.reduce(
                (total, item) => total + item.xianJinJJE,
                0,
              );
              this.lingShouJE = res.reduce(
                (total, item) => total + item.xianLingSJE,
                0,
              );
              this.jinJiaCE = res.reduce(
                (total, element) =>
                  (element.xianJinJia - element.yuanJinJia) *
                    element.tiaoJiaSL +
                  total,
                0,
              );
              this.lingShouCE = res.reduce(
                (total, element) =>
                  (element.xianLingSJ - element.yuanLingSJ) *
                    element.tiaoJiaSL +
                  total,
                0,
              );
            }
          })
          .catch((e) => {
            logger.error(e);
          })
          .finally(() => {
            this.loading = false;
            this.addRow();
          });
      });
    },
    handleInputChange(val, index, mingCheng) {
      let jiaGeID = this.tableData[index].jiaGeID;
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].jiaGeID == jiaGeID) {
          if (!mingCheng) {
            this.tableData[i].xianJinJia = val;
          } else {
            this.tableData[i].xianLingSJ = val;
          }
        }
      }
    },
    handleLSJInputChange(index) {
      let jiaGeID = this.tableData[index].jiaGeID;
      for (let i = 0; i < this.tableData.length; i++) {
        const item = this.tableData[i];
        if (item.jiaGeID == jiaGeID) {
          item.xianLingSJ = this.tableData[index].xianLingSJ;
        }
      }
    },
    // 药品选择
    async handleTableYaoPinDWChange(data, row, index) {
      if (data) {
        this.fenBuData = {};
        let yaoPinKC = await GetTiaoJiaYPPCKCList({ jiaGeID: data.jiaGeID });
        if (row.jiaGeID == '') {
          if (yaoPinKC.length > 0) {
            data = yaoPinKC[0];
            row.yaoPinMC = data.yaoPinMC;
            row.yaoPinGG = data.yaoPinGG;
            row.yaoPinXX = {
              guiGeID: data.guiGeID,
              yaoPinMC: data.yaoPinMC,
              yaoPinGG: data.yaoPinGG,
              jiaGeID: data.jiaGeID,
              chanDiMC: data.chanDiMC,
              kuCunSL: data.kuCunSL,
              baoZhuangDW: data.baoZhuangDW,
              danJia: data.danJia,
              yaoPinLXDM: data.yaoPinLXDM,
              yaoPinLXMC: data.yaoPinLXMC,
            };
            row.yaoPinMCYGG = row.yaoPinXX;
            row.yaoPinZC = data.yaoPinMC + ' ' + data.yaoPinGG;
            row.jiaGeID = data.jiaGeID;
            row.guiGeID = data.guiGeID;
            row.danJia = data.danJia;
            row.chanDiID = data.chanDiID;
            row.chanDiMC = data.chanDiMC;
            // row.caiGouDW = data.caiGouDW;
            row.tiaoJiaSL = data.kuCunSL;
            row.baoZhuangDW = data.baoZhuangDW;
            row.yuanJinJia = data.jinJia;
            row.yuanLingSJ = data.danJia;
            row.xianJinJia = '';
            row.xianLingSJ = '';
            row.zhangBuLBID = data.zhangBuLBID;
            row.zhangBuLBMC = data.zhangBuLBMC;
            row.yaoPinLXDM = data.yaoPinLXDM;
            row.yaoPinLXMC = data.yaoPinLXMC;
            row.gongHuoDWID = data.gongHuoDWID;
            row.gongHuoDWMC = data.gongHuoDWMC;
            row.piCiKCID = data.piCiKCID;
            row.shengChanPH = data.shengChanPH;
            row.yaoPinXQ = data.yaoPinXQ;
            row.yuanJinJJE = row.tiaoJiaSL * row.yuanJinJia;
            row.yuanLingSJE = row.tiaoJiaSL * row.yuanLingSJ;
            row.yuanPiFJ = 0;
            row.yuanPiFJE = 0;
            row.shiJiJJ = data.jinJia;
            row.xianPiFJ = 0;
            row.xianPiFJE = 0;
            row.yuanJiaJL = 0;
            row.xianJiaJL = 0;
            // 新行
            this.getFenBuSL(row);
            if (yaoPinKC.length > 1) {
              if (this.tableData[this.tableData.length - 1].jiaGeID == '') {
                this.tableData = this.tableData.slice(
                  0,
                  this.tableData.length - 1,
                );
              }
              yaoPinKC = yaoPinKC.slice(1, yaoPinKC.length);
              yaoPinKC.forEach((item) => {
                item.yaoPinXX = {
                  guiGeID: item.guiGeID,
                  yaoPinMC: item.yaoPinMC,
                  yaoPinGG: item.yaoPinGG,
                  jiaGeID: item.jiaGeID,
                  chanDiMC: item.chanDiMC,
                  kuCunSL: item.kuCunSL,
                  baoZhuangDW: item.baoZhuangDW,
                  danJia: item.danJia,
                  yaoPinLXDM: item.yaoPinLXDM,
                  yaoPinLXMC: item.yaoPinLXMC,
                };
                item.yaoPinMCYGG = item.yaoPinXX;
                item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
                // item.caiGouDW = item.caiGouDW;
                item.tiaoJiaSL = item.kuCunSL;
                item.yuanJinJia = item.jinJia;
                item.yuanLingSJ = item.danJia;
                item.xianJinJia = '';
                item.xianLingSJ = '';
                item.yuanJinJJE = item.tiaoJiaSL * item.yuanJinJia;
                item.yuanLingSJE = item.tiaoJiaSL * item.yuanLingSJ;
                item.yuanPiFJ = 0;
                item.yuanPiFJE = 0;
                item.shiJiJJ = item.jinJia;
                item.xianPiFJ = 0;
                item.xianPiFJE = 0;
                item.yuanJiaJL = 0;
                item.xianJiaJL = 0;
                item.shunXuHao = this.tableData.length + 1;
                this.tableData.push(item);
              });
            }
            if (this.tableData[this.tableData.length - 1].jiaGeID != '') {
              this.addRow();
            }
            // 跳转下一个编辑框
            this.$refs.editable._next();
          } else {
            this.$message({
              type: 'warning',
              message: '没有库存信息',
            });
          }
        } else {
          if (data.jiaGeID != row.jiaGeID) {
            MdMessageBox.confirm(
              '药品:' + row.yaoPinMC + '相关的记录将会全部删除，是否进行操作',
              '操作提醒',
              {
                cancelButtonText: '取消',
                confirmButtonText: '确定',
                type: 'warning',
              },
            )
              .then(() => {
                let yuanJiageId = row.jiaGeID;
                if (yaoPinKC.length > 0) {
                  data = yaoPinKC[0];
                  row.yaoPinMC = data.yaoPinMC;
                  row.yaoPinGG = data.yaoPinGG;
                  row.yaoPinXX = {
                    guiGeID: data.guiGeID,
                    yaoPinMC: data.yaoPinMC,
                    yaoPinGG: data.yaoPinGG,
                    jiaGeID: data.jiaGeID,
                    chanDiMC: data.chanDiMC,
                    kuCunSL: data.kuCunSL,
                    baoZhuangDW: data.baoZhuangDW,
                    danJia: data.danJia,
                    yaoPinLXDM: data.yaoPinLXDM,
                    yaoPinLXMC: data.yaoPinLXMC,
                  };
                  row.yaoPinZC = data.yaoPinMC + ' ' + data.yaoPinGG;
                  row.jiaGeID = data.jiaGeID;
                  row.guiGeID = data.guiGeID;
                  row.danJia = data.danJia;
                  row.chanDiID = data.chanDiID;
                  row.chanDiMC = data.chanDiMC;
                  // row.caiGouDW = data.caiGouDW;
                  row.tiaoJiaSL = data.kuCunSL;
                  row.baoZhuangDW = data.baoZhuangDW;
                  row.yuanJinJia = data.jinJia;
                  row.yuanLingSJ = data.danJia;
                  row.xianJinJia = data.jinJia;
                  row.xianLingSJ = data.danJia;
                  row.zhangBuLBID = data.zhangBuLBID;
                  row.zhangBuLBMC = data.zhangBuLBMC;
                  row.yaoPinLXDM = data.yaoPinLXDM;
                  row.yaoPinLXMC = data.yaoPinLXMC;
                  row.gongHuoDWID = data.gongHuoDWID;
                  row.gongHuoDWMC = data.gongHuoDWMC;
                  row.piCiKCID = data.piCiKCID;
                  row.shengChanPH = data.shengChanPH;
                  row.yaoPinXQ = data.yaoPinXQ;
                  row.yuanJinJJE = row.tiaoJiaSL * row.yuanJinJia;
                  row.yuanLingSJE = row.tiaoJiaSL * row.yuanLingSJ;
                  row.yuanPiFJ = 0;
                  row.yuanPiFJE = 0;
                  row.shiJiJJ = data.jinJia;
                  row.xianPiFJ = 0;
                  row.xianPiFJE = 0;
                  row.yuanJiaJL = 0;
                  row.xianJiaJL = 0;
                  // 新行
                  this.getFenBuSL(row);
                  this.tableData = this.tableData.filter(
                    (item) => item.jiaGeID != yuanJiageId,
                  );
                  if (yaoPinKC.length > 1) {
                    if (
                      this.tableData[this.tableData.length - 1].jiaGeID == ''
                    ) {
                      this.tableData = this.tableData.slice(
                        0,
                        this.tableData.length - 1,
                      );
                    }
                    yaoPinKC = yaoPinKC.slice(1, yaoPinKC.length);
                    yaoPinKC.forEach((item) => {
                      item.yaoPinXX = {
                        guiGeID: item.guiGeID,
                        yaoPinMC: item.yaoPinMC,
                        yaoPinGG: item.yaoPinGG,
                        jiaGeID: item.jiaGeID,
                        chanDiMC: item.chanDiMC,
                        kuCunSL: item.kuCunSL,
                        baoZhuangDW: item.baoZhuangDW,
                        danJia: item.danJia,
                        yaoPinLXDM: item.yaoPinLXDM,
                        yaoPinLXMC: item.yaoPinLXMC,
                      };
                      item.yaoPinMCYGG = item.yaoPinXX;
                      item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
                      // item.caiGouDW = item.caiGouDW;
                      item.tiaoJiaSL = item.kuCunSL;
                      item.yuanJinJia = item.jinJia;
                      item.yuanLingSJ = item.danJia;
                      item.xianJinJia = '';
                      item.xianLingSJ = '';
                      item.yuanJinJJE = item.tiaoJiaSL * item.yuanJinJia;
                      item.yuanLingSJE = item.tiaoJiaSL * item.yuanLingSJ;
                      item.yuanPiFJ = 0;
                      item.yuanPiFJE = 0;
                      item.shiJiJJ = item.jinJia;
                      item.xianPiFJ = 0;
                      item.xianPiFJE = 0;
                      item.yuanJiaJL = 0;
                      item.xianJiaJL = 0;
                      item.shunXuHao = this.tableData.length + 1;
                      this.tableData.push(item);
                    });
                  }
                  if (this.tableData[this.tableData.length - 1].jiaGeID != '') {
                    this.addRow();
                  }
                  // 跳转下一个编辑框
                  this.$refs.editable._next();
                } else {
                  this.$message({
                    type: 'warning',
                    message: '没有库存信息',
                  });
                }
              })
              .catch((error) => {});
          }
        }
      } else {
        this.tableData[index] = initRow();
        this.getFenBuSL(data);
      }
    },
    handleVisibleChange(val, row) {
      if (val && row.jiaGeID) {
        this.getFenBuSL(row);
      }
    },
    //药品选中 获取药品库存分布
    async getFenBuSL(row) {
      if (row.jiaGeID) {
        try {
          this.fenBuLoading = true;
          this.fenBuData = {};
          this.fenBuData = await GetTiaoJiaYPKCFB({ jiaGeID: row.jiaGeID });
        } catch (error) {
          logger.error(error);
        } finally {
          this.fenBuLoading = false;
        }
      } else {
        this.fenBuData = {};
      }
    },
    // 药品快速定位
    handleChangeYaoPinDWSR(data, el) {
      if (!data) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
        return;
      }
      // 获取doom
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(
          `#tiaoJiaTable .${this.cssPrefix}-base-table__body-wrapper`,
        );
      }
      // 去掉上一次定位样式
      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
      }
      //寻找index
      this.dingWeiYPIndexArr = this.tableData.reduce((pre, item, index) => {
        if (item.jiaGeID === data.jiaGeID) {
          pre.push(index);
        }
        return pre;
      }, []);

      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item, index) => {
          this.setDingWeiClass(this.tableBodyEle, item, index === 0);
        });
      } else {
        MdMessage({
          type: 'warning',
          message: '未找到该药品！',
        });
      }
    },
    clearDingWeiClass(tableBodyEle, index) {
      if (tableBodyEle && index >= 0) {
        let dingWeiEle = tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__row`,
        )[index];
        dingWeiEle.className = dingWeiEle.className.replace(
          this.prefixClass('dingwei-bg'),
          '',
        );
      }
    },
    //设置定位样式
    setDingWeiClass(tableBodyEle, index, dingWeiBZ = false) {
      let dingWeiEle = tableBodyEle.querySelectorAll(
        `.${this.cssPrefix}-base-table__row`,
      )[index];
      dingWeiEle.className =
        dingWeiEle.className !== ''
          ? dingWeiEle.className + ' ' + this.prefixClass('dingwei-bg')
          : this.prefixClass('dingwei-bg');
      if (dingWeiBZ) this.setDingWeiScroll(tableBodyEle, dingWeiEle, index);
    },
    // 定位滚动
    setDingWeiScroll(tableBodyEle, dingWeiEle, index) {
      tableBodyEle.scrollTop = index * dingWeiEle.clientHeight;
      this.dangQingDWYPIndex = index;
    },
    // 定位切换
    handleEnter() {
      let length = this.dingWeiYPIndexArr.length;
      if (this.query.likeQuery && length > 0) {
        let index = this.dingWeiYPIndexArr.findIndex(
          (item) => item === this.dangQingDWYPIndex,
        );
        if (index === length - 1) {
          index = 0;
        } else index++;
        let dingWeiEle = this.tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__row`,
        )[index];
        this.setDingWeiScroll(
          this.tableBodyEle,
          dingWeiEle,
          this.dingWeiYPIndexArr[index],
        );
      }
    },
    // 计划调价时间判断
    handleChangeJiHuaTJSJ(val) {
      if (dayjs(val).diff(dayjs()) < 0) {
        this.$message({
          type: 'warning',
          message: '计划调价时间不能小于当前时间！',
        });
        this.formData.jiHuaTJSJ = null;
      }
    },
    // table回车事件
    handleTableEnter({ length, activeIndex, callback }) {
      if ((activeIndex + 1) % length == 0 && !this.editTableDisabled) {
        this.addRow();
        callback({});
        return;
      }
      callback();
    },
    // 新增行
    addRow() {
      this.tableData.push({
        // tiaoJiaWH: '',
        tiaoJiaDID: '',
        jiaGeID: '',
        yaoPinMC: '',
        yaoPinGG: '',
        baoZhuangDW: '',
        zhangBuLBID: '',
        zhangBuLBMC: '',
        chanDiID: '',
        chanDiMC: '',
        // caiGouDW: '',
        tiaoJiaSL: 0,
        yuanJinJia: 0,
        yuanJinJJE: 0,
        yuanPiFJ: 0,
        yuanLingSJ: 0,
        yuanPiFJE: 0,
        yuanLingSJE: 0,
        shiJiJJ: 0,
        xianJinJia: '',
        xianJinJJE: 0,
        xianPiFJ: 0,
        xianLingSJ: '',
        xianPiFJE: 0,
        xianLingSJE: 0,
        yuanJiaJL: 0,
        xianJiaJL: 0,
        shunXuHao: this.tableData.length + 1,
        id: null,
      });
    },
    // 保存并记账 保存的方法使用一个
    handleSaveAndJiZhang() {
      if (this.tableData.length <= 1) {
        this.$message({
          message: '请输入数据！',
          type: 'warning',
        });
        return;
      }

      let nowData = cloneDeep(this.tableData);
      nowData.length = nowData.length - 1; // 保存时去掉最后的空行
      let xianJinJiaFlage = nowData.some((item) => {
        if (item.xianJinJia == '') {
          return true;
        }
      });
      if (xianJinJiaFlage) {
        this.$message({
          message: '现进价不能为空！',
          type: 'warning',
          duration: 2000,
        });
        return;
      }

      let xianLingSJFlage = nowData.some((item) => {
        if (item.xianLingSJ == '') {
          return true;
        }
      });
      if (xianLingSJFlage) {
        this.$message({
          message: '现零售价不能为空！',
          type: 'warning',
          duration: 2000,
        });
        return;
      }
      this.loading = true;
      nowData.forEach((x) => {
        delete x.yaoPinXX;
        // x.tiaoJiaWH = this.formData.tiaoJiaWH;
        x.xianLingSJ = Number(x.xianLingSJ);
        x.xianJinJia = Number(x.xianJinJia);
      });

      const jiHuaTJSJ = this.formData.jiHuaTJSJ
        ? dayjs(this.formData.jiHuaTJSJ).format('YYYY-MM-DD HH:mm:ss')
        : null;
      // 修改
      if (this.id) {
        const param = {
          tiaoJiaDH: this.resetData.tiaoJiaDH,
          // tiaoJiaWH: this.formData.tiaoJiaWH,
          danJuZTDM: this.resetData.danJuZTDM,
          danJuZTMC: this.resetData.danJuZTMC,
          ziDongTJBZ: jiHuaTJSJ ? 1 : 0,
          jiHuaTJSJ: jiHuaTJSJ,
          tiaoJiaFSDM: this.formData.tiaoJiaFSDM,
          tiaoJiaFSMC: this.tiaoJiaFSOptions.find(
            (x) => x.biaoZhunDM == this.formData.tiaoJiaFSDM,
          )?.biaoZhunMC,
          zhiDanSJ: this.resetData.zhiDanSJ,
          zhiDanRID: this.resetData.zhiDanRID,
          zhiDanRXM: this.resetData.zhiDanRXM,
          jiZhangSJ: this.resetData.jiZhangSJ,
          jiZhangRID: this.resetData.jiZhangRID,
          jiZhangRXM: this.resetData.jiZhangRXM,
          beiZhu: this.formData.beiZhu,
          addMingXiYPList: [],
          updateMingXiYPList: [],
          zuoFeiMingXiYPList: [],
          id: this.id,
        };
        nowData.forEach((x) => delete x.yaoPinXX);
        //差集
        param.addMingXiYPList = [...nowData].filter(
          (x) => x.id === null || x.id === '' || x.id == undefined,
        );
        // 原调价单药品ID
        let resetDataId = this.resetData.mingXiYPList.map((i) => i.id);
        // 现调价单药品ID
        let nowDataId = new Set(nowData.map((i) => i.id));

        param.zuoFeiMingXiYPList = [...resetDataId].filter(
          (x) => !nowDataId.has(x),
        );

        //交集、判断是否修改
        let editData = [...nowData].filter((x) =>
          [...this.resetData.mingXiYPList].some((y) => y.id === x.id),
        );
        if (editData) {
          editData.forEach((item) => {
            if (
              !isEqual(
                item,
                this.resetData.mingXiYPList.find((o) => o.id === item.id),
              )
            ) {
              param.updateMingXiYPList.push(item);
            }
          });
        }
        let caoZuoZT = 0; // 操作状态
        UpdateTiaoJiaDan(param)
          .then((res) => {
            caoZuoZT = 1;
            return JiZhangTJD({ tiaoJiaDID: this.id });
          })
          .then((res) => {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.loading = false;
            this.handleCloseCurrentTab();
          })
          .catch((e) => {
            if (caoZuoZT == 1) {
              // 保存成功，记账失败的情况
              this.loading = false;
              this.handleCloseCurrentTab();
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: '保存成功，记账失败' + e,
                confirmButtonText: '我知道了',
              });
            } else {
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: '操作失败' + e,
                confirmButtonText: '我知道了',
              });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 新增
        const param = {
          id: null,
          tiaoJiaDH: '',
          // tiaoJiaWH: this.formData.tiaoJiaWH,
          danJuZTDM: '',
          danJuZTMC: '',
          ziDongTJBZ: jiHuaTJSJ ? 1 : 0,
          jiHuaTJSJ: jiHuaTJSJ,
          tiaoJiaFSDM: this.formData.tiaoJiaFSDM,
          tiaoJiaFSMC: this.tiaoJiaFSOptions.find(
            (x) => x.biaoZhunDM == this.formData.tiaoJiaFSDM,
          )?.biaoZhunMC,
          zhiDanSJ: null,
          zhiDanRID: '',
          zhiDanRXM: '',
          jiZhangSJ: null,
          jiZhangRID: '',
          jiZhangRXM: '',
          beiZhu: this.formData.beiZhu,
          mingXiYPList: nowData,
        };
        let caoZuoZT = 0; // 操作状态
        AddTiaoJiaDan(param)
          .then((res) => {
            caoZuoZT = 1;
            return JiZhangTJD({ tiaoJiaDID: res });
          })
          .then((res) => {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.loading = false;
            this.handleCloseCurrentTab();
          })
          .catch((e) => {
            if (caoZuoZT == 1) {
              // 保存成功，记账失败的情况
              this.loading = false;
              this.handleCloseCurrentTab();
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: '保存成功，记账失败' + e,
                confirmButtonText: '我知道了',
              });
            } else {
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: '操作失败' + e,
                confirmButtonText: '我知道了',
              });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    // 保存
    async handleSave() {
      if (this.tableData.length <= 1) {
        this.$message({
          message: '请输入数据！',
          type: 'warning',
        });
        return;
      }

      let nowData = cloneDeep(this.tableData);
      nowData.length = nowData.length - 1; // 保存时去掉最后的空行
      let xianJinJiaFlage = nowData.some((item) => {
        if (item.xianJinJia == '') {
          return true;
        }
      });
      if (xianJinJiaFlage) {
        this.$message({
          message: '现进价不能为空！',
          type: 'warning',
          duration: 2000,
        });
        return;
      }

      let xianLingSJFlage = nowData.some((item) => {
        if (item.xianLingSJ == '') {
          return true;
        }
      });
      if (xianLingSJFlage) {
        this.$message({
          message: '现零售价不能为空！',
          type: 'warning',
          duration: 2000,
        });
        return;
      }
      this.loading = true;
      nowData.forEach((x) => {
        delete x.yaoPinXX;
        // x.tiaoJiaWH = this.formData.tiaoJiaWH;
        x.xianLingSJ = Number(x.xianLingSJ);
        x.xianJinJia = Number(x.xianJinJia);
      });
      const jiHuaTJSJ = this.formData.jiHuaTJSJ
        ? dayjs(this.formData.jiHuaTJSJ).format('YYYY-MM-DD HH:mm:ss')
        : null;
      // 修改
      if (this.id) {
        const param = {
          tiaoJiaDH: this.resetData.tiaoJiaDH,
          // tiaoJiaWH: this.formData.tiaoJiaWH,
          danJuZTDM: this.resetData.danJuZTDM,
          danJuZTMC: this.resetData.danJuZTMC,
          ziDongTJBZ: jiHuaTJSJ ? 1 : 0,
          jiHuaTJSJ: jiHuaTJSJ,
          tiaoJiaFSDM: this.formData.tiaoJiaFSDM,
          tiaoJiaFSMC: this.tiaoJiaFSOptions.find(
            (x) => x.biaoZhunDM == this.formData.tiaoJiaFSDM,
          )?.biaoZhunMC,
          zhiDanSJ: this.resetData.zhiDanSJ,
          zhiDanRID: this.resetData.zhiDanRID,
          zhiDanRXM: this.resetData.zhiDanRXM,
          jiZhangSJ: this.resetData.jiZhangSJ,
          jiZhangRID: this.resetData.jiZhangRID,
          jiZhangRXM: this.resetData.jiZhangRXM,
          beiZhu: this.formData.beiZhu,
          addMingXiYPList: [],
          updateMingXiYPList: [],
          zuoFeiMingXiYPList: [],
          id: this.id,
        };
        //差集
        param.addMingXiYPList = [...nowData].filter(
          (x) => x.id === null || x.id === '' || x.id == undefined,
        );
        let resetDataId = this.resetData.mingXiYPList.map((i) => i.id);
        let nowDataId = new Set(nowData.map((i) => i.id));

        param.zuoFeiMingXiYPList = [...resetDataId].filter(
          (x) => !nowDataId.has(x),
        );
        //交集、判断是否修改
        let editData = [...nowData].filter((x) =>
          [...this.resetData.mingXiYPList].some((y) => y.id === x.id),
        );
        if (editData) {
          editData.forEach((item) => {
            if (
              !isEqual(
                item,
                this.resetData.mingXiYPList.find((o) => o.id === item.id),
              )
            ) {
              param.updateMingXiYPList.push(item);
            }
          });
        }
        UpdateTiaoJiaDan(param)
          .then(async (res) => {
            this.$message({
              message: '保存成功',
              type: 'success',
            });
            this.handleCloseCurrentTab();
          })
          .catch((e) => {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: '保存失败' + e,
              confirmButtonText: '我知道了',
            });
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 新增
        const param = {
          id: null,
          tiaoJiaDH: '',
          // tiaoJiaWH: this.formData.tiaoJiaWH,
          danJuZTDM: '',
          danJuZTMC: '',
          ziDongTJBZ: jiHuaTJSJ ? 1 : 0,
          jiHuaTJSJ: jiHuaTJSJ,
          tiaoJiaFSDM: this.formData.tiaoJiaFSDM,
          tiaoJiaFSMC: this.tiaoJiaFSOptions.find(
            (x) => x.biaoZhunDM == this.formData.tiaoJiaFSDM,
          )?.biaoZhunMC,
          zhiDanSJ: null,
          zhiDanRID: '',
          zhiDanRXM: '',
          jiZhangSJ: null,
          jiZhangRID: '',
          jiZhangRXM: '',
          beiZhu: this.formData.beiZhu,
          mingXiYPList: nowData,
        };
        try {
          let tiaoJiaDanID = await AddTiaoJiaDan(param);
          this.$message({
            message: '保存成功',
            type: 'success',
          });
          this.handleCloseCurrentTab();
        } catch (e) {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: '保存失败' + e,
            confirmButtonText: '我知道了',
          });
        } finally {
          this.loading = false;
        }
      }
    },
    // 删除
    handleDel() {
      const rows = this.$refs.editable.invokeTableMethod('getAllCheckedRows');
      if (rows.length <= 0) {
        this.$message({
          message: '请选择需要删除的行',
          type: 'warning',
        });
        return;
      }
      MdMessageBox.confirm('是否确定删除', '操作提醒', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }).then(() => {
        rows.forEach((item) => {
          let rowIndex = findIndex(this.tableData, item);
          this.$refs.editable.removeRow(rowIndex);
        });
        let jiaGeIDs = rows.map((x) => x.jiaGeID);
        let tempTable = this.tableData.filter((x) =>
          jiaGeIDs.some((s) => s == x.jiaGeID),
        );
        tempTable.forEach((item) => {
          let rowIndex = findIndex(this.tableData, item);
          this.$refs.editable.removeRow(rowIndex);
        });
      });
    },
    // 关闭标签页，未记账页面刷新table
    handleCloseCurrentTab() {
      let closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      this.viewManager.close(closeTabKey);
      eventBus.$emit('handleTiaoJiaFetch');
    },
    async handlePrint(id) {
      try {
        this.loading = true;
        await printByUrl('YKXT006', { tiaoJiaDanID: id });
        MdMessage.success('打印成功！');
      } catch (e) {
        // Message.error(e.message || '打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message || '打印失败！',
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
      }
    },
    /**
     * 显示隐藏药品库存分布
     * **/
    handleYaoPinKCFBVisible() {
      this.yaoPinKCFBVisible = !this.yaoPinKCFBVisible;
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
    'ankucxx-dialog': AnKuCunXXSCDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-description {
  display: flex;
  justify-content: space-between;

  &-item {
    line-height: 20px;
    min-height: 20px;
    font-size: 14px;
    color: #333;
    padding: 5px 0;
    margin-right: 4px;

    &__label {
      color: #aaa;
      margin-left: 8px;
    }

    &__content {
      padding-left: 5px;

      &.#{$md-prefix}-fontWeight {
        font-weight: bold;
      }

      .#{$md-prefix}-content-color {
        color: #aaa;
        font-weight: normal;
      }
    }
  }
}
</style>
<style lang="scss" scope>
.#{$md-prefix}-procurement-left-rt {
  display: flex;
  justify-content: flex-start;
  margin-left: 8px;
  overflow: hidden;

  span {
    float: left;
    height: 30px;
    line-height: 30px;
    margin-right: 8px;
    padding: 0 8px;
    background-color: #ffffff;
    border: 1px solid #c3e5fd;
    // @include md-def('border-color', 'color-6');
    border-color: rgb(var(--md-color-6));
    border-radius: 4px;
    // color: #1e88e5;
    color: rgb(var(--md-color-6));
    // @include md-def('color', 'color-6');
    font-size: 14px;
    cursor: pointer;

    &:hover {
      // @include md-def('background-color', 'color-1');
      background-color: rgb(var(--md-color-1));
    }
  }
}

.#{$md-prefix}-tiaojiadan-page-wrap {
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  background-color: #e4e6e9;
  display: flex;
  overflow: hidden;

  .#{$md-prefix}-tiaojiadan-wrap {
    display: flex;
    flex: 1;
    min-width: 0px;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;

    .#{$md-prefix}-tiaojiadan-wrap-title {
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      height: 46px;
      // background-color: #edf6fd;
      background-color: rgb(var(--md-color-1));
      // @include md-def('background-color', 'color-1');
      margin-bottom: 8px;

      .#{$md-prefix}-title__left {
        display: flex;
        // padding: 4px;
        align-items: center;
        margin: 4px 8px;
        border-radius: 4px;

        .#{$md-prefix}-danjumc {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 8px;
          line-height: 30px;
          font-size: 16px;
          font-weight: bold;
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));

          i {
            margin-right: 4px;
            font-size: 18px;
            width: 20px;
          }
        }

        .#{$md-prefix}-danjuhao-input {
          // width: 180px;
        }

        .#{$md-prefix}-yaopindw {
          border-radius: 4px;
        }
      }

      .#{$md-prefix}-title__right {
        padding: 8px 0;

        .#{$md-prefix}-right-12 {
          margin-right: 12px;
        }

        .#{$md-prefix}-right-8 {
          margin-right: 8px;
        }
      }
    }

    .#{$md-prefix}-search-bar {
      width: 100%;
      padding: 0 8px;
    }

    .#{$md-prefix}-container__alias {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 0;
      padding: 0 8px;

      .#{$md-prefix}-editable {
        flex: 1;
        min-height: 0;
        ::v-deep .#{$md-prefix}-editable-table__container {
          height: 100% !important;
        }

        .#{$md-prefix}-dingwei-bg {
          td {
            background: #e2efff;
          }
        }
      }

      .#{$md-prefix}-table-addbtn {
        display: block;
        margin: 8px auto;
      }
    }
  }

  .#{$md-prefix}-tiaojiadan-right {
    width: 0px;
    flex-shrink: 0;
    background-color: #ffffff;
    margin-left: 4px;
    position: relative;
    transition: 0.25s;

    &.on {
      width: 520px;
    }

    .#{$md-prefix}-btn-visible {
      z-index: 1;
      position: absolute;
      left: -20px;
      top: 50%;
      width: 38px;
      height: 73px;
      line-height: 73px;
      background-image: url('~@/assets/images/shousuobg.png');
      background-color: transparent;
      transform: translateY(-36px) rotate(0deg);
      transition: 0.25s;
      &.on {
        left: -10px;
        transform: translateY(-36px) rotate(180deg);
      }
      i {
        vertical-align: middle;
        margin-left: 10px;
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #999999;
      }
    }

    .#{$md-prefix}-tiaojiadan-title {
      white-space: nowrap;
      color: #222222;
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;
      padding-left: 21px;
      margin-top: 8px;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        // @include md-def('background-color', 'color-6');
        background-color: rgb(var(--md-color-6));
        position: absolute;
        margin-left: -10px;
      }
    }

    .#{$md-prefix}-tiaojiadan-data {
      margin: 9px;

      .bggray {
        background-color: #f5f5f5;
        border-radius: 4px;
        padding: 4px;

        .name {
          font-weight: 600;
          color: #222222;
          font-size: 16px;
          margin: 10px;
          margin-bottom: 4px;
        }
      }

      .table {
        .footer {
          color: #1e88e5;
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));
          font-size: 14px;
          display: flex;
          padding: 6px 8px;
          justify-content: space-between;
        }
      }
    }
    .#{$md-prefix}-nodata {
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
::v-deep .#{$md-prefix}-select {
  width: 210px !important;
}

::v-deep .#{$md-prefix}-base-table__body td.is-selection > .cell {
  width: 100% !important;
  padding-left: 10px !important;
  padding-right: 0 !important;
}

::v-deep .#{$md-prefix}-base-table-column--selection .cell {
  padding-left: 10px !important;
  padding-right: 0 !important;
}
</style>
