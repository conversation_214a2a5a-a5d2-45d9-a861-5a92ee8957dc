import commonData from '@/system/utils/commonData';
import {
  yaoKuZDJZTimeShow,
  yaoKuZDJZTimeShow1,
} from '@/system/utils/formatDate';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { MdInput } from '@mdfe/medi-ui';

export default {
  weiJiZhangColumns: [
    {
      type: 'selection',
      width: 35,
    },
    {
      slot: 'tiaoJiaDH',
      label: '调价单',
      prop: 'tiaoJiaDH',
      width: 118,
    },
    {
      label: '调价方式',
      prop: 'tiaoJiaFSMC',
    },
    // {
    //   label: '调价文号',
    //   prop: 'tiaoJiaWH',
    // },
    {
      label: '计划调价时间',
      prop: 'jiHuaTJSJ',
      width: 166,
    },
    {
      label: '药品数',
      slot: 'yaoPinShu',
      align: 'right',
      width: 66,
    },
    {
      slot: 'yaoPinMX',
      label: '药品明细',
      prop: 'yaoPinMX',
      width: 472,
    },
    {
      label: '备注',
      prop: 'beiZhu',
      width: 108,
    },
    {
      label: '制单日期',
      prop: 'zhiDanSJ',
      width: 108,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '制单人',
      prop: 'zhiDanRXM',
      width: 100,
    },
    {
      slot: 'operate',
      label: '操作',
      prop: 'caoZuo',
      width: 118,
    },
  ],
  yiJiZhangColumns: [
    {
      type: 'selection',
      width: 35,
    },
    {
      slot: 'tiaoJiaDH',
      label: '调价单',
      prop: 'tiaoJiaDH',
      width: 118,
    },
    {
      label: '调价方式',
      prop: 'tiaoJiaFSMC',
      width: 90,
    },
    // {
    //   label: '调价文号',
    //   prop: 'tiaoJiaWH',
    //   width: 90,
    // },
    {
      label: '计划调价时间',
      prop: 'jiHuaTJSJ',
      width: 156,
    },
    {
      label: '药品数',
      slot: 'yaoPinZS',
      align: 'right',
      width: 66,
    },
    {
      slot: 'yaoPinMX',
      label: '药品明细',
      prop: 'yaoPinMX',
      minWidth: 320,
    },
    {
      label: '制单人',
      prop: 'zhiDanRXM',
      width: 90,
    },
    {
      label: '制单日期',
      prop: 'zhiDanSJ',
      width: 130,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow(cellValue);
      },
    },
    {
      label: '备注',
      prop: 'beiZhu',
      width: 108,
    },
    {
      label: '记账人',
      prop: 'jiZhangRXM',
      width: 90,
    },
    {
      label: '记账日期',
      prop: 'jiZhangSJ',
      width: 130,
      formatter(row, column, cellValue) {
        return yaoKuZDJZTimeShow1(cellValue);
      },
    },
  ],
  weiJiZXQColumns: [
    {
      prop: 'zhangBuLBMC',
      label: '',
      width: 34,
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.zhangBuLBMC,
        );
        return data ? data.tag : '';
      },
    },
    // {
    //   prop: 'tiaoJiaWH',
    //   label: '调价文号',
    //   width: 120,
    // },
    {
      slot: 'yaoPinMCGG',
      prop: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 301,
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      width: 160,
      showOverflowTooltip: true,
    },
    {
      prop: 'gongHuoDWMC',
      label: '供货单位',
      width: 100,
      hidden: true,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 50,
    },
    {
      prop: 'tiaoJiaSL',
      label: '调价数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'yuanJinJia',
      label: '原进价',
      align: 'right',
      width: 72,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'xianJinJia',
      label: '现进价',
      align: 'right',
      width: 72,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'yuanLingSJ',
      label: '原零售价',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'xianLingSJ',
      label: '现零售价',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
  ],
  yiJiZXQColumns: [
    {
      prop: 'zhangBuLBMC',
      label: '',
      width: 34,
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.zhangBuLBMC,
        );
        return data ? data.tag : '';
      },
    },
    // {
    //   prop: 'tiaoJiaWH',
    //   label: '调价文号',
    //   width: 100,
    // },
    {
      slot: 'yaoPinMCGG',
      prop: 'yaoPinMCGG',
      label: '药品名称与规格',
      width: 301,
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      minWidth: 140,
    },
    {
      prop: 'gongHuoDWMC',
      label: '供货单位',
      width: 100,
      hidden: true,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 50,
    },
    {
      prop: 'tiaoJiaSL',
      label: '调价数量',
      align: 'right',
      width: 84,
      formatter: (row, column, cellValue, index) => {
        return Math.abs(Number(cellValue)).toFixed(3);
      },
    },
    {
      prop: 'yuanJinJia',
      label: '原进价',
      align: 'right',
      width: 72,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'xianJinJia',
      label: '现进价',
      align: 'right',
      width: 72,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'yuanLingSJ',
      label: '原零售价',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      prop: 'xianLingSJ',
      label: '现零售价',
      align: 'right',
      width: 84,
      formatter(row, column, cellValue) {
        return formatJiaGe(cellValue);
      },
    },
    {
      label: '进价升降金额',
      align: 'right',
      width: 114,
      formatter: (row, column, cellValue, index) => {
        return (
          (Number(row.xianJinJia) -
            Number(row.yuanJinJia ? row.yuanJinJia : 0)) *
          Number(row.tiaoJiaSL)
        ).toFixed(2);
      },
    },
    {
      label: '零售升降金额',
      align: 'right',
      width: 114,
      formatter: (row, column, cellValue, index) => {
        return (
          (Number(row.xianLingSJ) -
            Number(row.yuanLingSJ ? row.yuanLingSJ : 0)) *
          Number(row.tiaoJiaSL)
        ).toFixed(2);
      },
    },
  ],
  xinZengTJDColumns: [
    {
      type: 'selection',
      width: 50,
      showOverflowTooltip: false,
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
    },
    {
      prop: 'yaoPinMCGG',
      label: '调价文号',
      component: MdInput,
      width: 135,
    },
    {
      prop: 'yaoPinMCGG',
      label: '药品名称与规格',
      component: MdInput,
    },
    {
      prop: 'yaoPinCD',
      label: '产地名称',
      width: 178,
    },
    {
      prop: 'yaoPinDW',
      label: '单位',
      width: 60,
    },
    {
      prop: 'tiaoJiaSL',
      label: '调价数量',
      width: 100,
    },
    {
      prop: 'tiaoJiaSL',
      label: '原进价',
      width: 100,
    },
    {
      prop: 'tiaoJiaSL',
      label: '现进价',
      component: MdInput,
      width: 100,
    },
    {
      prop: 'tiaoJiaSL',
      label: '原零售价',
      width: 100,
    },
    {
      prop: 'tiaoJiaSL',
      label: '现零售价',
      component: MdInput,
      width: 100,
    },
    {
      prop: 'tiaoJiaSL',
      label: '进价升降金额',
      width: 110,
    },
    {
      prop: 'tiaoJiaSL',
      label: '零售升降金额',
      width: 110,
    },
    {
      actions: [],
      width: 1,
    },
  ],
};
