<template>
  <div :class="prefixClass('shouliql-wrap')">
    <md-tabs v-model="activeName" :class="prefixClass('custom-tab-default')">
      <md-tab-pane
        v-for="tab in tabs"
        :name="tab.gongNengDM"
        :label="tab.mingCheng"
        lazy
        :key="tab.gongNengDM"
      >
        <component
          :is="tab.component"
          :active="activeName === tab.gongNengDM"
          :biaoQian="tab"
          :research="tab.research"
          :moKuaiDM="moKuaiDM"
          @add-extra="onAddExtra(tab, $event)"
          @go-to-tab="handleGoToTab"
        ></component>
      </md-tab-pane>
      <template #extra>
        <component :is="extra"></component>
      </template>
    </md-tabs>
    <tiaojia-drawer ref="tiaoJiaDrawer"> </tiaojia-drawer>
  </div>
</template>

<script>
import WeiJiZhangVue from './WeiJiZhang.vue';
import YiJiZhangVue from './YiJiZhang.vue';
import tiaoJiaDrawer from './components/TiaoJiaDrawer';
export default {
  name: 'YaoKuTJ',
  provide() {
    return {
      $YaoKuTJ: this,
    };
  },
  data() {
    return {
      activeName: 'WeiJiZhang',
      extra: null,
      moKuaiDM: 'WeiJiZhang',
      tabs: [
        {
          url: 'WeiJiZhang',
          gongNengDM: 'WeiJiZhang',
          mingCheng: '未记账',
          component: WeiJiZhangVue,
          extra: null,
        },
        {
          url: 'YiJiZhang',
          gongNengDM: 'YiJiZhang',
          mingCheng: '已记账',
          component: YiJiZhangVue,
          extra: null,
        },
      ],
    };
  },
  watch: {
    tabs: {
      handler() {
        this.extra = this.current?.extra;
      },
      deep: true,
    },
    activeName(activeName) {
      this.extra = this.current?.extra;
    },
    // $route: {
    //   //监听路由变化
    //   handler: function(to, from) {
    //     this.initData()
    //   }
    // }
  },
  created() {},

  methods: {
    handleGoToTab() {
      this.activeName = 'YiJiZhang';
    },
  },
  components: {
    'tiaojia-drawer': tiaoJiaDrawer,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-shouliql-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0px;
  .#{$md-prefix}-custom-tab-default {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;
    ::v-deep .#{$md-prefix}-tabs__header {
      margin-bottom: 8px;
      background: #fff;
    }
    ::v-deep .#{$md-prefix}-tabs__content {
      flex: 1;
      padding: 0 8px;
      padding-bottom: 8px;
    }
  }
}
::v-deep.#{$md-prefix}-tabs__extra {
  height: 33px;
  line-height: 33px;
}
::v-deep .#{$md-prefix}-data-table__pagination {
  height: 33px;
}

::v-deep .#{$md-prefix}-base-table__body td.is-selection > .cell {
  width: 100% !important;
  padding-left: 10px !important;
  padding-right: 0 !important;
}

::v-deep .#{$md-prefix}-base-table-column--selection .cell {
  padding-left: 10px !important;
  padding-right: 0 !important;
}
</style>
