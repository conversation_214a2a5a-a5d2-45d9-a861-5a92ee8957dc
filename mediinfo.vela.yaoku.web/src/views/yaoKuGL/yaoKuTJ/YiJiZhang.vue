<template>
  <div :class="prefixClass('page-wrap')">
    <div :class="prefixClass('yijizhang-wrap')">
      <div :class="prefixClass('search-bar')">
        <div :class="prefixClass('search-bar__left')">
          <md-date-picker-range-pro
            v-model="searchDate"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :class="prefixClass('procurement-date space-8')"
            @change="handleSearch"
          >
          </md-date-picker-range-pro>
          <md-input
            v-model="danJuHao"
            placeholder="输入单据号搜索"
            :class="prefixClass('danjuhao-input')"
            @input="handleSearch"
          >
            <template #suffix>
              <i
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
                @click="handleSearch"
              ></i>
            </template>
          </md-input>
          <biz-yaopindw
            v-model="yaoPinMCObj"
            placeholder="名称、输入码、别名、规格等关键字进行搜索"
            style="width: 340px; margin-left: 8px"
            @change="handleQueryYaoPinMC"
          >
          </biz-yaopindw>
          <!-- <md-button
            type="primary"
            noneBg
            @click="handleTiaoJiaBCD"
            style="margin-left: auto"
          >
            调价补差单</md-button
          > -->
        </div>
        <div :class="prefixClass('search-bar__right')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleSearch"
          >
            刷新</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            :class="prefixClass('kaidan-button')"
            noneBg
            @click="handleYuLan"
            >打印</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('container__alias')">
        <md-table-pro
          :columns="yiJiZhangColumns"
          ref="daiShouLiTable"
          :class="prefixClass('daishouli-table')"
          height="100%"
          :onFetch="handleFetch"
          :pagination="pagination"
          @selection-change="handleSelectionChange"
          :cell-class-name="tableCellClassName"
        >
          <template v-slot:tiaoJiaDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('qinglingdantip')"
            >
              <template #content>
                <div @click="copy(row.tiaoJiaDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template #reference> -->
              <span
                :class="prefixClass('qinglingdh')"
                @click="handleClickQingLingDan($event, row)"
                >{{ row.tiaoJiaDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
          </template>
          <template v-slot:yaoPinZS="{ row }">
            {{ row.yaoPinZS }}
            <!-- {{ row.yaoPinMXYPMCList.length }} -->
          </template>
          <template v-slot:yaoPinMX="{ row }">
            <biz-taglist
              :list="row.yaoPinMXYPMCList"
              @clickMore="({ event }) => handleClickQingLingDan(event, row)"
            ></biz-taglist>
          </template>
        </md-table-pro>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT015'"
      :fileName="'药库调价单'"
      :title="'调价单打印预览'"
    />
    <tiaojiabcd-dialog ref="TiaoJiaBCD"></tiaojiabcd-dialog>
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetTiaoJiaDCount, GetTiaoJiaDList } from '@/service/yaoPinYK/yaoKuTJ';
import BizTagList from '@/views/yaoKuGL/yaoKuTJ/components/BizTagList.vue';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import TiaoJiaBCD from './components/TiaoJiaBCDDialog.vue';
import tableData from './tableData';

export default {
  name: 'yiJiZhang',
  inject: ['$YaoKuTJ'],
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      yaoPinMCObj: null,
      searchDate: [
        dayjs(new Date().setDate(1)).format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      danJuHao: '', // 单据号
      yiJiZhangColumns: tableData.yiJiZhangColumns,
      selectedList: null, //选中调价单
      params: {},
      pagination: {
        pageSize: 1000, //默认1000
        pageSizes: [1000, 500, 100, 50, 40, 30, 20, 10],
        layout: 'total, sizes, prev, pager, next, jumper',
      },
    };
  },
  watch: {
    active: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
  },
  created() {
    this.$nextTick((_) => {
      this.$refs.daiShouLiTable.search({ pageSize: 100 });
    });
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    //勾选调价单
    handleSelectionChange(value) {
      this.selectedList = value;
    },
    //打印选中的调价单
    handleYuLan() {
      if (!this.selectedList || this.selectedList.length === 0) {
        MdMessage.warning('请勾选要打印的调价单!');
        return;
      }

      let tiaoJiaDanID = '';
      this.selectedList.forEach((r) => {
        tiaoJiaDanID = tiaoJiaDanID + '|' + r.id;
      });
      let params = {
        tiaoJiaDanID: tiaoJiaDanID,
      };

      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    // async handleTiaoJiaBCD() {
    //   if (!this.selectedList || this.selectedList.length === 0) {
    //     MdMessage.warning('请勾选要打印的调价单!');
    //     return;
    //   }
    //   const tiaoJiaDanIDs = [];
    //   this.selectedList.forEach((x) => {
    //     tiaoJiaDanIDs.push(x.id);
    //   });
    //   this.$refs.TiaoJiaBCD.showDialog({
    //     tiaoJiaDanIDs,
    //   });
    // },
    // table刷新
    async handleFetch({ page, pageSize }, config) {
      let kaiShiSJ = null,
        jieShuSJ = null;
      if (this.searchDate?.length > 0) {
        kaiShiSJ = this.searchDate[0] ? this.searchDate[0] : null;
        jieShuSJ = this.searchDate[1] ? this.searchDate[1] : null;
      }
      const params = {
        pageIndex: page,
        pageSize: pageSize,
        kaiShiSJ: kaiShiSJ,
        jieShuSJ: jieShuSJ,
        danJuZTDM: '3',
        likeQuery: this.danJuHao,
        jiaGeID: this.yaoPinMCObj?.jiaGeID || '',
      };
      const [items, total] = await Promise.all([
        GetTiaoJiaDList(params, config),
        !this.total && GetTiaoJiaDCount(params, config),
      ]);
      this.data = items;
      this.total = Number(total) || this.total;
      return {
        items: items,
        total: this.total,
      };
    },
    // 查询条件变更搜索
    handleSearch() {
      this.total = null;
      if (this.$refs.daiShouLiTable) {
        this.$refs.daiShouLiTable.search({ pageSize: 100 });
      } else {
        this.$nextTick(() => {
          this.$refs.daiShouLiTable.search({ pageSize: 100 });
        });
      }
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return this.prefixClass('qinglingdan-row');
      }
      return this.prefixClass('qinglingdan-row');
    },
    // 点击单号 打开侧滑详情
    handleClickQingLingDan(e, row) {
      e.stopPropagation();
      this.$YaoKuTJ.$refs.tiaoJiaDrawer.openDrawer(row, 2);
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.yaoPinMCObj = {
        ...e,
      };

      this.handleSearch();
    },
    // handleCopySuccess () {
    //   this.$message({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000
    //   })
    // },
    // handleCopyError () {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了',
    //   })
    // }
  },
  components: {
    'biz-taglist': BizTagList,
    'dayin-dialog': DaYinDialog,
    'tiaojiabcd-dialog': TiaoJiaBCD,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-fuzhi {
  // @include md-def('color', 'color-6');
  color: rgb(var(--md-color-6));
}
.#{$md-prefix}-qinglingdantip {
  min-width: 30px;
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
.#{$md-prefix}-daishouli-table.#{$md-prefix}-data-table
  .#{$md-prefix}-qinglingdan-row {
  color: #1e88e5;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-page-wrap {
  height: 100%;
  width: 100%;

  .#{$md-prefix}-yijizhang-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    .#{$md-prefix}-search-bar {
      display: flex;
      justify-content: space-between;
      height: 30px;
      width: 100%;
      &__left {
        display: flex;
        width: 100%;
        .#{$md-prefix}-space-8 {
          margin-right: 8px;
        }
        .#{$md-prefix}-procurement-date {
          width: 250px;
        }
        .#{$md-prefix}-danjuhao-input {
          width: 264px;
        }
      }
      &__right {
        display: flex;
        .#{$md-prefix}-kaidan-button {
          margin: 3px 0;
        }
      }
    }
    .#{$md-prefix}-container__alias {
      flex: 1;
      margin-top: 8px;
      min-height: 0;

      .#{$md-prefix}-qinglingdh {
        cursor: pointer;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
        &:hover {
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-jiyongtag {
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        margin: 0 0 2px 5px;
        background-color: #ff9900;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
      }
      .#{$md-prefix}-operate {
        display: flex;
        justify-content: center;
        align-content: center;
      }
    }
  }
}
::v-deep .#{$md-prefix}-data-table__pagination {
  margin-top: 8px !important;
}
</style>
