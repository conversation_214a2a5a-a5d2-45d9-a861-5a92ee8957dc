<template>
  <md-tabs
    v-model="activeName"
    :class="prefixClass('tabs-views')"
    @tab-change="handleTabClick"
  >
    <md-tab-pane label="未记账" name="first" lazy>
      <weijizhang
        v-loading="loading"
        :panCunKS="panCunKS"
        :panCunJS="panCunJS"
        @go-to-tab="handleGoToTab"
        ref="weiJiZhang"
      />
    </md-tab-pane>
    <md-tab-pane label="已记账" name="second" lazy>
      <yijizhang
        v-loading="loading"
        :panCunKS="panCunKS"
        :panCunJS="panCunJS"
        ref="yiJiZhang"
      />
    </md-tab-pane>
    <template v-slot:extra>
      <div class="pancun-action">
        <md-button v-if="!panCunZT" class="start" @click="panCunKS">
          <i class="iconfont iconkaishi"></i>盘存开始
        </md-button>
        <md-button v-else class="end" @click="panCunJS">
          <i class="iconfont iconjieshu"></i>盘存结束
        </md-button>
        <div v-if="panCunZT" class="pancun-state">
          <md-icon name="iconpancun" />
          <!-- <i class="iconfont iconpancun"></i>盘存中... -->
        </div>
      </div>
    </template>
  </md-tabs>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { mapActions, mapGetters } from 'vuex';

import WeiJiZhang from './WeiJiZhang';
import YiJiZhang from './YiJiZhang';

import {
  GetPanCunZT,
  GetWeiJiZDJ,
  PanCunJS,
  PanCunKS,
} from '@/service/yaoPinYK/yaoKuPC';
export default {
  name: 'yaokupc',
  data() {
    return {
      loading: false,
      activeName: 'first',
      router: this.$route,
    };
  },
  computed: {
    ...mapGetters({
      panCunZT: 'yaokupc/getPanCunZT',
    }),
  },
  watch: {
    $route(val) {
      if (val.path !== '/YaoKuPC') return;
      this.search();
    },
  },
  activated() {
    let val = this.$route.query.type;
    if (this.$route.path === '/YaoKuPC') {
      this.activeName = val ? val : 'first';
      this.search();
    }
  },
  mounted() {
    this.loading = true;
    GetPanCunZT()
      .then((res) => {
        this.setPanCunZT(res);
      })
      .catch((err) => {
        if (!err.message) {
          // Message.error('获取盘存状态失败')
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `获取盘存状态失败`,
            confirmButtonText: '我知道了',
          });
        }
      })
      .finally(() => {
        this.loading = false;
      });
  },
  methods: {
    ...mapActions({
      setPanCunZT: 'yaokupc/setPanCunZT',
    }),
    handleGoToTab() {
      this.$nextTick(() => {
        this.activeName = 'second';
        this.$refs.yiJiZhang.handleSearch();
      });
    },
    //盘存开始
    async panCunKS() {
      this.loading = true;
      try {
        const result = await GetWeiJiZDJ();
        if (Array.isArray(result) && result.length > 0) {
          let str = '';
          result.forEach((item) => {
            if (item.shuLiang != 0) {
              str += `${item.danJuLxMC}还有${item.shuLiang}张未记账,`;
            }
          });
          if (!str) {
            this.onPanCunKS();
            return;
          }
          await MdMessageBox.confirm(`${str}是否开始盘存?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.onPanCunKS();
            })
            .catch(() => {
              MdMessage.info('已取消');
            });
        } else {
          this.onPanCunKS();
        }
      } finally {
        this.loading = false;
      }
    },
    //盘存结束
    panCunJS() {
      MdMessageBox.confirm(`是否结束盘存?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            this.loading = true;
            await PanCunJS();
            this.setPanCunZT(false);
            MdMessage.success('盘存结束成功');
          } catch (err) {
            if (!err.message) {
              // Message.error('盘存结束失败')
              MdMessageBox({
                title: '系统消息',
                type: 'error',
                message: `盘存结束失败`,
                confirmButtonText: '我知道了',
              });
            }
          } finally {
            this.loading = false;
          }
        })
        .catch(() => {
          MdMessage.info('已取消');
        });
    },
    onPanCunKS() {
      PanCunKS()
        .then(() => {
          this.setPanCunZT(true);
          MdMessage.success('盘存开始');
        })
        .catch((err) => {
          if (!err.message) {
            // Message.error('盘存开始失败')
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: `盘存开始失败！`,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
    handleTabClick(val) {
      this.$nextTick(() => {
        if (val === 'first') {
          this.$refs.weiJiZhang.handleSearch();
        } else {
          this.$refs.yiJiZhang.handleSearch();
        }
      });
    },
    search() {
      this.$nextTick(() => {
        if (this.activeName === 'first') {
          if (this.$refs.weiJiZhang) {
            this.$refs.weiJiZhang.handleSearch();
          } else {
            this.$nextTick(() => {
              this.$refs.weiJiZhang.handleSearch();
            });
          }
        } else {
          this.$refs.yiJiZhang.handleSearch();
        }
      });
    },
  },
  components: {
    weijizhang: WeiJiZhang,
    yijizhang: YiJiZhang,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  height: 100%;
  display: flex;
  flex-direction: column;
}
::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
  .#{$md-prefix}-tabs__extra {
    height: 40px;
    line-height: 40px;
    padding-right: 0;
    .pancun-action {
      position: relative;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 40px;
      padding-right: 10px;
      // @include md-linear((color-2, #ffffff), to left); //代替换
      .#{$md-prefix}-button {
        width: 108px;
        height: 30px;
        border-radius: 15px;
        i {
          margin-right: 8px;
        }
        &.start {
          // @include md-def('background-color', 'color-6');
          background-color: rgb(var(--md-color-6));
          border: 2px solid rgba(30, 136, 229, 0.24);
          color: #fff;
          background-clip: padding-box;
          i {
            color: #ffffff;
            font-size: 14px;
          }
        }
        &.end {
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 15px;
          // @include md-def('border-color', 'color-4');
          border-color: rgb(var(--md-color-4));
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));
          i {
            // @include md-def('color', 'color-6');
            color: rgb(var(--md-color-6));
            font-size: 14px;
          }
        }
      }

      .pancun-state {
        position: absolute;
        top: 50%;
        right: 128px;
        transform: translateY(-50%);
        width: 92px;
        height: 24px;
        // @include md-linear((color-5, color-7), to left);
        color: #ffffff;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        border-radius: 12px;
        &::after {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: -8px;
          border: 5px solid;
          border-color: transparent transparent transparent #3ab4ff;
          // @include md-def('border-left-color', 'color-5');
          border-left-color: rgb(var(--md-color-5));
          content: '';
        }
        i {
          margin-right: 4px;
        }
      }
    }
  }
}
::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  background: #fff;
  min-height: 0px;
  padding: 8px;
}
</style>
