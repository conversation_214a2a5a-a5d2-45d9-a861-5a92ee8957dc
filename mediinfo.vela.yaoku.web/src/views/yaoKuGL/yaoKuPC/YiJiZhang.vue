<template>
  <div :class="prefixClass('has-tally')">
    <div :class="prefixClass('has-tally-header')">
      <div :class="prefixClass('has-tally-header-left')">
        <md-date-picker-range-pro
          v-model="query.danJuSJ"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 250px"
          @change="handleSearch"
        >
        </md-date-picker-range-pro>
        <md-input
          v-model="query.danJuHao"
          placeholder="输入单据号搜索"
          :class="prefixClass('change-input')"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('icon-seach input__icon cursor-pointer')"
            slot="suffix"
            @click="handleSearch"
          ></i>
        </md-input>
      </div>
      <div :class="prefixClass('has-tally-header-right')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          @click="handleSearch"
        >
          刷新</md-button
        >
        <md-button
          v-if="panCunZT"
          type="text"
          :icon="prefixClass('icon-xinzeng')"
          :disabled="!panCunZT"
          @click="handleKaiDan"
          >开单</md-button
        >
        <!-- <md-button
          type="primary"
          plain
          :disabled="panCunZT"
          @click="handlePanCunKS"
          >盘存开始</md-button
        >
        <md-button
          type="primary"
          plain
          :disabled="!panCunZT"
          @click="handlePanCunJS"
          >盘存结束</md-button
        > -->
      </div>
    </div>
    <div v-loading="loading" :class="prefixClass('has-tally-body')">
      <md-table-pro
        :columns="columns"
        height="100%"
        :onFetch="handleFetch"
        ref="mdTablePro"
      >
        <template #panCunDH="{ row }">
          <md-tooltip
            trigger="hover"
            effect="light"
            :popper-class="prefixClass('row-copy-tips')"
          >
            <template #content>
              <div @click="copy(row.panCunDH)">复制</div>
            </template>
            <!-- <template v-slot:reference> -->
            <span
              :class="prefixClass('row-copy-label')"
              @click="handleRowClick($event, row)"
              >{{ row.panCunDH }}</span
            >
            <!-- </template> -->
          </md-tooltip>
        </template>
        <template #yaoPinMXYPMCList="{ row }">
          <biz-tag-list :list="row.yaoPinMXYPMCList"> </biz-tag-list>
        </template>
      </md-table-pro>
    </div>
    <pancunxq-drawer ref="panCunXQDrawer" />
  </div>
</template>

<script>
import { MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { mapGetters } from 'vuex';

import BizTagList from '@/components/BizTagList';
import useClipboard from 'vue-clipboard3';
import PanCunXQDrawer from './components/PanCunXQDrawer';

import { GetPanCunDCount, GetPanCunDList } from '@/service/yaoPinYK/yaoKuPC';
export default {
  name: 'yijizhang',
  props: {
    panCunKS: {
      require: true,
      type: Function,
      default: () => {},
    },
    panCunJS: {
      require: true,
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      query: {
        danJuHao: '',
        danJuSJ: [dayjs().format('YYYY-MM-01'), dayjs().format('YYYY-MM-DD')],
      },
      columns: [
        {
          slot: 'panCunDH',
          label: '盘存单',
          prop: 'panCunDH',
          width: 120,
        },
        {
          label: '药品数',
          prop: 'yaoPinZS',
          align: 'right',
          width: 80,
        },
        {
          slot: 'yaoPinMXYPMCList',
          label: '药品明细',
          prop: 'yaoPinMXYPMCList',
        },
        {
          label: '记账日期',
          prop: 'jiZhangSJ',
          width: 110,
          formatter(row) {
            return dayjs(row.zhiDanSJ).format('YYYY-MM-DD');
          },
        },
        {
          label: '记账人',
          prop: 'jiZhangRXM',
          width: 110,
        },
      ],
      loading: false,
    };
  },
  computed: {
    ...mapGetters({
      panCunZT: 'yaokupc/getPanCunZT',
    }),
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    async handleFetch({ page: pageIndex, pageSize }, config) {
      const { danJuHao, danJuSJ } = this.query;
      const params = {
        danJuZTDM: '4',
        pageSize,
        pageIndex,
        zuoFeiBZ: 0,
        likeQuery: danJuHao,
      };
      if (Array.isArray(danJuSJ) && danJuSJ.length > 0) {
        params.kaiShiSJ = danJuSJ[0] ? danJuSJ[0] : null;
        params.jieShuSJ = danJuSJ[1] ? danJuSJ[1] : null;
      }
      const [items, total] = await Promise.all([
        GetPanCunDList(params, config),
        GetPanCunDCount(params, config),
      ]);
      items.forEach((el) => {
        el.yaoPinMXYPMCList = el.yaoPinMXYPMCList.map((m) => {
          return {
            yaoPinMC: m,
            xianShiXX: {
              jiaGeID: null,
              ziTiYS: false,
              tianJiaWZ: false,
              jiaCuBZ: false,
              xieTiBZ: false,
            },
          };
        });
      });
      return { items, total };
    },
    handleSearch() {
      this.$refs.mdTablePro.search({ pageSize: 100 });
    },
    //盘存开始
    handlePanCunKS() {
      this.panCunKS();
    },
    //盘存结束
    handlePanCunJS() {
      this.panCunJS();
    },
    //开单
    handleKaiDan() {
      this.$router.push({
        name: 'XinZengPC',
      });
    },
    //查看详情
    handleRowClick(event, row) {
      event.stopPropagation();
      this.$refs.panCunXQDrawer.openDrawer(row);
    },
    //复制成功
    // handleCopySuccess () {
    //   Message.success('复制成功')
    // },
    //复制失败
    // handleCopyError () {
    //   MessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败！`,
    //     confirmButtonText: '我知道了',
    //   })
    // }
  },
  components: {
    'biz-tag-list': BizTagList,
    'pancunxq-drawer': PanCunXQDrawer,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-has-tally {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    margin-bottom: 8px;
    &-left {
      display: flex;
      .#{$md-prefix}-change-input {
        width: 264px;
        margin-left: 8px;
      }
    }
    &-right {
      .#{$md-prefix}-button {
        margin-left: 8px;
      }
    }
  }
  &-body {
    flex: 1;
    min-height: 0;
    .#{$md-prefix}-row-copy-label {
      cursor: pointer;
      // @include md-def('color', 'color-6');

      &:hover {
        // // color: #1e88e5;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
        text-decoration: underline;
      }
    }
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-row-copy-tips {
  min-width: 30px;
  // @include md-def('color', 'color-6');
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
</style>
