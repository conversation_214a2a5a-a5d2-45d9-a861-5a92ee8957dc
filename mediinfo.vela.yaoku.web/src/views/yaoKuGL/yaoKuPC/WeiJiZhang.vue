<template>
  <div :class="prefixClass('not-tally')">
    <div :class="prefixClass('not-tally-header')">
      <div :class="prefixClass('not-tally-header-left')">
        <md-date-picker-range-pro
          v-model="query.danJuSJ"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 250px"
          @change="handleSearch"
        >
        </md-date-picker-range-pro>
        <md-input
          v-model="query.danJuHao"
          placeholder="输入单据号搜索"
          :class="prefixClass('change-input')"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('icon-seach  input__icon cursor-pointer')"
            slot="suffix"
            @click="handleSearch"
          ></i>
        </md-input>
      </div>
      <div :class="prefixClass('not-tally-header-right')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          @click="handleSearch"
        >
          刷新</md-button
        >
        <md-button
          v-if="panCunZT"
          type="primary"
          :icon="prefixClass('icon-xinzeng')"
          :disabled="!panCunZT"
          noneBg
          @click="handleKaiDan"
          >开单</md-button
        >

        <!-- <md-button
          type="primary"
          plain
          :disabled="panCunZT"
          @click="handlePanCunKS"
          >盘存开始</md-button
        >
        <md-button
          type="primary"
          plain
          :disabled="!panCunZT"
          @click="handlePanCunJS"
          >盘存结束</md-button
        > -->
      </div>
    </div>
    <div v-loading="loading" :class="prefixClass('not-tally-body')">
      <div v-if="panCunZT" :class="prefixClass('not-tally-body-container')">
        <md-table-pro
          :columns="columns"
          height="100%"
          :onFetch="handleFetch"
          ref="mdTablePro"
        >
          <template #panCunDH="{ row }">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('row-copy-tips')"
            >
              <template #content>
                <div @click="copy(row.panCunDH)">复制</div>
              </template>
              <!-- <template v-slot:reference> -->
              <span
                :class="prefixClass('row-copy-label')"
                @click="handleRowClick($event, row)"
                >{{ row.panCunDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
          </template>
          <template #yaoPinMXYPMCList="{ row }">
            <biz-tag-list
              :list="row.yaoPinMXYPMCList"
              @clickMore="({ event }) => handleRowClick(event, row)"
            >
            </biz-tag-list>
          </template>
          <template #operate="{ row }">
            <md-button
              type="text-bg"
              style="padding: 4px"
              @click="handleEdit(row)"
            >
              编辑
            </md-button>
            <md-button
              type="text-bg"
              style="margin-left: 4px; padding: 4px"
              @click="handleJiZhang(row)"
              >记账</md-button
            >
            <md-button
              type="danger"
              noneBg
              style="margin-left: 4px; padding: 4px"
              @click="handleZuoFei(row)"
              >作废</md-button
            >
          </template>
        </md-table-pro>
      </div>
      <div v-else :class="prefixClass('nodata')">
        <img src="@/assets/images/panCun.png" alt="盘存未开始" />
        <span>盘存未开始</span>
      </div>
    </div>
    <pancunxq-drawer ref="panCunXQDrawer" @jizhangpcd="handleJiZhang" />
  </div>
</template>

<script>
import BizTagList from '@/components/BizTagList';
import {
  GetPanCunDCount,
  GetPanCunDList,
  JiZhangPCD,
  ZuoFeiPCD,
} from '@/service/yaoPinYK/yaoKuPC';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import { mapGetters } from 'vuex';
import PanCunXQDrawer from './components/PanCunXQDrawer';

export default {
  name: 'weijizhang',
  props: {
    panCunKS: {
      require: true,
      type: Function,
      default: () => {},
    },
    panCunJS: {
      require: true,
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      isFirstLoad: true,
      tableData: [],
      query: {
        danJuSJ: [],
        danJuHao: '',
      },
      columns: [
        {
          slot: 'panCunDH',
          prop: 'panCunDH',
          label: '盘存单',
          width: 120,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          align: 'right',
          width: 80,
        },
        {
          slot: 'yaoPinMXYPMCList',
          prop: 'yaoPinMXYPMCList',
          label: '药品明细',
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 110,
          formatter(row) {
            return dayjs(row.zhiDanSJ).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 110,
        },
        {
          label: '操作',
          slot: 'operate',
          width: 140,
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      panCunZT: 'yaokupc/getPanCunZT',
    }),
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    async handleFetch({ page: pageindex, pageSize }, config) {
      if (!this.panCunZT) return;
      const { danJuHao, danJuSJ } = this.query;
      const params = {
        danJuZTDM: '1',
        pageSize,
        pageindex,
        zuoFeiBZ: 0,
        likeQuery: danJuHao,
      };
      if (Array.isArray(danJuSJ) && danJuSJ.length > 0) {
        params.kaiShiSJ = danJuSJ[0] ? danJuSJ[0] : null;
        params.jieShuSJ = danJuSJ[1] ? danJuSJ[1] : null;
      }
      const [items, total] = await Promise.all([
        GetPanCunDList(params, config),
        GetPanCunDCount(params, config),
      ]);
      if (this.isFirstLoad && Array.isArray(items) && items.length > 0) {
        this.isFirstLoad = false;
        this.query.danJuSJ = [
          dayjs(items[0].zhiDanSJ).format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ];
      }
      items.forEach((el) => {
        el.yaoPinMXYPMCList = el.yaoPinMXYPMCList.map((m) => {
          return {
            yaoPinMC: m,
            xianShiXX: {
              jiaGeID: null,
              ziTiYS: false,
              tianJiaWZ: false,
              jiaCuBZ: false,
              xieTiBZ: false,
            },
          };
        });
      });
      this.tableData = items;
      return { items, total };
    },
    handleSearch() {
      if (!this.panCunZT) return;
      this.$refs.mdTablePro.search({ pageSize: 100 });
    },
    handlePanCunKS() {
      this.panCunKS();
    },
    handlePanCunJS() {
      if (Array.isArray(this.tableData) && this.tableData.length > 0) {
        MdMessageBox.alert(`还有未记账的盘存单，不能结束盘存！`, '操作提醒！', {
          confirmButtonText: '好的',
          type: 'warning',
        });
        return;
      }
      this.panCunJS();
    },
    handleKaiDan() {
      this.$router.push({
        name: 'XinZengPC',
      });
    },
    handleEdit(row) {
      this.$router.push({
        name: 'BianJiPCD',
        query: {
          title: '盘存单-' + row.panCunDH,
          id: String(row.id),
          panCunDH: row.panCunDH,
        },
      });
    },
    handleJiZhang(row) {
      this.loading = true;
      JiZhangPCD({ panCunDID: row.id })
        .then(() => {
          MdMessage.success('记账成功');
          // this.handleSearch()
          this.$emit('go-to-tab');
        })
        .catch((err) => {
          if (!err.message) {
            // MdMessage.error('记账失败')
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: `记账失败`,
              confirmButtonText: '我知道了',
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleZuoFei(row) {
      MdMessageBox.confirm(`确认作废该盘存单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.loading = true;
          ZuoFeiPCD({ panCunDID: row.id })
            .then(() => {
              MdMessage.success('作废成功');
              this.handleSearch();
            })
            .catch((err) => {
              if (!err.message) {
                // MdMessage.error('作废失败')
                MdMessageBox({
                  title: '系统消息',
                  type: 'error',
                  message: `作废失败`,
                  confirmButtonText: '我知道了',
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          MdMessage.info('已取消作废');
        });
    },
    handleRowClick(event, row) {
      event.stopPropagation();
      this.$refs.panCunXQDrawer.openDrawer(row).then((_) => {
        this.handleSearch();
      });
    },
    // handleCopySuccess () {
    //   MdMessage.success('复制成功')
    // },
    // handleCopyError () {
    //   // MdMessage.error('复制失败')
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败！`,
    //     confirmButtonText: '我知道了'
    //   })
    // }
  },
  components: {
    'biz-tag-list': BizTagList,
    'pancunxq-drawer': PanCunXQDrawer,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-not-tally {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    margin-bottom: 8px;
    &-left {
      display: flex;
      .#{$md-prefix}-change-input {
        width: 264px;
        margin-left: 8px;
      }
    }
    &-right {
      .#{$md-prefix}-button {
        margin-left: 8px;
      }
    }
  }
  &-body {
    flex: 1;
    min-height: 0;
    &-container {
      height: 100%;
      .#{$md-prefix}-row-copy-label {
        cursor: pointer;
        // @include md-def('color', 'color-6');
        color: rgb(var(--md-color-6));
        &:hover {
          // @include md-def('color', 'color-6');
          color: rgb(var(--md-color-6));
          // color: #1e88e5;
          text-decoration: underline;
        }
      }
    }
  }
  .#{$md-prefix}-nodata {
    border: 1px solid;
    border-color: #dddddd;
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-row-copy-tips {
  min-width: 30px;
  // @include md-def('color', 'color-6');
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  &:hover {
    cursor: pointer;
  }
}
</style>
