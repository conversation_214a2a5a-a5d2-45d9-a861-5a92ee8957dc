<template>
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="442px"
    height="310px"
    title="批量生成盘存单"
    @submit="handleSave"
  >
    <md-form v-loading="loading" label-width="80px" use-status-icon>
      <md-col :span="24">
        <md-form-item label="账簿类别">
          <md-select v-model="formModel.zhangBuLB" placeholder="全部账簿">
            <md-option
              v-for="item in zhangBuLBOptions"
              :value="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :key="item.biaoZhunDM"
            ></md-option>
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="摆放位置">
          <!-- <md-input
            v-model="formModel.baiFangWZ"
            placeholder="请输入摆放位置"
          ></md-input> -->
          <!-- <md-select
            filterable
            remote
            multiple
            v-model="formModel.baiFangWZ"
            :remote-method="searchBaiFangWZ"
          >
            <md-option
              v-for="(option, index) in baiFangWZList"
              :key="option.baiFangWZ"
              :label="option.baiFangWZ"
              :value="option.baiFangWZ"
            ></md-option>
          </md-select> -->
          <md-tree-select
            v-model="formModel.baiFangWZ"
            :data="baiFangWZList"
            multiple
            collapse-ellipsi
            node-key="baiFangWZID"
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
          />
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="毒理分类">
          <md-select v-model="formModel.duLiFL" placeholder="全部分类">
            <md-option
              v-for="item in duLiFLOptions"
              :key="item.biaoZhunDM"
              :label="item.biaoZhunMC"
              :value="item.biaoZhunDM"
            />
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="剂型">
          <md-select v-model="formModel.jiXingID" placeholder="全部剂型">
            <md-option
              v-for="item in jiXingOptions"
              :key="item.jiXingID"
              :label="item.jiXingMC"
              :value="item.jiXingID"
            />
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24" :class="prefixClass('display-flex')">
        <md-checkbox v-model="formModel.checkFlag"></md-checkbox>
        <md-form-item label="药品价格大于" label-width="95px">
          <md-input v-model="formModel.yaoPinJG" placeholder="请输入价格" />
        </md-form-item>
      </md-col>
      <md-col :span="24" :class="prefixClass('display-flex')">
        <md-form-item label="生成规则">
          <md-radio-group v-model="formModel.shengChengGZPX" border>
            <md-radio label="1">摆放位置</md-radio>
            <md-radio label="2">账簿类别-药品分类-拼音首字母</md-radio>
          </md-radio-group>
        </md-form-item>
      </md-col>
    </md-form>
  </bmis-blue-dialog>
</template>

<script>
import BlueDialog from '@/components/blue-dialog/index.vue';

import { PiLiangSC } from '@/service/yaoPinYK/yaoKuPC';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { getZhangBuLBSelectList } from '@/service/xiTongSZ/zhangBuLBWH';
import { getJiXingSelectList } from '@/service/xiTongSZ/jiXingWH.js';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import {
  GetYaoPinBFWZSelect,
  getBaiFangWZTreeForPC,
} from '@/service/yaoPin/yaoPinZD';

const formModelInit = () => {
  return {
    zhangBuLB: '', //账簿类别
    baiFangWZ: [], //摆放位置
    duLiFL: '', //毒理分类
    checkFlag: false, //checkbox
    yaoPinJG: '', //药品价格大于
    shengChengGZPX: '',
    jiXingID: '',
  };
};
export default {
  name: '',
  data() {
    return {
      defaultProps: {
        label: 'baiFangWZMC',
        children: 'children',
        value: 'baiFangWZID',
      },
      visibleDialog: false,
      loading: false,
      zhangBuLBOptions: [],
      duLiFLOptions: [],
      jiXingOptions: [],
      formModel: formModelInit(),
      resolve: null,
      reject: null,
      baiFangWZList: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    showModal() {
      this.visibleDialog = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    init() {
      getYaoPinShuJuYZYList(['YP0006']).then((res) => {
        this.duLiFLOptions = res[0].zhiYuList;
      });
      getZhangBuLBSelectList().then((res) => {
        this.zhangBuLBOptions = res;
      });
      getJiXingSelectList().then((res) => {
        this.jiXingOptions = res;
      });
      this.getBaiFangWZList();
    },
    searchBaiFangWZ(val) {
      if (val == '' || val == undefined) {
        this.baiFangWZList = [];
        return;
      }
      this.getBaiFangWZList();
    },
    // 获取摆放位置列表
    async getBaiFangWZList(val) {
      try {
        const res = await getBaiFangWZTreeForPC({
          weiSheZBZ: 0,
        });
        this.baiFangWZList = res || [];
        this.$forceUpdate();
      } catch (e) {}
    },
    handleSave() {
      const {
        zhangBuLB,
        baiFangWZ,
        duLiFL,
        checkFlag,
        yaoPinJG,
        shengChengGZPX,
        jiXingID,
      } = this.formModel;
      const params = {
        yaoPinLX: getKuCunGLLX(),
        zhangBuLBID: zhangBuLB,
        baiFangWZList: baiFangWZ,
        duLiFLDM: duLiFL,
        shengChengGZPX,
        daYuJG: checkFlag ? Number(yaoPinJG) : null,
        jiXingID,
      };
      this.loading = true;
      PiLiangSC(params)
        .then((res) => {
          this.resolve(res);
          this.closeModal();
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
</style>
