<template>
  <div :class="prefixClass('inventory-container')">
    <div v-loading="loading" :class="prefixClass('add-inventory')">
      <div :class="prefixClass('add-inventory-header')">
        <div :class="prefixClass('add-inventory-header__action')">
          <div :class="prefixClass('add-inventory-header__action__left')">
            <span :class="prefixClass('pancun-title')"
              ><i class="iconfont icondanju"></i>盘存单</span
            >
            <biz-yaopindw
              v-model="yaoPingDW"
              showSuffix
              @change="handleDingWei"
            />
          </div>
          <div :class="prefixClass('add-inventory-header__action__right')">
            <md-button
              v-if="false"
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handlePrint"
              >打印
            </md-button>
            <md-button
              type="danger"
              :icon="prefixClass('icon-shanchuwap')"
              noneBg
              @click="handleDelete"
              >删除</md-button
            >
            <md-button
              type="primary"
              :icon="prefixClass('icon-shengcheng')"
              noneBg
              @click="handleCreatePCD"
              >批量生成盘存单
            </md-button>
            <md-button type="primary" plain @click="handleSave(false)"
              >保存</md-button
            >
            <md-button type="primary" @click="handleSave(true)"
              >保存并记账</md-button
            >
          </div>
        </div>
        <div :class="prefixClass('add-inventory-header__note')">
          <span>备注</span>
          <md-input v-model="beiZhu" placeholder="请输入" />
        </div>
      </div>
      <div :class="prefixClass('add-inventory-body')">
        <md-editable-table-pro
          v-table-enter="handleTableEnter"
          v-model="allTableData"
          :columns="columns"
          :new-row="newTableRow"
          :maxLength="9999"
          :addButtonDisabled="addButtonDisabled"
          :hideAddButton="true"
          :showDefaultOperate="false"
          :cell-style="setCellStyle"
          addPosition="start"
          ref="mdEditTable"
          id="panCunTable"
          height="100%"
          :default-sort="{ prop: '', order: 'ascending' }"
          @sort-change="handleSortChange"
          @selection-change="selectionChange"
        >
          <template #yaoPinMCYGG="{ row, $index }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              labelKey="yaoPinZC"
              append-to-body
              :class="prefixClass('yaopin-search')"
              @change="handleTableYaoPinDWChange($event, row, $index)"
            />
          </template>
          <template #panCunSL="{ row, $index }">
            <md-input
              v-number.float="{}"
              v-model="row.panCunSL"
              @input="handlePanCunSL(row)"
              @focus="handleFocus($event, 'panCunSL', $index)"
              :ref="`panCunSL${$index}`"
            />
          </template>
        </md-editable-table-pro>
        <md-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40, 100, 200]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
      <div :class="prefixClass('add-inventory-footer')">
        <div>
          <div :class="prefixClass('add-inventory-footer__info left')">
            <span>制单：</span>
            <span :class="prefixClass('info__name color-222')">{{
              zhiDanRXM
            }}</span>
            <span v-if="id" :class="prefixClass('info__time color-222')">{{
              zhiDanSJ
            }}</span>
          </div>
          <div :class="prefixClass('add-inventory-footer__info right')">
            <div :class="prefixClass('margin-right-12')">
              <span>共计：</span>
              <span :class="prefixClass('font-bold')">{{ yaoPingZS }}</span>
              <span>种药品</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span style="margin-right: 8px">合计</span>
              <span>进价金额：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.jinJiaJE
              }}</span>
              <span>元</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span>零售金额：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.lingShouJE
              }}</span>
              <span>元</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span>盈亏进价金额：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.yingKuiJJJE
              }}</span>
              <span>元</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span>盈亏零售金额：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.yingKuiLSJE
              }}</span>
              <span>元</span>
            </div>
          </div>
        </div>
        <div>
          <div :class="prefixClass('add-inventory-footer__info right')">
            <div :class="prefixClass('margin-right-12')">
              <span>盘盈药品数量：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.panYingYPZS
              }}</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span>盘亏药品数量：</span>
              <span :class="prefixClass('font-bold')">{{
                totalPrice.panKuiYPZS
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <piliangscpcd-dialog ref="piLiangSCPCDDialog" />
    <biz-yaopinph model="ykpc" ref="bizYaoPinPH" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep, isEqual } from 'lodash';

import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinPH from '@/views/yaoKuGL/yaoKuPC/components/BizYaoPinPHPC.vue';
import PiLiangSCPCDDialog from './PiLiangSCPCDDialog.vue';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import {
  AddPanCunDan,
  GetPanCunDXXById,
  JiZhangPCD,
  UpdatePanCunDan,
} from '@/service/yaoPinYK/yaoKuPC';
import commonData from '@/system/utils/commonData';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2.js';
import {
  getYongHuID,
  getYongHuXM,
  getKuCunGLLX,
} from '@/system/utils/local-cache';
import { multiply, subtract } from '@/system/utils/mathComputed';
import { printByUrl } from '@/system/utils/print';
import { makePY } from '@/system/utils/wubi-pinyin.js';
import { sortBy } from 'lodash-es';
const newRow = () => {
  return {
    yaoPinLXMC: '',
    yaoPinMC: '',
    yaoPinGG: '',
    yaoPinMCYGG: '',
    chanDiMC: '',
    baoZhuangDW: '',
    shengChanPH: '',
    yaoPinXQ: '',
    kuCunSL: null,
    panCunSL: null,
    panYingPKS: 0,
    jinJia: null,
    jinJiaJE: 0,
    yingKuiJJJE: 0,
    lingShouJia: null,
    lingShouJE: 0,
    yingKuiLSJE: '',
    baiFangWZ: '',
    mianFeiYPBZ: 0,
    zengPinBZ: 0,
    yingKuiBZ: 0,
  };
};
export default {
  name: 'xinzengpc',
  inject: ['viewManager'],
  props: {
    // id: {
    //   type: String,
    //   default: '',
    // },
    panCunDH: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      closeTimeOut: null,
      columns: [
        {
          type: 'selection',
          width: 40,
          align: 'center',
          showOverflowTooltip: false,
          selectable: (row, index) => {
            return !(
              this.currentPage == this.getMaxPageindex() &&
              this.allTableData.length - 1 == index
            );
          },
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          minWidth: 100,
          type: 'text',
          sortable: 'custom',
        },
        {
          label: '序号',
          width: 50,
          type: 'index',
          align: 'center',
        },
        {
          prop: 'yaoPinLXDM',
          align: 'center',
          width: 32,
          type: 'text',
          className: 'yaoPinLXDM',
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          slot: 'yaoPinMCYGG',
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          startMode: 'click',
          endMode: 'custom',
          formatter: (row) => {
            if (row.yaoPinMC && row.yaoPinGG) {
              return row.yaoPinMC + '/' + row.yaoPinGG;
            }
            return '';
          },
          sortable: 'custom',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 160,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 50,
          type: 'text',
          align: 'center',
          showOverflowTooltip: true,
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          minWidth: 120,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          type: 'text',
          width: 120,
          formatter: (row) => {
            return row.yaoPinXQ
              ? dayjs(row.yaoPinXQ).format('YYYY-MM-DD')
              : '-';
          },
        },
        {
          prop: 'kuCunSL',
          label: '账面库存',
          align: 'right',
          width: 90,
          type: 'text',
          formatter: (row) => {
            return Number(row.kuCunSL).toFixed(3);
          },
        },
        {
          slot: 'panCunSL',
          label: '实际库存',
          width: 90,
          align: 'right',
          startMode: 'dblclick',
          formatter: (row) => {
            return Number(row.panCunSL).toFixed(3);
          },
        },
        {
          prop: 'panYingPKS',
          label: '盘盈盘亏数',
          align: 'right',
          width: 100,
          type: 'text',
          formatter(row) {
            return subtract(row.panCunSL, row.kuCunSL).toFixed(3);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return formatJiaGe_2(cellValue, this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 110,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (row.jinJiaJE == 0 || row.jinJiaJE) {
              return Number(row.jinJiaJE).toFixed(this.jinJiaJEXSDW);
            }
          },
        },
        {
          prop: 'yingKuiJJJE',
          label: '盈亏进价金额',
          width: 114,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (
              (row.panCunSL || row.panCunSL == 0) &&
              (row.kuCunSL || row.kuCunSL == 0) &&
              row.jinJia
            ) {
              return multiply(
                subtract(row.panCunSL, row.kuCunSL),
                row.jinJia,
              ).toFixed(this.jinJiaJEXSDW);
            } else {
              return '0.00';
            }
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 110,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            cellValue = cellValue || 0;
            return formatJiaGe_2(cellValue, this.lingShouXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 110,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (row.lingShouJE == 0 || row.lingShouJE) {
              return Number(row.lingShouJE).toFixed(this.lingShouJEXSDW);
            }
          },
        },
        {
          prop: 'yingKuiLSJE',
          label: '盈亏零售金额',
          width: 114,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (
              (row.panCunSL || row.panCunSL == 0) &&
              (row.kuCunSL || row.kuCunSL == 0) &&
              row.lingShouJia
            ) {
              return multiply(
                subtract(row.panCunSL, row.kuCunSL),
                row.lingShouJia,
              ).toFixed(this.lingShouJEXSDW);
            }
          },
        },

        {
          prop: 'mianFeiYPBZ',
          label: '免',
          width: 48,
          align: 'center',
          type: 'text',
          render: (h, { row }) => {
            if (row.mianFeiYPBZ == 1) {
              return h('i', {
                class: 'iconfont icongou',
                style: {
                  color: '#1e88e5',
                },
              });
            }
          },
          hidden: true,
        },
        {
          prop: 'zengPinBZ',
          label: '赠',
          align: 'center',
          width: 48,
          type: 'text',
          render: (h, { row }) => {
            if (row.zengPinBZ == 1) {
              return h('i', {
                class: 'iconfont icongou',
                style: {
                  color: '#1e88e5',
                },
              });
            }
          },
          hidden: true,
        },
      ],
      propty: '',
      order: null,
      loading: false,
      tableData: [],
      yaoPinMC: '',
      yaoPingDW: '',
      beiZhu: '',
      zhiDanRXM: getYongHuXM(),
      zhiDanRID: getYongHuID(),
      zhiDanSJ: dayjs().format('YYYY-MM-DD'),
      selections: [],
      editTableData: [],
      currentPage: 1,
      pageSize: 100,
      allTableData: [newRow()],
      tableBodyEle: null,
      temporary: [newRow()],
    };
  },
  computed: {
    //新增一行禁用
    addButtonDisabled() {
      let len = this.allTableData.length;
      if (len == 0) return false;
      if (this.allTableData[len - 1].yaoPinMC) return false;
      return true;
    },
    //合计价格
    totalPrice() {
      let jinJiaJE = 0;
      let lingShouJE = 0;
      let yingKuiJJJE = 0;
      let yingKuiLSJE = 0;
      let panYingYPZS = 0;
      let panKuiYPZS = 0;
      this.temporary.forEach((item) => {
        jinJiaJE += item.jinJiaJE;
        lingShouJE += item.lingShouJE;
        yingKuiJJJE += multiply(
          subtract(item.panCunSL || 0, item.kuCunSL || 0),
          item.jinJia || 0,
        );
        yingKuiLSJE += multiply(
          subtract(item.panCunSL || 0, item.kuCunSL || 0),
          item.lingShouJia || 0,
        );
        if (subtract(item.panCunSL, item.kuCunSL) > 0) {
          panYingYPZS++;
        } else if (subtract(item.panCunSL, item.kuCunSL) < 0) {
          panKuiYPZS++;
        }
      });
      return {
        jinJiaJE: jinJiaJE.toFixed(this.jinJiaJEXSDW),
        lingShouJE: lingShouJE.toFixed(this.lingShouJEXSDW),
        yingKuiJJJE: yingKuiJJJE.toFixed(this.jinJiaJEXSDW),
        yingKuiLSJE: yingKuiLSJE.toFixed(this.lingShouJEXSDW),
        panYingYPZS,
        panKuiYPZS,
      };
    },
    //总数量
    totalPage() {
      return this.temporary.length - 1;
    },
    //药品总数
    yaoPingZS() {
      const tableData = this.temporary;
      const len = tableData.length;
      return tableData[len - 1].yaoPinMC ? len : len - 1;
    },
  },
  async mounted() {
    this.$nextTick(() => {
      this.editInit();
    });
    const arr = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  beforeDestroy() {
    this.closeTimeOut = null;
  },
  methods: {
    //键盘事件处理
    handleTableEnter({ length, activeIndex, callback }) {
      //处理翻页时，键盘的聚焦点重置为-1
      if (activeIndex == 2 * this.pageSize - 1) {
        ++this.currentPage;
        this.handleCurrentChange(this.currentPage);
        callback({ index: -1 });
        return;
      }
      //键盘翻页
      if ((activeIndex + 1) % length == 0) {
        this.newTableRow();
        callback({});
        return;
      }
      callback();
    },
    //升降序
    handleSortChange({ column, prop, order }) {
      this.propty = prop;
      this.order = order;
      if (order) {
        //删除最后一个空白行
        if (!this.temporary[this.temporary.length - 1].jiaGeID)
          this.temporary.splice(this.temporary.length - 1, 1);
        //按照首字母排序
        // if (prop === 'yaoPinMCYGG') {
        this.temporary = sortBy(this.temporary, (item) => {
          //判断第一个字符是不是数字
          // const isFirstStr = /^\d/.test(prop);
          // 获取中文首字母
          const baiFangWZ = item.baiFangWZ ? item.baiFangWZ : '*';
          const isYaoPinMC = prop === 'yaoPinMCYGG' ? item.yaoPinMC : baiFangWZ;
          const pinyin = makePY(isYaoPinMC);
          return pinyin.toLocaleUpperCase();
        });
        // }

        if (order === 'descending') {
          this.temporary.reverse();
        }
        this.handleCurrentChange(this.currentPage);
      }
    },
    //编辑初始化
    editInit() {
      if (!this.$route.query.id) return;
      this.loading = true;
      GetPanCunDXXById(this.$route.query.id)
        .then((res) => {
          this.zhiDanRXM = res.zhiDanRXM;
          this.zhiDanRID = res.zhiDanRID;
          this.zhiDanSJ = dayjs(res.zhiDanSJ).format('YYYY-MM-DD');
          this.beiZhu = res.beiZhu;
          res.mingXiYPList.forEach((item) => {
            item.yaoPinMCYGG = {
              baoZhuangDW: item.baoZhuangDW,
              baoZhuangLiang: item.baoZhuangLiang,
              chanDiMC: item.chanDiMC,
              danJia: item.danJia,
              guiGeID: item.guiGeID,
              jiaGeID: item.jiaGeID,
              jinJia: item.jinJia,
              kuCunSL: item.kuCunSL,
              yaoPinGG: item.yaoPinGG,
              yaoPinLXDM: item.yaoPinLXDM,
              yaoPinLXMC: item.yaoPinLXMC,
              yaoPinMC: item.yaoPinMC,
            };
          });
          this.editTableData = cloneDeep(res.mingXiYPList);
          this.allTableData = res.mingXiYPList;
          this.temporary = cloneDeep(res.mingXiYPList);
          this.newTableRow();
          const tableRef = this.$refs.mdEditTable.$el;
          const tableBodyHeight = tableRef.clientHeight;
          // this.pageSize = Math.floor(tableBodyHeight / 33);
          this.pageSize = 100;
          this.handleSizeChange(this.pageSize);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //表格药品定位change
    async handleTableYaoPinDWChange(data, row, index) {
      //清空直接恢复为默认值
      if (!data) {
        //清空
        this.resetTableRow(row);
        return;
      }
      //改变药品时，先重置再复制
      if (row.yaoPinMC) {
        this.resetTableRow(row);
      }
      delete data.jinJia;
      // delete data.kuCunSL;
      // delete data.danJia;
      Object.assign(row, data);
      try {
        this.loading = true;
        const result = await this.$refs.bizYaoPinPH.show({
          yaoPinMC: data.yaoPinMC,
          jiaGeID: data.jiaGeID,
        });
        this.loading = false;
        if (result) {
          Object.assign(row, result);
          //默认盘存数量 为库存数量
          row.panCunSL = row.kuCunSL;
          row.jinJiaJE = multiply(row.jinJia, row.kuCunSL);
          row.lingShouJE = multiply(row.kuCunSL, row.lingShouJia);
          //row.baiFangWZ = data.yaoPinBFWZ
          //去重
          let isRepeat = this.temporary.some((item, i) => {
            //数据响应，匹配到当前数据不做处理
            if (i == (this.currentPage - 1) * this.pageSize + index)
              return false;
            return (
              item.jiaGeID == row.jiaGeID &&
              item.yaoPinXQ == row.yaoPinXQ &&
              item.shengChanPH == row.shengChanPH &&
              item.jinJia == row.jinJia
            );
          });
          if (isRepeat) {
            MdMessage.warning('数据重复,请重新输入！');
            this.resetTableRow(row);
            this.$refs.mdEditTable.toNext(2 * index - 1);
            return;
          }
          if (index != this.pageSize - 1) {
            this.newTableRow();
          }
          this.$refs.mdEditTable.toNext(2 * index);
          row.yaoPinMCYGG = {
            guiGeID: data.guiGeID,
            yaoPinMC: data.yaoPinMC,
            yaoPinGG: data.yaoPinGG,
            jiaGeID: data.jiaGeID,
            chanDiMC: data.chanDiMC,
            kuCunSL: data.kuCunSL,
            baoZhuangDW: data.baoZhuangDW,
            danJia: data.danJia,
            yaoPinLXDM: data.yaoPinLXDM,
            yaoPinLXMC: data.yaoPinLXMC,
          };

          this.allTableData[index] = { ...Object.assign(row, result) };
          const currentIndex = this.pageSize * (this.currentPage - 1) + index;
          this.temporary[currentIndex] = { ...Object.assign(row, result) };
        }
      } catch {
        this.loading = false;
        this.resetTableRow(row);
        this.$refs.mdEditTable.toNext(2 * index - 1);
      }
    },
    // cell背景色
    setCellStyle({ row, column, rowIndex, columnIndex }) {
      let color = '';
      if (column.property === 'panYingPKS') {
        if (subtract(row.panCunSL, row.kuCunSL * 1) < 0) {
          color = '#ffe9e6';
        } else if (subtract(row.panCunSL, row.kuCunSL * 1) > 0) {
          color = '#e7f9f2';
        }
      } else if (column.property === 'yingKuiJJJE') {
        if (
          (row.panCunSL || row.panCunSL == 0) &&
          (row.kuCunSL || row.kuCunSL == 0) &&
          row.jinJia
        ) {
          if (
            multiply(subtract(row.panCunSL, row.kuCunSL), row.jinJia) * 1 <
            0
          ) {
            color = '#ffe9e6';
          } else if (
            multiply(subtract(row.panCunSL, row.kuCunSL), row.jinJia) * 1 >
            0
          ) {
            color = '#e7f9f2';
          }
        }
      } else if (column.property === 'yingKuiLSJE') {
        if (
          (row.panCunSL || row.panCunSL == 0) &&
          (row.kuCunSL || row.kuCunSL == 0) &&
          row.lingShouJia
        ) {
          if (
            multiply(subtract(row.panCunSL, row.kuCunSL), row.lingShouJia) * 1 <
            0
          ) {
            color = '#ffe9e6';
          } else if (
            multiply(subtract(row.panCunSL, row.kuCunSL), row.lingShouJia) * 1 >
            0
          ) {
            color = '#e7f9f2';
          }
        }
      }
      return {
        'background-color': color,
      };
    },
    //新增一行
    newTableRow() {
      if (this.addButtonDisabled) return;
      // this.allTableData.push(newRow())
      this.temporary.push(newRow());
      //重新计算当前页
      this.currentPage = this.getMaxPageindex();
      this.handleSizeChange(this.pageSize);
      // this.clearHeightLight()
    },
    //批量生成盘存单
    handleCreatePCD() {
      this.$refs.piLiangSCPCDDialog.showModal().then(async (res) => {
        if (res.length == 0) {
          MdMessage.warning('此范围内没有批次库存！');
          return;
        }
        //默认盘存数量为 当前账面库存数
        res.forEach((item) => {
          item.panCunSL = item.kuCunSL;
          item.panYingPKS = 0;
          item.yingKuiBZ = 0; //盘盈盘亏标志
          item.yaoPinMCYGG = {
            yaoPinZC: item.yaoPinMC + ' ' + item.yaoPinGG,
            baoZhuangDW: item.baoZhuangDW,
            baoZhuangLiang: item.baoZhuangLiang,
            chanDiMC: item.chanDiMC,
            danJia: item.danJia,
            guiGeID: item.guiGeID,
            jiaGeID: item.jiaGeID,
            jinJia: item.jinJia,
            kuCunSL: item.kuCunSL,
            yaoPinGG: item.yaoPinGG,
            yaoPinLXDM: item.yaoPyaoPinLXDMinGG,
            yaoPinLXMC: item.yaoPinLXMC,
            yaoPinMC: item.yaoPinMC,
          };
        });
        //批量生成 直接覆盖之前的
        this.allTableData = res;
        // 第一次进来不用排序
        this.temporary = cloneDeep(res);
        //按照首字母排序
        // this.temporary = sortBy(this.allTableData, (item) => {
        //   // 获取中文首字母
        //   const pinyin = makePY(item.yaoPinMC);
        //   return pinyin.toLocaleUpperCase();
        // });
        //清除高亮
        // this.newTableRow();
        this.clearHeightLight();
        this.handleResetTableData(this.pageSize);
      });
    },
    //删除
    handleDelete() {
      if (!(Array.isArray(this.selections) && this.selections.length > 0)) {
        MdMessage.warning('至少选中一条');
        return;
      }
      MdMessageBox.confirm('确定删除当前选中的药品?', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // this.clearHeightLight()
          //删除表格当前页数据和存储的表格数据
          this.temporary = this.handleDel(this.temporary);
          this.allTableData = this.handleDel(this.allTableData);

          //处理末尾删除时，数据被清空则回退一页
          this.currentPage =
            this.currentPage > this.getMaxPageindex()
              ? this.getMaxPageindex()
              : this.currentPage;
          this.handleSizeChange(this.pageSize);
          MdMessage.success('删除成功');
        })
        .catch((e) => {
          MdMessage.info('已取消删除');
        });
    },
    handlePanCunSL(row) {
      if (isNaN(+row.panCunSL) || +row.panCunSL < 0) {
        row.panCunSL = 0;
      }
      row.panYingPKS = row.panCunSL - row.kuCunSL;
      row.yingKuiBZ = row.panCunSL - row.kuCunSL == 0 ? 0 : 1;
      row.jinJiaJE = +(row.panCunSL * row.jinJia).toFixed(3);
      row.lingShouJE = +(row.panCunSL * row.lingShouJia).toFixed(3);
      const target = this.temporary.find((el) => el.jiaGeID === row.jiaGeID && el.shengChanPH === row.shengChanPH);
      if (target) {
        Object.assign(target, row);
      }
    },
    handleFocus(val, inputName, index) {
      setTimeout(() => {
        if (val) this.$refs['panCunSL' + index].select();
      }, 500);
    },
    //删除方法抽离
    handleDel(item) {
      return this.selections.reduce((prev, item) => {
        let index = prev.findIndex((i) => {
          return (
            i.jiaGeID == item.jiaGeID &&
            i.shengChanPH == item.shengChanPH &&
            i.jinJia == item.jinJia &&
            i.yaoPinXQ == item.yaoPinXQ
          );
        });

        //编辑状态存储id
        if (index > -1) {
          prev.splice(index, 1);
        }
        return prev;
      }, item);
    },
    changePanCunSL(row, index) {
      row.panCunSL = +row.panCunSL;
      const currentIndex = this.pageSize * (this.currentPage - 1) + index;
      this.temporary[currentIndex] = { ...row };
    },
    /**
     * 保存盘存单
     */
    async handleSave(type) {
      const tableData = this.temporary;
      const tableLen = tableData.length;
      if (this.yaoPingZS == 0) {
        MdMessage.warning('盘存单列表为空！');
        return;
      }
      this.loading = true;
      //判断是否有空值
      let nullIndex = tableData.findIndex((item, index) => {
        if (index == tableLen - 1) return false;
        return !item.jiaGeID || item.panCunSL < 0;
      });
      if (nullIndex > -1) {
        this.clearHeightLight();
        const { page, domIndex } = this.tableDomHeightLight(nullIndex);
        MdMessage.error(
          `第${page}页,第${domIndex + 1}条,药品名称或盘存数量为空`,
        );
        this.loading = false;
        return;
      }
      //删除最后一个
      if (!tableData[tableData.length - 1].jiaGeID) {
        tableData.splice(tableLen - 1, 1);
      }
      //默认参数
      const params = {
        beiZhu: this.beiZhu,
        zhiDanRID: getYongHuID(),
        zhiDanRXM: getYongHuXM(),
        zhiDanSJ: dayjs().format('YYYY-MM-DD'),
        yaoPinZS: tableData.length,
      };
      //新增
      let errorFlag;
      let activeName;
      if (!this.$route.query.id) {
        //新增参数处理
        params.mingXiYPList = tableData.map(
          ({ panYingPKS, ...item }, index) => {
            return {
              ...item,
              panYingPKS: Number(panYingPKS),
              shunXuHao: index,
            };
          },
        );
        try {
          //新增盘存单
          var addparam = {};
          addparam.beiZhu = params.beiZhu;
          addparam.mingXiYPList = [];
          params.mingXiYPList.forEach((x) => {
            addparam.mingXiYPList.push({
              jiaGeID: x.jiaGeID,
              guiGeID: x.guiGeID,
              jinJia: x.jinJia,
              lingShouJia: x.lingShouJia,
              shengChanPH: x.shengChanPH,
              yaoPinXQ: x.yaoPinXQ,
              kuCunSL: x.kuCunSL,
              panCunSL: x.panCunSL,
              shunXuHao: x.shunXuHao,
              baiFangWZ: x.baiFangWZ,
            });
          });
          const result = await AddPanCunDan(addparam);
          MdMessage.success('保存成功!');
          activeName = 'first';
          if (result && type) {
            //记账盘存单
            errorFlag = true;
            activeName = 'second';
            await JiZhangPCD({ panCunDID: result });
            MdMessage.success('记账成功!');
          }

          //await this.handlePrint(result)
          this.closeTab();
          this.$router.push({
            path: '/YaoKuPC',
            query: {
              type: activeName,
            },
          });
        } catch (err) {
          this.newTableRow();
          if (errorFlag) {
            this.closeTab();
          }
        } finally {
          this.loading = false;
        }
      } else {
        params.id = this.$route.query.id;
        params.panCunDH = this.panCunDH;
        params.yaoPinZS = tableData.length;
        params.addMingXiYPList = tableData.filter((item, index) => {
          if (!item.id) {
            item.panYingPKS = Number(item.panYingPKS);
            return item;
          }
        });
        params.zuoFeiMingXiYPList = this.editTableData.reduce((prev, item) => {
          let flag = tableData.some((i) => i.id === item.id);
          if (!flag) {
            prev.push(item.id);
          }
          return prev;
        }, []);
        params.updateMingXiYPList = tableData.filter((item) => {
          return this.editTableData.some(
            (i) => i.id === item.id && !isEqual(item, i),
          );
        });
        try {
          //更新盘存单
          //新增盘存单
          var updateparam = {};
          updateparam.beiZhu = params.beiZhu;
          updateparam.id = params.id;
          updateparam.addMingXiYPList = [];
          updateparam.updateMingXiYPList = [];
          updateparam.zuoFeiMingXiYPList = [];
          params.addMingXiYPList.forEach((x) => {
            updateparam.addMingXiYPList.push({
              jiaGeID: x.jiaGeID,
              guiGeID: x.guiGeID,
              jinJia: x.jinJia,
              lingShouJia: x.lingShouJia,
              shengChanPH: x.shengChanPH,
              yaoPinXQ: x.yaoPinXQ,
              kuCunSL: x.kuCunSL,
              panCunSL: x.panCunSL,
              shunXuHao: x.shunXuHao,
              baiFangWZ: x.baiFangWZ,
            });
          });
          params.updateMingXiYPList.forEach((x) => {
            updateparam.updateMingXiYPList.push({
              id: x.id,
              panCunSL: x.panCunSL,
              kuCunSL: x.kuCunSL,
            });
          });
          updateparam.zuoFeiMingXiYPList = params.zuoFeiMingXiYPList;
          const result = await UpdatePanCunDan(updateparam);
          MdMessage.success('保存成功!');
          activeName = 'first';
          if (result && type) {
            errorFlag = true;
            //记账盘存单
            await JiZhangPCD({ panCunDID: result });
            MdMessage.success('记账成功!');
            activeName = 'second';
          }
          //await this.handlePrint(result)
          this.closeTab();
          this.$router.push({
            path: '/YaoKuPC',
            query: {
              type: activeName,
            },
          });
        } catch (err) {
          this.newTableRow();
          if (errorFlag) {
            this.closeTab();
          }
        } finally {
          this.loading = false;
        }
      }
    },
    //重置table
    handleResetTableData() {
      this.currentPage = 1;
      this.handleSizeChange(this.pageSize);
    },
    //获取最大页数
    getMaxPageindex() {
      return Math.ceil(this.temporary.length / this.pageSize);
    },
    // pageSize变化事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.tableData = this.temporary.slice(
        val * (this.currentPage - 1),
        val * this.currentPage,
      );
      //temporary存储页面table的全部数据,根据当前页拆分数据
      this.allTableData = this.temporary.slice(
        val * (this.currentPage - 1),
        val * this.currentPage,
      );
    },
    // currentPage变化事件
    handleCurrentChange(val) {
      // this.clearHeightLight()
      this.currentPage = val;

      this.tableData = this.temporary.slice(
        this.pageSize * (val - 1),
        this.pageSize * val,
      );
      this.allTableData = this.temporary.slice(
        this.pageSize * (val - 1),
        this.pageSize * val,
      );
      //当表格处于最后一页时
      if (
        this.temporary[this.temporary.length - 1].jiaGeID ==
        this.allTableData[this.allTableData.length - 1].jiaGeID
      ) {
        this.allTableData.push(newRow());
      }
    },
    //关闭Tab
    closeTab() {
      let closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      this.closeTimeOut = setTimeout(() => {
        this.viewManager.close(closeTabKey);
      }, 500);
    },
    //定位
    async handleDingWei(data) {
      if (!data) return;
      if (!(Array.isArray(this.allTableData) && this.allTableData.length > 0)) {
        MdMessage.warning('盘存单列表为空,不能定位！');
        return;
      }
      try {
        this.loading = true;
        this.clearHeightLight();
        const result = await this.$refs.bizYaoPinPH.show({
          yaoPinMC: data.yaoPinMC,
          jiaGeID: data.jiaGeID,
        });
        let index = this.allTableData.findIndex((item) => {
          return (
            item.jiaGeID == result.jiaGeID &&
            item.shengChanPH == result.shengChanPH &&
            item.jinJia == result.jinJia
          );
        });
        if (index == -1) {
          MdMessage.warning('未找到该药品');
          return;
        }
        this.tableDomHeightLight(index);
      } finally {
        this.loading = false;
      }
    },
    //获取dom,清除高亮
    clearHeightLight() {
      //获取table DOM
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(
          `#panCunTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
        );
      }
      // 清除高亮
      const rowList = Array.from(
        this.tableBodyEle.querySelectorAll(
          `.mediinfo-vela-yaoku-web-base-table__row`,
        ),
      );
      rowList.forEach((item) => {
        item.classList.remove(this.prefixClass('heightLight'));
      });
    },
    //dom定位
    tableDomHeightLight(index) {
      let pageIndex = parseInt(index / this.pageSize);
      let page = pageIndex > 0 ? pageIndex + 1 : 1;
      let domIndex = index % this.pageSize;
      this.handleCurrentChange(page);
      //获取高亮dom节点
      const childDom = this.tableBodyEle.querySelectorAll(
        `.mediinfo-vela-yaoku-web-base-table__row`,
      )[domIndex];
      childDom.classList.add(this.prefixClass('heightLight'));
      this.loading = false;
      return { page, domIndex };
    },
    //重置当前row
    resetTableRow(row) {
      const resetRow = newRow();
      for (let key in row) {
        if (resetRow[key] !== void 0) {
          row[key] = resetRow[key];
        } else {
          delete row[key];
        }
      }
    },
    //table选中事件
    selectionChange(selection) {
      this.selections = selection;
    },
    async handlePrint(id) {
      try {
        this.loading = true;
        await printByUrl('YKXT003', { id });
        MdMessage.success('打印成功！');
      } catch (e) {
        // MdMessage.error('打印失败！')
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    'piliangscpcd-dialog': PiLiangSCPCDDialog,
    'biz-yaopindw': BizYaoPinDW,
    'biz-yaopinph': BizYaoPinPH,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-inventory-container {
  height: 100%;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  background-color: #eaeff3;
  padding: 8px;
}

.#{$md-prefix}-add-inventory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;

  &-header {
    flex-shrink: 0;

    &__action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      // background-color: #edf6fd;
      background-color: rgb(var(--md-color-1));
      // @include md-def('background-color', 'color-1');

      padding: 0 8px;

      &__left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        // background-color: #e3ecf3;
        border-radius: 4px;

        .#{$md-prefix}-yaopin-search {
          width: 180px;
        }

        .#{$md-prefix}-pancun-title {
          font-weight: 600;
          // @include md-def('color', 'color-8');
          color: rgb(var(--md-color-8));
          font-size: 16px;
          line-height: 28px;
          margin: 0 8px;
          white-space: nowrap;
          i {
            font-size: 18px;
          }
        }

        .#{$md-prefix}-more-action {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          height: 30px;
          background-color: #ffffff;
          border-radius: 4px;
          margin-left: 4px;
          cursor: pointer;

          &:hover {
            background: #e6f7ff;
          }
        }
      }

      &__right {
        .#{$md-prefix}-button {
          margin-left: 8px;

          &.#{$md-prefix}-button--text {
            margin-right: 4px;
          }
        }
      }
    }

    &__note {
      display: flex;
      padding: 8px;
      height: 30px;

      > span {
        color: #333333;
        font-size: 14px;
        line-height: 30px;
        text-align: right;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .#{$md-prefix}-input {
        flex: 1;
      }
    }
  }

  &-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    padding: 0 8px;

    ::v-deep .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      .#{$md-prefix}-editable-table__container {
        height: 100% !important;
      }

      .#{$md-prefix}-table.#{$md-prefix}-table--edit {
        min-height: 0;
        display: flex;
        flex-direction: column;
      }

      .#{$md-prefix}-base-table__header-wrapper {
        flex-shrink: 0;

        td.td-text.is-center {
          padding-left: 0px;
        }
      }

      .#{$md-prefix}-base-table__body-wrapper {
        overflow: auto;
        flex: 1;

        .#{$md-prefix}-base-table__row.#{$md-prefix}-heightLight {
          > td {
            background: #e2efff;
          }
        }
      }

      .cell {
        .#{$md-prefix}-date-editor {
          width: 100%;
        }
      }
    }

    ::v-deep .#{$md-prefix}-pagination {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;
    }
  }

  &-footer {
    position: absolute;
    bottom: 9px;
    // display: flex;
    // justify-content: space-between;
    padding-left: 8px;
    flex-shrink: 0;
    line-height: 20px;
    font-size: 14px;
    & > div {
      display: flex;
    }
    .#{$md-prefix}-add-inventory-footer__info {
      display: flex;
      .#{$md-prefix}-info__name {
        margin-right: 8px;
      }

      .#{$md-prefix}-margin-right-12 {
        margin-right: 12px;
      }

      &.#{$md-prefix}-right {
        span {
          color: #aaa;
        }
      }
      span {
        color: #666666;

        &.#{$md-prefix}-color-222 {
          color: #222222;
        }

        &.#{$md-prefix}-font-bold {
          font-weight: 600;
          color: #222222;
        }
      }
    }
  }
}
::v-deep .yaoPinLXDM .cell {
  text-align: left;
}
</style>
