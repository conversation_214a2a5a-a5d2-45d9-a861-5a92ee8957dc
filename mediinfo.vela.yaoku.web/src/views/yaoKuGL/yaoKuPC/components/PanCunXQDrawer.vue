<template>
  <md-drawer
    v-model="visible"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    size="90%"
    :modalClass="prefixClass('pancun-drawer')"
    ref="panCunXQDrawer"
  >
    <div
      v-loading="loading"
      :loading-text="loadingText"
      :class="prefixClass('drawer__alias')"
    >
      <div :class="prefixClass('pandun-title')">
        <span :class="prefixClass('title-left')">
          {{ '详情 -' + drawerData.panCunDH }}
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <md-button
              v-if="drawerData.danJuZTDM != '4'"
              type="primary"
              noneBg
              @click="handleJiZhang"
            >
              <i
                class="iconfont iconedit"
                style="font-size: 14px; margin-right: 4px"
              />记账
            </md-button>
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content__alias')">
        <div :class="prefixClass('content-zhuangtai')">
          <md-descriptions direction="horizontal">
            <md-descriptions-item label="制单" :column-percent="80">{{
              drawerData.zhiDanRXM +
                ' ' +
                yaoKuZDJZTimeShow(drawerData.zhiDanSJ) || '-'
            }}</md-descriptions-item>
          </md-descriptions>
          <md-descriptions
            direction="horizontal"
            v-if="drawerData.danJuZTDM == '4'"
          >
            <md-descriptions-item label="记账" :column-percent="80">
              {{
                drawerData.jiZhangRXM +
                ' ' +
                yaoKuZDJZTimeShow(drawerData.jiZhangSJ)
              }}</md-descriptions-item
            >
          </md-descriptions>
          <md-descriptions direction="horizontal">
            <md-descriptions-item label="备注" :column-percent="80">{{
              drawerData.beiZhu || '-'
            }}</md-descriptions-item>
          </md-descriptions>
          <md-descriptions direction="horizontal">
            <md-descriptions-item>
              <biz-yaopindw
                v-if="visible"
                v-model="query.likeQuery"
                :placeholder="'药品名称及规格'"
                @change="handleKuaiSuDW($event)"
              />
            </md-descriptions-item>
          </md-descriptions>
        </div>
        <div :class="prefixClass('content-table')">
          <md-table
            :columns="columns"
            :data="tableData"
            height="100%"
            :stripe="false"
            :row-style="setRowStyle"
            id="panCunXQTable"
            ref="panCunXQTable"
          >
          </md-table>
        </div>
        <div :class="prefixClass('description pos-bottom-direction')">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ drawerData.yaoPinZS
              }}<span :class="prefixClass('content-color')">种药品</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >合计 进价金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.totalJinJia
              }}<span :class="prefixClass('content-color')">元</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >零售金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.totalLingSJ
              }}<span :class="prefixClass('content-color')">元</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >盈亏进价金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.totalYingKuiJJJE
              }}<span :class="prefixClass('content-color')">元</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >盈亏零售金额:</span
            >
            <span :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.totalYingKuiLSJE
              }}<span :class="prefixClass('content-color')">元</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >盘盈药品总数:</span
            >
            <span
              :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.panYingYPZS }}</span
            >
            <span :class="prefixClass('description-item__label')"
              >盘亏药品总数:</span
            >
            <span
              :class="prefixClass('description-item__content fontWeight')"
              >{{ totalPrice.panKuiYPZS }}</span
            >
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT003'"
        :fileName="'药库盘存单'"
        :title="'药库盘存单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import { subtract } from '@/system/utils/mathComputed';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetPanCunDXXById, JiZhangPCD } from '@/service/yaoPinYK/yaoKuPC';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { printByUrl } from '@/system/utils/print';
const drawerDataInit = () => {
  return {
    beiZhu: '',
    danJuZTDM: '',
    zhiDanRen: '',
    zhiDanSJ: '',
    zhiDanRXM: '',
    jiZhangRXM: '',
    jiZhangSJ: '',
    yaoPinZS: '',
  };
};
export default {
  name: 'pancunxq-drawer',
  data() {
    return {
      query: {
        likeQuery: {}, // 药品定位数据绑定
      },
      dangQingDWYPIndex: null, //药品定位， 当前聚焦的index
      dingWeiYPIndexArr: [], //药品定位， 搜索到的所有药品index
      loading: false,
      visible: false,
      loadingText: '正在加载中...',
      params: {},
      panCunDID: '',
      drawerData: drawerDataInit(),
      yaoKuZDJZTimeShow: yaoKuZDJZTimeShow,
      columns: [
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
        },
        {
          prop: 'yaoPinLX',
          width: 32,
          // align: 'center',
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yaoPinMC',
          label: '药品名称与规格',
          width: 300,
          formatter: (row) => {
            return row.yaoPinMC + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          width: 180,
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 48,
        },
        // {
        //   prop: 'shengChanPH',
        //   label: '生产批号',
        //   width: 120,
        // },
        // {
        //   prop: 'yaoPinXQ',
        //   label: '药品效期',
        //   width: 100,
        //   formatter(row) {
        //     return row.yaoPinXQ
        //       ? dayjs(row.yaoPinXQ).format('YYYY-MM-DD')
        //       : '-';
        //   },
        // },
        {
          prop: 'kuCunSL',
          label: '账面库存',
          align: 'right',
          width: 100,
          formatter(row) {
            return Number(row.kuCunSL).toFixed(3);
          },
        },
        {
          prop: 'panCunSL',
          label: '实际库存',
          align: 'right',
          width: 100,
          formatter(row) {
            return Number(row.panCunSL).toFixed(3);
          },
        },
        {
          prop: 'panYingPKS',
          label: '盘盈盘亏数',
          align: 'right',
          width: 90,
          formatter(row) {
            return subtract(row.panCunSL, row.kuCunSL).toFixed(3);
            //return Number(row.panYingPKS).toFixed(3)
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          width: 110,
          formatter(row) {
            return formatJiaGe(row.jinJia);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          width: 110,
          formatter(row) {
            return Number(row.jinJiaJE).toFixed(3);
          },
        },
        {
          prop: 'yingKuiJJJE',
          label: '盈亏进价金额',
          width: 110,
          align: 'right',
          formatter(row) {
            return ((row.panCunSL - row.kuCunSL) * row.jinJia).toFixed(3);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 110,
          align: 'right',
          formatter(row) {
            return formatJiaGe(row.lingShouJia);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 110,
          align: 'right',
          formatter(row) {
            return Number(row.lingShouJE).toFixed(3);
          },
        },
        {
          prop: 'yingKuiLSJE',
          label: '盈亏零售金额',
          width: 110,
          align: 'right',
          formatter(row) {
            return ((row.panCunSL - row.kuCunSL) * row.lingShouJia).toFixed(3);
          },
        },

        // {
        //   prop: 'mianFeiYPBZ',
        //   label: '免',
        //   width: 48,
        //   align: 'center',
        //   showOverflowTooltip: false,
        //   render: (h, { row }) => {
        //     if (row.mianFeiYPBZ != 1) return;
        //     return h('i', {
        //       class: 'iconfont icongou',
        //       style: {
        //         color: '#1e88e5',
        //       },
        //     });
        //   },
        // },
        // {
        //   prop: 'zengPinBZ',
        //   label: '赠',
        //   align: 'center',
        //   width: 48,
        //   showOverflowTooltip: false,
        //   render: (h, { row }) => {
        //     if (row.zengPinBZ != 1) return;
        //     return h('i', {
        //       class: 'iconfont icongou',
        //       style: {
        //         color: '#1e88e5',
        //       },
        //     });
        //   },
        // },
      ],
      resolve: null,
      reject: null,
      tableData: [],
      allTableData: [],
      page: 0,
      pageSize: 30,
      isFirshLoad: true,
      tableBodyEle: null,
    };
  },
  computed: {
    totalPrice() {
      let totalJinJia = 0;
      let totalLingSJ = 0;
      let totalYingKuiJJJE = 0;
      let totalYingKuiLSJE = 0;
      let panYingYPZS = 0;
      let panKuiYPZS = 0;
      this.tableData.forEach((item) => {
        totalJinJia += item.jinJiaJE;
        totalLingSJ += item.lingShouJE;
        totalYingKuiJJJE += (item.panCunSL - item.kuCunSL) * item.jinJia;
        totalYingKuiLSJE += (item.panCunSL - item.kuCunSL) * item.lingShouJia;
        if (subtract(item.panCunSL, item.kuCunSL) > 0) {
          panYingYPZS++;
        } else if (subtract(item.panCunSL, item.kuCunSL) < 0) {
          panKuiYPZS++;
        }
      });
      return {
        totalJinJia: totalJinJia.toFixed(3),
        totalLingSJ: totalLingSJ.toFixed(3),
        totalYingKuiJJJE: totalYingKuiJJJE.toFixed(3),
        totalYingKuiLSJE: totalYingKuiLSJE.toFixed(3),
        panYingYPZS,
        panKuiYPZS,
      };
    },
    totalPage() {
      return Math.ceil(this.tableData.length / this.pageSize);
    },
  },
  mounted() {
    // window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    // window.removeEventListener('click', this.handleClickBodyCloseDrawer);
    // if (this.tableBodyEle) {
    // this.tableBodyEle.removeEventListener('scroll', this.handleScrollLoad);
    // }
  },
  methods: {
    async openDrawer(option) {
      this.visible = true;
      this.loading = true;
      this.panCunDID = option.id;
      this.clearDrawer();
      // if (this.isFirshLoad) {
      //   this.isFirshLoad = false;
      //   this.$nextTick(() => {
      //     this.tableBodyEle = document.querySelector(
      //       `#panCunXQTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
      //     );
      //     this.tableBodyEle.addEventListener('scroll', this.handleScrollLoad);
      //   });
      // }
      GetPanCunDXXById(option.id)
        .then((res) => {
          res.jiZhangSJ =
            res.jiZhangSJ && dayjs(res.jiZhangSJ).format('YYYY-MM-DD');
          res.zhiDanSJ =
            res.zhiDanSJ && dayjs(res.zhiDanSJ).format('YYYY-MM-DD');
          Object.assign(this.drawerData, res);
          this.tableData = res.mingXiYPList;
          // this.tableData = this.tableData.concat(
          //   res.mingXiYPList.slice(0, this.pageSize),
          // );
        })
        .finally(() => {
          this.loading = false;
        });
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    handleDaoChu() {},
    //预览
    async handleYuLan() {
      const params = {
        id: this.panCunDID,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handlePrint() {
      try {
        this.loading = true;
        this.loadingText = '正在打印中...';
        await printByUrl('YKXT003', { id: this.panCunDID });
        MdMessage.success('打印成功！');
      } catch (e) {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.loading = false;
        this.loadingText = '正在加载中...';
      }
    },
    handleJiZhang() {
      this.loading = true;
      JiZhangPCD({ panCunDID: this.panCunDID })
        .then((_) => {
          MdMessage.success('记账成功');
          this.visible = false;
          this.resolve();
        })
        .catch((err) => {
          if (!err.message) {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: `记账失败！`,
              confirmButtonText: '我知道了',
            });
          }
          this.reject();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    closeDrawer() {
      this.visible = false;
    },
    handleClickBodyCloseDrawer(e) {
      this.closeDrawer();
      // if (!this.$refs.panCunXQDrawer.$el.contains(e.target)) {
      // }
    },
    handleScrollLoad(e) {
      const ele = e.target;
      // console.log(ele.scrollTop+ele.clientHeight+ "_"+ele.scrollHeight)
      if (
        ele.scrollTop > 10 &&
        ele.scrollTop + ele.clientHeight == ele.scrollHeight
      ) {
        // console.log(this.page + '_' + this.totalPage + '____________')
        if (this.page == this.totalPage - 1) {
          MdMessage.warning('已经到底了');
          return;
        }
        this.page++;
        let data = [];
        data = this.allTableData.slice(
          this.page * this.pageSize,
          (this.page + 1) * this.pageSize,
        );
        this.tableData = this.tableData.concat(data);
      }
    },
    clearDrawer() {
      this.drawerData = drawerDataInit();
      this.page = 0;
      this.tableData = [];
      this.allTableData = [];
    },
    setRowStyle({ row }) {
      const panYingPks = subtract(row.panCunSL, row.kuCunSL);
      if (panYingPks > 0) {
        return { backgroundColor: 'rgba(82,196,26,.2)' };
      } else if (panYingPks < 0) {
        return { backgroundColor: 'rgba(255,153,0,.2)' };
      }
    },
    // 去掉定位样式
    clearDingWeiClass(tableBodyEle, index) {
      if (tableBodyEle && index >= 0) {
        let dingWeiEle = tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__body .${this.cssPrefix}-base-table__row`,
        )[index];
        dingWeiEle.className = dingWeiEle.className.replace(
          this.prefixClass('dingwei-bg'),
          '',
        );
      }
    },
    //设置定位样式
    setDingWeiClass(tableBodyEle, index, dingWeiBZ = false) {
      let dingWeiEle = tableBodyEle.querySelectorAll(
        `.${this.cssPrefix}-base-table__row`,
      )[index];
      dingWeiEle.className =
        dingWeiEle.className !== ''
          ? dingWeiEle.className + ' ' + this.prefixClass('dingwei-bg')
          : this.prefixClass('dingwei-bg');
      if (dingWeiBZ) this.setDingWeiScroll(tableBodyEle, dingWeiEle, index);
    },
    // 定位滚动
    setDingWeiScroll(tableBodyEle, dingWeiEle, index) {
      // tableBodyEle.scrollTop = index * dingWeiEle.clientHeight;
      this.$refs.panCunXQTable.invokeTableMethod(
        'setScrollTop',
        index * dingWeiEle.clientHeight,
      );
      this.dangQingDWYPIndex = index;
    },
    // 定位切换
    handleEnter() {
      let length = this.dingWeiYPIndexArr.length;
      if (this.query.likeQuery && length > 0) {
        let index = this.dingWeiYPIndexArr.findIndex(
          (item) => item === this.dangQingDWYPIndex,
        );
        if (index === length - 1) {
          index = 0;
        } else index++;
        let dingWeiEle = this.tableBodyEle.querySelectorAll(
          `#panCunXQTable .${this.cssPrefix}-base-table__row`,
        )[index];
        this.setDingWeiScroll(
          this.tableBodyEle,
          dingWeiEle,
          this.dingWeiYPIndexArr[index],
        );
      }
    },
    // 药品快速定位处理
    handleKuaiSuDW(data, el) {
      //清空
      if (!data) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
        return;
      }
      // 获取doom
      if (!this.tableBodyEle) {
        this.tableBodyEle = this.$refs.panCunXQTable.$el.querySelector(
          `.mediinfo-vela-yaoku-web-base-table__body-wrapper .mediinfo-vela-yaoku-web-base-table__body`,
        );
      }
      // 去掉上一次定位样式
      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item) => {
          this.clearDingWeiClass(this.tableBodyEle, item);
        });
        this.dingWeiYPIndexArr = [];
      }
      //寻找index
      this.dingWeiYPIndexArr = this.tableData.reduce((pre, item, index) => {
        if (item.jiaGeID === data.jiaGeID) {
          pre.push(index);
        }
        return pre;
      }, []);
      if (this.dingWeiYPIndexArr.length > 0) {
        this.dingWeiYPIndexArr.forEach((item, index) => {
          this.setDingWeiClass(this.tableBodyEle, item, index === 0);
        });
      } else {
        MdMessage({
          type: 'warning',
          message: '未找到该药品！',
        });
      }
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
    'biz-yaopindw': BizYaopindw,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-pancun-drawer {
  position: initial !important;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-pancun-drawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;
  .#{$md-prefix}-drawer__alias {
    display: flex;
    flex-direction: column;
    height: 100%;
    .#{$md-prefix}-pandun-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;
      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;
      }
      .#{$md-prefix}-title-right {
        padding-right: 12px;
      }
      .#{$md-prefix}-title-toolbar {
        margin-right: 14px;
        .md-button {
          margin-left: 4px;
        }
      }
      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #666666;
          cursor: pointer;
        }
      }
    }
    .#{$md-prefix}-content__alias {
      flex: 1;
      display: flex;
      flex-direction: column;
      // margin-top: 10px;
      padding: 0 8px 8px 8px;
      min-height: 0;
      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }
      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
        padding: 7px 0rem;
      }
      .#{$md-prefix}-content-table {
        flex: 1;
        min-height: 0;
      }
      ::v-deep .#{$md-prefix}-dingwei-bg {
        background: #e2efff !important;
        > td {
          background: #e2efff !important;
        }
      }
      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        margin: 3px 8px 0 0;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        &.#{$md-prefix}-shouli {
          background-color: #e2efff;
          color: #1e88e5;
        }
        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }
      .#{$md-prefix}-description {
        display: flex;
        &-item {
          line-height: 20px;
          min-height: 20px;
          font-size: 14px;
          color: #333;
          padding: 5px 0;
          &__label {
            color: #aaa;
            margin-left: 8px;
          }
          &__content {
            padding-left: 5px;
            &.#{$md-prefix}-fontWeight {
              font-weight: bold;
            }
            .#{$md-prefix}-content-color {
              color: #aaa;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
}
.#{$md-prefix}-pos-bottom-direction {
  justify-content: space-between;
}
::v-deep .#{$md-prefix}-scrollbar__view {
  height: 100%;
}
::v-deep .#{$md-prefix}-descriptions {
  flex: 1;
}
::v-deep .#{$md-prefix}-description-item {
  color: #222;
}
::v-deep .#{$md-prefix}-description-item {
  font-size: 14px;
}
</style>
