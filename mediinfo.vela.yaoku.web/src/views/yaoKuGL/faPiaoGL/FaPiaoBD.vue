<template>
  <div :class="prefixClass('fapiaobd-wrap')" v-loading="pageLoading">
    <div :class="prefixClass('fapiaobd-left')">
      <md-input
        v-model="GongHuoDWMC"
        @keyup.enter.native="getBuDengFPList"
        placeholder="输入供货单位搜索"
      >
        <i
          slot="suffix"
          :class="prefixClass('input__icon icon-seach')"
          @click="getBuDengFPList"
        ></i>
      </md-input>
      <biz-list
        v-loading="loading"
        :dataList="gongHuoDWList"
        :total="listToal"
        titleKey="gongHuoDWMC"
        idKey="gongHuoDWID"
        :pageSize.sync="pageSize"
        :current-page.sync="pageIndex"
        :class="prefixClass('bizlist')"
        ref="bizList"
        @rowClick="handleListRowClick"
        @current-change="handleCurrentChange"
      />
    </div>
    <div :class="prefixClass('fapiaobd-right')">
      <div :class="prefixClass('fapiaobd-right-filter')">
        <div :class="prefixClass('repiaobd-right-div')">
          <span>发票号码</span>
          <md-input v-model="faPiaoHM" placeholder="请输入" />
        </div>
        <div :class="prefixClass('repiaobd-right-div')">
          <span>发票日期</span>
          <md-date-picker
            v-model="faPiaoRQ"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            :pickerOptions="pickerOptions"
          >
          </md-date-picker>
        </div>
        <div :class="prefixClass('repiaobd-right-div')">
          <span>发票金额</span>
          <md-input :value="faPiaoJE" disabled />
        </div>
        <div :class="prefixClass('repiaobd-right-div nowidth')">
          <md-button type="primary" @click="handleSave">保存</md-button>
        </div>
      </div>
      <div :class="prefixClass('fapiaobd-right-table')">
        <div :class="prefixClass('fapiaobd-right-table-top')">
          <p>请选择发票对应的明细</p>
          <span
            >已选：<b>{{ selection.length }}</b
            >种药品 合计进价金额：<b>{{ faPiaoJE }}</b
            >元 零售金额：<b>{{ lingShouJE }}</b
            >元
          </span>
          <md-input
            v-model="tableSearchText"
            placeholder="输入药品或入库单快速定位"
            @keyup.enter.native="handleSearchYP"
          >
            <i
              slot="suffix"
              :class="prefixClass('input__icon icon-seach')"
              @click="handleSearchYP"
            />
          </md-input>
        </div>
        <div :class="prefixClass('fapiaobd-right-table-content')">
          <md-table-pro
            :columns="columns"
            height="100%"
            ref="table"
            :autoLoad="false"
            :onFetch="handleFetch"
            @selection-change="handleSelect"
          ></md-table-pro>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';

import {
  GetBuDengPMXCountByGHDW,
  GetBuDengPMXListByGHDW,
  getBuDengFPCount,
  getBuDengFPList,
  updateBuDengFP,
} from '@/service/yaoPinYK/faPiaoGL';
import BizList from '@/views/yaoKuGL/components/BizList';

export default {
  name: 'fapiaobd',
  props: {
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    jinJiaXSDW: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      GongHuoDWMC: '',
      faPiaoHM: null,
      faPiaoRQ: null,
      gongHuoDWList: [],
      tableData: [],
      selectTableData: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      columns: [
        {
          type: 'selection',
          width: 36,
          align: 'center',
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 35,
          align: 'center',
          formatter: (row, column, cellValue) => {
            return cellValue ? cellValue.slice(0, 1) : '';
          },
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称与规格',
          minWidth: 240,
          showOverflowTooltip: true,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 160,
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          minWidth: 50,
        },
        {
          prop: 'ruKuSL',
          label: '数量',
          minWidth: 60,
        },
        {
          prop: 'jinJia',
          label: '进价',
          minWidth: 80,
          formatter: (row, cell, cellValue) => {
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          minWidth: 100,
          formatter: (row, cell, cellValue) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'zhiDanSJ',
          label: '入库日期',
          width: 108,
          formatter: (row, cell, cellValue) => {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          minWidth: 150,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          minWidth: 108,
          formatter: (row, cell, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'ruKuDH',
          label: '入库单号',
          minWidth: 115,
        },
      ],
      listToal: 0,
      pageSize: 10,
      pageIndex: 1,
      tableSearchText: '',
      currentGongHuoDWID: null,
      selection: [],
      loading: false,
      pageLoading: false,
    };
  },
  computed: {
    faPiaoJE() {
      let jinE = 0;
      this.selection.forEach((item) => {
        jinE = +item.jinJiaJE + jinE;
      });
      return jinE.toFixed(3);
    },
    lingShouJE() {
      let lingShouJE = 0;
      this.selection.forEach((item) => {
        lingShouJE = +item.lingShouJE + lingShouJE;
      });
      return lingShouJE.toFixed(3);
    },
  },
  created() {
    this.getData();
  },
  methods: {
    handleSearchYP() {
      this.$refs.table.search({ pageSize: 100 });
    },
    handleSelect(selection) {
      this.selection = selection;
    },
    handleSave() {
      const rows = this.selection;
      const idList = rows.map((item) => item.id);
      // if (!this.faPiaoHM && !this.faPiaoRQ) {
      //   MdMessage({
      //     message: '请输入发票号与发票日期！',
      //     type: 'warning',
      //   })
      //   return
      // }
      if (!this.faPiaoHM) {
        MdMessage({
          message: '请输入发票号码!',
          type: 'warning',
        });
        return;
      }
      // if (!this.faPiaoRQ) {
      //   MdMessage({
      //     message: '请选择发票日期!',
      //     type: 'warning',
      //   })
      //   return
      // }
      if (!idList || idList.length === 0) {
        MdMessage({
          message: '请勾选发票对应的药品!',
          type: 'warning',
        });
        return;
      }
      this.pageLoading = true;
      const params = {
        faPiaoHM: this.faPiaoHM,
        faPiaoRQ: this.faPiaoRQ,
        idList: idList,
      };
      updateBuDengFP(params)
        .then((res) => {
          MdMessage({
            type: 'success',
            message: '保存成功',
          });
          this.getData();
        })
        .catch((e) => {
          MdMessage({
            message: e,
            type: 'warning',
          });
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    handleCurrentChange(currentPage) {
      this.pageIndex = currentPage;
      this.getBuDengFPList();
    },
    handleListRowClick(row) {
      this.currentGongHuoDWID = row.gongHuoDWID;
      this.$refs.table.search();
    },
    getData() {
      this.faPiaoHM = null;
      this.faPiaoRQ = null;
      this.getBuDengFPList();
    },
    async handleFetch({ page, pageSize }, config) {
      const params = {
        GongHuoDWID: this.currentGongHuoDWID,
        PageSize: pageSize,
        PageIndex: page,
        LikeQuery: this.tableSearchText,
      };
      let [items, total] = [[], 0];
      if (this.gongHuoDWList.length && this.gongHuoDWList.length > 0) {
        [items, total] = await Promise.all([
          GetBuDengPMXListByGHDW(params, config),
          GetBuDengPMXCountByGHDW(params, config),
        ]);
      }
      return {
        items,
        total,
      };
    },
    getBuDengFPList() {
      this.loading = true;
      const params = {
        GongHuoDWMC: this.GongHuoDWMC,
        PageSize: this.pageSize,
        PageIndex: this.pageIndex,
      };
      this.gongHuoDWList = [];
      getBuDengFPList(params)
        .then((res) => {
          this.gongHuoDWList = res;
          if (res.length > 0) {
            const id = res[0].gongHuoDWID;
            this.currentGongHuoDWID = id;
            this.$refs.bizList.setCurrentSelect(id);
          }
          this.handleSearchYP();
        })
        .finally(() => {
          this.loading = false;
        });
      getBuDengFPCount(params).then((res) => {
        this.listToal = res;
      });
    },
    handleSearch() {},
  },
  components: {
    'biz-list': BizList,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-fapiaobd-wrap {
  display: flex;
  height: 100%;
  background: #fff;
  .#{$md-prefix}-fapiaobd-left {
    display: flex;
    flex-shrink: 0;
    width: 280px;
    box-sizing: border-box;
    flex-direction: column;
    padding: 8px;
    .#{$md-prefix}-bizlist {
      margin-top: 8px;
      flex: 1;
      min-height: 0;
    }
  }
  .#{$md-prefix}-fapiaobd-right {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    min-width: 0;
    box-sizing: border-box;
    border: 8px solid #f0f2f5;
    .#{$md-prefix}-fapiaobd-right-filter {
      display: flex;
      width: 100%;
      height: 46px;
      // background: #e4f0fb;
      background-color: rgb(var(--md-color-1));
      border: 1px solid;
      // border-color: #dddddd;
      border-color: rgb(var(--md-color-6));
      .#{$md-prefix}-repiaobd-right-div {
        display: flex;
        width: 220px;
        // margin: 0 8px;
        margin-left: 12px;
        align-items: center;
        &:first-of-type {
          margin-left: 8px;
        }
        span {
          flex-shrink: 0;
          margin-right: 8px;
        }

        &.#{$md-prefix}-nowidth {
          width: auto;
        }
      }
    }
    .#{$md-prefix}-fapiaobd-right-table {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0;
      padding: 8px;
      padding-top: 0;
      .#{$md-prefix}-fapiaobd-right-table-top {
        position: relative;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 14px 8px;
        p {
          color: #666666;
          // font-size: 14px;
          font-size: var(--md-font-2);
        }
        span {
          display: inline-block;
          padding: 0 4px;
          margin-left: 12px;
          color: #aaaaaa;
          // font-size: 14px;
          font-size: var(--md-font-2);
          // background-color: #e4f0fb;
          background-color: var(--md-color-1);
          b {
            color: #000;
          }
        }
        .#{$md-prefix}-input {
          position: absolute;
          right: 8px;
          width: 240px;
        }
      }
      .#{$md-prefix}-fapiaobd-right-table-content {
        flex: 1;
        min-height: 0;
        box-sizing: border-box;
      }
    }
  }
}
</style>
