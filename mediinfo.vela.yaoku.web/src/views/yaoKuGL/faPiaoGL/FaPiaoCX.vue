<template>
  <div :class="prefixClass('fapiaocx')">
    <div :class="prefixClass('fapiaocx-filter')">
      <div :class="prefixClass('repiaobd-right-div')">
        <span>发票号</span>
        <md-autocomplete
          v-model="faPiaoHMObj"
          :fetch-suggestions="remoteMethodDanWeiMC"
          placeholder="请输入内容"
          :clearable="true"
          :trigger-on-focus="false"
          style="width: 100%"
          @clear="handleClrear"
          @select="handleSelect"
          ref="autocomplete"
        ></md-autocomplete>
      </div>
      <div :class="prefixClass('repiaobd-right-div')" v-show="hasFaPiao">
        <span>发票日期</span>
        <span>{{ faPiaoRQ }}</span>
      </div>
      <div :class="prefixClass('repiaobd-right-div')" v-show="hasFaPiao">
        <span>发票金额</span>
        <span>{{ calcJinE }}</span>
      </div>
    </div>

    <div :class="prefixClass('fapiaocx-table')" v-show="hasFaPiao">
      <div :class="prefixClass('fapiaocx-table-top')">
        <biz-yaopindw
          v-model="tableSearchText"
          :class="prefixClass('yaopin-search')"
          @change="handleDingWei"
          style="width: 240px"
          showSuffix
        />
        <md-button
          type="primary"
          :class="prefixClass('f-right')"
          @click="handleSave"
          :loading="tableLoading"
          >保存</md-button
        >
        <md-button
          type="text"
          :icon="prefixClass('icon-shanchuwap')"
          plain
          style="margin-right: 8px"
          :class="prefixClass('f-right')"
          @click="handleDelete"
          :loading="tableLoading"
          >删除</md-button
        >
      </div>
      <div :class="prefixClass('fapiaocx-table-content')">
        <md-editable-table-pro
          v-table-enter
          v-loading="tableLoading"
          :columns="columns"
          v-model="tableData"
          style="height: 100%"
          height="100%"
          :hideAddButton="true"
          :showDefaultOperate="false"
          :new-row="newRow"
          :autoFill="true"
          highlight-current-row
          @selection-change="handleTableSelect"
          ref="table"
        >
          <template #faPiaoRQ="{ row, cellRef }">
            <md-date-picker
              v-model="row.faPiaoRQ"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="cellRef.endEdit()"
              @blur="cellRef.endEdit()"
            >
            </md-date-picker>
          </template>
          <template #yaoPinLXMC="{ row, $index }">
            <span :data-name="'scroll-' + $index">{{ row.yaoPinLXMC }}</span>
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('table-append')">
        共计：<span>{{ tableData.length }}</span
        >种药品 合计 进价金额：<span>{{ calcJinE }} </span>元 零售金额：<span>{{
          calcLingShouJE
        }}</span
        >元
      </div>
    </div>
    <div :class="prefixClass('fapiaocx-empty-img')" v-show="!hasFaPiao">
      <div class="img-box">
        <img
          src="@/assets/images/wufapiao.svg"
          :class="prefixClass('wufapiaoimg')"
          alt="..."
        />
        <p>请先选择查询的发票</p>
      </div>
    </div>
  </div>
</template>

<script>
import { MdMessage,MdInput } from '@mdfe/medi-ui';
import dayjs from 'dayjs';

import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  getWeiJieSYPMXListByFP,
  updatePiLiangFPXX,
  GetFaPiaoListByFPHMLike,
} from '@/service/yaoPinYK/faPiaoGL';

import { debounce } from 'lodash';

export default {
  name: 'fapiaocx',
  props: {
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    jinJiaXSDW: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      faPiaoHMObj: '',
      faPiaoHM: '',
      tableSearchText: {},
      tableData: [],
      newRow: () => {
        return {};
      },
      columns: [
        {
          type: 'selection',
        },
        {
          slot: 'yaoPinLXMC',
          label: '',
          width: 50,
          type: 'text',
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          minWidth: 140,
          component: MdInput,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'faPiaoRQ',
          slot: 'faPiaoRQ',
          label: '发票日期',
          minWidth: 108,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          endMode: 'custom',
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称与规格',
          minWidth: 255,
          type: 'text',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 160,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          minWidth: 60,
          type: 'text',
        },
        {
          prop: 'ruKuSL',
          label: '数量',
          minWidth: 60,
          type: 'text',
        },
        {
          prop: 'jinJia',
          label: '进价',
          minWidth: 100,
          type: 'text',
          align: 'right',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          minWidth: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'zhiDanSJ',
          label: '入库日期',
          minWidth: 108,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          minWidth: 120,
          type: 'text',
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          minWidth: 108,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'ruKuDH',
          label: '入库单号',
          minWidth: 150,
          type: 'text',
        },
      ],
      tableLoading: false,
      selection: [],
      deleteData: [],
    };
  },
  computed: {
    hasFaPiao() {
      return this.tableData.length > 0 || this.deleteData.length > 0;
    },
    calcJinE() {
      let n = 0;
      this.tableData.forEach((item) => {
        n = +item.jinJiaJE + n;
      });
      return n.toFixed(this.jinJiaJEXSDW);
    },
    calcLingShouJE() {
      let n = 0;
      this.tableData.forEach((item) => {
        n = +item.lingShouJE + n;
      });
      return n.toFixed(this.lingShouJEXSDW);
    },
    faPiaoRQ() {
      if (this.tableData?.length === 0) {
        return '-';
      } else {
        return this.tableData[0].faPiaoRQ
          ? dayjs(this.tableData[0]?.faPiaoRQ).format('YYYY-MM-DD')
          : '-';
      }
    },
  },
  methods: {
    //业务搜索
    remoteMethodDanWeiMC: debounce(async function (queryString, cb) {
      const params = {
        faPiaoHMLike: queryString,
      };
      let list = await GetFaPiaoListByFPHMLike(params);
      let arr = list.map((data) => {
        return {
          value: data,
          id: data,
        };
      });
      cb(arr);
      if (arr.length == 0) {
        this.form.data.danWeiID = '';
      }
      return arr.length;
    }, 200),
    handleSelect(val) {
      this.faPiaoHM = val.id ? val.value : '';
      this.handleSearch();
    },
    handleClrear() {
      this.faPiaoHM = '';
      this.handleSearch();
    },
    handleDingWei(data) {
      if (!data) return;
      const yaoPinMC = data.yaoPinMC;
      let rowIndex = null;
      rowIndex = this.tableData.findIndex((item) => item.yaoPinMC === yaoPinMC);
      if (rowIndex < 0) {
        MdMessage({
          message: '未找到此药品信息！',
          type: 'warning',
        });
        return;
      }
      this.$refs.table.invokeTableMethod(
        'setCurrentRow',
        this.tableData[rowIndex],
      );
    },
    handleSave() {
      this.tableLoading = true;
      let params = this.tableData.map((item) => {
        return {
          faPiaoHM: item.faPiaoHM,
          faPiaoRQ: item.faPiaoRQ,
          id: item.id,
          caoZuoLX: '1',
        };
      });
      params = params.concat(this.deleteData);
      updatePiLiangFPXX(params)
        .then((res) => {
          MdMessage({
            message: '保存成功！',
            type: 'success',
          });
          this.handleSearch();
        })
        .catch((e) => {
          MdMessage({
            message: e,
            type: 'warning',
          });
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    handleDelete() {
      const selection = this.selection;
      const params = selection.map((item) => {
        return {
          faPiaoHM: item.faPiaoHM,
          faPiaoRQ: item.faPiaoRQ,
          id: item.id,
          caoZuoLX: '2',
        };
      });
      this.deleteData = this.deleteData.concat(params);
      this.tableData = this.tableData.filter((item) => {
        return !this.selection.includes(item);
      });

      // updatePiLiangFPXX(params)
      //   .then((res) => {
      //     Message.success('删除成功')
      //     this.handleSearch()
      //   })
    },
    handleTableSelect(selection) {
      this.selection = selection;
    },
    handleSearch() {
      if (this.faPiaoHM) {
        this.tableLoading = true;
        this.deleteData = [];
        const params = {
          faPiaoHM: this.faPiaoHM,
        };
        getWeiJieSYPMXListByFP(params)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
      } else {
        this.tableData = [];
        MdMessage({
          type: 'warning',
          message: '请输入发票号查询！',
        });
      }
    },
  },
  components: {
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-f-right {
  float: right;
}
.#{$md-prefix}-fapiaocx {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  min-width: 0;
  box-sizing: border-box;
  border: 8px solid #f0f2f5;
  background-color: #fff;
  .#{$md-prefix}-fapiaocx-filter {
    display: flex;
    width: 100%;
    height: 46px;
    background-color: rgb(var(--md-color-1));
    .#{$md-prefix}-repiaobd-right-div {
      display: flex;
      margin: 0 8px;
      align-items: center;
      span {
        line-height: 46px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      &.nowidth {
        width: auto;
      }
    }
  }
  .#{$md-prefix}-fapiaocx-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    background: #fff;
    .#{$md-prefix}-fapiaocx-table-top {
      box-sizing: border-box;
      padding: 8px;
      p {
        color: #666666;
        font-size: 14px;
      }
      span {
        display: inline-block;
        padding: 0 4px;
        margin-left: 12px;
        color: #aaaaaa;
        font-size: 14px;
        background-color: #e4f0fb;
        b {
          color: #000;
        }
      }
      .#{$md-prefix}-input {
        width: 240px;
      }
    }
    .#{$md-prefix}-fapiaocx-table-content {
      min-height: 0;
      flex: 1;
      overflow: hidden;
    }
    ::v-deep .#{$md-prefix}-editable-table {
      .#{$md-prefix}-table {
        height: 100%;
      }
    }
  }
}
.#{$md-prefix}-fapiaocx-empty-img {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  justify-content: center;
  align-items: center;
  .img-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  .#{$md-prefix}-wufapiaoimg {
    width: 120px;
    height: 120px;
    filter: drop-shadow(200px 0 #ccc);
    transform: translateX(-200px);
  }
  p {
    font-size: 14px;
    line-height: 20px;
    color: #aaaaaa;
    margin-top: 16px;
  }
}
.#{$md-prefix}-yaopin-search {
  display: inline-block;
}
.#{$md-prefix}-table-append {
  text-align: right;
  color: #aaaaaa;
  font-size: 14px;
  line-height: 20px;
  box-sizing: border-box;
  padding: 8px;
  span {
    color: #000;
    font-weight: bold;
  }
}
</style>
