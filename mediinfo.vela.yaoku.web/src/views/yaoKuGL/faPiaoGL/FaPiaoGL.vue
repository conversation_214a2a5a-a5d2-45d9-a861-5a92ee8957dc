<template>
  <md-tabs v-model="activeName" :class="prefixClass('tabs-views')">
    <md-tab-pane label="发票补登" name="first" lazy>
      <fapiaobd
        :jinJiaXSDW="jinJiaXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        :lingShouJEXSDW="lingShouJEXSDW"
      />
    </md-tab-pane>
    <md-tab-pane label="发票查询" name="second" lazy>
      <fapiaocx
        :jinJiaXSDW="jinJiaXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        :lingShouJEXSDW="lingShouJEXSDW"
      />
    </md-tab-pane>
  </md-tabs>
</template>

<script>
import FaPiaoBD from './FaPiaoBD';
import FaPiaoCX from './FaPiaoCX';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
export default {
  name: 'fapiaogl',
  data() {
    return {
      activeName: 'first',
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
    };
  },
  async mounted() {
    const arr = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
  },
  components: {
    fapiaobd: FaPiaoBD,
    fapiaocx: FaPiaoCX,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
}
::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  min-height: 0;
  background: #eaeff3;
}
</style>
