<template>
  <div class="HISYK-zhuanKeZYYPGL">
    <div class="HISYK-zhuanKeZYYPGL-header">
      <div class="HISYK-zhuanKeZYYPGL-header-left">
        <BizYaoPinDW
          v-model="yaoPinMC"
          placeholder="药品名称与规格"
          type="zkzy"
          class="HISYK-mr8 wid"
          @change="handleYaoPin"
        />
        <md-checkbox
          v-model="qiYongBZ"
          :true-label="1"
          :false-label="0"
          label="仅展示启用规则"
          @change="handleSearch"
        ></md-checkbox>
      </div>
      <div>
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          class="HISYK-mr8"
          @click="handleSearch()"
          >刷新</md-button
        >
        <md-button type="primary" @click="handleAdd('add')">新增权限</md-button>
      </div>
    </div>
    <div class="HISYK-zhuanKeZYYPGL-content">
      <md-table-pro
        element-loading-text="正在加载中..."
        height="100%"
        :columns="columns"
        autoLoad
        ref="tablePro"
        :onFetch="handleFetch"
      >
        <template v-slot:qiYongBZ="{ row }">
          <md-switch
            v-model="row.qiYongBZ"
            :activeValue="1"
            :inactiveValue="0"
            @change="handleSwitchChange(row)"
          ></md-switch>
        </template>
        <template v-slot:yaoPinMC="{ row }">
          <div>{{ row.yaoPinMC }} {{ row.yaoPinGG }}</div>
        </template>
      </md-table-pro>
    </div>
    <ZKZYDialog ref="addDialog" />
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { MdMessage } from '@mdfe/medi-ui';
import ZKZYDialog from './components/ZKZYDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  GetZhuanKeZYList,
  GetZhuanKeZYCount,
  UpdateZhuanKeZYQYBZ,
} from '@/service/yaoPin/zhuanKeZY';
defineOptions({ name: 'ZhuanKeZYYPGL' });
const columns = Object.freeze([
  {
    label: '',
    prop: 'yaoPinLXMC',
    width: 50,
    align: 'center',
    formatter(v) {
      return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
    },
  },
  {
    label: '药品名称与规格',
    prop: 'yaoPinMC',
    slot: 'yaoPinMC',
    'min-width': 300,
    showOverflowTooltip: true,
  },
  {
    label: '产地名称',
    prop: 'chanDiMC',
    'min-width': 250,
    showOverflowTooltip: true,
  },
  {
    label: '单位',
    prop: 'baoZhuangDW',
    width: 80,
  },
  {
    label: '院内编码',
    prop: 'yuanNeiBM',
    width: 200,
  },
  {
    label: '启用',
    prop: 'qiYongBZ',
    slot: 'qiYongBZ',
    width: 50,
    align: 'center',
  },
  {
    type: 'operate',
    label: '操作',
    width: 60,
    count: 1,
    align: 'center',
    actions: [
      {
        text: '编辑',
        onPressed: ({ row }) => {
          handleAdd('edit', row);
        },
      },
    ],
  },
]);
let yaoPinMC = reactive({});
let qiYongBZ = ref(1);
const handleFetch = async ({ page, pageSize }, config) => {
  const params = {
    pageSize: pageSize,
    pageIndex: page,
    jiaGeID: yaoPinMC ? yaoPinMC.jiaGeID : '',
    qiYongBZ: qiYongBZ.value ? qiYongBZ.value : null,
  };
  const [items, total] = await Promise.all([
    GetZhuanKeZYList(params, config),
    GetZhuanKeZYCount(params, config),
  ]);
  return {
    items,
    total,
  };
};
const { proxy } = getCurrentInstance();
const handleSearch = () => {
  proxy.$refs.tablePro.search();
};
const handleYaoPin = (value) => {
  yaoPinMC = value ? value : {};
  handleSearch();
};
const handleAdd = async (mode, row) => {
  await proxy.$refs.addDialog.showModel({
    mode: mode,
    params: {
      ...row,
    },
  });
  handleSearch();
};
const handleSwitchChange = async (row) => {
  await UpdateZhuanKeZYQYBZ({
    id: row.id,
    qiYongBZ: row.qiYongBZ,
  });
  if (row.qiYongBZ == 0) {
    MdMessage({
      type: 'success',
      message: '停用成功！',
    });
  } else {
    MdMessage({
      type: 'success',
      message: '启用成功！',
    });
  }
  handleSearch();
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.HISYK-zhuanKeZYYPGL {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--md-spacing-3);
  padding-bottom: 0;

  .HISYK-mr8 {
    margin-right: var(--md-spacing-3);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--md-spacing-3);
    &-left {
      .wid {
        width: 300px;
      }
    }
  }
  &-content {
    flex: 1;
    min-height: 0;
  }
}
</style>
