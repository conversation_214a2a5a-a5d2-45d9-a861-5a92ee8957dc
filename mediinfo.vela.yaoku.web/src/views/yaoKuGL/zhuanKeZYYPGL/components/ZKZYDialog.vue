<template>
  <md-dialog
    v-model="zkzyData.visible"
    :title="title + '权限'"
    :before-close="handleClose"
    size="large"
    :draggable="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    content-padding="0"
    :class="prefixClass('ZhuanKeZYYPGL-dialog')"
  >
    <div>
      <div :class="prefixClass('ZhuanKeZYYPGL-dialog-title')">
        <span>药品名称与规格</span>
        <BizYaoPinDW
          v-model="zkzyData.yaoPinMC"
          v-if="zkzyData.visible"
          labelKey="yaoPinZC"
          placeholder="药品名称与规格"
          type="zkzy"
          :columnsList="columnsList"
          :disabled="zkzyData.formData.id ? true : false"
          class="HISYK-mr8"
          @change="handleSearchYPMCYGG"
        />
        <div :class="prefixClass('ZhuanKeZYYPGL-dialog-subtitle')">
          <span>产地名称：{{ zkzyData.formData.chanDiMC || '-' }}</span
          >单位：{{ zkzyData.formData.baoZhuangDW || '-' }}
        </div>
      </div>
      <div :class="prefixClass('ZhuanKeZYYPGL-dialog-content')">
        <md-tabs
          v-model="zkzyData.shiYongFWDM"
          type="card"
          :before-leave="handleTab"
        >
          <md-tab-pane
            v-for="item in zkzyData.shiYongFWOptions"
            :label="item.biaoZhunMC"
            :name="item.biaoZhunDM"
            :key="item.biaoZhunDM"
          ></md-tab-pane>
        </md-tabs>
        <div :class="prefixClass('ZhuanKeZYYPGL-dialog-tab')">
          <div :class="prefixClass('ZhuanKeZYYPGL-dialog-tab-radio')">
            <span>限制类型</span>
            <md-radio-group
              v-model="zkzyData.xianZhiLXDM"
              border
              @change="leiXingChange"
              :class="prefixClass('ZhuanKeZYYPGL-dialog-tab-radio-group')"
            >
              <md-radio
                v-for="item in zkzyData.xianZhiLXOptions"
                :label="item.biaoZhunDM"
                :key="item.biaoZhunDM"
                >{{ item.biaoZhunMC }}</md-radio
              >
            </md-radio-group>
          </div>
          <md-table
            v-show="zkzyData.shiYongFWDM === '1'"
            :columns="ksColumns"
            :data="zkzyData.ksList"
            edit
            class="zkzy_table"
          >
            <template v-slot:keShiMC="{ row }">
              <SelectTable
                v-model="row.detail"
                :columns="ksColumns.slice(0, 2)"
                remote
                filterable
                :fetchData="handleFetchKS"
                value-key="id"
                label-key="keShiMC"
                clearable
                placeholder="请选择"
                @change="handleKS($event, row)"
              />
            </template>
            <template v-slot:keShiID="{ row }">
              <md-input v-model="row.keShiID" disabled></md-input>
            </template>
          </md-table>
          <md-table
            v-show="zkzyData.shiYongFWDM === '2'"
            :columns="ysColumns"
            :data="zkzyData.ysList"
            edit
            class="zkzy_table"
          >
            <template v-slot:yongHuXM="{ row }">
              <SelectTable
                v-model="row.detail"
                :columns="ysColumns.slice(0, 2)"
                remote
                filterable
                :fetchData="handleFetchYS"
                value-key="yongHuID"
                label-key="yongHuXM"
                clearable
                placeholder="请选择"
                @change="handleYS($event, row)"
              />
            </template>
            <template v-slot:yongHuID="{ row }">
              <md-input v-model="row.yongHuDLM" disabled></md-input>
            </template>
          </md-table>
          <md-table
            v-show="zkzyData.shiYongFWDM === '3'"
            :columns="hzColumns"
            :data="zkzyData.hzList"
            edit
            class="zkzy_table"
          >
            <template v-slot:xingMing="{ row }">
              <SelectTable
                v-model="row.detail"
                :columns="hzSelcetColumns"
                remote
                filterable
                :fetchData="handleFetchHZ"
                value-key="id"
                label-key="xingMing"
                clearable
                placeholder="请选择"
                @change="handleHZ($event, row)"
              />
            </template>
            <template v-slot:xingBieMC="{ row }">
              <md-input v-model="row.xingBieMC" disabled></md-input>
            </template>
            <template v-slot:shenFenZH="{ row }">
              <md-input v-model="row.shenFenZH" disabled></md-input>
            </template>
          </md-table>
        </div>
      </div>
    </div>
    <template #footer>
      <md-button
        v-if="zkzyData.formData.id"
        type="danger"
        plain
        class="dialog-footer-zuofei"
        @click="handleCancel"
      >
        作废
      </md-button>
      <md-button type="primary" plain @click="handleClose">取消</md-button>
      <md-button type="primary" @click="handleClick"> 保存 </md-button>
    </template>
  </md-dialog>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  watch,
  computed,
  nextTick,
} from 'vue';
import lyra from '@mdfe/lyra';
import { cloneDeep, debounce } from 'lodash';
import { MdMessage, MdMessageBox, useNamespace } from '@mdfe/medi-ui';
import SelectTable from '@mdfe/material.select-table';
import formatJiaGe from '@/system/utils/formatJiaGe';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  getPanDuanZKZYXX,
  getKeShiList,
  getHuanZheList,
  getYiShengList,
  SaveZhuanKeZY,
  GetZhuanKeZYXX,
  ZuoFeiZhuanKeZY,
  UpdateZhuanKeZY,
} from '@/service/yaoPin/zhuanKeZY';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
defineOptions({ name: 'ZhuanKeZYYPGL-dialog' });
//新增时清空药品数据，初始化
const initYaoPin = () => {
  return {
    jiaGeID: null,
    guiGeID: null,
    yaoPinMC: null,
    yaoPinGG: null,
    chanDiID: null,
    chanDiMC: null,
    baoZhuangDW: null,
    yaoPinLXDM: null,
    yaoPinLXMC: null,
    yuanNeiBM: null,
    yaoPinZC: null,
    id: null,
  };
};
const initDetail = () => {
  return {
    keShiID: '',
    keShiMC: '',
    yongHuDLM: '',
    yongHuID: '',
    yongHuXM: '',
    bingRenID: '',
    xingMing: '',
    xingBieDM: '',
    xingBieMC: '',
    shenFenZH: '',
    shunXuHao: '',
    id: '',
  };
};
const ksColumns = Object.freeze([
  {
    slot: 'keShiID',
    prop: 'keShiID',
    label: '科室ID',
  },
  {
    slot: 'keShiMC',
    prop: 'keShiMC',
    label: '科室名称',
  },
  {
    type: 'operate',
    label: '',
    width: 50,
    count: 1,
    align: 'center',
    actions: [
      {
        icon: useNamespace('icon').b('shanchu'),
        type: 'danger',
        onPressed: (props) => {
          delRow(zkzyData.ksList, props.$index);
        },
      },
    ],
  },
]);
const ysColumns = Object.freeze([
  {
    slot: 'yongHuID',
    prop: 'yongHuDLM',
    label: '工号',
  },
  {
    slot: 'yongHuXM',
    prop: 'yongHuXM',
    label: '医生名称',
  },
  {
    type: 'operate',
    label: '',
    width: 50,
    count: 1,
    align: 'center',
    actions: [
      {
        icon: useNamespace('icon').b('shanchu'),
        type: 'danger',
        onPressed: (props) => {
          delRow(zkzyData.ysList, props.$index);
        },
      },
    ],
  },
]);
const hzColumns = Object.freeze([
  {
    slot: 'xingMing',
    prop: 'xingMing',
    label: '患者姓名',
  },
  {
    slot: 'xingBieMC',
    prop: 'xingBieMC',
    label: '性别',
    width: 100,
  },
  {
    slot: 'shenFenZH',
    prop: 'shenFenZH',
    label: '身份证号',
    width: 300,
  },
  {
    type: 'operate',
    label: '',
    width: 50,
    count: 1,
    align: 'center',
    actions: [
      {
        icon: useNamespace('icon').b('shanchu'),
        type: 'danger',
        onPressed: (props) => {
          delRow(zkzyData.hzList, props.$index);
        },
      },
    ],
  },
]);
const hzSelcetColumns = Object.freeze([
  {
    prop: 'xingMing',
    label: '姓名',
    width: 150,
  },
  {
    prop: 'chuShengRQ',
    label: '出生日期',
    width: 100,
  },
  {
    prop: 'shenFenZH',
    label: '身份证号',
    width: 300,
  },
  {
    prop: 'jiGuanDZXX',
    label: '家庭地址',
    width: 300,
    showOverflowTooltip: true,
  },
]);
const columnsList = Object.freeze([
  {
    label: '',
    prop: 'yaoPinLXMC',
    width: 50,
    align: 'center',
    formatter(v) {
      return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
    },
    showOverflowTooltip: true,
  },
  {
    render: (h, { row }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label =
        row.xianShiXX && row.xianShiXX.tianJiaWZ
          ? row.xianShiXX.tianJiaWZ + ' ' + row.yaoPinMC
          : row.yaoPinMC;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    label: '药品名称',
    prop: 'yaoPinMCYGG',
    width: 400,
    showOverflowTooltip: true,
  },
  {
    label: '规格',
    prop: 'yaoPinGG',
    width: 200,
    formatter(v) {
      if (v.jiaGeID) {
        return v.yaoPinGG;
      } else {
        return '';
      }
    },
    render: (h, { row }) => {
      const tagStyles = (styleData) => {
        let sty = {};
        if (styleData && styleData.jiaCuBZ) {
          sty['font-weight'] = 'bold';
        }
        if (styleData && styleData.xieTiBZ) {
          sty['font-style'] = 'oblique';
        }
        sty.color = styleData ? styleData.ziTiYS : 'unset';
        return sty;
      };
      const label = row.yaoPinGG;
      return h('span', { style: tagStyles(row.xianShiXX) }, label);
    },
    showOverflowTooltip: true,
  },
  {
    label: '产地名称',
    prop: 'chanDiMC',
    width: 140,
    showOverflowTooltip: true,
  },
  {
    label: '单位',
    prop: 'baoZhuangDW',
    width: 50,
  },
  {
    label: '库存量',
    prop: 'kuCunSL',
    align: 'right',
    width: 100,
    formatter: (v) => {
      return v.kuCunSL ? Number(v.kuCunSL).toFixed(3) : Number(0).toFixed(3);
    },
  },
  {
    label: '单价',
    prop: 'danJia',
    width: 70,
    align: 'right',
    formatter: (v) => {
      return v.danJia ? formatJiaGe(v.danJia) : formatJiaGe(0);
    },
  },
]);
const zkzyData = reactive({
  visible: false,
  xianZhiLXDM: '1',
  shiYongFWDM: '1',
  xianZhiLXOptions: [], //限制类型选项
  shiYongFWOptions: [], //适用范围选项
  xianZhiLXDM_KS: '1',
  xianZhiLXDM_YS: '1',
  xianZhiLXDM_HZ: '1',
  delData: [], //编辑时删除掉已有数据
  yaoPinMC: {},
  compareList_KS: [],
  compareList_YS: [],
  compareList_HZ: [],
  formData: {},
  ksList: [],
  ysList: [],
  hzList: [],
});
let resolve;
let reject;
const title = computed(() => {
  return zkzyData.formData.id ? '编辑' : '新增';
});
watch(
  () => zkzyData.ksList,
  () => {
    const list = zkzyData.ksList;
    if (list && list.length > 0) {
      const keShiID = list[list.length - 1].keShiID;
      if (keShiID) {
        addRow(zkzyData.ksList, { ...initDetail(), detail: '' });
      }
    }
  },
  { deep: true },
);
watch(
  () => zkzyData.ysList,
  () => {
    const list = zkzyData.ysList;
    if (list && list.length > 0) {
      const yongHuID = list[list.length - 1].yongHuID;
      if (yongHuID) {
        addRow(zkzyData.ysList, { ...initDetail(), detail: '' });
      }
    }
  },
  { deep: true },
);
watch(
  () => zkzyData.hzList,
  () => {
    const list = zkzyData.hzList;
    if (list && list.length > 0) {
      const bingRenID = list[list.length - 1].bingRenID;
      if (bingRenID) {
        addRow(zkzyData.hzList, { ...initDetail(), detail: '' });
      }
    }
  },
  { deep: true },
);
const addRow = (datas, params) => {
  datas.push({
    ...params,
  });
};
const showModel = async ({ mode, params }) => {
  zkzyData.visible = true;
  zkzyData.params = params;
  //进入页面时重置一些需要清空的数据项
  reset();
  //初始化适用范围与限制类型的字典数据
  ziDianLoad();
  //药品数据初始化
  zkzyData.formData = initYaoPin();
  await nextTick();
  getDetail(mode, params.id);
  return new Promise((resolves, rejects) => {
    resolve = resolves;
    reject = rejects;
  });
};
//父组件调用子组件方法，需要在子组件中使用defineExpose把方法抛出
defineExpose({
  showModel,
});
//编辑时获取数据详情及数据处理
const getDetail = async (mode, ids) => {
  if (mode == 'edit') {
    const res = await GetZhuanKeZYXX({ id: ids });
    let {
      baoZhuangDW,
      chanDiID,
      chanDiMC,
      jiaGeID,
      guiGeID,
      yaoPinMC,
      yaoPinGG,
      id,
    } = res;
    Object.assign(zkzyData.formData, {
      baoZhuangDW,
      chanDiID,
      chanDiMC,
      jiaGeID,
      guiGeID,
      yaoPinMC,
      yaoPinGG,
      id,
    });
    zkzyData.formData.yaoPinZC = res.yaoPinMC + ' ' + res.yaoPinGG;
    zkzyData.yaoPinMC = zkzyData.formData;
    if (res.zhuanKeZYMXList && res.zhuanKeZYMXList.length) {
      res.zhuanKeZYMXList.forEach((e) => {
        let {
          xianZhiLXDM,
          xianZhiLXMC,
          shiYongFWDM,
          shiYongFWMC,
          keShiID,
          id,
          keShiMC,
          yongHuDLM,
          yongHuID,
          yongHuXM,
          xingMing,
          xingBieMC,
          shenFenZH,
          bingRenID,
          xingBieDM,
        } = e;
        if (e.shiYongFWDM === '1') {
          zkzyData.ksList.push({
            xianZhiLXDM,
            xianZhiLXMC,
            shiYongFWDM,
            shiYongFWMC,
            keShiID,
            id,
            keShiMC,
            detail: {
              keShiID,
              keShiMC,
            },
          });
          zkzyData.xianZhiLXDM_KS = e.xianZhiLXDM;
        } else if (e.shiYongFWDM === '2') {
          zkzyData.ysList.push({
            xianZhiLXDM,
            xianZhiLXMC,
            shiYongFWDM,
            shiYongFWMC,
            yongHuDLM,
            id,
            yongHuID,
            yongHuXM,
            detail: {
              yongHuDLM,
              yongHuID,
              yongHuXM,
            },
          });
          zkzyData.xianZhiLXDM_YS = e.xianZhiLXDM;
        } else if (e.shiYongFWDM === '3') {
          zkzyData.hzList.push({
            xianZhiLXDM,
            xianZhiLXMC,
            shiYongFWDM,
            shiYongFWMC,
            xingMing,
            xingBieMC,
            shenFenZH,
            id,
            bingRenID,
            xingBieDM,
            detail: {
              xingMing,
              xingBieMC,
              shenFenZH,
              bingRenID,
              xingBieDM,
            },
          });
          zkzyData.xianZhiLXDM_HZ = e.xianZhiLXDM;
        }
      });
    }
    //便于切换适用范围时，展示不同适用范围对应的初始限制类型
    if (zkzyData.shiYongFWDM === '1') {
      zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_KS;
    } else if (zkzyData.shiYongFWDM === '2') {
      zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_YS;
    } else if (zkzyData.shiYongFWDM === '3') {
      zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_HZ;
    }
    //存储编辑时各适用范围对应的初始值，用于保存前比较出前后数据变化的项，只保存变更的项
    zkzyData.compareList_KS = cloneDeep(zkzyData.ksList);
    zkzyData.compareList_YS = cloneDeep(zkzyData.ysList);
    zkzyData.compareList_HZ = cloneDeep(zkzyData.hzList);
  }
  //如果初始时值为空，自动增加一项
  if (!zkzyData.ksList.length) {
    addRow(zkzyData.ksList, { ...initDetail(), detail: '' });
  }
  if (!zkzyData.ysList.length) {
    addRow(zkzyData.ysList, { ...initDetail(), detail: '' });
  }
  if (!zkzyData.hzList.length) {
    addRow(zkzyData.hzList, { ...initDetail(), detail: '' });
  }
};
//清空数据
const reset = () => {
  zkzyData.ksList = [];
  zkzyData.ysList = [];
  zkzyData.hzList = [];
  zkzyData.shiYongFWDM = '1';
  zkzyData.xianZhiLXDM = '1';
  zkzyData.xianZhiLXDM_KS = '1';
  zkzyData.xianZhiLXDM_YS = '1';
  zkzyData.xianZhiLXDM_HZ = '1';
  zkzyData.delData = [];
  zkzyData.compareList_KS = [];
  zkzyData.compareList_YS = [];
  zkzyData.compareList_HZ = [];
};
const { proxy } = getCurrentInstance();
//获取限制类型和适用范围
const ziDianLoad = async () => {
  try {
    const datas = await getYaoPinShuJuYZYList(['YP0101', 'YP0102']);
    zkzyData.xianZhiLXOptions =
      datas && datas[0].zhiYuList ? datas[0].zhiYuList : [];
    zkzyData.shiYongFWOptions =
      datas && datas[1].zhiYuList ? datas[1].zhiYuList : [];
  } catch (error) {
    proxy.$logger.error(error);
  }
};
const handleFetchHZ = async ({ inputValue, page, pageSize }) => {
  let params = {
    likeQuery: inputValue,
    pageSize: pageSize,
    pageIndex: page,
  };
  const result = await getHuanZheList(params);
  return result;
};
const handleFetchYS = async ({ inputValue, page, pageSize }) => {
  let params = {
    likeQuery: inputValue,
    pageSize: pageSize,
    pageIndex: page,
    yongHuLB: 1,
  };
  const result = await getYiShengList(params);
  return result;
};
const handleFetchKS = async ({ inputValue, page, pageSize }) => {
  const { JiGouID } = lyra.getShareDataSync(); //47142000133068311A1001
  let params = {
    zuZhiJGID: JiGouID,
    likeQuery: inputValue,
    pageSize: pageSize,
    pageIndex: page,
    yongHuLB: 1,
  };
  const result = await getKeShiList(params);
  return result;
};
//选择科室时
const handleKS = (data, row) => {
  let arr = cloneDeep(zkzyData.ksList);
  arr.forEach((e) => {
    if (e.keShiID === data.keShiID) {
      MdMessage({
        type: 'error',
        message: `${data.keShiMC}已被选择`,
      });
      nextTick(() => (zkzyData.ksList = cloneDeep(arr)));
    } else {
      row.keShiID = data.keShiID;
      row.keShiMC = data.keShiMC;
    }
  });
};
//选择医生时
const handleYS = (data, row) => {
  let arr = cloneDeep(zkzyData.ysList);
  arr.forEach((e) => {
    if (e.yongHuID === data.yongHuID) {
      MdMessage({
        type: 'error',
        message: `${data.yongHuXM}已被选择`,
      });
      nextTick(() => (zkzyData.ysList = cloneDeep(arr)));
    } else {
      row.yongHuDLM = data.yongHuDLM;
      row.yongHuID = data.yongHuID;
      row.yongHuXM = data.yongHuXM;
    }
  });
};
//选择患者时
const handleHZ = (data, row) => {
  let arr = cloneDeep(zkzyData.hzList);
  arr.forEach((e) => {
    if (e.bingRenID === data.id && e.shenFenZH === data.shenFenZH) {
      MdMessage({
        type: 'error',
        message: `${data.xingMing}已被选择`,
      });
      nextTick(() => (zkzyData.hzList = cloneDeep(arr)));
    } else {
      row.xingMing = data.xingMing;
      row.xingBieMC = data.xingBieMC;
      row.xingBieDM = data.xingBieDM;
      row.shenFenZH = data.shenFenZH;
      row.bingRenID = data.id;
    }
  });
};
//选择药品后验证是否已经存在
const handleSearchYPMCYGG = async (value) => {
  if (value) {
    let {
      jiaGeID,
      guiGeID,
      yaoPinMC,
      yaoPinGG,
      chanDiID,
      chanDiMC,
      baoZhuangDW,
      yaoPinLXDM,
      yaoPinLXMC,
      yuanNeiBM,
      yaoPinZC,
    } = value;
    Object.assign(zkzyData.formData, {
      jiaGeID,
      guiGeID,
      yaoPinMC,
      yaoPinGG,
      chanDiID,
      chanDiMC,
      baoZhuangDW,
      yaoPinLXDM,
      yaoPinLXMC,
      yuanNeiBM,
      yaoPinZC,
    });
    const result = await getPanDuanZKZYXX({ jiaGeID: value.jiaGeID });
    if (!result) {
      MdMessage.error('药品专科专用信息已经存在，不允许再次新增！');
      zkzyData.formData = initYaoPin();
    }
  } else {
    zkzyData.formData = initYaoPin();
    zkzyData.formData.yaoPinMC = '';
  }
};
const handleClose = () => {
  zkzyData.yaoPinMC = {};
  zkzyData.visible = false;
};
const handleCancel = async () => {
  await MdMessageBox.confirm('', '确定作废该数据?', {
    confirmButtonText: '作废',
    cancelButtonText: '取消',
    type: 'warning',
  });
  await ZuoFeiZhuanKeZY({ id: zkzyData.params.id });
  MdMessage({
    type: 'success',
    message: '作废成功!',
  });
  resolve(true);
  handleClose();
};
const saveLoading = ref(false);
//防抖和loading
const handleClick = debounce(function () {
  handleSave();
}, 1000);
const handleSave = async () => {
  if (saveLoading.value) return;
  saveLoading.value = true;
  try {
    let params = {
      ...zkzyData.formData,
      qiYongBZ: zkzyData.formData.id ? zkzyData.params.qiYongBZ : 1,
    };
    let zhuanKeZYMXList = [];
    let xinZengZKZYMXList = [];
    let zuoFeiZKZYMXList = [];
    let ks = [];
    let ys = [];
    let hz = [];
    //pop()删除最后一个空数据
    zkzyData.ksList.pop();
    if (zkzyData.ksList.length) {
      zkzyData.ksList.forEach((e, index) => {
        let obj_ks = {
          ...initDetail(),
          jiaGeID: zkzyData.formData.jiaGeID,
          yaoPinMC: zkzyData.formData.yaoPinMC,
          xianZhiLXDM: zkzyData.xianZhiLXDM_KS,
          xianZhiLXMC: zkzyData.xianZhiLXDM_KS === '1' ? '有权限' : '无权限',
          shiYongFWDM: '1',
          shiYongFWMC: '科室',
          keShiID: e.keShiID,
          keShiMC: e.keShiMC,
          shunXuHao: index + 1,
          id: e.id ? e.id : '',
        };
        if (zkzyData.formData.id && e.id) {
          ks.push({ ...obj_ks });
        } else if (zkzyData.formData.id && !e.id) {
          xinZengZKZYMXList.push({ ...obj_ks });
        } else {
          zhuanKeZYMXList.push({ ...obj_ks });
        }
      });
    }
    zkzyData.ysList.pop();
    if (zkzyData.ysList.length) {
      zkzyData.ysList.forEach((e, index) => {
        let obj_ys = {
          ...initDetail(),
          jiaGeID: zkzyData.formData.jiaGeID,
          yaoPinMC: zkzyData.formData.yaoPinMC,
          xianZhiLXDM: zkzyData.xianZhiLXDM_YS,
          xianZhiLXMC: zkzyData.xianZhiLXDM_YS === '1' ? '有权限' : '无权限',
          shiYongFWDM: '2',
          shiYongFWMC: '医生',
          yongHuDLM: e.yongHuDLM,
          yongHuID: e.yongHuID,
          yongHuXM: e.yongHuXM,
          shunXuHao: index + 1,
          id: e.id ? e.id : '',
        };
        if (zkzyData.formData.id && e.id) {
          ys.push({ ...obj_ys });
        } else if (zkzyData.formData.id && !e.id) {
          xinZengZKZYMXList.push({ ...obj_ys });
        } else {
          zhuanKeZYMXList.push({ ...obj_ys });
        }
      });
    }
    zkzyData.hzList.pop();
    if (zkzyData.hzList.length) {
      zkzyData.hzList.forEach((e, index) => {
        let obj_hz = {
          ...initDetail(),
          jiaGeID: zkzyData.formData.jiaGeID,
          yaoPinMC: zkzyData.formData.yaoPinMC,
          xianZhiLXDM: zkzyData.xianZhiLXDM_HZ,
          xianZhiLXMC: zkzyData.xianZhiLXDM_HZ === '1' ? '有权限' : '无权限',
          shiYongFWDM: '3',
          shiYongFWMC: '患者',
          bingRenID: e.bingRenID,
          xingMing: e.xingMing,
          xingBieDM: e.xingBieDM,
          xingBieMC: e.xingBieMC,
          shenFenZH: e.shenFenZH,
          shunXuHao: index + 1,
          id: e.id ? e.id : '',
        };
        if (zkzyData.formData.id && e.id) {
          hz.push({ ...obj_hz });
        } else if (zkzyData.formData.id && !e.id) {
          xinZengZKZYMXList.push({ ...obj_hz });
        } else {
          zhuanKeZYMXList.push({ ...obj_hz });
        }
      });
    }
    if (zkzyData.formData.id) {
      params.xinZengZKZYMXList = xinZengZKZYMXList;
      zuoFeiZKZYMXList = zkzyData.delData.length
        ? zkzyData.delData.map((item) => item.id)
        : [];
      let list1 = findChangedItems(zkzyData.compareList_KS, ks, 'keShiID');
      let list2 = findChangedItems(zkzyData.compareList_YS, ys, 'yongHuID');
      let list3 = findChangedItems(zkzyData.compareList_HZ, hz, 'bingRenID');
      params.xiuGaiZKZYMXList = [].concat(list1, list2, list3);
      params.zuoFeiZKZYMXList = zuoFeiZKZYMXList;
      await UpdateZhuanKeZY(params);
    } else {
      params.zhuanKeZYMXList = zhuanKeZYMXList;
      await SaveZhuanKeZY(params);
    }
    MdMessage({
      type: 'success',
      message: `${zkzyData.formData.id ? '修改' : '保存'}成功！`,
    });
    saveLoading.value = false;
    resolve(true);
    handleClose();
  } catch (error) {
    saveLoading.value = false;
    proxy.$logger.error(error);
  }
};
//过滤掉两个数据中重复的项
const findChangedItems = (oldArray, newArray, key) => {
  let arr = [];
  if (newArray.length && oldArray.length) {
    newArray.forEach((e) => {
      oldArray.forEach((item) => {
        if (e.id === item.id && e[key] !== item[key]) {
          arr.push(e);
        }
      });
    });
  }
  return arr;
};
//限制类型切换提示
const leiXingChange = (value) => {
  //当前table没有数据切换类型时不提示
  let isTiShi = false;
  switch (zkzyData.shiYongFWDM) {
    case '1':
      isTiShi = zkzyData.ksList.length > 1 ? true : false;
      break;
    case '2':
      isTiShi = zkzyData.ysList.length > 1 ? true : false;
      break;
    case '3':
      isTiShi = zkzyData.hzList.length > 1 ? true : false;
      break;
  }
  if (!isTiShi) {
    handleXZLXData(value);
    return;
  }
  MdMessageBox.confirm('修改限制类型将清空现有类型下的规则，确定修改吗？', '', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      initData();
      handleXZLXData(value);
    })
    .catch(() => {
      zkzyData.xianZhiLXDM = value === '1' ? '2' : '1';
    });
};
const handleXZLXData = (value) => {
  switch (zkzyData.shiYongFWDM) {
    case '1':
      zkzyData.xianZhiLXDM_KS = value;
      break;
    case '2':
      zkzyData.xianZhiLXDM_YS = value;
      break;
    case '3':
      zkzyData.xianZhiLXDM_HZ = value;
      break;
  }
};
//适用范围切换
const handleTab = async (activeName, oldActiveName) => {
  if (activeName !== oldActiveName) {
    zkzyData.shiYongFWDM = activeName;
    switch (activeName) {
      case '1':
        zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_KS;
        break;
      case '2':
        zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_YS;
        break;
      case '3':
        zkzyData.xianZhiLXDM = zkzyData.xianZhiLXDM_HZ;
        break;
    }
  }
};
//切换时重置数据
const initData = () => {
  switch (zkzyData.shiYongFWDM) {
    case '1':
      if (zkzyData.formData.id) {
        delRow(zkzyData.ksList);
      }
      zkzyData.ksList = [];
      addRow(zkzyData.ksList, { ...initDetail(), detail: '' });
      break;
    case '2':
      if (zkzyData.formData.id) {
        delRow(zkzyData.ysList);
      }
      zkzyData.ysList = [];
      addRow(zkzyData.ysList, { ...initDetail(), detail: '' });
      break;
    case '3':
      if (zkzyData.formData.id) {
        delRow(zkzyData.hzList);
      }
      zkzyData.hzList = [];
      addRow(zkzyData.hzList, { ...initDetail(), detail: '' });
      break;
  }
};
const delRow = (datas, index) => {
  if (!datas.length) return;
  //传入index参数时是单个删除，未传入时是整个删除，例如限制类型切换
  //ID存在代表是编辑获取的已有数据，删除操作需要把除ID外的数据值清空
  if (index >= 0) {
    let del = datas.splice(index, 1);
    let id = del[0].id;
    if (id) {
      for (let prop in del[0]) {
        del[0][prop] = '';
      }
      del[0].id = id;
      zkzyData.delData.push(del[0]);
    }
  } else {
    datas.forEach((e) => {
      let id = e.id;
      if (id) {
        for (let prop in e) {
          e[prop] = '';
        }
        e.id = id;
        zkzyData.delData.push(e);
      }
    });
  }
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-ZhuanKeZYYPGL-dialog {
  height: 100%;
  background: #fff;
  margin-bottom: var(--md-spacing-3);
  display: flex;
  flex-direction: column;

  &-title {
    padding: var(--md-spacing-3);
    color: #222222;
    font-size: 14px;
    background-color: rgb(var(--md-color-1));
    display: flex;
    align-items: center;
    border-radius: 4px;
    margin: var(--md-spacing-3);

    .HISYK-mr8 {
      width: 300px;
    }
    span {
      margin-right: var(--md-spacing-3);
    }
    div {
      font-weight: normal;

      span {
        margin-right: var(--md-spacing-3);
      }
    }
  }
  &-subtitle {
    margin-left: var(--md-spacing-3);
    > span {
      margin-right: var(--md-spacing-3);
    }
  }
  &-tab {
    margin-top: var(--md-spacing-3);
    &-radio {
      display: flex;
      align-items: center;
      line-height: 30px;
      margin: 0 var(--md-spacing-3) var(--md-spacing-3);
      span {
        margin-right: var(--md-spacing-3);
      }
      &-group {
        flex: 1;
      }
    }
    .zkzy_table {
      margin: 0 var(--md-spacing-3);
      width: calc(100% - var(--md-spacing-3) -var(--md-spacing-3));
      max-width: calc(100% - var(--md-spacing-3) -var(--md-spacing-3));
    }
  }
  .table {
    flex: 1;
    overflow: hidden;
    margin-bottom: var(--md-spacing-3);
  }
  .dialog-footer-zuofei {
    float: left;
  }
}
</style>
<style lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
.#{$md-prefix}-ZhuanKeZYYPGL-dialog {
  &-content {
    .#{$md-prefix}-tabs--card > .#{$md-prefix}-tabs__header {
      padding-top: var(--md-spacing-2);
      margin-bottom: 0px;
      border: none;
      .#{$md-prefix}-tabs__item {
        padding: 0 var(--md-spacing-6);
      }
      .#{$md-prefix}-tabs__item.is-active {
        border: none;
        background: #fff;
      }
    }
  }
}
</style>
