<template>
  <div :class="prefixClass('inventory-container')">
    <div :class="prefixClass('add-inventory')" v-loading="pageLoading">
      <div :class="prefixClass('add-inventory-header')">
        <div :class="prefixClass('add-inventory-header__action')">
          <div :class="prefixClass('add-inventory-header__action__left')">
            <span :class="prefixClass('ckdtitle')"
              ><i class="iconfont icondanju"></i>出库单</span
            >
            <biz-yaopindw
              v-model="query.likeQuery"
              valueKey="yaoPinMC"
              labelKey="yaoPinMC"
              :kuCunSFWK="false"
              showSuffix
              :showXiaoGuiGeYP="showXiaoGuiGeYP"
              type="yk"
              @change="handleKuaiSuDW($event)"
            >
            </biz-yaopindw>
            <!-- <md-button
              v-if="params.danWeiBMZCID"
              type="primary"
              plain
              style="margin-left: 8px"
              @click="handleAnXiaoHLSC"
              >按消耗量生成</md-button
            > -->
          </div>
          <div :class="prefixClass('add-inventory-header__action__right')">
            <md-button
              v-if="!isChongHong"
              type="danger"
              :icon="prefixClass('icon-shanchuwap')"
              noneBg
              @click="handleDelete"
              >删除
            </md-button>
            <md-button type="primary" plain @click="handleSave">保存</md-button>
            <md-button type="primary" @click="handleSaveAndJiZhang"
              >保存并记账</md-button
            >
          </div>
        </div>
        <div :class="prefixClass('add-inventory-header__note')">
          <span :class="prefixClass('require')">出库方式</span>
          <md-select
            v-model="params.chuRuKFSID"
            :disabled="isChongHong || isZiDongSC"
            style="flex: 1"
            filterable
            @change="handleSelectChange($event, 'chuRuKFS')"
          >
            <md-option
              v-for="item in chuRuKFSOptions"
              :key="item.chuRuKFSID"
              :label="item.chuRuKFSMC"
              :value="item.chuRuKFSID"
            />
          </md-select>
          <span
            style="width: 100px"
            :class="
              prefixClass(['margin-left-8', isRequireDWBM ? 'require' : ''])
            "
          >
            单位/部门
          </span>
          <md-select
            v-model="params.danWeiBMZCID"
            filterable
            remote
            reserve-keyword
            :disabled="isChongHong"
            placeholder="输入搜索选择"
            :remote-method="filterMethod"
            style="flex: 2"
            @change="handleSelectChange($event, 'danWeiBMZC')"
          >
            <md-option
              v-for="item in danWeiBMZCOptions"
              :key="item.danWeiBMZCID"
              :label="item.danWeiBMZCMC"
              :value="item.danWeiBMZCID"
            />
          </md-select>
          <span
            v-if="showZhangBLB == 1"
            style="width: 100px"
            :class="prefixClass(['margin-left-8', 'require'])"
          >
            账簿类别
          </span>
          <md-select
            v-if="showZhangBLB == 1"
            v-model="params.zhangBuLBID"
            placeholder="输入搜索选择"
            style="flex: 2"
            @change="handleChangeZB"
            @focus="handlerFocusZB"
          >
            <md-option
              v-for="item in zhangBuOptions"
              :key="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :value="item.zhangBuLBID"
            />
          </md-select>
          <span style="width: 80px" :class="prefixClass(['margin-left-8'])">
            出库人
          </span>
          <md-select
            v-model="params.chuKuDKZList"
            multiple
            collapse-tags
            value-key="xiangMuZDM"
            label-key="xiangMuZMC"
            placeholder="输入搜索选择"
            style="flex: 2"
            filterable
            :remote-method="filterChuKuRen"
            remote
          >
            <md-option
              v-for="item in chuKuRList"
              :key="item.xiangMuZDM"
              :label="item.xiangMuZMC"
              :value="item"
            />
          </md-select>
          <span>备注</span>
          <md-input
            v-model="params.beiZhu"
            placeholder="请输入"
            :disabled="isChongHong"
            style="flex: 3"
            :class="prefixClass('input-class')"
          />
        </div>
      </div>
      <div v-show="!isChongHong" :class="prefixClass('add-inventory-body')">
        <md-editable-table-pro
          v-model="tableData"
          v-table-enter="(data) => handleTableEnter(data)"
          row-key="id"
          :columns="columns"
          height="100%"
          :showDefaultOperate="false"
          hide-add-button
          :autoFill="true"
          :maxLength="9999"
          :close-control-on-confirm="false"
          :close-control-on-recovery="false"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          id="editTable"
          ref="editTable"
          :key="tableKey"
          @sort-change="soltHandle"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #caoZuoSZDM="{ row, cellRef }">
            <md-select
              v-model="row.caoZuoSZDM"
              :disabled="row.canShuRu"
              @change="CaoZuoSZChange($event, row, cellRef)"
            >
              <md-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </template>
          <template #bieMing="{ row, cellRef }">
            <md-input v-model="row.bieMing" placeholder="请填写" clearable />
          </template>
          <template #yaoPinMCYGG="{ row, $index, cellRef }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              labelKey="yaoPinZC"
              :kuCunSFWK="false"
              :showChanDiJC="true"
              :showXiaoGuiGeYP="showXiaoGuiGeYP"
              :zhangBuLBID="params.zhangBuLBID == '0' ? '' : params.zhangBuLBID"
              :muBiaoWZGLLX="muBiaoWZGLLX"
              @change="handleTableYaoPinDWChange($event, row, $index, cellRef)"
              @blur="() => cellRef.endEdit()"
              :class="prefixClass('yaopin-select')"
            >
            </biz-yaopindw>
          </template>
          <template v-slot:xiaoHaoLiang="{ row }">
            <md-popover
              v-if="row.jiaGeID"
              ref="popoverRef"
              trigger="click"
              :width="480"
              @show="handleShow(row)"
            >
              <div>
                <md-date-picker
                  v-model="timeRange"
                  type="daterange"
                  :teleported="false"
                  range-separator="/"
                  start-placeholder="Start Date"
                  end-placeholder="End Date"
                  style="width: 93%; margin-bottom: 8px"
                  @change="handleChangeRQ"
                />
                <md-table
                  v-loading="loadingXHL"
                  :columns="columnsXHL"
                  :data="data"
                  style="margin-top: 8px"
                />
              </div>
              <template #reference>
                <span
                  :class="prefixClass('xiaohaoliang')"
                  @click="isShow = !isShow"
                >
                  {{ Math.abs(Number(row.xiaoHaoLiang)) }}
                </span>
              </template>
            </md-popover>
            <span v-else>
              {{ Math.abs(Number(row.xiaoHaoLiang)) }}
            </span>
          </template>
          <template #lengCangBZ="{ row }">
            <md-checkbox
              v-model="row.lengCangBZ"
              :true-label="1"
              :false-label="0"
            ></md-checkbox>
          </template>
          <template #chuKuSL="{ row, $index }">
            <md-input
              v-model="row.chuKuSL"
              v-number.float="{ def: 0 }"
              placeholder="请填写"
              :clearable="false"
              @blur="CheckChuKuSL(row, $index)"
            />
          </template>
          <template #chuKuSLYL="{ row }">
            <span :style="{ color: isTuiHuan ? '#f12933' : '' }">{{
              Number(row.chuKuSL).toFixed(xiaoShuDianWS)
            }}</span>
          </template>
          <template #qingLingSL="{ row, $index }">
            <md-input
              v-model="row.qingLingSL"
              v-number.float="{ def: 0 }"
              placeholder="请填写"
              :clearable="false"
            />
          </template>
          <template #qingLingSLYL="{ row }">
            <span :style="{ color: isTuiHuan ? '#f12933' : '' }">{{
              row.qingLingSL
                ? Number(row.qingLingSL).toFixed(xiaoShuDianWS)
                : Number(0).toFixed(xiaoShuDianWS)
            }}</span>
          </template>
          <template #beiZhu="{ row }">
            <md-input v-model="row.beiZhu" placeholder="请填写" />
          </template>
        </md-editable-table-pro>
      </div>
      <div v-show="isChongHong" :class="prefixClass('add-inventory-body')">
        <md-editable-table-pro
          v-model="tableData"
          v-table-enter
          row-key="id"
          :columns="chongHuoColumns"
          height="100%"
          :showDefaultOperate="false"
          hide-add-button
          :maxLength="9999"
          id="editTable1"
          ref="editTable1"
        >
          <template #chuKuSL="{ row, $index }">
            <md-input
              v-model="row.chuKuSL"
              v-number.float="{ def: 0 }"
              placeholder="请填写"
              :clearable="false"
              @blur="CheckChuKuSL(row, $index)"
            />
          </template>
          <template #chuKuSLYL="{ row }">
            <span style="color: #f12933">{{
              Number(row.chuKuSL).toFixed(xiaoShuDianWS)
            }}</span>
          </template>
          <template #qingLingSL="{ row, $index }">
            <md-input
              v-model="row.qingLingSL"
              v-number.float="{ def: 0 }"
              placeholder="请填写"
              :clearable="false"
            />
          </template>
          <template #qingLingSLYL="{ row }">
            <span :style="{ color: isTuiHuan ? '#f12933' : '' }">{{
              qingLingSL
                ? Number(row.qingLingSL).toFixed(xiaoShuDianWS)
                : Number(0).toFixed(xiaoShuDianWS)
            }}</span>
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('add-inventory-footer')">
        <div :class="prefixClass('add-inventory-footer__info left')">
          <span>制单：</span>
          <span :class="prefixClass('info__name color-222')">{{
            bottomData.zhiDanRXM
          }}</span>
          <span :class="prefixClass('info__time color-222')">{{
            formatDate(bottomData.zhiDanSJ)
          }}</span>
        </div>
        <div :class="prefixClass('add-inventory-footer__info right')">
          <div :class="prefixClass('margin-right-12')">
            <span>共计：</span>
            <span :class="prefixClass('font-bold')">{{
              zongShuJEData.yaoPinZS
            }}</span>
            <span class="">种药品</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span style="margin-right: 8px">合计</span>
            <span>进价金额：</span>
            <span
              :class="prefixClass('font-bold')"
              :style="{ color: isTuiHuan || isChongHong ? '#f12933' : '' }"
              >{{ Number(zongShuJEData.jinJiaJE).toFixed(xiaoShuDianWS) }}</span
            >
            <span class="">元</span>
          </div>
          <div :class="prefixClass('margin-right-12')">
            <span>零售金额：</span>
            <span
              :class="prefixClass('font-bold')"
              :style="{ color: isTuiHuan || isChongHong ? '#f12933' : '' }"
              >{{
                Number(zongShuJEData.lingShouJE).toFixed(xiaoShuDianWS)
              }}</span
            >
            <span class="">元</span>
          </div>
        </div>
      </div>
    </div>
    <biz-yaopinph
      ref="yaopinph"
      @showDialog="showDialog"
      :isShowCKInput="isShowCKInput"
    />
    <anxiaohl-dialog ref="anXiaoHaoLSCDialog" />
    <piliangdjfp-dialog ref="piliangdjfp" />
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { h } from 'vue';
import { focusEditTableDom } from '@/system/utils/focusEditTable';
import BizYaopindw from '@/components/YaoKu/BizYaoPinDW';
import { GetYaoPinXHLMXList } from '@/service/yaoPinYK/caiGouJH.js';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import { GetDanWeiBMXXByCRKFS } from '@/service/yaoPinYK/common';
import { GetQingLingMXByXHL } from '@/service/yaoPinYK/shouLiQL';
import {
  AddChuKuDan,
  GetChuKuDXQXX,
  JiZhangChuKuDan,
  UpdateChuKuDan,
  ZuoFeiChuKuDan,
} from '@/service/yaoPinYK/yaoPinCK';
import {
  getZhongYaoYPRKJGXZ,
  getKuFangSZList,
} from '@/service/yaoPin/YaoPinZDJCSJ';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { pageByWeiZhi } from '@/service/gongYong/yongHuXX';
import { GetYaoPinCDJGListByJGIDList } from '@/service/yaoPinYK/yaoPinRK';
import commonData from '@/system/utils/commonData';
import { formatMoney } from '@/system/utils/formatMoney';
import {
  getJiGouID,
  getKuCunGLLX,
  getWeiZhiID,
  getWeiZhiMC,
  getYongHuXM,
} from '@/system/utils/local-cache';
import { add, multiply } from '@/system/utils/mathComputed';
import { MdMessage, MdMessageBox ,MdInput} from '@mdfe/medi-ui';
import lyra from '@mdfe/lyra';
// //TODO
// import { SelectNormal } from '@mdfe/bmis-ui'
import BizYaopinph from '@/components/YaoKu/BizYaoPinPHCK';
import { GetChuKuYPPCList } from '@/service/yaoPinYK/common';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { printByUrl } from '@/system/utils/print';
import PiliangdjfpDialog from '../yaoPinRK/components/PiLiangDJFPDialog';
import AnXiaoHaoLSCDialog from './components/AnXiaoHaoLSCDialog.vue';
import commonList from './js/tableData';
import columnMixin from '@/components/mixin/columnMixin';

import { logger } from '@/service/log';
const initData = () => {
  return {
    yaoPinZC: '',
    yaoPinLX: '',
    yaoPinLXMC: '',
    yaoPinMCYGG: {},
    duLiFLMC: '',
    duLiFLDM: '',
    ruKuCDMC: '',
    yaoPinGG: '',
    yaoPinMC: '',
    shengPingTBM: '',
    chanDiMC: '',
    baoZhuangDW: '',
    kuCunZL: '0',
    chuKuHKCSL: '',
    yiLingWFSL: '',
    kuCunZKSL: '',
    kuCunSL: '',
    chuKuSL: 0,
    jinJia: '',
    jiaGeID: '',
    jinJiaJE: '',
    jinXiaoCJL: '',
    lingShouJia: '',
    lingShouJE: '',
    shengChanPH: '',
    yaoKuKCSL: '',
    yaoPinXQ: '',
    baiFangWZ: '',
    lingChaJBZ: '',
    yaoFangKCL: '',
    yaoFangXHL: '',
    mianFeiYPBZ: false,
    zengPinBZ: false,
    beiZhu: '',
    xiaoHaoLiang: 0,
    caiGouBZ: 0,
    lengCangBZ: 0,
    qingLingSL: 0,
  };
};
export default {
  name: 'xinzengckd',
  inject: ['viewManager'],
  mixins: [columnMixin],
  data() {
    return {
      chuKuRListFilter: [],
      chuKuRList: [],
      duMaJDLZD: '',
      showZhangBLB: '0',
      beforeZhangBuChange: '',
      isShowCKInput: false,
      xiaoShuDianWS: 2,
      isZhongYaoKXS: 5,
      muBiaoWZGLLX: '', // 目标位置管理类型
      selectLoading: false, //单位、部门分页loading
      pageLoading: false,
      isTuiHuan: false,
      isZiDongSC: false,
      isShowDialog: false,
      query: {
        likeQuery: '',
      },
      zhangBuOptions: [],
      params: {
        zhangBuLBID: '',
        zhangBuLBMC: '',
        chuRuKFSID: '',
        chuRuKFSMC: '',
        beiZhu: '',
        danWeiBMZCID: '',
        danWeiBMZCMC: '',
        hongDanBZ: 0,
        weiZhiID: getWeiZhiID(),
        weiZhiMC: getWeiZhiMC(),
        chuKuDKZList: [],
      },
      tableKey: 0,
      bottomData: {
        zhiDanRXM: getYongHuXM(),
        zhiDanSJ: dayjs().format('YYYY-MM-DD'),
        jinJiaJE: 0,
        lingShouJE: 0,
        yaoPinZS: 0,
      },
      chuRuKFSOptions: [],
      danWeiBMZCOptions: [],
      danWeiBMOriginOptions: [],
      BlurError: true,
      leiXingDM: null,
      columns: [
        {
          prop: 'xunHao',
          label: '序号',
          control: true,
          width: 70,
          align: 'center',
          formatter: (row, column, cellValue, index) => {
            return index + 1;
          },
        },
        {
          type: 'selection',
          width: 40,
          selectable: (row, index) => {
            return this.tableData.length !== index + 1;
          },
        },
        {
          prop: 'yaoPinLX',
          label: '药品类型',
          width: 34,
          field: true,
          //align: 'center',
          type: 'text',
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
          labelClassName: 'yaoPinLXMCHeader',
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 300,
          slot: 'yaoPinMCYGG',
          field: true,
          formatter: (row, column, cellValue, index) => {
            if (row.yaoPinMC) return row.yaoPinMC + ' ' + row.yaoPinGG;
            return '';
          },
          labelClassName: 'sheZhiHeader',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          endMode: 'custom',
          fieldDisabled: true,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          hidden: true,
          field: true,
          type: 'text',
          minWidth: 160,
          showOverflowTooltip: true
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 160,
          type: 'text',
          field: true,
          showOverflowTooltip: true,
          fieldDisabled: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
          type: 'text',
          field: true,
          fieldDisabled: true,
        },
        {
          prop: 'kuCunZL',
          label: '全院库存',
          align: 'right',
          width: 80,
          type: 'text',
          field: true,
        },
        {
          prop: 'yaoKuKCSL',
          label: '药库库存',
          width: 90,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue
              ? Number(cellValue).toFixed(this.xiaoShuDianWS)
              : '';
          },
          hidden: false,
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          width: 90,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'xiaoHaoLiang',
          slot: 'xiaoHaoLiang',
          label: '消耗量',
          width: 90,
          align: 'right',
          field: true,
          type: 'check',
          renderHeader: ({ column, _self }) => {
            return h('div', column.label);
          },
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 90,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'chuKuSL',
          label: '出库数量',
          previewSlot: 'chuKuSLYL',
          width: 120,
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          slot: 'chuKuSL',
          align: 'right',
          field: true,
          fieldDisabled: true,
        },
        // {
        //   prop: 'qingLingSL',
        //   label: '请领数量',
        //   width: 120,
        //   align: 'right',
        //   type: 'text',
        //   field: true,
        //   formatter: (row, column, cellValue, index) => {
        //     return Number(cellValue).toFixed(this.xiaoShuDianWS);
        //   },
        //   hidden: false,
        // },
        {
          prop: 'qingLingSL',
          label: '请领数量',
          previewSlot: 'qingLingSLYL',
          width: 95,
          field: true,
          labelClassName: 'requireHeaderRight',
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              { class: _self.prefixClass('require') },
              column.label,
            );
          },
          disabled: ({ $index }) => {
            return this.guanLianDJLX ? true : false;
          },
          slot: 'qingLingSL',
          align: 'right',
          // hidden: true,
        },
        {
          prop: 'chuKuHKCSL',
          label: '出库后库存数量',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            const nums = cellValue ? cellValue : 0;
            return Number(nums).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'kuCunZKSL',
          label: '锁定库存',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            const nums = cellValue ? cellValue : 0;
            return Number(nums).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'yiLingWFSL',
          label: '已领未发',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            const nums = cellValue ? cellValue : 0;
            return Number(nums).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return formatMoney(Number(cellValue), this.jinJiaXSDW);
          },
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          type: 'text',
          field: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          showOverflowTooltip: false,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
          type: 'text',
          startMode: 'click',
          endMode: 'custom',
          fieldDisabled: true,
          field: true,
        },
        {
          prop: 'zuiJinCKSCPH',
          label: '最近出库批号',
          width: 120,
          align: 'right',
          type: 'text',
          field: true,
          // hidden: true,
        },
        {
          prop: 'ruKuCDMC',
          label: '入库产地',
          minWidth: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'lengCangBZ',
          slot: 'lengCangBZ',
          label: '冷藏',
          width: 50,
          align: 'center',
          type: 'check',
          field: true,
        },
        {
          prop: 'duLiFLMC',
          label: '毒理分类',
          width: 86,
          type: 'text',
          field: true,
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          type: 'text',
          field: true,
        },
        {
          prop: 'zhongBaoZXS',
          label: '中包装',
          type: 'text',
          minWidth: 140,
          field: true,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          width: 78,
          field: true,
          type: 'text',
          component: MdInput,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
      ],
      zuoFeiChuKuDMXList: [],
      chongHuoColumns: commonList.xinZengCHCKDColumns,
      tableData: [],
      tableSelection: [],
      oldList: [],
      dangQingDWYPIndex: null,
      tableBodyEle: null,
      type: null,
      jiaGeID: '',
      row: {},
      timeRange: [],
      loadingXHL: false,
      columnsXHL: [
        {
          label: '机构',
          prop: 'jiGouMC',
          showOverflowTooltip: true,
        },
        {
          label: '药房',
          prop: 'weiZhiMC',
          width: 96,
          align: 'left',
        },
        {
          label: '消耗量',
          prop: 'xiaoHaoLiang',
          width: 100,
          align: 'right',
        },
        {
          label: '库存数量',
          prop: 'kuCunSL',
          width: 100,
          align: 'right',
        },
      ],
      data: [],
      isShow: false,
      xiaoHaoTS: 0, //消耗天数
      xiaoHaoKSSJ: null, //消耗开始时间
      xiaoHaoJSSJ: null, //消耗结束时间
      guanLianDJLX: '', //是否是手工单
      delPiCiList: [], //储存编辑后的批次数据
      controlExtraColumns: [
        {
          slot: 'caoZuoSZDM',
          label: '键盘操作设置',
          cIndex: 5,
        },
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 6,
        },
      ],
      jinJiaJEXSDW: 2,
      jinJiaXSDW: '',
      showXiaoGuiGeYP: false,
    };
  },
  computed: {
    isRequireDWBM() {
      let curData = this.chuRuKFSOptions.find(
        (item) => item.chuRuKFSID == this.params.chuRuKFSID,
      );
      return curData && curData.danWeiBMDM != 1;
    },
    //按入库单出库时，ruKuDanID处理，
    ruKuDanID() {
      return this.$route.query.ruKuDanID ? this.$route.query.ruKuDanID : '';
    },
    //冲红处理
    isChongHong() {
      return this.params.hongDanBZ == '1';
    },
    //计算底部信息 药品总数、进价金额、零售金额
    zongShuJEData() {
      let lingShouJE = 0;
      let jinJiaJE = 0;
      let val = this.tableData;
      val.forEach((item) => {
        //出库数量为空时， 直接赋值0
        if (item.chuKuSL || item.chuKuSL === 0) {
          // 进价金额 = 进价 * 数量
          item.jinJiaJE = multiply(
            this.stringToNumber(item.jinJia),
            item.chuKuSL,
          );
          // 零售金额 = 零售价 * 数量
          item.lingShouJE = multiply(
            this.stringToNumber(item.lingShouJia),
            item.chuKuSL,
          );
          let chuRuKFS = this.chuRuKFSOptions.find(
            (x) => x.chuRuKFSID == this.params.chuRuKFSID,
          );
          if (chuRuKFS != null && chuRuKFS.kuCunZJDM == '1') {
            item.chuKuHKCSL = Number(item.yaoKuKCSL) + Number(item.chuKuSL);
          } else {
            item.chuKuHKCSL = Number(item.yaoKuKCSL) - Number(item.chuKuSL);
          }
        } else {
          item.jinJiaJE = 0;
          item.lingShouJE = 0;
        }
        //累加
        lingShouJE = add(lingShouJE, this.stringToNumber(item.lingShouJE));
        jinJiaJE = add(jinJiaJE, this.stringToNumber(item.jinJiaJE));
      });
      return {
        lingShouJE,
        jinJiaJE,
        yaoPinZS: !this.isChongHong ? val.length - 1 : val.length,
      };
    },
  },
  /**
   *  处理切换时数据更新问题（新增跳转到同一页面时， 数据处理问题）
   */
  async created() {
    let xiaoShu = null;
    this.pageLoading = true;
    const query = this.$route.query;
    try {
      await this.getColumnInit();
      //获取出入库方式字典
      await this.getZiDianSJ();
      const moRenCHKFS = await getZhongYaoYPRKJGXZ({
        xiangMuDM: 'yaoPinRKMRCKFS',
      });
      const res = await getZhangBuQXXQ();
      this.zhangBuOptions = res.zhangBuLBXXList || [];
      // this.zhangBuOptions.unshift({
      //   zhangBuLBMC: '全部',
      //   zhangBuLBID: '0',
      // })
      const arr = await getKuFangSZList([
        'shiFouAZBLBGL',
        'yaoPinCKDDMJYPDLZD',
        'jinJiaXSDWS',
        'jinJiaJEXSDWS',
        'ShiFouYXXGGRK',
      ]);
      if (arr.length > 0) {
        arr.forEach((el) => {
          if (el.xiangMuDM == 'shiFouAZBLBGL') {
            this.showZhangBLB = el.xiangMuZDM;
          } else if (el.xiangMuDM == 'yaoPinCKDDMJYPDLZD') {
            this.duMaJDLZD = el.xiangMuZDM;
          } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
            this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
          } else if (el.xiangMuDM == 'jinJiaXSDWS') {
            this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : 0;
          } else if (el.xiangMuDM == 'ShiFouYXXGGRK') {
            this.showXiaoGuiGeYP = el.xiangMuZDM == 1 ? true : false;
          }
        });
      }
      const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
      // 判断进价零售价是否设置了值，没有则赋默认值
      this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
      // JVADT74039 【嵊州人民】西药库新增出库的出库方式默认为药房领用
      if (moRenCHKFS && moRenCHKFS.xiangMuZDM) {
        this.params.chuRuKFSID = moRenCHKFS.xiangMuZDM;
        this.params.chuRuKFSMC = moRenCHKFS.xiangMuZMC;
      }
      const newDate = dayjs();
      this.xiaoHaoKSSJ = newDate.format('YYYY-MM') + '-01 00:00:00';
      this.xiaoHaoJSSJ = newDate.format('YYYY-MM-DD') + ' 23:59:59';
      const timeDiff = Math.abs(
        dayjs(this.xiaoHaoJSSJ) - dayjs(this.xiaoHaoKSSJ),
      );
      this.xiaoHaoTS = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      // 编辑进入， 请求获取数据
      if (query.id) {
        await this.getDetail(query.id);
        // this.columns[13].hidden = this.guanLianDJLX ? true : false;
        // this.columns[12].hidden = this.guanLianDJLX ? false : true;
        this.tableKey++;
      }
      //判断如果是中药库就隐藏，西药库显示
      const isCaoYao = getKuCunGLLX().includes('3');
      // this.columns[7].hidden = isCaoYao;
      this.isShowCKInput = !isCaoYao;
      this.tableKey++;
      // 按入库单进入时， 会传入出入库方式，此时按该出入库方式id获取单位部门数据
      if (this.params.chuRuKFSID) {
        await this.getDanWeiBMOptions(this.params.chuRuKFSID);
      }
      //不是冲红单则添加空行
      if (
        (!this.isChongHong && this.tableData.length == 0) ||
        this.tableData[this.tableData.length - 1].jiaGeID
      ) {
        let data = initData();
        this.tableData.push(data);
      }
      //获取小数参数要是没有值，就默认等于2
      xiaoShu = await GetCanShuZhi({
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '3', //0表示关闭，1表示开启
        gongNengID: '0',
      });
    } finally {
      this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 3 : xiaoShu;
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
      this.pageLoading = false;
      const chukuRenArr = await pageByWeiZhi();
      this.chuKuRListFilter = chukuRenArr.map((m) => {
        return {
          xiangMuMC: '出库人id',
          xiangMuDM: 'chuKuRID',
          xiangMuZMC: m.yongHuXM,
          xiangMuZDM: m.yongHuID,
        };
      });
      this.chuKuRList = cloneDeep(this.chuKuRListFilter);
    }
  },
  async activated() {
    //存储type  0 为新增， 其他为按入库单入库
    let val = this.$route.query.type;
    if (this.$route.path === '/XinZengCKD' && val !== this.type) {
      //type变化时， 更新数据。 新增或者按入库单入库
      this.type = val;
      await this.checkType(val);
      //按入库单出库制单时，添加空行
      if (
        this.$route.query.ruKuDanID &&
        this.tableData[this.tableData.length - 1].jiaGeID
      ) {
        this.tableData.push(initData());
      }
    }
  },
  methods: {
    /**
     * 2 库房 3 科室 4 单位
     * @param leiXingDM  danWeiBMID: "3002" danWeiBMMC: "挂号室" leiXingDM: "3" leiXingMC: "科室"
     * @return String 存储字段key
     */
    danWeiBMLX(leiXingDM) {
      let key = 'ruKuWZ';
      switch (leiXingDM) {
        case '2':
          key = 'ruKuWZ';
          break;
        case '3':
          key = 'ruKuKS';
          break;
        case '4':
          key = 'diaoBoDW';
          break;
      }
      return key;
    },
    /**
     * 顶部药品选择定位触发，
     * 1. 选药品后， 选择批次
     * 2. 清空上表格中已选中的行样式
     * 3. 获取table的dom节点
     * 4. 查找列表数据，并生成定位样式
     * @param data
     * @returns {Promise<void>}
     */
    async handleKuaiSuDW(data) {
      if (data) {
        let data1 = await this.$refs.yaopinph.show({
          jiaGeID: data.jiaGeID,
          yaoPinMC: data.yaoPinMC,
          model: 'ck',
        });
        data.shengChanPH = data1.shengChanPH;
      }
      //清空
      if (!data) {
        this.clearDingWeiClass(this.tableBodyEle, this.dangQingDWYPIndex);
        this.dangQingDWYPIndex = null;
        return;
      }
      // 获取doom
      if (!this.tableBodyEle) {
        if (!this.isChongHong) {
          this.tableBodyEle = document.querySelector(
            `#editTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
          );
        } else {
          this.tableBodyEle = document.querySelector(
            `#editTable1 .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
          );
        }
      }
      // 去掉上一次定位样式
      if (this.dangQingDWYPIndex) {
        this.clearDingWeiClass(this.tableBodyEle, this.dangQingDWYPIndex);
        this.dangQingDWYPIndex = null;
      }
      //寻找index
      this.dangQingDWYPIndex = this.tableData.findIndex(
        (x) => x.jiaGeID === data.jiaGeID && x.shengChanPH === data.shengChanPH,
      );
      if (this.dangQingDWYPIndex !== -1) {
        this.setDingWeiClass(this.tableBodyEle, this.dangQingDWYPIndex, true);
      } else {
        MdMessage({
          type: 'warning',
          message: '未找到该药品！',
        });
      }
    },
    //账簿类别focus
    handlerFocusZB() {
      this.beforeZhangBuChange = cloneDeep(this.params.zhangBuLBID);
    },
    filterChuKuRen(query) {
      if (query !== '') {
        this.chuKuRList = this.chuKuRListFilter.filter((item) => {
          return (
            item.xiangMuZDM.indexOf(query) > -1 ||
            item.xiangMuZMC.indexOf(query) > -1
          );
        });
      } else {
        this.chuKuRList = this.chuKuRListFilter;
      }
    },
    //切换账簿提醒
    async handleChangeZB(val) {
      let obj = this.zhangBuOptions.find((item) => item.zhangBuLBID == val);
      this.params.zhangBuLBMC = obj.zhangBuLBMC;
      if (this.tableData.length === 1) return;
      try {
        await MdMessageBox.confirm(
          '切换账簿类别会清空表格数据，确认切换？',
          '操作提醒！',
          {
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          },
        );
        this.tableData = [];
        this.tableData.push(initData());
      } catch (e) {
        this.params.zhangBuLBID = this.beforeZhangBuChange;
        let obj = this.zhangBuOptions.find(
          (item) => item.zhangBuLBID == this.beforeZhangBuChange,
        );
        this.params.zhangBuLBMC = obj.zhangBuLBMC;
      }
    },

    showDialog(type) {
      // if (type) {
      //   // this.$refs.editTable.stopEnter();
      // } else {
      //   // this.$refs.editTable.actionEnter();
      // }
    },
    // 去掉定位样式
    clearDingWeiClass(tableBodyEle, index) {
      if (tableBodyEle && index >= 0) {
        let dingWeiEle = tableBodyEle.querySelectorAll(
          `.mediinfo-vela-yaoku-web-base-table__row`,
        )[index];
        dingWeiEle.className = dingWeiEle.className.replace(
          this.prefixClass('dingwei-bg'),
          '',
        );
      }
    },
    // 设置定位样式
    setDingWeiClass(tableBodyEle, index, dingWeiBZ = false) {
      let dingWeiEle = tableBodyEle.querySelectorAll(
        `.mediinfo-vela-yaoku-web-base-table__row`,
      )[index];
      dingWeiEle.className =
        dingWeiEle.className !== ''
          ? dingWeiEle.className + ' ' + this.prefixClass('dingwei-bg')
          : this.prefixClass('dingwei-bg');
      if (dingWeiBZ) this.setDingWeiScroll(tableBodyEle, dingWeiEle, index);
    },
    // 滚动条设置到定位位置
    setDingWeiScroll(tableBodyEle, dingWeiEle, index) {
      tableBodyEle.scrollTop = index * dingWeiEle.clientHeight;
      this.dangQingDWYPIndex = index;
    },
    /**
     * 根据出入库方式获取单位部门
     * @param id
     * @returns {Promise<void>}
     */
    getDanWeiBMOptions(id) {
      GetDanWeiBMXXByCRKFS({ chuRuKFSID: id }).then((res) => {
        this.danWeiBMZCOptions = res.reduce((pre, item) => {
          this.leiXingDM = item.leiXingDM;
          //拼接目标位置库存管理类型 1111  西成草卫
          let muBiaoWZGLLX = '';
          if (item.kuCunGLLXs) {
            for (var i = 1; i <= 4; i++) {
              if (item.kuCunGLLXs.find((r) => r == i)) {
                muBiaoWZGLLX += '1';
              } else {
                muBiaoWZGLLX += '0';
              }
            }
          }
          pre.push({
            danWeiBMZCID: item.danWeiBMID,
            danWeiBMZCMC: item.danWeiBMMC,
            shuRuMa1: item.shuRuMa1,
            muBiaoWZGLLX: muBiaoWZGLLX,
            zuZhiJGID: item.zuZhiJGID,
            zuZhiJGMC: item.zuZhiJGMC,
          });

          return pre;
        }, []);
        this.danWeiBMOriginOptions = cloneDeep(this.danWeiBMZCOptions);
      });
    },
    /**
     * 将数据分为 新增、修改、作废等数据
     * @param data
     * @param params
     */
    fenLeiMXData(data, params) {
      let updateChuKuDMXList = [];
      let addChuKuDMXList = [];
      // let zuoFeiChuKuDMXList = [];
      data.forEach((item) => {
        //根据是否有id，判断是修改还是新增
        if (item.id) {
          //在初始数据中， 寻找若存在，则为修改数据
          let index = this.oldList.findIndex((item1) => item1.id === item.id);
          if (index !== -1) {
            updateChuKuDMXList.push(item);
          }
        } else {
          addChuKuDMXList.push(item);
        }
      });
      //提取现在数据里，有的id
      let idList = data.reduce((pre, item) => {
        if (item.id) {
          pre.push(item.id);
        }
        return pre;
      }, []);
      // //遍历源数据的id ， 不在当前保存数据里的id即为待删除id
      this.oldList.forEach((item) => {
        if (!idList.includes(item.id)) {
          if (!this.zuoFeiChuKuDMXList.includes(item.id)) {
            this.delPiCiList.push(item.id);
          }
        }
      });
      params.updateChuKuDMXList = updateChuKuDMXList;
      params.addChuKuDMXList = addChuKuDMXList;
      params.zuoFeiChuKuDMXList = this.zuoFeiChuKuDMXList || [];
    },
    /**
     * 获取出入库方式
     */
    getZiDianSJ() {
      GetChuRuKFSByFXDM('2').then((res) => {
        let optionData = [];
        res.forEach((item) => {
          optionData.push({
            chuRuKFSMC: item.fangShiMC,
            chuRuKFSID: item.fangShiID,
            kuCunZJDM: item.kuCunZJDM,
            danWeiBMDM: item.danWeiBMDM,
            danWeiBMMC: item.danWeiBMMC,
          });
        });
        this.chuRuKFSOptions = optionData;
      });
    },
    stringToNumber(str) {
      return str ? Number(str) : 0;
    },
    /**
     * 判断生产批号是否重复， 选择药品改变时触发
     * @param row
     * @param rowIndex
     * @returns {boolean}
     */
    checkYaoPingRepeat(row, rowIndex) {
      if (row.shengChanPH === '') return;
      // 根据价格id、生产批号判断行数据是否重复
      let index = this.tableData.findIndex(
        (item, index1) =>
          item.jiaGeID === row.jiaGeID &&
          item.shengChanPH === row.shengChanPH &&
          index1 !== rowIndex,
      );
      // 重复则弹出提示
      if (index !== -1) {
        MdMessage({
          type: 'warning',
          message: `第${index + 1}行已有相同生产批号(${
            row.shengChanPH
          })的药品'${row.yaoPinMC}'！`,
        });
        row.shengChanPH = '';
      }
      return index === -1;
    },
    /**
     * 行选择药品触发
     * @param val
     * @param row
     * @param rowIndex
     * @returns {Promise<void>}
     */
    async handleTableYaoPinDWChange(val, row, rowIndex, cellRef) {
      //清空数据时， 将行数据置空
      if (!val) {
        if (row.jiaGeID) Object.assign(row, initData());

        return;
      }
      try {
        this.pageLoading = true;
        this.$refs.editTable.stopEnter();
        let data;
        // const canshuList = this.params.danWeiBMZCID.split(',');
        const PiHaoList = await GetChuKuYPPCList({
          jiaGeID: val.jiaGeID,
          chuRuKFSID: this.params.chuRuKFSID,
          danWeiBMID: this.params.danWeiBMZCID,
          zuZhiJGID: getJiGouID(),
          kaiShiSJ: this.xiaoHaoKSSJ,
          jieShuSJ: this.xiaoHaoJSSJ,
          pageIndex: 1,
          pageSize: 999,
        });
        // 调用批次选择处理， 只有一条直接赋值， 多条则弹框
        if (PiHaoList.length > 1) {
          this.$refs.editTable.stopEnter();
          this.isShowDialog = true;
          data = await this.$refs.yaopinph.show({
            jiaGeID: val.jiaGeID,
            yaoPinMC: val.yaoPinMC,
            kaiShiSJ: this.xiaoHaoKSSJ,
            jieShuSJ: this.xiaoHaoJSSJ,
            chuRuKFSID: this.params.chuRuKFSID,
            danWeiBMID: this.params.danWeiBMZCID,
            zuZhiJGID: getJiGouID(),
            model: 'ck',
          });
        } else {
          this.isShowDialog = false;
          data = PiHaoList[0];
        }
        // 寻找表格中是否有普通药品
        const isNormal =
          this.tableData &&
          this.tableData.some((fl) => fl.jiaGeID && fl.duLiFLDM == 0);
        const isDuMaJ =
          this.tableData &&
          this.tableData.some((fl) => fl.jiaGeID && fl.duLiFLDM != 0);
        let piCiDLFLDM = null;
        piCiDLFLDM = data.duLiFLDM;
        // 批次弹窗输入出库数量返回的是数组
        if (Array.isArray(data)) {
          piCiDLFLDM = data[0].duLiFLDM;
        }
        // 毒麻经独立制单且是西药库时
        // 有普通药品但是当前选择药品是毒麻精；表格没有普通药品但当前选择行是普通药品
        if (
          this.tableData.length > 1 &&
          this.duMaJDLZD == 1 &&
          ((isNormal && piCiDLFLDM != 0) || (isDuMaJ && piCiDLFLDM == 0))
        ) {
          await MdMessageBox.confirm(
            `当前出库单中同时存在普通药品和毒麻精药品，无法出库`,
            '',
            {
              showCancelButton: false,
              closeOnClickModal: false,
              confirmButtonText: '删除药品',
              type: 'warning',
            },
          );
          this.tableData[rowIndex] = initData();
          focusEditTableDom({
            rowIndex: rowIndex,
            columns: this.columns,
            key: 'yaoPinMCYGG',
          });
          // this.$refs.editTable.toNext(3 * rowIndex);
          return;
        }
        //西药库如果填了出库数量
        if (Array.isArray(data)) {
          this.tableData = this.tableData.filter(
            (fl) => fl.jiaGeID != val.jiaGeID,
          );
          data.forEach((el, elIndex) => {
            el.yaoPinMC = val.yaoPinMC;
            el.guanLianDJID = '';
            el.guanLianDJLX = '';
            el.lengCangBZ = el.lengCangBZ == 64 || el.lengCangBZ == 1 ? 1 : 0;
            el.qingLingSL = el.chuKuSL;
            el.yaoPinMCYGG = val;
            el.yaoPinGG = val.yaoPinGG;
            el.chanDiMC = val.chanDiMC;
            el.baoZhuangDW = val.baoZhuangDW;
            el.yaoPinLXMC = val.yaoPinLXMC;
            el.yaoPinZC = el.yaoPinMC + ' ' + el.yaoPinGG;
            const newDate = dayjs();
            el.xiaoHaoKSSJ = this.xiaoHaoKSSJ
              ? this.xiaoHaoKSSJ
              : newDate.format('YYYY-MM') + '-01 00:00:00';
            el.xiaoHaoJSSJ = this.xiaoHaoJSSJ
              ? this.xiaoHaoJSSJ
              : newDate.format('YYYY-MM-DD') + ' 23:59:59';
            this.tableData.splice(this.tableData.length - 1, 0, el);
          });
          // 跳转下一个
          row.chuKuSL = row.chuKuSL > 0 ? row.chuKuSL : null;
          this.$refs.editTable.toNext(3 * rowIndex);
          return;
        }
        // const delindex = this.tableData.findIndex(
        //   (item, index1) => item.jiaGeID === val.jiaGeID && index1 !== rowIndex,
        // );
        // if (delindex > -1 && this.isShowCKInput) {
        //   if (rowIndex == this.tableData.length - 1) {
        //     this.tableData[rowIndex] = initData();
        //   } else {
        //     this.tableData = this.tableData.filter(
        //       (fl, indexD) => indexD !== delindex,
        //     );
        //   }
        //   focusEditTableDom({
        //     rowIndex: rowIndex,
        //     columns: this.columns,
        //     key: 'yaoPinMCYGG',
        //   });
        //   return;
        // }
        // 赋值药品名称
        data.yaoPinMC = val.yaoPinMC;
        // 判断是否重复选择， 若重复 则回跳到选择药品单元格
        if (!this.checkYaoPingRepeat(data, rowIndex)) {
          focusEditTableDom({
            rowIndex: rowIndex,
            columns: this.columns,
            key: 'yaoPinMCYGG',
          });
          this.tableData[index] = initData();
          return;
        }
        //选择其他药品后 清空关联单据id 和关联单据类型
        row.guanLianDJID = '';
        row.guanLianDJLX = '';
        data.lengCangBZ = data.lengCangBZ == 64 || data.lengCangBZ == 1 ? 1 : 0;
        // 合并药品批次信息
        Object.assign(row, data);
        // 合并药品基本信息
        Object.assign(row, {
          // kuCunSL: val.kuCunSL,
          yaoPinMC: val.yaoPinMC,
          yaoPinGG: val.yaoPinGG,
          chanDiMC: val.chanDiMC,
          baoZhuangDW: val.baoZhuangDW,
          yaoPinLXMC: val.yaoPinLXMC,
        });
        // 当前是最后一行， 新增一行数据
        if (
          rowIndex === this.tableData.length - 1 &&
          this.tableData[this.tableData.length - 1].jiaGeID
        )
          this.tableData.push(initData());
        // 跳转下一个
        row.chuKuSL = null;
        this.$refs.editTable.actionEnter();
        cellRef.endEdit();
        row.yaoPinMCYGG = val;
        row.yaoPinZC = row.yaoPinMC + ' ' + row.yaoPinGG;
        const newDate = dayjs();
        row.xiaoHaoKSSJ = this.xiaoHaoKSSJ
          ? this.xiaoHaoKSSJ
          : newDate.format('YYYY-MM') + '-01 00:00:00';
        row.xiaoHaoJSSJ = this.xiaoHaoJSSJ
          ? this.xiaoHaoJSSJ
          : newDate.format('YYYY-MM-DD') + ' 23:59:59';
      } catch (e) {
        logger.error(e);
        Object.assign(row, initData());
        this.$refs.editTable.toNext(3 * rowIndex - 1);
      } finally {
        this.pageLoading = false;
        this.isShowDialog = false;
      }
    },
    /**
     * 判断数据处理类型
     * @param val 0——新增  其他——按入库单出库
     * @returns {Promise<void>}
     */
    async checkType(val) {
      if (val == '0') {
        this.tableData = [];
        this.params = {
          chuKuDKZList: [],
          zhangBuLBID: '',
          zhangBuLBMC: '',
          chuRuKFSID: '',
          chuRuKFSMC: '',
          beiZhu: '',
          danWeiBMZCID: '',
          danWeiBMZCMC: '',
          hongDanBZ: 0,
          weiZhiID: getWeiZhiID(),
          weiZhiMC: getWeiZhiMC(),
        };
      } else await this.getDataFromLocalStorage();
    },
    /**
     * 按入库单出库时触发，
     * 1. 获取localStorage中传递的药品数据，
     * 2. 获取药品的具体信息
     * 3. 按顺序号排序药品
     * @returns {Promise<void>}
     */
    async getDataFromLocalStorage() {
      let list = JSON.parse(sessionStorage.getItem('chuKuDanList'));
      const { jiaGeIDs, yaoPinXX } = list.reduce(
        (pre, item) => {
          pre.jiaGeIDs.push(item.jiaGeID);
          pre.yaoPinXX.set(item.jiaGeID + '_' + item.shengChanPH, item);
          return pre;
        },
        { jiaGeIDs: [], yaoPinXX: new Map() },
      );
      let data = await GetYaoPinCDJGListByJGIDList({
        JiaGeIDList: jiaGeIDs,
        zuZhiJGID: getJiGouID(),
        RuKuDID: this.ruKuDanID,
      });
      let midList = [];
      data.forEach((yaoPinItem) => {
        let item = yaoPinXX.get(
          yaoPinItem.jiaGeID + '_' + yaoPinItem.shengChanPH,
        );
        item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
        item.yaoPinMCYGG = {
          baoZhuangDW: item.baoZhuangDW,
          chanDiMC: item.chanDiMC,
          danJia: item.danJia,
          guiGeID: item.guiGeID,
          jiaGeID: item.jiaGeID,
          jinJia: item.jinJia,
          kuCunSL: item.kuCunSL,
          yaoPinGG: item.yaoPinGG,
          yaoPinLXDM: item.yaoPinLXDM,
          yaoPinLXMC: item.yaoPinLXMC,
          yaoPinMC: item.yaoPinMC,
        };
        item.chuKuSL = item.ruKuSL;
        Object.assign(item, yaoPinItem);
        item.guanLianDJID = item.id;
        item.guanLianDJLX = '1';
        item.lengCangBZ = item.lengCangBZ == 64 || item.lengCangBZ == 1 ? 1 : 0;
        delete item.id;
        item = Object.assign(initData(), item);
        midList.push(item);
      });
      // 按顺序号排序
      midList.sort((a, b) => a.shunXuHao - b.shunXuHao);
      this.tableData = midList;
    },
    formatDate(date) {
      if (date) return dayjs(date).format('YYYY-MM-DD');
      return '';
    },
    /**
     * 判断出库数量是否规范
     * 输入出库数量，失焦时触发
     * @param row
     * @param rowIndex
     * @constructor
     */
    CheckChuKuSL(row, rowIndex) {
      if (this.isShowDialog) return;
      row.kuCunSL = row.kuCunSL ? Number(row.kuCunSL) : 0;
      row.chuKuSL = row.chuKuSL ? Number(row.chuKuSL) : 0;
      row.yaoKuKCSL = row.yaoKuKCSL ? Number(row.yaoKuKCSL) : 0;
      if (row.chuKuSL > row.kuCunSL && !this.isChongHong && !this.isTuiHuan) {
        row.chuKuSL = row.kuCunSL;
        MdMessage({
          type: 'warning',
          message: `出库数量应该小于库存数量`,
        });
        this.$refs.editTable.toNext(3 * rowIndex);
        this.BlurError = false;
        return;
      }
      if (row.chuKuSL > row.yaoKuKCSL && !this.isChongHong && !this.isTuiHuan) {
        row.chuKuSL = row.yaoKuKCSL;
        MdMessage({
          type: 'warning',
          message: `出库数量应该小于库存数量`,
        });
        this.$refs.editTable.toNext(3 * rowIndex);
        this.BlurError = false;
        return;
      }
      if (row.chuKuSL <= 0) {
        row.chuKuSL = 1;
        MdMessage({
          type: 'warning',
          message: `出库单的出库数量应该大于0`,
        });
        this.$refs.editTable.toNext(3 * rowIndex);
        this.BlurError = false;
        return;
      }
      // 出库单的出库数量和请领数量  不做联动
      // row.qingLingSL = row.chuKuSL;
      this.BlurError = true;
    },

    async getDetail(id) {
      const data = await GetChuKuDXQXX({ chuKuDID: id });
      this.guanLianDJLX = data.guanLianDJLX;
      if (data.ruKuKSID) {
        this.leiXingDM = '3';
        this.params.danWeiBMZCID = data.ruKuKSID;
        this.params.danWeiBMZCMC = data.ruKuKSMC;
      }
      if (data.ruKuWZID) {
        this.leiXingDM = '2';
        this.params.danWeiBMZCID = data.ruKuWZID;
        this.params.danWeiBMZCMC = data.ruKuWZMC;
      }
      if (data.diaoBoDWID) {
        this.leiXingDM = '4';
        this.params.danWeiBMZCID = data.diaoBoDWID;
        this.params.danWeiBMZCMC = data.diaoBoDWMC;
      }
      // this.params.danWeiBMZCID = data.danWeiBMID;
      // this.params.danWeiBMZCMC = data.danWeiBMMC;
      if (data.guanLianDJID) {
        this.isZiDongSC = true;
      }
      this.xiaoHaoTS = data.xiaoHaoTS;
      this.xiaoHaoKSSJ = data.xiaoHaoKSSJ;
      this.xiaoHaoJSSJ = data.xiaoHaoJSSJ;
      Object.assign(this.bottomData, {
        zhiDanRXM: data.zhiDanRXM,
        zhiDanSJ: data.zhiDanSJ,
        jinJiaJE: data.jinJiaJE,
        lingShouJE: data.lingShouJE,
        yaoPinZS: data.yaoPinZS,
      });
      Object.assign(this.params, {
        chuKuDKZList: data.chuKuDKZList,
        zhangBuLBID: data.zhangBuLBID,
        zhangBuLBMC: data.zhangBuLBMC,
        chuRuKFSID: data.chuRuKFSID,
        chuRuKFSMC: data.chuRuKFSMC,
        hongDanBZ: data.hongDanBZ,
        beiZhu: data.beiZhu,
        chuKuDH: data.chuKuDH,
        id: data.id,
        weiZhiID: data.weiZhiID,
        weiZhiMC: data.weiZhiMC,
      });
      this.oldList = cloneDeep(data.chuKuDMXList);
      data.chuKuDMXList.forEach((item) => {
        let chuRuKFS = this.chuRuKFSOptions.find(
          (x) => x.chuRuKFSID == this.params.chuRuKFSID,
        );
        if (chuRuKFS != null && chuRuKFS.kuCunZJDM == '1') {
          item.chuKuHKCSL = item.yaoKuKCSL + item.chuKuSL;
        } else {
          item.chuKuHKCSL = item.yaoKuKCSL - item.chuKuSL;
        }
        item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
        item.yaoPinMCYGG = {
          baoZhuangDW: item.baoZhuangDW,
          chanDiMC: item.chanDiMC,
          danJia: item.danJia,
          guiGeID: item.guiGeID,
          jiaGeID: item.jiaGeID,
          jinJia: item.jinJia,
          kuCunSL: item.kuCunSL,
          yaoPinGG: item.yaoPinGG,
          yaoPinLXDM: item.yaoPinLXDM,
          yaoPinLXMC: item.yaoPinLXMC,
          yaoPinMC: item.yaoPinMC,
        };
      });
      this.isTuiHuan = false;
      if (this.isChongHong) {
        data.chuKuDMXList.forEach((item) => {
          item.chuKuSL = Math.abs(item.chuKuSL);
        });
      } else if (data.chuKuDMXList[0].chuKuSL < 0) {
        this.isTuiHuan = true;
        data.chuKuDMXList.forEach((item) => {
          item.chuKuSL = Math.abs(item.chuKuSL);
        });
      }
      this.tableData = data.chuKuDMXList;
    },
    newRow() {
      return initData();
    },
    // 点击排序
    soltHandle(column) {
      this.tableData.forEach((item, index) => {
        if (!item.yaoPinMC) {
          this.tableData.splice(index, 1);
        }
        return item;
      });
      if (column.order === 'descending') {
        // 降序
        this.tableData.sort(function (a, b) {
          return a.yaoPinMCYGG < b.yaoPinMCYGG ? 1 : -1;
        });
      } else if (column.order === 'ascending') {
        // 升序
        this.tableData.sort(function (a, b) {
          return a.yaoPinMCYGG > b.yaoPinMCYGG ? 1 : -1;
        });
      }
      this.tableData.push(initData());
    },
    canSave(data) {
      if (data.length === 0 || (data.length === 1 && !data[0].jiaGeID)) {
        MdMessage({
          type: 'warning',
          message: '出库单列表为空！',
        });
        return;
      }
      if (!this.params.chuRuKFSID) {
        MdMessage({
          type: 'warning',
          message: `出库单据的出库方式不能为空！`,
        });
        return false;
      }
      if (this.isRequireDWBM && !this.params.danWeiBMZCID) {
        MdMessage({
          type: 'warning',
          message: `出库单据的单位部门不能为空！`,
        });
        return false;
      }
      if (this.showZhangBLB == 1 && !this.params.zhangBuLBID) {
        MdMessage({
          type: 'warning',
          message: `出库单据的账簿类别不能为空！`,
        });
        return false;
      }
      let flag = true;
      data.every((item, index) => {
        if (index === data.length - 1 && !data.jiaGeID) {
          return true;
        }
        if (!item.jiaGeID) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品名称与规格不能为空！`,
          });
          flag = false;
          return false;
        }
        if (item.chuKuSL <= 0) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品'${item.yaoPinMC}'的出库数量应该大于0`,
          });
          flag = false;
          return false;
        }
        if (
          item.chuKuSL > item.kuCunSL &&
          !this.isChongHong &&
          !this.isTuiHuan
        ) {
          MdMessage({
            type: 'warning',
            message: `第${index + 1}行药品'${
              item.yaoPinMC
            }'的出库数量应该小于库存数量`,
          });

          flag = false;
          return false;
        }
        return true;
      });
      return flag;
    },
    async onSave() {
      if (!this.BlurError) {
        this.BlurError = true;
        return Promise.reject();
      }
      let paramsData = cloneDeep(this.tableData);
      if (!this.canSave(paramsData)) return Promise.reject();
      //将数据转化为数字类型
      paramsData.forEach((item, index) => {
        if (item.kuCunSL) {
          item.kuCunSL = Number.parseFloat(item.kuCunSL);
        } else item.kuCunSL = 0;
        if (item.jinJiaJE) {
          item.jinJiaJE =
            this.isChongHong || this.isTuiHuan
              ? -Number(item.jinJiaJE)
              : Number(item.jinJiaJE);
        } else item.jinJiaJE = 0;
        item.jinJia = Number(item.jinJia);
        item.lingShouJia = Number(item.lingShouJia);
        item.lingShouJE =
          this.isChongHong || this.isTuiHuan
            ? -Number(item.lingShouJia)
            : Number(item.lingShouJia) * Number(item.chuKuSL);
        item.chuKuSL =
          this.isChongHong || this.isTuiHuan
            ? -Number(item.chuKuSL)
            : Number(item.chuKuSL);
        item.shunXuHao = index;
      });
      let key = this.danWeiBMLX(this.leiXingDM);
      //根据当前选中的类型代码， 给相应的id、名称赋值
      this.params[key + 'ID'] = this.params.danWeiBMZCID;
      this.params[key + 'MC'] = this.params.danWeiBMZCMC;
      let params = {
        ...this.params,
        ...this.bottomData,
        ...this.zongShuJEData,
      };
      if (this.isChongHong || this.isTuiHuan) {
        params.jinJiaJE = -Math.abs(params.jinJiaJE);
        params.lingShouJE = -Math.abs(params.lingShouJE);
      }
      if (this.ruKuDanID) {
        params.guanLianDJID = this.ruKuDanID;
        params.guanLianDJLX = '1';
      }
      var danWeiBM = this.danWeiBMZCOptions.find(
        (item) => item.danWeiBMZCID === this.params.danWeiBMZCID,
      );
      this.params.zuZhiJGID = danWeiBM == null ? null : danWeiBM.zuZhiJGID;
      this.params.zuZhiJGMC = danWeiBM == null ? null : danWeiBM.zuZhiJGMC;
      let id = null;
      if (!paramsData[paramsData.length - 1].jiaGeID) {
        paramsData.splice(paramsData.length - 1, 1);
      }
      var datas = {};
      if (this.$route.query.id) {
        this.fenLeiMXData(paramsData, params);
        datas.id = params.id;
        datas.chuRuKFSID = params.chuRuKFSID;
        datas.chuRuKFSMC = params.chuRuKFSMC;
        datas.beiZhu = params.beiZhu;
        datas.ruKuWZID =
          params.ruKuWZID == null
            ? params.ruKuWZID
            : params.ruKuWZID.split(',')[0];
        datas.ruKuWZMC = params.ruKuWZMC;
        datas.diaoBoDWID =
          params.diaoBoDWID == null
            ? params.diaoBoDWID
            : params.diaoBoDWID.split(',')[0];
        datas.diaoBoDWMC = params.diaoBoDWMC;
        datas.ruKuKSID =
          params.ruKuKSID == null
            ? params.ruKuKSID
            : params.ruKuKSID.split(',')[0];
        datas.ruKuKSMC = params.ruKuKSMC;
        datas.ruKuJGID = danWeiBM == null ? null : danWeiBM.zuZhiJGID;
        datas.ruKuJGMC = danWeiBM == null ? null : danWeiBM.zuZhiJGMC;
        datas.xiaoHaoTS = this.xiaoHaoTS; //消耗天数
        datas.xiaoHaoKSSJ = this.xiaoHaoKSSJ; //消耗开始时间
        datas.xiaoHaoJSSJ = this.xiaoHaoJSSJ; //消耗结束时间
        (datas.zhangBuLBID =
          params.zhangBuLBID == '0' ? '' : params.zhangBuLBID),
          (datas.zhangBuLBMC =
            params.zhangBuLBID == '0' ? '' : params.zhangBuLBMC),
          // if (
          //   params.zuoFeiChuKuDMXList[0] == undefined &&
          //   params.zuoFeiChuKuDMXList.length == 1
          // ) {
          //   datas.zuoFeiChuKuDMXList = [];
          // } else {
          //   const i = params.zuoFeiChuKuDMXList.length - 1;
          //   datas.zuoFeiChuKuDMXList = params.zuoFeiChuKuDMXList;
          //   datas.zuoFeiChuKuDMXList.splice(i, 1);
          // }
          (datas.zuoFeiChuKuDMXList = this.zuoFeiChuKuDMXList.concat(
            this.delPiCiList,
          ));
        datas.chuKuDKZList = params.chuKuDKZList;
        datas.addChuKuDMXList = [];
        datas.updateChuKuDMXList = [];
        params.addChuKuDMXList.forEach((element) => {
          datas.addChuKuDMXList.push({
            id: null,
            lengCangBZ: element.lengCangBZ,
            //chuKuDID:element.chuKuDID,
            jiaGeID: element.jiaGeID,
            shengChanPH: element.shengChanPH,
            yaoKuKCSL: element.yaoKuKCSL,
            YaoPinXQ: element.yaoPinXQ,
            BaiFangWZ: element.baiFangWZ,
            qingLingSL: element.qingLingSL,
            ChuKuSL: element.chuKuSL,
            KuCunSL: element.kuCunSL,
            ShunXuHao: element.shunXuHao,
            //YaoPinTHYYDM:element.yaoPinTHYYDM,
            //YaoPinTHYYMC:element.yaoPinTHYYMC,
            //PiCiKCiD:element.piCiKCiD,
            //GuanLianDJLX:element.guanLianDJLX,
            //GuanLianDJID:element.guanLianDJID,
            xiaoHaoLiang: element.xiaoHaoLiang,
            BeiZhu: element.beiZhu,
            JinJia: element.jinJia,
            LingShouJia: element.lingShouJia,
          });
        });

        params.updateChuKuDMXList.forEach((element) => {
          datas.updateChuKuDMXList.push({
            id: element.id,
            //ChuKuDID:element.ChuKuDID,
            lengCangBZ: element.lengCangBZ,
            JiaGeID: element.jiaGeID,
            ShengChanPH: element.shengChanPH,
            YaoPinXQ: element.yaoPinXQ,
            yaoKuKCSL: element.yaoKuKCSL,
            BaiFangWZ: element.baiFangWZ,
            qingLingSL: element.qingLingSL,
            ChuKuSL: element.chuKuSL,
            KuCunSL: element.kuCunSL,
            ShunXuHao: element.shunXuHao,
            //YaoPinTHYYDM:element.yaoPinTHYYDM,
            //YaoPinTHYYMC:element.yaoPinTHYYMC,
            //PiCiKCiD:element.piCiKCiD,
            GuanLianDJLX: element.guanLianDJLX,
            GuanLianDJID: element.guanLianDJID,
            BeiZhu: element.beiZhu,
            JinJia: element.jinJia,
            LingShouJia: element.lingShouJia,
            xiaoHaoLiang: element.xiaoHaoLiang,
            heBingBZ:element.heBingBZ||'',
          });
        });
        id = await UpdateChuKuDan(datas);
      } else {
        params.chuKuDMXList = paramsData;
        datas.chuKuDMXList = [];
        // 新增
        datas.chuKuDKZList = params.chuKuDKZList;
        datas.zhangBuLBID = params.zhangBuLBID == '0' ? '' : params.zhangBuLBID;
        datas.zhangBuLBMC = params.zhangBuLBID == '0' ? '' : params.zhangBuLBMC;
        params.chuKuDMXList = paramsData;
        datas.chuRuKFSID = params.chuRuKFSID;
        datas.chuRuKFSMC = params.chuRuKFSMC;
        datas.ruKuWZID =
          params.ruKuWZID == null
            ? params.ruKuWZID
            : params.ruKuWZID.split(',')[0];
        datas.ruKuWZMC = params.ruKuWZMC;
        datas.ruKuKSID =
          params.ruKuKSID == null
            ? params.ruKuKSID
            : params.ruKuKSID.split(',')[0];
        datas.ruKuKSMC = params.ruKuKSMC;
        datas.diaoBoDWID =
          params.diaoBoDWID == null
            ? params.diaoBoDWID
            : params.diaoBoDWID.split(',')[0];
        datas.diaoBoDWMC = params.diaoBoDWMC;
        datas.ruKuJGID = danWeiBM == null ? null : danWeiBM.zuZhiJGID;
        datas.ruKuJGMC = danWeiBM == null ? null : danWeiBM.zuZhiJGMC;
        // datas.TuiKuFSID = params.TuiKuFSID
        // datas.TuiKuFSMC = params.TuiKuFSMC
        datas.beiZhu = params.beiZhu;
        datas.guanLianDJLX = params.guanLianDJLX;
        datas.guanLianDJID = params.guanLianDJID;
        datas.xiaoHaoTS = this.xiaoHaoTS; //消耗天数
        datas.xiaoHaoKSSJ = this.xiaoHaoKSSJ; //消耗开始时间
        datas.xiaoHaoJSSJ = this.xiaoHaoJSSJ; //消耗结束时间

        params.chuKuDMXList.forEach((element) => {
          datas.chuKuDMXList.push({
            jiaGeID: element.jiaGeID,
            shengChanPH: element.shengChanPH,
            yaoKuKCSL: element.yaoKuKCSL,
            yaoPinXQ: element.yaoPinXQ,
            BaiFangWZ: element.baiFangWZ,
            qingLingSL: element.qingLingSL,
            ChuKuSL: element.chuKuSL,
            KuCunSL: element.kuCunSL,
            ShunXuHao: element.shunXuHao,
            lengCangBZ: element.lengCangBZ,
            duLiFLDM: element.duLiFLDM,
            duLiFLMC: element.duLiFLMC,
            //YaoPinTHYYDM:element.yaoPinTHYYDM,
            //YaoPinTHYYMC:element.yaoPinTHYYMC,
            //PiCiKCiD:element.piCiKCiD,
            xiaoHaoLiang: element.xiaoHaoLiang,
            guanLianDJLX: element.guanLianDJLX,
            guanLianDJID: element.guanLianDJID,
            BeiZhu: element.beiZhu,
            JinJia: element.jinJia,
            LingShouJia: element.lingShouJia,
            heBingBZ:element.heBingBZ||'',
          });
        });
        id = await AddChuKuDan(datas);
      }
      if (id) {
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        //this.handleDaYin(id)
      }
      return Promise.resolve(id);
    },
    closeTab() {
      let closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      // this.viewManager.close(closeTabKey);
      setTimeout(() => {
        this.viewManager.close(closeTabKey);
      }, 500);
    },
    async handleSave() {
      this.pageLoading = true;
      try {
        await this.onSave();
        this.closeTab();
        await this.$router.push({
          path: '/YaoPinCK',
          query: {
            showType: 'second',
          },
        });
      } finally {
        this.pageLoading = false;
      }
    },
    handleSaveAndJiZhang() {
      this.pageLoading = true;
      this.onSave()
        .then((id) => {
          this.handleJiZhang(this.$route.query.id ? this.$route.query.id : id)
            .then((_) => {
              this.closeTab();
              this.$router.push({
                path: '/YaoPinCK',
                query: {
                  showType: 'third',
                },
              });
            })
            .catch((e) => {
              this.closeTab();
              this.$router.push({
                path: '/YaoPinCK',
                query: {
                  showType: 'second',
                },
              });
            })
            .finally(() => {
              this.pageLoading = false;
            });
        })
        .catch((e) => {
          this.pageLoading = false;
        });
    },
    //按消耗量生成药品
    async handleAnXiaoHLSC() {
      await MdMessageBox({
        title: '提示',
        message: '按消耗量生成将会覆盖列表所有药品信息！',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      this.xiaoHaoTS = 0;
      this.xiaoHaoKSSJ = null;
      this.xiaoHaoJSSJ = null;
      this.$refs.anXiaoHaoLSCDialog.showModel().then((res) => {
        if (!res) return;
        this.pageLoading = true;
        this.xiaoHaoTS = res.xiaoHaoTS;
        this.xiaoHaoKSSJ = res.KaiShiSJ;
        this.xiaoHaoJSSJ = res.JieShuSJ;
        const params = {
          ...res,
          BeiQingLWZID: this.params.weiZhiID,
        };
        //拿到弹框选择日期之后掉接口请求数据
        GetQingLingMXByXHL(params)
          .then((res) => {
            //先清空一下之前选择的行和删除的行
            this.tableData = [];
            res.forEach((item) => {
              item.xiaoHaoTS = this.xiaoHaoTS;
              item.xiaoHaoKSSJ = this.xiaoHaoKSSJ;
              item.xiaoHaoJSSJ = this.xiaoHaoJSSJ;
              item.yaoPinMCYGG = {
                baoZhuangDW: item.baoZhuangDW,
                chanDiMC: item.chanDiMC,
                danJia: item.danJia,
                guiGeID: item.guiGeID,
                jiaGeID: item.jiaGeID,
                jinJia: item.jinJia,
                kuCunSL: item.piCiKCSL,
                yaoPinGG: item.yaoPinGG,
                yaoPinLXDM: item.yaoPinLXDM,
                yaoPinLXMC: item.yaoPinLXMC,
                yaoPinMC: item.yaoPinMC,
              };
              item.kuCunSL = item.piCiKCSL;
              item.beiQingLWZKCSL = item.muBiaoKC;
            });
            //直接将原table数据覆盖
            this.tableData = [...res, this.newRow()];
          })
          .catch((e) => {
            logger.error(e);
          })
          .finally(() => {
            this.pageLoading = false;
          });
      });
    },
    async handleShow(row) {
      this.data = [];
      this.row = row;
      const xiaoHaoKSSJ = row.xiaoHaoKSSJ
        ? dayjs(row.xiaoHaoKSSJ).format('YYYY-MM-DD')
        : dayjs(this.xiaoHaoKSSJ).format('YYYY-MM-DD');
      const xiaoHaoJSSJ = row.xiaoHaoJSSJ
        ? dayjs(row.xiaoHaoJSSJ).format('YYYY-MM-DD')
        : dayjs(this.xiaoHaoJSSJ).format('YYYY-MM-DD');
      this.timeRange = [xiaoHaoKSSJ, xiaoHaoJSSJ];
      this.getKuCunFBByJGIDAndRQ();
    },
    async handleChangeRQ(val) {
      this.data = [];
      const xiaoHaoKSSJ = dayjs(val[0]).format('YYYY-MM-DD');
      const xiaoHaoJSSJ = dayjs(val[1]).format('YYYY-MM-DD');
      this.timeRange = [xiaoHaoKSSJ, xiaoHaoJSSJ];
      this.getKuCunFBByJGIDAndRQ();
    },
    async getKuCunFBByJGIDAndRQ() {
      try {
        this.loadingXHL = true;
        const time = this.timeRange;
        let res = await GetYaoPinXHLMXList({
          jiaGeID: this.row.jiaGeID,
          kaiShiSJ: time[0],
          jieShuSJ: time[1],
        });
        if (res) {
          this.data = res;
        }
      } finally {
        this.loadingXHL = false;
      }
    },
    handleJiZhang(id) {
      return JiZhangChuKuDan({ chuKUDID: id }).then((res) => {
        MdMessage({
          type: 'success',
          message: '记账成功',
        });
      });
    },
    handleDaoChu() {},
    async handleDaYin(id) {
      const params = {
        chuKuDanID: id,
      };
      await printByUrl('YKXT005', params);
    },
    getTableComp() {
      const refs = this.isChongHong ? 'editTable1' : 'editTable';
      return this.$refs[refs];
    },
    handleDelete() {
      if (this.$route.query.id) {
        let tableRow = this.tableData.length - 1;
        let tableSelection =
          this.getTableComp().invokeTableMethod('getAllCheckedRows');
        this.zuoFeiChuKuDMXList =
          tableSelection && tableSelection.map((m) => m.id);
        if (tableSelection.length <= 0) {
          this.$message({
            message: '请选择需要删除的行',
            type: 'warning',
          });
          return;
        }
        //勾选数据时二次提示
        if (tableRow - tableSelection.length < 1) {
          MdMessageBox.confirm('是否要作废此单据？', '操作提醒！', {
            cancelButtonText: '否',
            confirmButtonText: '是',
            type: 'warning',
          })
            .then(async () => {
              await ZuoFeiChuKuDan(this.$route.query.id);
              this.$router.push({
                path: '/YaoPinCK',
                query: {
                  showType: 'second',
                },
              });
            })
            .catch(async () => {
              let tableComp = this.getTableComp();
              let tableSelection =
                tableComp.invokeTableMethod('getAllCheckedRows');
              tableSelection.forEach((item) => {
                let index = this.tableData.findIndex(
                  (item1) =>
                    item1.yaoPinMCYGG === item.yaoPinMCYGG &&
                    item1.chanDiMC === item.chanDiMC &&
                    item.shengChanPH == item1.shengChanPH,
                );
                this.$refs[
                  this.isChongHong ? 'editTable1' : 'editTable'
                ].removeRow(index);
              }, []);
              tableComp.invokeTableMethod('clearSelection');
            });
        } else {
          let tableComp = this.getTableComp();
          let tableSelection = tableComp.invokeTableMethod('getAllCheckedRows');
          tableSelection.forEach((item) => {
            let index = this.tableData.findIndex(
              (item1) =>
                item1.yaoPinMCYGG === item.yaoPinMCYGG &&
                item1.chanDiMC === item.chanDiMC &&
                item.shengChanPH == item1.shengChanPH,
            );
            this.$refs[this.isChongHong ? 'editTable1' : 'editTable'].removeRow(
              index,
            );
          }, []);
          tableComp.invokeTableMethod('clearSelection');
        }
      } else {
        let tableComp = this.getTableComp();
        let tableSelection = tableComp.invokeTableMethod('getAllCheckedRows');
        tableSelection.forEach((item) => {
          let index = this.tableData.findIndex(
            (item1) =>
              item1.yaoPinMCYGG === item.yaoPinMCYGG &&
              item1.chanDiMC === item.chanDiMC &&
              item.shengChanPH == item1.shengChanPH,
          );
          this.$refs[this.isChongHong ? 'editTable1' : 'editTable'].removeRow(
            index,
          );
        }, []);
        tableComp.invokeTableMethod('clearSelection');
      }
    },
    filterMethod(query) {
      if (query !== '') {
        this.danWeiBMZCOptions = this.danWeiBMOriginOptions.filter((item) => {
          return (
            item.danWeiBMZCMC.toLowerCase().indexOf(query.toLowerCase()) > -1 ||
            (item.shuRuMa1 &&
              item.shuRuMa1.toLowerCase().indexOf(query.toLowerCase()) > -1)
          );
        });
      } else {
        this.danWeiBMZCOptions = this.danWeiBMOriginOptions;
      }
    },
    handleLoadMore() {
      // 单位、部分分页函数
      this.getDanWeiBMOptions(this.params.chuRuKFSID);
    },
    handleSelectChange(val, key) {
      if (val && key) {
        let optionsKey = key + 'Options';
        let mingChengKey = key + 'MC';
        let daiMaKey = key + 'ID';
        let data = this[optionsKey].find((item) => item[daiMaKey] === val);
        if (key === 'chuRuKFS') {
          this.params.danWeiBMZCID = '';
          this.params.danWeiBMZCMC = '';
          //当选择的出库方式下单位部门是其他药房
          const isCaoYao = getKuCunGLLX().includes('3');
          // if (data && data.danWeiBMMC === '其他药房' && isCaoYao) {
          // this.columns[17].hidden = false;
          this.tableKey++;
          // } else {
          //   this.tableKey++;
          //   // this.columns[17].hidden = true;
          // }
          this.getDanWeiBMOptions(val);
        }

        if (data.muBiaoWZGLLX) {
          this.muBiaoWZGLLX = data.muBiaoWZGLLX;
        } else {
          this.muBiaoWZGLLX = '';
        }
        this.params[mingChengKey] = data ? data[mingChengKey] : '';
      } else {
        this.muBiaoWZGLLX = '';
      }
    },
  },
  components: {
    'biz-yaopinph': BizYaopinph,
    'biz-yaopindw': BizYaopindw,
    'piliangdjfp-dialog': PiliangdjfpDialog,
    'anxiaohl-dialog': AnXiaoHaoLSCDialog,
  },
};
</script>

<style lang="scss" scoped>
@import '../../../components/table-column-set/tableSet.scss';

.#{$md-prefix}-inventory-container {
  height: 100%;
  box-sizing: border-box;
  min-height: 0;
  background-color: #eaeff3;
  padding: 8px;
}

.#{$md-prefix}-add-inventory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;

  &-header {
    flex-shrink: 0;

    &__action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 46px;
      background-color: rgb(var(--md-color-1));
      padding: 0 8px;

      &__left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        // background-color: #e3ecf3;
        border-radius: 4px;

        // padding: 0 4px;
        .#{$md-prefix}-ckdtitle {
          font-weight: 600;
          color: rgb(var(--md-color-8));
          font-size: 16px;
          line-height: 28px;
          margin: 0 8px 0 6px;

          i {
            font-size: 18px;
          }
        }

        ::v-deep .#{$md-prefix}-input__inner {
          // background-color: #fff;
        }
      }

      &__right {
        .#{$md-prefix}-button {
          margin-left: 8px;

          &.#{$md-prefix}-button--text {
            margin-right: 4px;
          }
        }
      }
    }

    &__note {
      display: flex;
      padding: 8px;
      height: 30px;

      > span {
        width: 70px;
        color: #333333;
        font-size: var(--md-font-2);
        line-height: 30px;
        text-align: right;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .#{$md-prefix}-margin-left-8 {
        margin-left: 8px;
      }

      .#{$md-prefix}-input-class {
        flex: 1;
      }
    }
  }

  &-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 0 8px;

    ::v-deep .#{$md-prefix}-table-set-icon {
      padding-top: 4px;
    }

    ::v-deep .#{$md-prefix}-tooltip {
      min-width: 40px;
    }

    ::v-deep .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .#{$md-prefix}-dingwei-bg {
        td {
          background: #e2efff;
        }
      }

      .#{$md-prefix}-table.#{$md-prefix}-table--edit {
        min-height: 0;
        display: flex;
        flex-direction: column;
      }

      .#{$md-prefix}-base-table__header-wrapper {
        flex-shrink: 0;

        td.td-text.is-center {
          padding-left: 0px;
        }
      }

      .#{$md-prefix}-base-table__body-wrapper {
        overflow: auto;
        flex: 1;

        .#{$md-prefix}-base-table__row.heightLight {
          > td {
            background: #e2efff;
          }
        }
      }

      .cell {
        .#{$md-prefix}-date-editor {
          width: 100%;
        }
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    flex-shrink: 0;
    line-height: 20px;
    font-size: 14px;

    &__info {
      display: flex;

      .#{$md-prefix}-info__name {
        margin-right: 8px;
      }

      .#{$md-prefix}-margin-right-12 {
        margin-right: 12px;
      }

      &.#{$md-prefix}-right {
        span {
          color: #aaa;
        }
      }
    }

    span {
      color: #666666;

      &.#{$md-prefix}-color-222 {
        color: #222222;
      }

      &.#{$md-prefix}-font-bold {
        font-weight: 600;
        color: #222222;
      }
    }
  }
}

::v-deep .#{$md-prefix}-base-table .#{$md-prefix}-checkbox {
  margin-right: 0;
}

.#{$md-prefix}-yaopin-select {
  &::v-deep .biz-input {
    height: 33px;
    border-color: transparent;

    &.focus {
      border-radius: 0;
    }
  }
}

::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}

::v-deep .#{$md-prefix}-select {
  width: 210px;
}

::v-deep .#{$md-prefix}-base-table-column--selection > .cell {
  justify-content: center;
}

::v-deep .yaoPinLXMCHeader > .cell {
  display: none;
}

.#{$md-prefix}-xiaohaoliang {
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    // color: #1e88e5;
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}
</style>
