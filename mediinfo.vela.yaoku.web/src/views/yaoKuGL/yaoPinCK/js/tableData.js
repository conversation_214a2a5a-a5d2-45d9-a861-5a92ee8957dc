// import { Get<PERSON>an<PERSON>hu<PERSON>hi } from '@/system/utils/canShu';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { MdInput } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { h } from 'vue';
let jinJiaXSDW = '';
let lingShouXSDW = '';
let jinJiaJEXSDW = 2;
let lingShouJEXSDW = 2;
let xiaoShuDianWS = 2;

const canShu = async () => {
  try {
    const res = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  } catch (error) { }
  //如果是中药库
  const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
  // 判断进价零售价是否设置了值，没有则赋默认值
  jinJiaXSDW = jinJiaXSDW ? jinJiaXSDW : xiaoShuDianWS;
  lingShouXSDW = lingShouXSDW ? lingShouXSDW : xiaoShuDianWS;
};
canShu();
export default {
  columns: [
    {
      prop: 'qingLingDH',
      label: '请领单号',
      minWidth: 120,
      showOverflowTooltip: false,
    },
    {
      prop: 'qingLingLY',
      label: '请领来源',
      width: 80,
    },
    {
      prop: 'qingLingLXMC',
      label: '请领类型',
      width: 80,
    },
    {
      prop: 'yaoPinZS',
      label: '药品数',
      width: 60,
      align: 'right',
    },
    {
      prop: 'zhiDanSJ',
      label: '请领日期',
      width: 100,
      formatter: (row, column, cellValue, index) => {
        return dayjs(cellValue).format('YYYY-MM-DD');
      },
    },
  ],
  anRuKuDanColumns: [
    {
      prop: 'ruKuDH',
      label: '入库单号',
      minWidth: 120,
      showOverflowTooltip: false,
      field: true,
    },
    {
      prop: 'gongHuoDWMC',
      label: '供货单位',
      minWidth: 150,
      field: true,
    },
    {
      prop: 'chuRuKFSMC',
      label: '入库方式',
      width: 80,
      field: true,
    },
    {
      prop: 'yaoPinZS',
      label: '药品数',
      width: 60,
      align: 'right',
      field: true,
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      width: 80,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(jinJiaJEXSDW);
      },
      field: true,
    },
    {
      prop: 'zhiDanSJ',
      label: '制单日期',
      width: 100,
      sortable: true,
      formatter: (row) => {
        return yaoKuZDJZTimeShow(row.zhiDanSJ);
      },
      field: true,
    },
    {
      prop: 'zhiDanRXM',
      label: '制单人',
      sortable: true,
      width: 110,
      control: true
    },
  ],
  rightColumns: [
    {
      label: '序号',
      type: 'index',
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
      align: 'center',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      prop: 'shengPingTBM',
      label: '省平台ID',
      minWidth: 100,
      showOverflowTooltip: true,
    },
    {
      prop: 'yaoPinMCYGG',
      label: '药品名称与规格',
      minWidth: 100,
      showOverflowTooltip: true,
      formatter: (row, column, cellValue, index) => {
        return row.yaoPinMC + ' ' + row.yaoPinGG;
      },
      sortable: true
    },
    {
      prop: 'chanDiMC',
      label: '产地',
      showOverflowTooltip: true,
      minWidth: 80,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 50,
    },
    {
      prop: 'qingLingSL',
      label: '申请数量',
      width: 85,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'daiChuKSL',
      label: '待出库数量',
      width: 100,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'dangQianKCSL',
      label: '当前库存',
      width: 85,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      slot: 'jiYongBZ',
      label: '急用',
      width: 50,
      align: 'center',
    },
  ],
  anRuKuDanRightColumns: [
    {
      label: '序号',
      type: 'index',
    },
    {
      type: 'selection',
      width: 40,
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
      type: 'text',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      prop: 'faPiaoHM',
      label: '发票号码',
      width: 100,
    },
    {
      prop: 'faPiaoRQ',
      label: '发票日期',
      width: 100,
      formatter: (row, column, cellValue, index) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      prop: 'shengPingTBM',
      label: '省平台ID',
      minWidth: 100,
      showOverflowTooltip: true,
    },
    {
      prop: 'yaoPinMCYGG',
      label: '药品名称与规格',
      minWidth: 150,
      showOverflowTooltip: true,
      sortable: 'custom',
      formatter: (row, column, cellValue, index) => {
        return row.yaoPinMC + ' ' + row.yaoPinGG;
      },
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      showOverflowTooltip: true,
      width: 120,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'ruKuSL',
      label: '入库数量',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'jinJia',
      label: '进价',
      align: 'right',
      width: 90,
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(jinJiaXSDW);
      },
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      align: 'right',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(jinJiaJEXSDW);
      },
    },
    {
      prop: 'jinXiaoCJL',
      label: '扣率',
      width: 100,
      align: 'right',
      formatter: (row) => {
        return row.kouLv
          ? row.kouLv
          : row.lingShouJia && row.jinJia
            ? (Number(row.jinJia) / Number(row.lingShouJia)).toFixed(
              xiaoShuDianWS,
            )
            : '';
      },
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      width: 120,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(lingShouXSDW);
      },
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      width: 120,
      align: 'right',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(lingShouJEXSDW);
      },
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      width: 120,
    },
    {
      prop: 'yaoPinXQ',
      label: '药品效期',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      prop: 'zuiJinCKSCPH',
      label: '最近出库批号',
      width: 120,
      align: 'right',
      type: 'text',
      hidden: true,
    },
    {
      prop: 'zhuCeSB',
      label: '注册商标',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return cellValue == 1 ? '有' : '无';
      },
    },
    {
      prop: 'chuChangJYHGD',
      label: '产品合格证',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return cellValue == 1 ? '合格' : '不合格';
      },
    },
    {
      prop: 'baoZhuangQK',
      label: '包装',
      width: 60,
      formatter: (row, column, cellValue, index) => {
        return cellValue == 1 ? '合格' : '不合格';
      },
    },
    {
      prop: 'waiGuanZL',
      label: '外观情况',
      width: 85,
      formatter: (row, column, cellValue, index) => {
        return cellValue == 1 ? '完整' : '不完整';
      },
    },
    {
      prop: 'yanShouJL',
      label: '验收结果',
      width: 120,
      formatter: (row, column, cellValue, index) => {
        return cellValue == 1 ? '合格' : '不合格';
      },
    },
    {
      prop: 'yanShouRXM',
      label: '验收人',
      width: 120,
    },
    {
      prop: 'piZhunWH',
      label: '批准文号',
      width: 120,
    },
    {
      prop: 'jinKouYPZH',
      label: '进口药品证号',
      width: 120,
    },
    {
      prop: 'guoJiaYBDM',
      label: '国家医保代码',
      width: 120,
    },
    {
      prop: 'guoJiaYBMC',
      label: '国家医保名称',
      width: 120,
    },
    {
      prop: 'baiFangWZ',
      label: '摆放位置',
      width: 120,
    },
  ],
  xinZengCKDColumns: [
    {
      type: 'selection',
      width: 40,
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
      align: 'center',
      type: 'text',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      prop: 'shengPingTBM',
      label: '省平台ID',
      minWidth: 100,
      showOverflowTooltip: true,
    },
    {
      prop: 'yaoPinMCYGG',
      label: '药品名称与规格',
      width: 300,
      showOverflowTooltip: true,
      slot: 'yaoPinMCYGG',
      formatter: (row, column, cellValue, index) => {
        if (cellValue.yaoPinMC && cellValue.yaoPinGG)
          return cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG;
        return cellValue;
      },
      renderHeader: ({ column, _self }) => {
        return h('div', { class: _self.prefixClass('require') }, column.label);
      },
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      minWidth: 160,
      type: 'text',
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
      type: 'text',
    },
    {
      prop: 'kuCunZL',
      label: '库存总量',
      width: 90,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      width: 90,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'chuKuSL',
      label: '出库数量',
      width: 90,
      renderHeader: ({ column, _self }) => {
        return h('div', { class: _self.prefixClass('require') }, column.label);
      },
      slot: 'chuKuSL',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
      align: 'right',
    },
    {
      prop: 'jinJia',
      label: '进价',
      width: 120,
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(xiaoShuDianWS);
      },
      align: 'right',
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      width: 120,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      width: 120,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      width: 120,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      width: 120,
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
    },
    {
      prop: 'yaoPinXQ',
      label: '药品效期',
      width: 120,
      showOverflowTooltip: false,
      formatter: (row, column, cellValue, index) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      type: 'text',
      startMode: 'click',
      endMode: 'custom',
    },
    {
      prop: 'baiFangWZ',
      label: '摆放位置',
      width: 120,
      type: 'text',
      // slot: 'baiFangWZ',
      // formatter: (row, column, cellValue, index) => {
      //   return cellValue
      // },
      // startMode: 'click',
      // endMode: 'custom'
    },
    {
      prop: 'lingChaJBZ',
      label: '零差/加价',
      width: 90,
      align: 'center',
      type: 'text',
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.lingChaJBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'mianFeiYPBZ',
      label: '免',
      width: 40,
      align: 'center',
      type: 'text',
      showOverflowTooltip: false,
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.mianFeiYPBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'zengPinBZ',
      label: '赠',
      width: 40,
      type: 'text',
      align: 'center',
      showOverflowTooltip: false,
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.zengPinBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'beiZhu',
      label: '备注',
      width: 78,
      component: MdInput,
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
    },
  ],
  xinZengCHCKDColumns: [
    {
      label: '序号',
      type: 'index',
    },
    {
      type: 'selection',
      width: 40,
    },
    {
      prop: 'yaoPinLX',
      label: '',
      width: 34,
      align: 'center',
      type: 'text',
      formatter: (row) => {
        let data = commonData.yaoPinLBArr.find(
          (item) => item.name === row.yaoPinLXMC,
        );
        return data ? data.tag : '';
      },
    },
    {
      prop: 'shengPingTBM',
      label: '省平台ID',
      minWidth: 100,
      showOverflowTooltip: true,
    },
    {
      prop: 'yaoPinMCYGG',
      label: '药品名称与规格',
      width: 240,
      type: 'text',
      showOverflowTooltip: true,
      formatter: (row, column, cellValue, index) => {
        if (cellValue.yaoPinMC && cellValue.yaoPinGG)
          return cellValue.yaoPinMC + ' ' + cellValue.yaoPinGG;
        return cellValue;
      },
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      minWidth: 160,
      type: 'text',
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
      type: 'text',
    },
    {
      prop: 'kuCunSL',
      label: '库存数量',
      width: 90,
      align: 'right',
      type: 'text',
    },
    {
      prop: 'chuKuSL',
      label: '出库数量',
      width: 90,
      renderHeader: ({ column, _self }) => {
        return h('div', { class: _self.prefixClass('require') }, column.label);
      },
      slot: 'chuKuSL',
      previewSlot: 'chuKuSLYL',
      align: 'right',
    },
    {
      prop: 'chuKuHKCSL',
      label: '出库后库存数量',
      width: 120,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'jinJia',
      label: '进价',
      width: 120,
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(xiaoShuDianWS);
      },
      align: 'right',
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      width: 120,
      align: 'right',
      type: 'text',
      render: (h, params) => {
        return (
          <span style="color: #f12933;">
            {Number(params.row.jinJiaJE).toFixed(xiaoShuDianWS)}
          </span>
        );
      },
    },
    {
      prop: 'lingShouJia',
      label: '零售价',
      width: 120,
      align: 'right',
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return Number(formatJiaGe(cellValue)).toFixed(xiaoShuDianWS);
      },
    },
    {
      prop: 'lingShouJE',
      label: '零售金额',
      width: 120,
      align: 'right',
      type: 'text',
      render: (h, params) => {
        return (
          <span style="color: #f12933;">
            {Number(params.row.lingShouJE).toFixed(xiaoShuDianWS)}
          </span>
        );
      },
    },
    {
      prop: 'shengChanPH',
      label: '生产批号',
      width: 120,
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
    },
    {
      prop: 'yaoPinXQ',
      label: '药品效期',
      width: 120,
      showOverflowTooltip: false,
      formatter: (row, column, cellValue, index) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      type: 'text',
      startMode: 'click',
      endMode: 'custom',
    },
    {
      prop: 'baiFangWZ',
      label: '摆放位置',
      width: 120,
      type: 'text',
      // slot: 'baiFangWZ',
      // formatter: (row, column, cellValue, index) => {
      //   return cellValue
      // },
      // startMode: 'click',
      // endMode: 'custom'
    },
    {
      prop: 'lingChaJBZ',
      label: '零差/加价',
      width: 90,
      align: 'center',
      type: 'text',
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.lingChaJBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'mianFeiYPBZ',
      label: '免',
      width: 40,
      align: 'center',
      type: 'text',
      showOverflowTooltip: false,
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.mianFeiYPBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'zengPinBZ',
      label: '赠',
      width: 40,
      type: 'text',
      align: 'center',
      showOverflowTooltip: false,
      render: (h, { row }) => {
        return h('i', {
          class: ['iconfont', row.zengPinBZ == 1 ? 'icongou' : ''],
          style: {
            color: '#1e88e5',
          },
        });
      },
    },
    {
      prop: 'beiZhu',
      label: '备注',
      width: 78,
      type: 'text',
      formatter: (row, column, cellValue, index) => {
        return cellValue;
      },
    },
  ],
};
