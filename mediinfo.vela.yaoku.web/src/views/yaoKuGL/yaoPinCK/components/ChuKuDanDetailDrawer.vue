<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('chukudanrawer')"
    :modalClass="prefixClass('chukudanrawermodal')"
    ref="chukudandrawer"
    @closed="handlerClose"
  >
    <div :class="prefixClass('drawer__alias')" v-loading="pageLoading">
      <div :class="prefixClass('ckd-title')">
        <span :class="prefixClass('title-left')">
          {{ title }}
          <span v-if="shiFouJY == 1" :class="prefixClass('jiyongtag')">急</span>
          <span v-if="rowData.hongDanBZ" :class="prefixClass('chonghongBZ')"
            >冲</span
          >
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <!-- <md-button
              type="primary"
              :icon="prefixClass('icon-daochu')"
              @click="handleDaoChu"
              noneBg
              >导出
            </md-button> -->
            <md-button
              v-if="!isYiJiZhang && showWeiJiZQLD == 1"
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan('qld')"
              >请领单打印</md-button
            >
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan('')"
              >预览</md-button
            >
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleDaYin"
              noneBg
              >打印
            </md-button> -->
            <md-button
              v-if="!isYiJiZhang"
              type="primary"
              @click="handleJiZhang"
              noneBg
            >
              <i
                class="iconfont iconedit"
                style="font-size: 14px; margin-right: 4px"
              />记账
            </md-button>
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <div :class="prefixClass('content-zhuangtai')">
          <div :class="prefixClass('content-description-item')">
            <label>出库方式：</label><span>{{ rowData.chuRuKFSMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>单位部门：</label><span>{{ rowData.danWeiBMMC }}</span>
          </div>
          <div
            :class="prefixClass('content-description-item')"
            v-if="showZhangBLB"
          >
            <label>账簿类别：</label><span>{{ rowData.zhangBuLBMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>备注：</label><span>{{ rowData.beiZhu }}</span>
          </div>
        </div>
        <div :class="prefixClass('content-table')">
          <md-table
            :columns="columns"
            :cell-class-name="handleChongHongSLColor"
            :data="rowData.chuKuDMXList"
            height="100%"
            :level="level"
            :controlLevel="controlLevel"
            :customLevels="customLevels"
            :control-loading="controlLoading"
            :controlColumnLayout="controlColumnLayout"
            :controlExtraColumns="controlExtraColumns"
            @getNewColumn="getNewColumn"
            @recovery-column="recoveryColumn"
            @control-cancel="controlCancel"
            @level-change="levelChange"
            @sort-change="handleSortChange"
          >
            <template #caoZuoSZDM="{ row, cellRef }">
              <md-select
                v-model="row.caoZuoSZDM"
                :disabled="row.canShuRu"
                @change="CaoZuoSZChange($event, row, cellRef)"
              >
                <md-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </md-select>
            </template>
            <template #lieMingCheng="{ row }">
              <span
                :class="prefixClass('lieMingCheng')"
                style="margin-left: 8px"
              >
                {{ row.lieMingCheng }}
              </span>
            </template>
            <template #bieMing="{ row }">
              <md-input v-model="row.bieMing" placeholder="请填写" clearable />
            </template>
            <template #mianFeiYPBZ="{ row }">
              <i
                v-if="row.mianFeiYPBZ"
                class="iconfont icongou"
                style="color: #f12933; font-size: 16px"
              />
            </template>
            <template v-slot:xiaoHaoLiang="{ row }">
              <md-popover
                placement="bottom"
                title=""
                :width="315"
                trigger="click"
              >
                <div>
                  <md-date-picker
                    v-model="timeRange"
                    type="daterange"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled="true"
                    style="width: 93%; margin-bottom: 8px"
                  />
                  <md-table :columns="columnsXHL" :data="data" />
                </div>
                <template v-slot:reference>
                  <span
                    :class="prefixClass('xiaohaoliang')"
                    @click="handleRowXHL(row)"
                  >
                    {{ Math.abs(Number(row.xiaoHaoLiang)) }}
                  </span>
                </template>
              </md-popover>
            </template>
            <template v-slot:lengCangBZ="{ row }">
              <div
                v-if="row.lengCangBZ == 1 || row.lengCangBZ == 64"
                :class="prefixClass('lengcangbz')"
              >
                <i class="iconfont icongou" />
              </div>
              <div v-else></div>
            </template>
            <template #zengPinBZ="{ row }">
              <i
                v-if="row.zengPinBZ"
                class="iconfont icongou"
                style="color: #f12933; font-size: 16px"
              />
            </template>
          </md-table>
        </div>

        <div :class="prefixClass('description pos-bottom-direction')">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">
              {{
                feiKongShow(rowData.zhiDanRXM) +
                ' ' +
                formatDate(rowData.zhiDanSJ)
              }}
            </span>
            <span
              v-if="isYiJiZhang"
              :class="prefixClass('description-item__label')"
            >
              记账:
            </span>
            <span
              v-if="isYiJiZhang"
              :class="prefixClass('description-item__content')"
            >
              {{
                feiKongShow(rowData.jiZhangRXM) +
                ' ' +
                formatDate(rowData.jiZhangSJ)
              }}
            </span>
          </div>
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')">
              {{ rowData.yaoPinZS }}
              <span :class="prefixClass('content-color')">种药品</span></span
            >
            <span :class="prefixClass('description-item__label')"
              >合计 进价金额:</span
            >
            <span
              :class="
                prefixClass([
                  'description-item__content fontWeight',
                  fuShuXS ? 'chonghong-number__color' : '',
                ])
              "
            >
              {{
                Math.abs(Number(rowData.jinJiaJEHJ)).toFixed(this.xiaoShuDianWS)
              }}
              <span :class="prefixClass('content-color')">元</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >零售金额:</span
            >
            <span
              :class="
                prefixClass([
                  'description-item__content fontWeight',
                  fuShuXS ? 'chonghong-number__color' : '',
                ])
              "
            >
              {{
                Math.abs(Number(rowData.lingShouJEHJ)).toFixed(
                  this.xiaoShuDianWS,
                )
              }}
              <span :class="prefixClass('content-color')">元</span>
            </span>
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="params.id"
        :fileName="params.fileName"
        :title="params.title"
      />
    </div>
  </md-drawer>
</template>

<script>
import columnMixin from '@/components/mixin/columnMixin';
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetYaoPinXHLMXList } from '@/service/yaoPinYK/caiGouJH.js';
import { GetChuKuDXQXX } from '@/service/yaoPinYK/yaoPinCK';
import commonData from '@/system/utils/commonData';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { add } from '@/system/utils/mathComputed';
import { printByUrl } from '@/system/utils/print';
import { makePY } from '@/system/utils/wubi-pinyin.js';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { sortBy } from 'lodash-es';
export default {
  name: 'chukudan-drawer',
  mixins: [columnMixin],
  props: {
    size: { type: [String, Number], default: '75%' },
    xiaoShuDianWS: { type: [String, Number], default: 3 },
    isZhongYaoKXS: { type: [String, Number], default: 3 },
    showZhangBLB: { type: [String, Number], default: '0' },
    showWeiJiZQLD: { type: [String, Number], default: '0' },
  },
  data() {
    return {
      drawer: false,
      pageLoading: false,
      params: {},
      title: '',
      type: 1, // 默认1 1-未记账 2-已记账
      shiFouJY: 0,
      zhuangTai: 0,
      rowData: {
        danWeiBMMC: '-',
        chuRuKFSMC: '-',
        beiZhu: '-',
        zhiDanRXM: '',
        zhiDanSJ: '',
        jiZhangRXM: '',
        jiZhangSJ: '',
        chuKuDMXList: [],
        yaoPinZS: 0,
        jinJiaJEHJ: 0,
        lingShouJEHJ: 0,
        hongDanBZ: 0,
      },
      fuShuXS: false,
      isShow: true,
      timeRange: [],
      columnsXHL: [
        {
          label: '药房',
          prop: 'weiZhiMC',
          width: 96,
          align: 'left',
        },
        {
          label: '消耗量',
          prop: 'xiaoHaoLiang',
          width: 100,
          align: 'right',
        },
        {
          label: '库存数量',
          prop: 'kuCunSL',
          width: 100,
          align: 'right',
        },
      ],
      data: [],
      columns: [
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'yaoPinLX',
          label: '药品类型',
          width: 34,
          type: 'text',
          field: true,
          formatter: (row) => {
            let data = commonData.yaoPinLBArr.find(
              (item) => item.name === row.yaoPinLXMC,
            );
            return data ? data.tag : '';
          },
          labelClassName: 'yaoPinLXMCHeader',
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          minWidth: 100,
          field: true,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 150,
          field: true,
          showOverflowTooltip: true,
          formatter: (row) => {
            return row.yaoPinMC + ' ' + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          showOverflowTooltip: true,
          width: 120,
          field: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
          field: true,
        },
        {
          prop: 'kuCunZL',
          label: '全院库存',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return parseFloat(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'yaoKuKCSL',
          label: '药库库存',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return parseFloat(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'kuCunSL',
          label: '库存数量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return parseFloat(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'xiaoHaoLiang',
          slot: 'xiaoHaoLiang',
          label: '消耗量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return parseFloat(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'chuKuSL',
          label: '出库数量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'qingLingSL',
          label: '请领数量',
          align: 'right',
          width: 90,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'chuKuHKCSL',
          label: '出库后库存数量',
          align: 'right',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Number(formatJiaGe(cellValue)).toFixed(this.isZhongYaoKXS);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          width: 120,
          field: true,
          formatter: (row, column, cellValue, index) => {
            return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          field: true,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          field: true,
          formatter: (row, column, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'lengCangBZ',
          slot: 'lengCangBZ',
          label: '冷藏',
          width: 80,
          type: 'text',
          field: true,
        },
        {
          prop: 'duLiFLMC',
          label: '毒理分类',
          width: 90,
          type: 'text',
          field: true,
        },
        {
          prop: 'guoJiaYBDM',
          label: '国家医保代码',
          width: 120,
          field: true,
        },
        {
          prop: 'guoJiaYBMC',
          label: '国家医保名称',
          width: 120,
          field: true,
        },
        {
          prop: 'baiFangWZ',
          label: '摆放位置',
          width: 120,
          field: true,
          // sortable: 'custom',
        },
        {
          prop: 'zhongBaoZXS',
          label: '中包装',
          type: 'text',
          width: 140,
          field: true,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          width: 78,
          field: true,
          control: true,
          fieldDisabled: true,
        },
      ],
      controlExtraColumns: [
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 5,
        },
      ],
    };
  },
  computed: {
    isYiJiZhang() {
      return this.type === 2;
    },
  },
  mounted() {
    // window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    // window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    //升降序
    handleSortChange({ column, prop, order }) {
      this.propty = prop;
      this.order = order;
      if (order) {
        //按照首字母排序
        this.rowData.chuKuDMXList = sortBy(
          this.rowData.chuKuDMXList,
          (item) => {
            // 获取中文首字母
            const pinyin = makePY(item.baiFangWZ);
            return pinyin.toLocaleUpperCase();
          },
        );
        // }

        if (order === 'descending') {
          this.rowData.chuKuDMXList.reverse();
        }
      }
    },
    feiKongShow(str, defaultString = '') {
      return str ? str : defaultString;
    },
    handleChongHongSLColor({ column }) {
      //冲红 退库时 出库数量、进价金额、零售金额、显示为红色
      if (
        this.fuShuXS &&
        (column.property === 'chuKuSL' ||
          column.property === 'jinJiaJE' ||
          column.property === 'lingShouJE')
      ) {
        return this.prefixClass('chonghong-number__color');
      }
      if (this.rowData.kuCunZJDM == '1' && column.property === 'chuKuSL') {
        return this.prefixClass('chonghong-number__color');
      }
    },
    formatDate(date) {
      return yaoKuZDJZTimeShow(date);
    },
    jiSuanJE(data) {
      let lingShouJEHJ = 0;
      let jinJiaJEHJ = 0;
      data.forEach((item) => {
        lingShouJEHJ = add(lingShouJEHJ, Number(item.lingShouJE));
        jinJiaJEHJ = add(jinJiaJEHJ, Number(item.jinJiaJE));
      });
      this.rowData.lingShouJEHJ = lingShouJEHJ;
      this.rowData.jinJiaJEHJ = jinJiaJEHJ;
      this.rowData.yaoPinZS = data.length;
    },
    handleClickBodyCloseDrawer(e) {
      this.closeDrawer();
      // if (!this.$refs.chukudandrawer.$el.contains(e.target)) {
      // }
    },
    //打开
    async openDrawer(option) {
      this.pageLoading = true;
      try {
        this.drawer = true;
        this.getColumnInit();
        let data = await GetChuKuDXQXX({ chuKuDID: option.id });
        Object.assign(this.rowData, data);

        this.fuShuXS =
          this.rowData.hongDanBZ === 1 ||
          (this.rowData && this.rowData.chuRuKFSID === '1349');
        this.type = option.type ? option.type : 1;
        this.title = `详情 - ${data.chuKuDH}`;
        this.rowData.chuKuDMXList.forEach((item) => {
          item.chuKuHKCSL = item.yaoKuKCSL - item.chuKuSL;
        });
        const isCaoYao = getKuCunGLLX().includes('3');
        if (data && data.danWeiBMMC === '其他药房' && isCaoYao) {
          this.columns[15].hidden = false;
        } else {
          this.columns[15].hidden = true;
        }
        this.jiSuanJE(this.rowData.chuKuDMXList);
      } finally {
        this.pageLoading = false;
      }
      return new Promise((resolve, reject) => {
        this.finish = resolve;
        this.reject = reject;
      });
    },
    handleDaoChu() {},
    //预览
    async handleYuLan(type) {
      const params = {
        chuKuDanID: this.rowData.id,
        id: type ? 'YKXT032' : 'YKXT005',
        fileName: type ? '请领单' : '药品出库单',
        title: type ? '请领单打印预览' : '药品出库单打印预览',
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    async handleDaYin() {
      try {
        this.pageLoading = true;
        const params = {
          chuKuDanID: this.rowData.id,
        };
        await printByUrl('YKXT005', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    async handleJiZhang() {
      this.$emit('jizhangckd', this.rowData);
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    handlerClose() {
      this.$emit('closed');
    },
    async handleRowXHL(row) {
      this.data = [];
      const xiaoHaoKSSJ = row.xiaoHaoKSSJ
        ? dayjs(row.xiaoHaoKSSJ).format('YYYY-MM-DD')
        : dayjs().add(-2, 'day').format('YYYY-MM-DD');
      const xiaoHaoJSSJ = row.xiaoHaoJSSJ
        ? dayjs(row.xiaoHaoJSSJ).format('YYYY-MM-DD')
        : dayjs().startOf('day').format('YYYY-MM-DD');
      const time = [xiaoHaoKSSJ, xiaoHaoJSSJ];
      this.timeRange = time;
      let res = await GetYaoPinXHLMXList({
        jiaGeID: row.jiaGeID,
        kaiShiSJ: time[0],
        jieShuSJ: time[1],
      });
      if (res) {
        this.data = res;
      }
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>
<style lang="scss">
.yaoPinLXMCHeader > .cell {
  display: none;
}
.#{$md-prefix}-chukudanrawermodal {
  position: initial !important;
  overflow: hidden;
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-chukudanrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;
  height: 100%;
  overflow: hidden;
  .#{$md-prefix}-lengcangbz {
    text-align: center;
    color: #1e88e5;
  }
  .#{$md-prefix}-drawer__alias {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .#{$md-prefix}-ckd-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;
      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;
        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }
        .#{$md-prefix}-chonghongBZ {
          width: 20px;
          height: 20px;
          background-color: #f12933;
          border-radius: 2px;
          color: #ffffff;
          font-size: 12px;
          text-align: center;
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 2px 5px;
          line-height: 14px;
        }
      }
      .#{$md-prefix}-title-toolbar {
        margin-right: 18px;
      }
      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }
    .#{$md-prefix}-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      // margin-top: 10px;
      padding: 0 8px 8px 8px;
      overflow: hidden;
      ::v-deep .caozuo-icon {
        transform: translateY(3px);
      }
      &-description-item {
        margin-top: 12px;
        &:nth-last-child(n + 1) {
          margin-right: 48px;
        }
        > label {
          height: 20px;
          color: #666666;
          font-size: 14px;
          line-height: 20px;
        }
        > span {
          height: 20px;
          color: #222222;
          font-size: 14px;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }
      .#{$md-prefix}-xiaohaoliang {
        cursor: pointer;
        color: rgb(var(--md-color-6));

        &:hover {
          // color: #1e88e5;
          color: rgb(var(--md-color-6));
          text-decoration: underline;
          line-height: 20px;
        }
      }
      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }
      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
        margin-bottom: 12px;
      }

      .#{$md-prefix}-content-table {
        flex: 1;
        min-height: 0;
      }
      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        margin: 3px 8px 0 0;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        &.#{$md-prefix}-shouli {
          background-color: #e2efff;
          color: #1e88e5;
        }
        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }
      .#{$md-prefix}-description {
        display: flex;
        &-item {
          line-height: 20px;
          min-height: 20px;
          font-size: 14px;
          color: #333;
          padding: 5px 0;
          &__label {
            color: #666;
            margin-left: 8px;
          }
          &__content {
            padding-left: 5px;
            &.#{$md-prefix}-fontWeight {
              font-weight: bold;
            }
            .#{$md-prefix}-content-color {
              color: #666;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
}
.#{$md-prefix}-pos-bottom-direction {
  justify-content: space-between;
}
::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}
::v-deep .#{$md-prefix}-description-item {
  color: #222;
}
::v-deep .#{$md-prefix}-description-item {
  font-size: 14px;
}
::v-deep .#{$md-prefix}-chonghong-number__color .cell {
  color: #f12933 !important;
}
</style>
*/
