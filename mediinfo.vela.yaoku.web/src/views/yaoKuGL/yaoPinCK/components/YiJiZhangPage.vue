<template>
  <div :class="prefixClass('weijizhang-box')">
    <div :class="prefixClass('weijizhang-content-top')">
      <div :class="prefixClass('weijizhang-content-top-filters')">
        <md-date-picker-range-pro
          v-model="timeRange"
          style="margin-right: 8px; width: 250px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        >
        </md-date-picker-range-pro>
        <md-select
          v-if="showZhangBLB == 1"
          v-model="query.zhangBuLBID"
          @change="handleSearch"
          placeholder=""
          style="margin-right: 8px; width: 140px"
        >
          <md-option
            v-for="item in zhangBuLBOptions"
            :value="item.zhangBuLBID"
            :label="item.zhangBuLBMC"
            :key="item.zhangBuLBID"
          ></md-option>
        </md-select>
        <md-select
          v-model="query.chuRuKFSID"
          filterable
          style="margin-right: 8px; width: 140px"
          @clear="handleClear"
          @change="handleSelectChange($event, 'chuRuKFS')"
        >
          <md-option
            v-for="item in chuRuKFSOptions"
            :key="item.chuRuKFSID"
            :label="item.chuRuKFSMC"
            :value="item.chuRuKFSID"
          />
        </md-select>
        <md-select
          v-model="query.danWeiBMID"
          :disabled="!query.chuRuKFSID"
          :class="prefixClass('filter-width')"
          style="margin-right: 8px"
          :remote-method="filterMethod"
          remote
          filterable
          placeholder="输入选择单位部门"
          @change="handleSelectChange($event, 'danWeiBM')"
        >
          <md-option
            v-for="item in danWeiBMOptions"
            :key="item.danWeiBMID"
            :label="item.danWeiBMMC"
            :value="item.danWeiBMID"
          />
        </md-select>
        <md-input
          v-model="query.chuKuDH"
          :class="prefixClass('filter-width')"
          placeholder="输入单据号搜索"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('input__icon icon-seach')"
            slot="suffix"
            @click="handleSearch"
          />
        </md-input>
        <biz-yaopindw
          v-model="yaoPinMCObj"
          placeholder="名称、输入码、别名、规格等关键字进行搜索"
          style="width: 340px; margin-left: 8px"
          @change="handleQueryYaoPinMC"
        >
        </biz-yaopindw>
      </div>
      <div :class="prefixClass('weijizhang-content-top-buttons')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-dayinji')"
          style="margin-left: auto"
          @click="handlerPLDY"
          >批量打印</md-button
        >
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          style="margin-left: auto"
          @click="handleSearch"
          >刷新</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('weijizhang-table-box')">
      <md-table-pro
        :columns="columns"
        height="100%"
        border
        :stripe="false"
        :onFetch="handleFetch"
        ref="table"
        :level="level"
        :controlLevel="controlLevel"
        :customLevels="customLevels"
        :control-loading="controlLoading"
        :controlColumnLayout="controlColumnLayout"
        @select-all="onSelection"
        @select="onSelection"
        @sort-change="handleSortChange"
        @getNewColumn="getNewColumn"
        @recovery-column="recoveryColumn"
        @control-cancel="controlCancel"
        @level-change="levelChange"
      >
        <template #yaoPinMXYPMCList="{ row }">
          <biz-tag-list
            :list="row.yaoPinXSs"
            label-key="yaoPinMC"
            @clickMore="({ event }) => handleRowClick(event, row)"
          >
          </biz-tag-list>
        </template>
        <template #chuKuDH="{ row }">
          <div :class="prefixClass('item-inline')">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('chukudantip')"
            >
              <template #content>
                <div @click="copy(row.chuKuDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <span
                :class="prefixClass('rukudh')"
                @click="handleRowClick($event, row)"
                >{{ row.chuKuDH }}</span
              >
            </md-tooltip>
            <div :class="prefixClass('chonghongBZ')" v-if="row.hongDanBZ">
              冲
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <md-button type="text-bg" @click="handleDaYin(row)">打印 </md-button>
        </template>
      </md-table-pro>
      <div :class="prefixClass('weijizhang-table-footer')">
        <span>合计</span>
        进价金额： <span :class="prefixClass('number')">{{ jinJiaJE }}</span> 元
        <span style="margin-left: 5px"> 零售金额：</span>
        <span :class="prefixClass('number')">{{ lingShouJJE }}</span> 元
        <span style="margin-left: 5px"> 进零差额：</span>
        <span :class="prefixClass('number')">{{ jinLingCE }}</span> 元
      </div>
    </div>

    <chukudan-detail-drawer
      ref="chukudandetail"
      :xiaoShuDianWS="xiaoShuDianWS"
      :isZhongYaoKXS="isZhongYaoKXS"
      :showZhangBLB="showZhangBLB"
      size="75%"
    />
  </div>
</template>

<script>
import columnMixin from '@/components/mixin/columnMixin';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizTagList from '@/components/BizTagList';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { GetDanWeiBMXXByCRKFS } from '@/service/yaoPinYK/common';
import { GetYiJiZCKDCount, GetYiJiZCKDList } from '@/service/yaoPinYK/yaoPinCK';
import { yaoKuZDJZTimeShow1 } from '@/system/utils/formatDate';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { printByUrl,sleep } from '@/system/utils/print';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { onAppHide, onAppShow } from '@mdfe/stark-app';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
//TODO
// import { SelectNormal } from '@mdfe/bmis-ui'
import { logger } from '@/service/log';
import { GetCanShuZhi } from '@/system/utils/canShu';
import useClipboard from 'vue-clipboard3';
import ChukudanDetailDrawer from './ChuKuDanDetailDrawer';
export default {
  name: 'yijizhang-page',
  mixins: [columnMixin],
  props: {
    // 冲红标志，
    isChongHong: {
      type: String,
      default: '0',
    },
    // 待打开详情的入库单号的id
    openId: {
      type: String,
      default: '',
    },
    showZhangBLB: {
      type: String,
      default: '0',
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
  },
  data() {
    this.cleanups = [];
    return {
      isCVal: null,
      createdRow: {},
      xiaoShuDianWS: 3,
      isZhongYaoKXS: 5,
      timeRange: [],
      yaoPinMCObj: null,
      // 表格排序方式
      sortObj: {
        sortField: '', //排序字段
        sortDir: '', //排序方向
      },
      query: {
        jiaGeID: '',
        kaiShiSJ: '',
        chuKuDH: '',
        jieShuSJ: '',
        chuRuKFSID: '',
        zuoFeiBZ: '0',
        danWeiBMID: '',
        likeQuery: '',
        zhangBuLBID: '',
      },
      zhangBuLBOptions: [],
      jinJiaJE: '',
      lingShouJJE: '',
      jinLingCE: '',
      danWeiBMOptions: [],
      danWeiBMOriginOptions: [],
      chuRuKFSOptions: [],
      selectList: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
          slot: 'chuKuDH',
          prop: 'chuKuDH',
          label: '出库单',
          minWidth: 160,
          field: true,
        },
        {
          prop: 'chuRuKFSMC',
          label: '出库方式',
          minWidth: 108,
          field: true,
        },
        {
          prop: 'danWeiBMMC',
          label: '单位部门',
          minWidth: 100,
          showOverflowTooltip: true,
          field: true,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          minWidth: 60,
          align: 'right',
          field: true,
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          minWidth: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1
                ? this.prefixClass('chonghong-number__color')
                : '';
            return h(
              'span',
              { class: className },
              Math.abs(Number(scope.row.jinJiaJE)).toFixed(this.jinJiaJEXSDW),
            );
          },
          field: true,
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          minWidth: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 || scope.row.lingShouJE < 0
                ? this.prefixClass('chonghong-number__color')
                : '';

            return h(
              'span',
              { class: className },
              Math.abs(Number(scope.row.lingShouJE)).toFixed(
                this.lingShouJEXSDW,
              ),
            );
          },
          field: true,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          minWidth: 143,
          showOverflowTooltip: true,
          field: true,
        },
        {
          slot: 'yaoPinMXYPMCList',
          prop: 'yaoPinMXYPMCList',
          label: '药品明细',
          minWidth: 200,
          field: true,
        },
        {
          prop: 'jiZhangSJ',
          label: '记账日期',
          width: 100,
          formatter: (row) => {
            return yaoKuZDJZTimeShow1(row.jiZhangSJ);
          },
          field: true,
        },
        {
          prop: 'jiZhangRXM',
          label: '记账人',
          minWidth: 110,
          field: true,
        },
        {
          slot: 'operate',
          prop: 'operate',
          label: '操作',
          minWidth: 75,
          fixed: 'right',
          control: true,
          labelClassName: 'set-Icon',
        },
      ],
    };
  },
  // //修改-出库单查看详情，切换页面回来，希望侧滑还在
  // activated() {
  //   if (this.isCVal) {
  //     let timer = setTimeout(() => {
  //       this.handleRowClick(this.createdRow.e, this.createdRow.row);
  //       clearTimeout(timer);
  //       timer = null;
  //       this.isCVal = null;
  //     }, 500);
  //   }
  // },
  deactivated() {
    if (this.$refs.chukudandetail.drawer && this.createdRow.row) {
      this.isCVal = this.createdRow.row;
    }
  },
  async created() {
    await this.getColumnInit();
    this.cleanups.push(
      onAppShow(() => {
        if (this.isCVal) {
          let timer = setTimeout(() => {
            this.handleRowClick(this.createdRow.e, this.createdRow.row);
            clearTimeout(timer);
            timer = null;
            this.isCVal = null;
          }, 500);
        }
      }),
      onAppHide(() => {
        if (this.$refs.chukudandetail.drawer && this.createdRow.row) {
          this.isCVal = this.createdRow.row;
        }
      }),
    );
    await this.getZiDIanSJ();
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      this.xiaoShuDianWS = await GetCanShuZhi(params);
    } catch (error) {
      console.error(error);
      logger.error(error);
    } finally {
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (getKuCunGLLX().indexOf('3') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  mounted() {
    this.timeRange = [
      dayjs().format('YYYY-MM-01'),
      dayjs().format('YYYY-MM-DD'),
    ];
    this.query.kaiShiSJ = dayjs().format('YYYY-MM-01');
    this.query.jieShuSJ = dayjs().format('YYYY-MM-DD');
  },
  watch: {
    watch: {
      openId: {
        handler: function (val) {
          if (this.isChongHong === '1') {
            this.handleChongHong(val);
          }
        },
        immediate: true,
      },
    },
  },
  methods: {
    onSelection(val) {
      this.selectList = val;
    },
    async handlerPLDY() {
      try {
        if (this.selectList.length == 0) {
          MdMessage({
            type: 'warning',
            message: '请选择要打印的数据！',
          });
          return;
        }
        // 增加5s延迟
        const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
        this.selectList.sort((a, b) => a.index - b.index);

        for (const el of this.selectList) {
          await printByUrl('YKXT005', { chuKuDanID: el.id });
          await sleep(5000); // 打印后延迟 5 秒
        }
      } catch (e) {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },

    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    handleChongHong(type) {
      this.handleClear();
      this.$nextTick(() => {
        this.handleSearch();
        this.$refs.chukudandetail.openDrawer({
          id: this.$route.query.id,
          type: 2,
        });
      });
    },
    async getZiDIanSJ() {
      await Promise.all([
        GetChuRuKFSByFXDM('2').then((res) => {
          let optionData = [];
          res.forEach((item) => {
            optionData.push({
              chuRuKFSMC: item.fangShiMC,
              chuRuKFSID: item.fangShiID,
            });
          });
          optionData.unshift({
            chuRuKFSMC: '所有出库方式',
            chuRuKFSID: '',
          });
          this.chuRuKFSOptions = optionData;
        }),
        getZhangBuQXXQ().then((res) => {
          this.zhangBuLBOptions = res.zhangBuLBXXList || [];
          this.query.zhangBuLBID = res.zhangBuLBXXList
            .map((m) => m.zhangBuLBID)
            .join(',');
          this.zhangBuLBOptions.unshift({
            zhangBuLBID: this.query.zhangBuLBID,
            zhangBuLBMC: '全部账簿类别',
          });
        }),
      ]);
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    async getDanWeiBMOptions(id) {
      await GetDanWeiBMXXByCRKFS({ chuRuKFSID: id }).then((res) => {
        this.danWeiBMOptions = res;
        this.danWeiBMOriginOptions = cloneDeep(res);
      });
    },
    handleDateRangeChange(val) {
      if (val && val.length > 0) {
        this.query.kaiShiSJ = val[0]
          ? dayjs(val[0]).format('YYYY-MM-DD')
          : null;
        this.query.jieShuSJ = val[1]
          ? dayjs(val[1]).format('YYYY-MM-DD')
          : null;
      } else {
        this.query.kaiShiSJ = '';
        this.query.jieShuSJ = '';
      }
      this.handleSearch();
    },
    filterMethod(query) {
      if (query !== '') {
        this.danWeiBMOptions = this.danWeiBMOriginOptions.filter((item) => {
          return (
            item.danWeiBMMC.toLowerCase().indexOf(query.toLowerCase()) > -1 ||
            (item.shuRuMa1 &&
              item.shuRuMa1.toLowerCase().indexOf(query.toLowerCase()) > -1)
          );
        });
      } else {
        this.danWeiBMOptions = this.danWeiBMOriginOptions;
      }
    },
    handleSelectChange(val, key) {
      if (key === 'chuRuKFS') {
        this.query.danWeiBMID = '';
        this.query.danWeiBMMC = '';
        this.getDanWeiBMOptions(val);
      }
      this.handleSearch();
    },
    handleSearch() {
      this.$refs.table.search({ pageSize: 100 });
    },
    async handleDaYin(row) {
      try {
        this.pageLoading = true;
        const params = {
          chuKuDanID: row.id,
        };
        await printByUrl('YKXT005', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    async handleFetch({ page, pageSize }) {
      const params = {
        pageIndex: page,
        pageSize,
        ...this.query,
        ...this.sortObj,
      };
      const [items, total] = await Promise.all([
        GetYiJiZCKDList(params),
        GetYiJiZCKDCount(params),
      ]);
      const itemsWithIndex = items.map((item, index) => ({
        ...item,
        index: index + 1, // 如果需要从 1 开始，使用 index + 1
      }));
      this.jinJiaJE = Number(total.jinJiaJE).toFixed(2);
      this.lingShouJJE = Number(total.lingShouJJE).toFixed(2);
      this.jinLingCE = (this.lingShouJJE - this.jinJiaJE).toFixed(2);
      return {
        items: itemsWithIndex,
        total: total.count,
      };
    },
    handleCopySuccess() {
      MdMessage({
        message: '复制成功',
        type: 'success',
        duration: 2000,
      });
    },
    handleCopyError() {
      // this.$message({
      //   message: '复制失败',
      //   type: 'error',
      //   duration: 2000
      // })
      MdMessageBox({
        title: '系统消息',
        type: 'error',
        message: `复制失败！`,
        confirmButtonText: '我知道了',
      });
    },
    async handleRowClick(e, row) {
      this.createdRow = { e: e, row: row };
      e.stopPropagation();
      await this.$refs.chukudandetail.openDrawer({
        id: row.id,
        type: 2,
      });
      this.handleSearch();
    },
    handleClear() {
      this.query.chuRuKFS = '0';
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.query.jiaGeID = e.jiaGeID || '';
      this.handleSearch();
    },
    /**
     * 升降序
     * **/
    handleSortChange({ column, prop, order }) {
      if (order) {
        this.sortObj = {
          sortField: prop, //排序字段
          sortDir: order == 'ascending' ? 'asc' : 'desc', //排序方向
        };
      } else {
        this.sortObj = {
          sortField: '', //排序字段
          sortDir: '', //排序方向
        };
      }
      this.handleSearch();
    },
  },
  components: {
    'chukudan-detail-drawer': ChukudanDetailDrawer,
    'biz-tag-list': BizTagList,
    'biz-yaopindw': BizYaoPinDW,
  },
  beforeUnmount() {
    if (this.cleanups) {
      this.cleanups.forEach((cleanup) => {
        cleanup();
      });
      this.cleanups = [];
    }
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-weijizhang-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  ::v-deep .#{$md-prefix}-chonghong-number__color {
    color: #f12933 !important;
  }
  .#{$md-prefix}-weijizhang-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    &-filters {
      display: flex;
      width: 100%;
      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }

  .#{$md-prefix}-weijizhang-table-box {
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
  }
  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .#{$md-prefix}-chonghongBZ {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    color: #fff;
    background-color: #f12933;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
  }
}

.#{$md-prefix}-rukudh {
  margin-right: 5px;
  cursor: pointer;
  color: rgb(var(--md-color-6));
  &:hover {
    // color: #1e88e5;
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}
.#{$md-prefix}-weijizhang-table-footer {
  position: absolute;
  bottom: 16px;
  color: #aaa;
  font-size: 14px;
  .#{$md-prefix}-number {
    font-weight: 600;
    color: #222222;
  }
}
</style>
<style lang="scss">
.set-Icon {
  .#{$md-prefix}-table-set-icon {
    padding-top: 4px !important;
  }
}
.#{$md-prefix}-rukudantip {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
