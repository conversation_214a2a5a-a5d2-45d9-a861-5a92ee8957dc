<template>
  <bmis-blue-dialog
    v-model:visible="visible"
    width="560px"
    height="300px"
    title="按消耗量生成"
    @submit="handleSave"
    appendToBody
    :closeOnClickModal="false"
    save-text="生成"
  >
    <div :class="prefixClass('anKuCXXSC-dialog')">
      <div>
        <span>消耗时间</span>
        <md-input
          v-model="day"
          v-number.float="{ decimal: 0 }"
          @input="handleDayChange"
          style="flex: 1"
        >
          <template #suffix> 天内 </template>
        </md-input>
      </div>
      <div :class="prefixClass('anKuCXXSC-dialog__time')">
        <span>消耗日期</span>
        <md-date-picker-com
          v-model="time"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleTimeChange"
        >
        </md-date-picker-com>
      </div>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import BlueDialog from '@/components/blue-dialog/index.vue';
import { MdInput, MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
export default {
  name: 'ankucunxx-dialog',
  data() {
    return {
      visible: false,
      time: [],
      day: 0, //默认显示3天
    };
  },

  methods: {
    handleSave() {
      const time = this.time;
      if (!time[0] || !time[1]) {
        MdMessage({
          type: 'warning',
          message: '请选择开始和结束日期',
        });
        //Message.warning('请选择开始和结束日期!')
        return;
      }
      const KaiShiSJ = dayjs(time[0])?.format('YYYY-MM-DD');
      const JieShuSJ = dayjs(time[1])?.format('YYYY-MM-DD');
      const xiaoHaoTS = this.day;
      this.resolve({ KaiShiSJ, JieShuSJ, xiaoHaoTS });
      this.visible = false;
    },
    handleDayChange(val) {
      this.dat = val;
      this.time = [
        dayjs()
          .add(-(val - 1), 'day')
          .toDate(),
        dayjs().startOf('day').toDate(),
      ];
    },
    handleTimeChange() {
      const time = this.time;
      if (time[0] && time[1]) {
        const kaiShiSJ = dayjs(
          dayjs(time[0]).format('YYYY-MM-DD') + ' 00:00:00',
        );
        const jieShuSJ = dayjs(
          dayjs(time[1]).format('YYYY-MM-DD') + ' 23:59:59',
        );
        const timeDiff = Math.abs(jieShuSJ - kaiShiSJ);
        this.day = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      }
    },
    showModel() {
      this.visible = true;
      const day = 3;
      this.day = day;
      this.time = [
        dayjs()
          .add(-(day - 1), 'day')
          .toDate(),
        dayjs().startOf('day').toDate(),
      ];
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
    'md-input': MdInput,
  },
};
</script>

<style scoped lang="scss">
.#{$md-prefix}-anKuCXXSC-dialog {
  margin-top: 24px;
  padding-right: 20px;
  box-sizing: border-box;

  div {
    //line-height: 45px;
    display: flex;
    align-items: center;
  }

  &__time {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  span {
    // display: block;
    // width: 170px;
    margin-left: 20px;
    margin-right: 8px;
  }

  // ::v-deep .#{$md-prefix}-input {
  //   width: 140px;
  // }
}
</style>
