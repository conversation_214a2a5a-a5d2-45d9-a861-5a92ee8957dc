<template>
  <md-frameset
    :class="prefixClass('frameset')"
    :gutter="0"
    :frameborder="false"
    padding="0"
    style="display: flex"
  >
    <md-frame-pane style="width: 300px; position: absolute">
      <div :class="prefixClass('frame-pane-box frame-pane-left')">
        <div :class="prefixClass('daiRuKuPage-left-header')">
          <md-radio-group
            v-model="showType"
            :class="prefixClass('radio-box')"
            @change="handleShowTypeChange"
          >
            <!--<md-radio label="1">按请领单</md-radio>-->
            <md-radio label="2">按入库单</md-radio>
          </md-radio-group>
          <md-button
            type="primary"
            @click="ClearList"
            :icon="prefixClass('icon-shanchuwap')"
            noneBg
            style="margin-right: 8px"
            >清空列表</md-button
          >
        </div>

        <div :class="prefixClass('left-content')">
          <div>
            <md-date-picker-range-pro
              v-model="timeRange"
              :class="prefixClass('query-size')"
              range-separator="/"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleTimeRangeChange"
            >
            </md-date-picker-range-pro>
            <md-input
              v-model="query.likeQuery"
              :class="prefixClass('query-size')"
              placeholder="输入单据号搜索"
              @keyup.enter.native="handleSearch"
            >
              <i
                :class="prefixClass('input__icon icon-seach')"
                slot="suffix"
                @click="handleSearch"
              ></i>
            </md-input>
          </div>
          <yaopingrk-left-table
            @current-change="handleCurrentChange"
            :get-list-data="getLeftTableData"
            ref="leftTable"
          />
        </div>
      </div>
    </md-frame-pane>
    <md-frame-pane style="flex: 1; padding-left: 300px">
      <div
        v-show="!hasData"
        :class="prefixClass('frame-pane-box frame-pane-right')"
      >
        <div :class="prefixClass('nodata')">
          <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
          <span>暂无数据...</span>
        </div>
      </div>
      <div
        v-show="hasData"
        v-loading="rightPageLoading"
        :class="prefixClass('frame-pane-box frame-pane-right')"
      >
        <div :class="prefixClass('content-top')">
          <div :class="prefixClass('content-top-filters')">
            <biz-yaopindw
              v-model="likeQuery"
              @change="handleChangeYaoPinDW"
              :kuCunSFWK="false"
              showSuffix
            />
            <md-checkbox
              v-model="rightQuery.KeChuKBZ"
              v-show="isAnQingLingDan"
              style="margin-left: 8px"
              @change="handleRightSearch"
              true-label="1"
              false-label="0"
            >
              可出库
            </md-checkbox>
          </div>
          <div :class="prefixClass('content-top-buttons')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-shuaxin')"
              noneBg
              style="margin-right: 8px"
              @click="handleSearch"
              >刷新</md-button
            >
            <md-button type="primary" @click="handleZhiDan">出库制单</md-button>
          </div>
        </div>
        <div v-show="isAnQingLingDan" :class="prefixClass('table-box-border')">
          <md-table-pro
            :columns="rightColumns"
            height="100%"
            border
            resize
            id="rightTable1"
            :stripe="false"
            :row-class-name="handleRowClass"
            :onFetch="handleFetch"
            ref="table"
          >
            <template #jiYongBZ="{ row }">
              <i
                v-if="row.jiYongBZ"
                class="iconfont icongou"
                style="color: #f12933; font-size: 16px"
              />
            </template>
          </md-table-pro>
        </div>
        <div v-show="isAnRuKuDan" :class="prefixClass('table-box-border')">
          <md-table-pro
            :columns="anRuKuDanRightColumns"
            height="100%"
            border
            resize
            id="rightTable2"
            :stripe="false"
            :row-class-name="handleRowClass"
            :cell-class-name="handleChongHongSLColor"
            :onFetch="handleFetch"
            ref="table1"
          >
            <template #jiYongBZ="{ row }">
              <i
                v-if="row.jiYongBZ"
                class="iconfont icongou"
                style="color: #f12933; font-size: 16px"
              />
            </template>
          </md-table-pro>
        </div>
      </div>
    </md-frame-pane>
    <biz-yaopinph ref="yaopinph" />
    <damaj-dialog ref="DuMaJDialog" />
  </md-frameset>
</template>

<script>
import dayjs from 'dayjs';
import DuMaJDialog from '@/views/yaoKuGL/yaoPinRK/components/DuMaJDLZDDialog';
import {
  GetDaiChuKQLDCount,
  GetDaiChuKQLDList,
  GetDaiChuKQLDMXCount,
  GetDaiChuKQLDMXList,
  GetDaiChuKRKDCount,
  GetDaiChuKRKDList,
  GetDaiChuKRKDMXCount,
  GetDaiChuKRKDMXList,
  QingLingCKZD,
  UpdateQingLingDanZT,
  UpdateRuKuDanZT,
} from '@/service/yaoPinYK/yaoPinCK';
import tableData from '../js/tableData.js';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinPH from '@/components/YaoKu/BizYaoPinPH';
import YaopingrkLeftTable from '@/views/yaoKuGL/components/YaoPingDRKLeftTable';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';

const defaultQuery = () => ({
  kaiShiSJ: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  jieShuSJ: dayjs().format('YYYY-MM-DD'),
  likeQuery: '',
});

export default {
  name: 'daichuku-page',
  props: {
    activeName: {
      type: String,
      default: 'first',
    },
    duMaJDLZD: {
      type: String,
    },
  },
  data() {
    return {
      timeRange: [],
      rightPageLoading: false, //右侧页面加载
      likeQuery: {}, //右侧药品搜索绑定数据， 仅供显示用
      showType: '2', // radio选中 1 按请领单， 2 按入库单
      currentRow: null, // 左侧表格选中的数据
      query: defaultQuery(), // 左侧筛选条件
      rightQuery: {
        //右侧筛选条件
        KeChuKBZ: '0', // 可出库标志 0 全部， 1 只显示可出库
        likeQuery: '', // 药品名称查询
      },
      columns: tableData.anRuKuDanColumns,
      rightColumns: tableData.rightColumns,
      anRuKuDanRightColumns: tableData.anRuKuDanRightColumns,
      selectionData: [],
      tableData: [],
      hasData: false,
      isAnQingLingDanFirst: true,
      isAnRuKuDanFirst: true,
    };
  },
  computed: {
    // radio选中的是请领单
    isAnQingLingDan() {
      return this.showType === '1';
    },
    // radio选中的是入库单
    isAnRuKuDan() {
      return this.showType === '2';
    },
  },
  mounted() {
    this.timeRange = [
      dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ];
  },
  methods: {
    /**
     * 右侧药品选择改变， 按药品名称进行搜索
     * @param data
     * @returns {Promise<void>}
     */
    async handleChangeYaoPinDW(data) {
      if (data) {
        this.rightQuery.likeQuery = data.yaoPinMC;
      } else {
        this.rightQuery.likeQuery = '';
      }
      this.handleRightSearch();
    },
    handleTimeRangeChange(time) {
      if (time && time.length > 0) {
        this.query.kaiShiSJ = time[0]
          ? dayjs(time[0]).format('YYYY-MM-DD')
          : '';
        this.query.jieShuSJ = time[1]
          ? dayjs(time[1]).format('YYYY-MM-DD')
          : '';
      } else {
        this.query.kaiShiSJ = '';
        this.query.jieShuSJ = '';
      }
      if (!this.isShowFilter) this.handleSearch();
    },
    async handleSearch() {
      await this.$refs.leftTable.search();
    },
    handleRightSearch() {
      const isAnQIngLingDan = this.isAnQingLingDan ? 'table' : 'table1';
      if (this.$refs[isAnQIngLingDan]) {
        this.$refs[isAnQIngLingDan].clearData();
        if (this.isAnQingLingDan) {
          if (this.isAnQingLingDanFirst) {
            this.$refs.table.doLoad();
            this.isAnQingLingDanFirst = false;
          } else this.$refs.table.search();
        } else {
          if (this.isAnRuKuDanFirst) {
            this.isAnRuKuDanFirst = false;
            this.$refs.table1.doLoad();
          } else this.$refs.table1.search();
        }
      }
    },
    /**
     * 判断是否有可出库数据， 按请领单出库时  handleZhiDan中调用
     * @returns {boolean}
     */
    checkQingLingDCK() {
      return !this.tableData.some((item) => item.keChuKBZ === 1);
    },
    /**
     * 出库制单，
     * 1. 按请领单制单时， 根据可出库的药品自动生成出库单
     * 2. 按入库单制单时， 跳转新增出库单页面，进行手动修改录入制单
     * @returns {Promise<void>}
     */
    async handleZhiDan() {
      if (this.isAnQingLingDan) {
        this.rightPageLoading = true;
        if (this.checkQingLingDCK()) {
          this.rightPageLoading = false;
          MdMessage({
            type: 'warning',
            message: '当前无可出库数据！',
          });
          return;
        }
        try {
          await QingLingCKZD({ qingLingDID: this.currentRow.id });
          //this.$emit('update:activeName', 'second')
          this.$router.push({
            path: '/YaoPinCK',
            query: {
              showType: 'second',
            },
          });
          MdMessage({
            type: 'success',
            message: '制单成功',
          });
        } finally {
          this.rightPageLoading = false;
        }
      } else {
        this.handleRuKuZhiDan();
      }
    },
    /**
     * 入库制单跳转
     */
    async handleRuKuZhiDan() {
      this.selectionData = [];
      this.selectionData = this.$refs.table1
        .getComp('table')
        .getAllCheckedRows();
      if (this.selectionData.length === 0) {
        MdMessage({
          type: 'warning',
          message: '请选择一条药品数据！',
        });
        return;
      }

      let items = this.selectionData;
      // 开启了毒麻精药品独立制单，删除毒麻精药品
      // 选中的药品里是否含有毒麻精药品
      const isCludeDMJ = items.some((s) => s.duLiFLDM != 0);
      const isNormal = items.some((s) => s.duLiFLDM == 0);
      // 同时包含毒麻精和普通药品
      if (this.duMaJDLZD == 1 && isCludeDMJ && isNormal) {
        const res = await this.$refs.DuMaJDialog.showDialog(items);
        items = res;
      }
      // 存储数据
      sessionStorage.setItem('chuKuDanList', JSON.stringify(items));
      let type = sessionStorage.getItem('typeCount1');
      if (!type) {
        sessionStorage.setItem('typeCount1', '1');
        type = '1';
      }
      this.$router.replace({
        path: '/XinZengCKD',
        query: {
          type: type,
          ruKuDanID: this.currentRow.id,
        },
      });
      type = Number.parseInt(type) + 1 + '';
      sessionStorage.setItem('typeCount1', type);
    },
    /**
     * 左侧表格选中改变时触发
     * @param currentRow
     */
    handleCurrentChange(currentRow) {
      this.currentRow = currentRow;
      this.rightQuery = {
        KeChuKBZ: '',
        likeQuery: '',
      };
      if (currentRow) this.handleRightSearch();
    },
    /**
     * 获取左侧表格数据，  无数据时， 右侧置为空数据页
     * @param pageIndex
     * @param pageSize
     * @returns {Promise<{total: *, items: *}>}
     */
    async getLeftTableData(pageIndex, pageSize, tableParams) {
      let params = {
        ...tableParams,
        pageIndex,
        pageSize,
        kaiShiSJ: this.query.kaiShiSJ,
        jieShuSJ: this.query.jieShuSJ,
      };
      let data = null;
      if (params.pageSize) {
        if (this.showType === '1') {
          params.qingLingDH = this.query.likeQuery;
          data = await Promise.all([
            GetDaiChuKQLDList(params),
            GetDaiChuKQLDCount(params),
          ]);
        }
        if (this.showType === '2') {
          params.ruKuDH = this.query.likeQuery;
          data = await Promise.all([
            GetDaiChuKRKDList(params),
            GetDaiChuKRKDCount(params),
          ]);
        }
        const [items, total] = data;
        this.hasData = total > 0;
        return {
          items: items,
          total: total,
        };
      }
    },
    /**
     * 按请领单表格中 不能出库的行，设置为灰色
     * @param row
     * @returns {string}
     */
    handleRowClass({ row }) {
      if (!row.keChuKBZ && this.showType === '1') {
        return this.prefixClass('disabled-row');
      }
    },
    handleChongHongSLColor({ column }) {
      //冲红 退库时 出库数量、进价金额、零售金额、显示为红色
      if (
        this.currentRow &&
        this.currentRow.chuRuKFSID === '9003' &&
        column.property === 'ruKuSL'
      ) {
        return this.prefixClass('chonghong-number__color');
      }
    },
    async ClearList() {
      MdMessageBox.alert('是否清除列表？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          if (this.showType == '1') {
            //请领单
            let params = {
              kaiShiSJ: this.query.kaiShiSJ == '' ? null : this.query.kaiShiSJ,
              jieShuSJ: this.query.jieShuSJ == '' ? null : this.query.jieShuSJ,
              QingLingDH: this.query.likeQuery,
            };
            UpdateQingLingDanZT(params);
            this.getLeftTableData();
            this.handleSearch();
          } else {
            //入库单
            let params = {
              kaiShiSJ: this.query.kaiShiSJ == '' ? null : this.query.kaiShiSJ,
              jieShuSJ: this.query.jieShuSJ == '' ? null : this.query.jieShuSJ,
              RuKuDH: this.query.likeQuery,
            };
            UpdateRuKuDanZT(params);
            this.getLeftTableData();
            this.handleSearch();
          }
        })
        .catch((error) => {});
    },
    /**
     * 右侧表格数据获取
     * @param page
     * @param pageSize
     * @returns {Promise<{total: *, items: *}>}
     */
    async handleFetch({ page, pageSize, sort }) {
      let params = {
        pageIndex: page,
        pageSize,
        ...this.rightQuery,
      };
      if (sort && sort.prop) {
        // 字段映射表，key为前端字段，value为后端字段
        const sortFieldMap = {
          yaoPinMCYGG: 'yaoPinMC',
          // 以后有更多字段可在这里扩展
        };
        const mappedField = sortFieldMap[sort.prop];
        if (mappedField) {
          params['sortField'] = mappedField;
          params['sortDir'] =
            sort.order === 'ascending'
              ? 'asc'
              : sort.order === 'descending'
                ? 'desc'
                : '';
        }
      }
      let data = null;
      if (this.isAnRuKuDan) {
        params.ruKuDID = this.currentRow.id;
        data = await Promise.all([
          GetDaiChuKRKDMXList(params),
          GetDaiChuKRKDMXCount(params),
        ]);
      }
      if (this.isAnQingLingDan) {
        params.qingLingDID = this.currentRow.id;
        data = await Promise.all([
          GetDaiChuKQLDMXList(params),
          GetDaiChuKQLDMXCount(params),
        ]);
      }
      const [items, total] = data;
      this.tableData = items;
      return {
        items: items,
        total: total,
      };
    },
    /**
     * radio切换触发， 更改表格列配置
     * @param val
     */
    handleShowTypeChange(val) {
      this.query = defaultQuery();
      this.timeRange = [
        dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ];
      if (val === '1') {
        this.columns = tableData.columns;
        this.rightColumns = tableData.rightColumns;
      } else if (val === '2') {
        this.columns = tableData.anRuKuDanColumns;
        this.rightColumns = tableData.anRuKuDanRightColumns;
      }
      this.handleSearch();
    },
  },
  components: {
    'yaopingrk-left-table': YaopingrkLeftTable,
    'biz-yaopindw': BizYaoPinDW,
    'biz-yaopinph': BizYaoPinPH,
    'damaj-dialog': DuMaJDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-daiRuKuPage-left-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgb(var(--md-color-1));
}
.#{$md-prefix}-table-box-border {
  flex: 1;
  min-height: 0;
}

::v-deep .#{$md-prefix}-heji-row {
  background-color: #f2f9f9;

  > td {
    border-right: 0;
    border-left: 0;

    .cell {
      //height: 20px;
      font-weight: 600;
      color: #377777;
      font-size: 14px;
      //line-height: 20px;
    }
  }
}

.#{$md-prefix}-content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &-filters {
    display: flex;

    .#{$md-prefix}-filter-width {
      width: 230px;
    }
  }

  &-buttons {
    display: flex;
  }
}

.#{$md-prefix}-frame-pane-right {
  margin: 8px;
  padding: 8px;
  height: calc(100% - 32px);
}

.#{$md-prefix}-frame-pane-left {
  height: 100%;
}

.#{$md-prefix}-frame-pane-box {
  background: #fff;
  display: flex;
  flex-direction: column;
}

.#{$md-prefix}-radio-box {
  height: 36px;
  display: flex;
  align-items: center;
  // background-color: #edf6fd;
  background-color: rgb(var(--md-color-1));
  padding: 0 8px;
}

.#{$md-prefix}-radio-text {
  height: 20px;
  color: #222222;
  font-size: 14px;
  line-height: 20px;
}

.#{$md-prefix}-left-content {
  min-height: 0;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.#{$md-prefix}-query-size {
  width: 100%;
  margin-bottom: 8px;
}

.#{$md-prefix}-riqi-width-anfapiao {
  width: calc(100% - 38px);
}

.#{$md-prefix}-filter- .re {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 30px;
  height: 28px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
}

.#{$md-prefix}-icon-class {
  font-size: 16px;
}

.#{$md-prefix}-left-filter {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 999;
  padding: 8px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .#{$md-prefix}-item-inline-label {
      width: 62px;
      height: 20px;
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      text-align: right;
    }

    &-input {
      margin-left: 8px;
      width: calc(100% - 68px);
    }
  }

  .#{$md-prefix}-left-filter-buttons {
    margin-top: 8px;
    .button-class {
      width: 64px;
      height: 30px;
      margin-left: 8px;
    }
  }
}

.#{$md-prefix}-left-filter-more {
  margin-top: 76px;
}
.#{$md-prefix}-right-table-tips {
  height: 20px;
  color: #aaaaaa;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  padding: 8px;
  > span {
    color: #333;
    font-weight: bold;
  }
}
::v-deep .#{$md-prefix}-disabled-row {
  background: #f8f8f8;
}
::v-deep .#{$md-prefix}-dingwei-bg {
  td {
    background: #e2efff;
  }
}
::v-deep .#{$md-prefix}-select {
  width: 210px;
}
::v-deep .#{$md-prefix}-chonghong-number__color .cell {
  color: #f12933 !important;
}
</style>
