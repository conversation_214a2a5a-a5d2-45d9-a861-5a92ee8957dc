<template>
  <div :class="prefixClass('weijizhang-box')" v-loading="pageLoading">
    <div :class="prefixClass('weijizhang-content-top')">
      <div :class="prefixClass('weijizhang-content-top-filters')">
        <md-date-picker-range-pro
          v-model="timeRange"
          style="margin-right: 8px; width: 250px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        >
        </md-date-picker-range-pro>
        <md-select
          v-if="showZhangBLB == 1"
          v-model="query.zhangBuLBID"
          placeholder=""
          @change="handleSearch"
          style="margin-right: 8px; width: 140px"
        >
          <md-option
            v-for="item in zhangBuLBOptions"
            :value="item.zhangBuLBID"
            :label="item.zhangBuLBMC"
            :key="item.biaoZhunDM"
          ></md-option>
        </md-select>
        <md-select
          v-model="query.chuRuKFSID"
          filterable
          style="margin-right: 8px; width: 140px"
          @clear="handleClear"
          @change="handleSelectChange($event, 'chuRuKFS')"
        >
          <md-option
            v-for="item in chuRuKFSOptions"
            :key="item.chuRuKFSID"
            :label="item.chuRuKFSMC"
            :value="item.chuRuKFSID"
          />
        </md-select>
        <md-select
          v-model="query.danWeiBMID"
          :disabled="!query.chuRuKFSID"
          :class="prefixClass('filter-width')"
          style="margin-right: 8px"
          filterable
          :remote-method="filterMethod"
          remote
          placeholder="输入选择单位部门"
          @change="handleSelectChange($event, 'danWeiBM')"
        >
          <md-option
            v-for="item in danWeiBMOptions"
            :key="item.danWeiBMID"
            :label="item.danWeiBMMC"
            :value="item.danWeiBMID"
          />
        </md-select>
        <md-input
          v-model="query.chuKuDH"
          :class="prefixClass('filter-width')"
          placeholder="输入单据号搜索"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('input__icon icon-seach')"
            slot="suffix"
            @click="handleSearch"
          />
        </md-input>
        <biz-yaopindw
          v-model="yaoPinMCObj"
          placeholder="名称、输入码、别名、规格等关键字进行搜索"
          style="width: 340px; margin-left: 8px"
          @change="handleQueryYaoPinMC"
        >
        </biz-yaopindw>
      </div>
      <div :class="prefixClass('weijizhang-content-top-buttons')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          style="margin-left: auto"
          @click="handleSearch"
          >刷新</md-button
        >
        <md-button
          type="primary"
          :icon="prefixClass('icon-xinzeng')"
          noneBg
          @click="handleKaiDan"
          >开单
        </md-button>
      </div>
    </div>
    <div :class="prefixClass('weijizhang-table-box')">
      <md-table-pro
        :columns="columns"
        :onFetch="handleFetch"
        :row-class-name="tableRowClassName"
        height="100%"
        border
        :stripe="false"
        ref="table"
      >
        <template #yaoPinMXYPMCList="{ row, $index }">
          <biz-tag-list
            :list="row.yaoPinXSs"
            @clickMore="({ event }) => handleRowClick(event, row, $index)"
          >
          </biz-tag-list>
        </template>
        <template #chuKuDH="{ row, $index }">
          <div :class="prefixClass('item-inline')">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popper-class="prefixClass('chukudantip')"
            >
              <template #content>
                <div @click="copy(row.chuKuDH)" :class="prefixClass('fuzhi')">
                  复制
                </div>
              </template>
              <!-- <template v-slot:reference> -->
              <span
                :class="prefixClass('rukudh')"
                @click="handleRowClick($event, row, $index)"
                >{{ row.chuKuDH }}</span
              >
              <!-- </template> -->
            </md-tooltip>
            <div :class="prefixClass('chonghongBZ')" v-if="row.hongDanBZ">
              冲
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <md-button type="text-bg" @click.stop="handleEdit(row)">
            编辑
          </md-button>
          <md-button type="text-bg" @click.stop="handleJiZhang(row)">
            记账
          </md-button>
          <md-button
            v-if="row.guanLianDJLX != '2' && row.guanLianDJLX != '3'"
            type="danger"
            noneBg
            @click.stop="handleZuoFei(row)"
          >
            作废
          </md-button>
          <md-button type="text-bg" @click="handleDaYin(row)">打印 </md-button>
        </template>
      </md-table-pro>
    </div>
    <chukudan-detail-drawer
      ref="chukudandetail"
      size="75%"
      :xiaoShuDianWS="xiaoShuDianWS"
      :isZhongYaoKXS="isZhongYaoKXS"
      :showZhangBLB="showZhangBLB"
      :showWeiJiZQLD="showWeiJiZQLD"
      @jizhangckd="handleJiZhang"
    />
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizTagList from '@/components/BizTagList';
import { logger } from '@/service/log';
import { GetChuRuKFSByFXDM } from '@/service/yaoPinYK/chuRuKFS';
import { GetDanWeiBMXXByCRKFS } from '@/service/yaoPinYK/common';
import { ChongHongJZChuKuDan } from '@/service/yaoPinYK/danJuCH';
import {
  GetWeiJiZCKDCount,
  GetWeiJiZCKDKSRQ,
  GetWeiJiZCKDList,
  JiZhangChuKuDan,
  ZuoFeiChuKuDan,
} from '@/service/yaoPinYK/yaoPinCK';
import { printByUrl } from '@/system/utils/print';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { getZhangBuQXXQ } from '@/service/xiTongSZ/zhangBuLBWH';
import { getWeiZhiMC } from '@/system/utils/local-cache';
import useClipboard from 'vue-clipboard3';
import ChukudanDetailDrawer from './ChuKuDanDetailDrawer';
export default {
  name: 'weijizhang-page',
  props: {
    // 冲红标志，
    isChongHong: {
      type: String,
      default: '0',
    },
    // 待打开详情的入库单号的id
    openId: {
      type: String,
      default: '',
    },
    showZhangBLB: {
      type: String,
      default: '0',
    },
    jinJiaJEXSDW: {
      type: String,
      default: 2,
    },
    lingShouJEXSDW: {
      type: String,
      default: 2,
    },
    showWeiJiZQLD: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      zhangBuLBOptions: [],
      xiaoShuDianWS: 3,
      timeRange: [
        dayjs().format('YYYY-MM') + '-01',
        dayjs().format('YYYY-MM-DD'),
      ],
      currentTableIndex: null,
      yaoPinMCObj: null,
      query: {
        jiaGeID: '',
        kaiShiSJ: '',
        jieShuSJ: dayjs().format('YYYY-MM-DD'),
        chuRuKFSID: '',
        zuoFeiBZ: '0',
        danWeiBMID: '',
        chuKuDH: '',
        zhangBuLBID: '',
      },
      danWeiBMOptions: [],
      danWeiBMOriginOptions: [],
      chuRuKFSOptions: [],
      pageLoading: false,
      columns: [
        {
          slot: 'chuKuDH',
          label: '出库单',
          width: 118,
        },
        {
          prop: 'chuRuKFSMC',
          label: '出库方式',
          width: 108,
        },
        {
          prop: 'danWeiBMMC',
          label: '单位部门',
          width: 188,
          showOverflowTooltip: true,
        },
        {
          prop: 'yaoPinZS',
          label: '药品数',
          width: 60,
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          width: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 ? 'chonghong-number__color' : '';
            return h(
              'span',
              { class: className },
              Math.abs(Number(scope.row.jinJiaJE)).toFixed(this.jinJiaJEXSDW),
            );
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          width: 120,
          align: 'right',
          render: (h, scope) => {
            const className =
              scope.row.hongDanBZ === 1 || scope.row.lingShouJE < 0
                ? 'chonghong-number__color'
                : '';

            return h(
              'span',
              { class: className },
              Math.abs(Number(scope.row.lingShouJE)).toFixed(
                this.lingShouJEXSDW,
              ),
            );
          },
        },
        {
          label: '备注',
          prop: 'beiZhu',
          width: 143,
          showOverflowTooltip: true,
        },
        {
          slot: 'yaoPinMXYPMCList',
          label: '药品明细',
          minWidth: 200,
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 110,
          formatter: (row) => {
            return yaoKuZDJZTimeShow(row.zhiDanSJ);
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 110,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 155,
          fixed: 'right',
        },
      ],
      isZhongYaoKXS: 5,
    };
  },
  watch: {
    openId: {
      handler: function (val) {
        if (this.isChongHong === '1') {
          this.handleChongHong(val);
        }
      },
      immediate: true,
    },
  },
  async created() {
    await this.getZiDIanSJ();
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '2', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      this.xiaoShuDianWS = await GetCanShuZhi(params);
    } catch (error) {
      console.error(error);
      logger.error(error);
    } finally {
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    handleZuoFei(row) {
      MdMessageBox.confirm('此操作将删除该行数据，是否继续？', '操作提醒！', {
        type: 'warning',
      }).then(async () => {
        this.pageLoading = true;
        try {
          await ZuoFeiChuKuDan(row.id);
          MdMessage({
            type: 'success',
            message: '作废成功！',
          });
          this.handleSearch();
        } finally {
          this.pageLoading = false;
        }
      });
    },
    /**
     * 处理其他页面跳转到此时， 自动打开详情抽屉 （冲红跳转、请领跳转）
     * @param id 入库单id
     */
    handleChongHong(id) {
      this.handleClear();
      this.$nextTick(() => {
        this.handleSearch();
        this.$refs.chukudandetail.openDrawer({
          id: id,
          type: 1,
        });
      });
    },
    /**
     * 根据出入库方式 获取单位部门数据
     * @param id
     */
    async getDanWeiBMOptions(id) {
      await GetDanWeiBMXXByCRKFS({ chuRuKFSID: id }).then((res) => {
        this.danWeiBMOptions = res;
        this.danWeiBMOriginOptions = cloneDeep(res);
      });
    },
    /**
     * 获取字典数据
     * 1. 出入库方式字典数据
     * 2. 第一个未记账数据的制单日期
     */
    async getZiDIanSJ() {
      await Promise.all([
        GetChuRuKFSByFXDM('2').then((res) => {
          let optionData = [];
          res.forEach((item) => {
            optionData.push({
              chuRuKFSMC: item.fangShiMC,
              chuRuKFSID: item.fangShiID,
            });
          });
          optionData.unshift({
            chuRuKFSMC: '所有出库方式',
            chuRuKFSID: '',
          });
          this.chuRuKFSOptions = optionData;
        }),
        getZhangBuQXXQ().then((res) => {
          this.zhangBuLBOptions = res.zhangBuLBXXList || [];
          this.query.zhangBuLBID = res.zhangBuLBXXList
            .map((m) => m.zhangBuLBID)
            .join(',');
          this.zhangBuLBOptions.unshift({
            zhangBuLBID: this.query.zhangBuLBID,
            zhangBuLBMC: '全部账簿类别',
          });
        }),
        GetWeiJiZCKDKSRQ().then((res) => {
          let date = dayjs(res).format('YYYY-MM-DD');
          this.query.kaiShiSJ = date;
          this.defaultDate = {
            kaiShiSJ: date,
            jieShuSJ: dayjs().format('YYYY-MM-DD'),
          };
          this.timeRange = [date, dayjs().format('YYYY-MM-DD')];
        }),
      ]);
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    async handleDaYin(row) {
      try {
        this.pageLoading = true;
        const params = {
          chuKuDanID: row.id,
        };
        await printByUrl('YKXT005', params);
        MdMessage({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    handleDateRangeChange(val) {
      if (val && val.length > 0) {
        this.query.kaiShiSJ = val[0]
          ? dayjs(val[0]).format('YYYY-MM-DD')
          : null;
        this.query.jieShuSJ = val[1]
          ? dayjs(val[1]).format('YYYY-MM-DD')
          : null;
      } else {
        this.query.kaiShiSJ = '';
        this.query.jieShuSJ = '';
      }
      this.handleSearch();
    },
    /**
     * 单位部门本地搜索 （拼音码与名称）
     * @param query
     */
    filterMethod(query) {
      if (query !== '') {
        this.danWeiBMOptions = this.danWeiBMOriginOptions.filter((item) => {
          return (
            item.danWeiBMMC.toLowerCase().indexOf(query.toLowerCase()) > -1 ||
            (item.shuRuMa1 &&
              item.shuRuMa1.toLowerCase().indexOf(query.toLowerCase()) > -1)
          );
        });
      } else {
        this.danWeiBMOptions = this.danWeiBMOriginOptions;
      }
    },
    handleSelectChange(val, key) {
      let optionsKey = key + 'Options';
      let mingChengKey = key + 'MC';
      let daiMaKey = key + 'ID';
      let data = this[optionsKey].find((item) => item[daiMaKey] === val);
      this.query[mingChengKey] = data ? data[mingChengKey] : '';
      if (key === 'chuRuKFS') {
        this.query.danWeiBMID = '';
        this.query.danWeiBMMC = '';
        this.getDanWeiBMOptions(val);
      }
      this.handleSearch();
    },
    handleSearch() {
      this.$nextTick(() => {
        this.$refs.table.search({ pageSize: 100 });
      });
    },
    handleKaiDan() {
      this.$router.replace({
        path: '/XinZengCKD',
        query: {
          type: '0',
        },
      });
      sessionStorage.setItem('typeCount1', '1');
    },
    handleEdit(row) {
      this.$router.push({
        name: 'XiuGaiCKD',
        query: {
          id: row.id,
          title: '出库单-' + row.chuKuDH,
        },
      });
    },
    handleJiZhang(row) {
      MdMessageBox.confirm('记账后该出库单无法修改，确定记账？', '操作提醒！', {
        type: 'warning',
      }).then(async () => {
        this.pageLoading = true;
        try {
          if (row.hongDanBZ) {
            await ChongHongJZChuKuDan({ chuKuDID: row.id });
          } else await JiZhangChuKuDan({ chuKuDID: row.id });
          MdMessage({
            type: 'success',
            message: '记账成功',
          });
          this.$emit('go-to-tab');
          // this.handleSearch()
        } finally {
          this.pageLoading = false;
        }
      });
    },
    async handleFetch({ page, pageSize }) {
      const params = {
        pageIndex: page,
        pageSize,
        ...this.query,
      };
      const [items, total] = await Promise.all([
        GetWeiJiZCKDList(params),
        GetWeiJiZCKDCount(params),
      ]);
      return {
        items: items,
        total: total,
      };
    },
    // handleCopySuccess() {
    //   MdMessage({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000,
    //   });
    // },
    // handleCopyError() {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败！`,
    //     confirmButtonText: '我知道了',
    //   });
    // },
    async handleRowClick(e, row, index) {
      e.stopPropagation();
      this.currentTableIndex = index;
      await this.$refs.chukudandetail.openDrawer({
        id: row.id,
        type: 1,
      });
      this.handleSearch();
    },
    tableRowClassName({ row, rowIndex }) {
      let className = '';
      if (rowIndex === this.currentTableIndex) {
        className = 'row-height';
      } else {
        className = '';
      }
      return className;
    },
    handleClear() {
      this.query.chuRuKFSID = '';
      this.query.chuRuKFSMC = '';
    },
    // 选择query里的药品名称
    handleQueryYaoPinMC(e) {
      this.query.jiaGeID = e.jiaGeID || '';
      this.handleSearch();
    },
  },
  components: {
    'chukudan-detail-drawer': ChukudanDetailDrawer,
    'biz-tag-list': BizTagList,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-weijizhang-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  ::v-deep .chonghong-number__color {
    color: #f12933 !important;
  }

  .#{$md-prefix}-weijizhang-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-filters {
      display: flex;

      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }

  ::v-deep .row-height {
    background-color: rgb(var(--md-color-2));
  }

  .#{$md-prefix}-weijizhang-table-box {
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
  }

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .#{$md-prefix}-chonghongBZ {
    margin-left: 4px;
    width: 20px;
    height: 20px;
    background-color: #f12933;
    border-radius: 2px;
    color: #ffffff;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
  }
}

.#{$md-prefix}-rukudh {
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}
</style>
<style lang="scss">
.#{$md-prefix}-chukudantip {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
