<template>
  <md-tabs v-model="activeName" :class="prefixClass('tabs-views')">
    <!-- v-if="isShowRKZD" -->
    <md-tab-pane
      label="待出库"
      name="first"
      :class="prefixClass('content-bg')"
      lazy
    >
      <daichuku-page
        :activeName.sync="activeName"
        ref="daichuku"
        :duMaJDLZD="duMaJDLZD"
      />
    </md-tab-pane>
    <md-tab-pane label="未记账" name="second" lazy>
      <weijizhang-page
        :showZhangBLB="showZhangBLB"
        :isChongHong="isChongHong"
        :openId="openId"
        :lingShouJEXSDW="lingShouJEXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        :showWeiJiZQLD="showWeiJiZQLD"
        @go-to-tab="handleGoToTab"
        ref="weijizhang"
      />
    </md-tab-pane>
    <md-tab-pane label="已记账" name="third" lazy>
      <yijizhang-page
        :showZhangBLB="showZhangBLB"
        :isChongHong="isChongHong"
        :openId="openId"
        :lingShouJEXSDW="lingShouJEXSDW"
        :jinJiaJEXSDW="jinJiaJEXSDW"
        ref="yijizhang"
      />
    </md-tab-pane>
  </md-tabs>
</template>

<script>
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import DaichukuPage from './components/DaiChuKuPage';
import WeijizhangPage from './components/WeiJiZhangPage';
import YijizhangPage from './components/YiJiZhangPage';
export default {
  name: 'yaoPinCK',
  data() {
    return {
      jinJiaXSDW: '',
      lingShouXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      duMaJDLZD: '',
      isShowRKZD: false, //是否显示入库制单，只有中药库显示
      activeName: 'first', // 当前选中导航
      openId: '', // 进入页面时，路由处传入的id （跳转到该页，并显示该id的详情信息。）
      isChongHong: '0', // 是否是其他页面进入
      showZhangBLB: '0',
      showWeiJiZQLD: '0',
    };
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val.path === '/YaoPinCK') {
          let query = val.query;
          // 判断是否跳转到某一个导航（second未记账、third已记账）
          if (query && query.showType) {
            if (this.activeName === query.showType) {
              switch (query.showType) {
                case 'second':
                  if (this.$refs.weijizhang)
                    this.$refs.weijizhang.handleSearch();
                  break;
                case 'third':
                  if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch();
                  break;
              }
            }
            // 切换激活项
            this.activeName = query.showType;
          }
          // 冲红跳转处理
          if (query.isChongHong) {
            this.isChongHong = query.isChongHong;
            this.openId = query.id;
          }
        }
      },
    },
    // 标签切换时请求数据，首次进入时不执行
    activeName: {
      handler: function (val) {
        switch (val) {
          case 'first':
            if (this.$refs.daichuku) this.$refs.daichuku.handleSearch();
            break;
          case 'second':
            if (this.$refs.weijizhang) this.$refs.weijizhang.handleSearch();
            break;
          case 'third':
            if (this.$refs.yijizhang) this.$refs.yijizhang.handleSearch();
        }
      },
    },
  },
  async mounted() {
    const arr = await getKuFangSZList([
      'shiFouAZBLBGL',
      'yaoPinCKDDMJYPDLZD',
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
      'yaoPinCKWJZSFDYQLD',
    ]);
    if (arr.length > 0) {
      arr.forEach((el) => {
        if (el.xiangMuDM == 'shiFouAZBLBGL') {
          this.showZhangBLB = el.xiangMuZDM;
        } else if (el.xiangMuDM == 'yaoPinCKDDMJYPDLZD') {
          this.duMaJDLZD = el.xiangMuZDM;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          this.lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'yaoPinCKWJZSFDYQLD') {
          this.showWeiJiZQLD = el.xiangMuZDM;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
    this.lingShouXSDW = this.lingShouXSDW ? this.lingShouXSDW : xiaoShuDianWS;
    // this.isShowRKZD = false;
    // this.activeName = 'second';
    // // this.$refs.weijizhang.handleSearch();
    // if (getKuCunGLLX().indexOf('3') > -1) {
    //   this.isShowRKZD = true;
    //   this.activeName = 'first';
    // }
  },
  methods: {
    //未记账跳转记账页签 yy
    handleGoToTab() {
      this.activeName = 'third';
    },
  },
  components: {
    'daichuku-page': DaichukuPage,
    'yijizhang-page': YijizhangPage,
    'weijizhang-page': WeijizhangPage,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
}

::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  min-height: 0;
  background: #eaeff3;
}

::v-deep .#{$md-prefix}-base-table-column--selection > .cell {
  justify-content: center;
}
</style>
