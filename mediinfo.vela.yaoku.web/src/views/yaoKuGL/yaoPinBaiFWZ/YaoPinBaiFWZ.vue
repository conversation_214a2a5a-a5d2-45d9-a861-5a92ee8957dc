<template>
  <div :class="prefixClass('KuCunSXXSZ-wrap')">
    <md-tabs v-model="activeName" @tab-click="handleTabsChange">
      <!-- <md-tab-pane label="按主名" name="first">
        <basicName :options="options" :activeList="activeList" />
      </md-tab-pane>
      <md-tab-pane label="按规格" name="second">
        <basicGG :options="options" :activeList="activeList" />
      </md-tab-pane> -->
      <md-tab-pane label="按产地" name="third">
        <basicCD :options="options" :activeList="activeList" />
      </md-tab-pane>
    </md-tabs>
  </div>
</template>

<script>
import { GetZhangBuLBSelectList } from '@/service/yaoPinYK/yaoPinBFWZ';
import basicCD from './components/basicCD';
import basicGG from './components/basicGG';
import basicName from './components/basicName';
export default {
  name: 'YaoPinBaiFWZ-wrap',
  data() {
    return {
      activeName: 'third',
      options: [],
      activeList: ['third'],
    };
  },
  created() {
    GetZhangBuLBSelectList().then((res) => {
      this.options = [{ zhangBuLBMC: '全部账簿类别', zhangBuLBID: '' }, ...res];
    });
  },
  methods: {
    handleTabsChange(val) {
      if (!this.activeList.includes(val.paneName)) {
        this.activeList.push(val.paneName);
      }
    },
  },
  components: {
    basicCD: basicCD,
    basicGG: basicGG,
    basicName: basicName,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-KuCunSXXSZ-wrap {
  flex: 1;
  overflow: hidden;
  ::v-deep {
    .#{$md-prefix}-tabs__header {
      margin-bottom: 0px;
    }
    .#{$md-prefix}-tabs--top {
      display: flex;
      flex-direction: column;
      height: 100%;
      .#{$md-prefix}-tabs__content {
        flex: 1;
        min-height: 0;
        border: 8px solid #f0f2f5;
      }
    }
  }
}
</style>
