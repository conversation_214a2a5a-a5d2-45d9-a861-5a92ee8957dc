::v-deep .has-gutter {
  .cell {
    font-weight: bold;
    font-size: 14px;
  }
}

::v-deep .cell {
  color: #222222;
}
.#{$md-prefix}-top {
  padding: 8px;
  display: flex;
  justify-content: space-between;

  .#{$md-prefix}-top-form {
    display: flex;

    .#{$md-prefix}-form-jigoutree {
      width: 260px;
      margin-right: 8px;
    }
  }
  &_buttons {
    display: flex;
    justify-content: flex-start;
    height: 30px;
  }
}

.#{$md-prefix}-yaoPingGG-box {
  height: 100%;
  background: #eaeff3;
}
.#{$md-prefix}-yaoPingGG {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .#{$md-prefix}-border-table {
    flex: 1;
    padding: 0 8px 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .#{$md-prefix}-pagination-wrap {
      height: 28px;
      text-align: right;
      margin-top: 8px;
    }
    .#{$md-prefix}-table-rap {
      flex: 1;
      overflow: auto;
    }
  }
}
.#{$md-prefix}-num-tab {
  margin-right: 4px;
  padding: 6px 8px;
  background: #f5f5f5;
  border-radius: 2px;
}
