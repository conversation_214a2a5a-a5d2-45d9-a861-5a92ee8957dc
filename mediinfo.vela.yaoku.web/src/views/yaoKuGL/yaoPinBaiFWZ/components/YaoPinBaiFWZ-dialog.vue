<template>
  <md-dialog
    :title="title"
    width="560px"
    v-model="dialogVisible"
    class="dialog-lg clear-scroll-x"
    :close-on-click-modal="false"
    height="300px"
    ref="tk"
    append-to-body
    top="5vh"
  >
    <md-editable-table-pro
      v-enter
      v-model="list"
      :columns="columns"
      :new-row="addRow"
      :add-button-disabled="addButtonDisabled"
      :onBeforeRemove="delRow"
    >
      <template v-slot:bangFangWZID="{ row }">
        <span>
          <md-select
            v-model="row.bangFangWZID"
            placeholder="请选择"
            @visible-change="selectChange($event)"
            @change="handleBaiFangWZ(row, $event)"
          >
            <md-option
              v-for="item in baiFangWZOptions"
              :key="item.baiFangWZID"
              :label="item.baiFangWZMC"
              :value="item.baiFangWZID"
            >
            </md-option>
          </md-select>
        </span>
      </template>
    </md-editable-table-pro>

    <template #footer>
      <div class="dialog-footer">
        <md-button class="cancel__button" @click="cancel">取 消</md-button>
        <md-button class="" type="primary" :loading="load" @click="submit"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import {
  BaoCunYPBFWZ,
  GetYaoPinBFWZListByGGID,
  GetYaoPinBFWZListByJGID,
  GetYaoPinBFWZListByYPID,
  getBaiFangWZSelect,
} from '@/service/yaoPinYK/yaoPinBFWZ';
import { MdMessageBox } from '@mdfe/medi-ui';
import {} from '@mdfe/stark-app';
import { cloneDeep, isEqual } from 'lodash';
export default {
  name: 'YaoPinBaiFWZ-dialog',
  data() {
    return {
      dialogVisible: false,
      title: '',
      load: false,
      baiFangWZOptions: [],
      columns: [
        {
          slot: 'bangFangWZID',
          label: '摆放位置',
        },
        // {
        //   type: "operate",
        //   width: 50,
        //   align:'center',
        //   actions: [
        //     {
        //       icon: "HIS-icon-shanchu",
        //       type: "danger",
        //       onPressed: (props) => {
        //         this.delRow(props.$index);
        //       },
        //     },
        //   ],
        // },
      ],
      addDisabled: false,
      list: [],
      cloneList: [],
      weiHuFS: 0,
      idS: {
        yaoPinID: '',
        guiGeID: '',
        jiaGeID: '',
      },
      addYaoPinBFWZList: [],
      updateYaoPinBFWZList: [],
      zuoFeiYaoPinBFWZList: [],
    };
  },
  computed: {
    addButtonDisabled() {
      let len = this.list.length;
      if (len == 0) {
        return false;
      }
      if (len > 0 && this.list[len - 1].bangFangWZID) {
        return false;
      }

      return true;
    },
  },
  methods: {
    async cancel() {
      if (!isEqual(this.cloneList, this.list)) {
        try {
          await MdMessageBox.confirm('数据有修改, 是否关闭弹窗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          });
          this.dialogVisible = false;
        } catch {}
      } else {
        this.dialogVisible = false;
      }
    },
    submit() {
      let addYaoPinBFWZList = [];
      this.list.forEach((item) => {
        if (!item.id) {
          addYaoPinBFWZList.push(item);
        }
      });
      let isFullStrAdd = addYaoPinBFWZList.every((item) => {
        return item.bangFangWZID;
      });
      let isFullStrUpdate = this.updateYaoPinBFWZList.every((item) => {
        return item.bangFangWZID;
      });
      if (!isFullStrAdd || !isFullStrUpdate) {
        // this.$message({ message: "摆放位置不能为空", type: "error" });
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `摆放位置不能为空`,
          confirmButtonText: '我知道了',
        });
        return;
      }
      if (
        addYaoPinBFWZList.length > 0 ||
        this.zuoFeiYaoPinBFWZList.length > 0 ||
        this.updateYaoPinBFWZList.length > 0
      ) {
        this.load = true;
        BaoCunYPBFWZ({
          addYaoPinBFWZList,
          zuoFeiYaoPinBFWZList: this.zuoFeiYaoPinBFWZList,
          updateYaoPinBFWZList: this.updateYaoPinBFWZList,
        }).then((res) => {
          this.dialogVisible = false;
          this.load = false;
          this.$message({ message: '添加成功', type: 'success' });
          this.resolve({ mode: 'edit' });
        });
      } else {
        this.dialogVisible = false;
      }
    },
    async showDialog(option) {
      this.title = '编辑摆放位置-' + option.data.yaoPinMC;
      this.dialogVisible = true;
      this.updateYaoPinBFWZList = [];
      this.zuoFeiYaoPinBFWZList = [];
      this.baiFangWZOptions = await getBaiFangWZSelect();
      await this.getList(option);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    delRow(index) {
      if (this.list[index].id) {
        this.zuoFeiYaoPinBFWZList.push(this.list[index].id);
      }
      // this.list.splice(index, 1);
    },
    addRow() {
      if (this.addDisabled) return;
      this.list.push({
        bangFangWZID: '',
        baiFangWZ: '',
        weiHuFS: this.weiHuFS,
        ...this.idS,
      });
    },
    handleStrChange(row) {
      if (row.id) {
        let index = this.cloneList.findIndex((item) => item.id == row.id);
        if (this.updateYaoPinBFWZList.length > 0) {
          let updataIndex = this.updateYaoPinBFWZList.findIndex(
            (item) => item.id == row.id,
          );
          if (this.cloneList[index].bangFangWZID != row.bangFangWZID) {
            if (updataIndex >= 0) {
              this.updateYaoPinBFWZList[updataIndex] = row;
            } else {
              this.updateYaoPinBFWZList.push(row);
            }
          } else {
            this.updateYaoPinBFWZList.splice(updataIndex, 1);
          }
        } else {
          this.updateYaoPinBFWZList.push(row);
        }

        {
        }
      }
    },
    async getList(option) {
      switch (option.name) {
        case 'basicCD':
          this.weiHuFS = '3';
          this.idS.jiaGeID = option.data.jiaGeID;
          GetYaoPinBFWZListByJGID({
            jiaGeID: option.data.jiaGeID,
          }).then((res) => {
            this.list = res;
            this.cloneList = cloneDeep(this.list);
          });
          break;
        case 'basicGG':
          this.weiHuFS = '2';
          this.idS.guiGeID = option.data.guiGeID;
          GetYaoPinBFWZListByGGID({ guiGeID: option.data.guiGeID }).then(
            (res) => {
              this.list = res;
              this.cloneList = cloneDeep(this.list);
            },
          );
          break;
        case 'basicName':
          this.weiHuFS = '1';
          this.idS.yaoPinID = option.data.yaoPinID;
          GetYaoPinBFWZListByYPID({ yaoPinID: option.data.yaoPinID }).then(
            (res) => {
              this.list = res;
              this.cloneList = cloneDeep(this.list);
            },
          );
          break;

        default:
          break;
      }
    },
    /**
     * 延时关闭上一节点， 供下拉选择时，回车事件获取表格输入定位。
     * @param value
     * @param cellRef
     */
    selectChange(value, cellRef) {
      if (!value) {
        setTimeout(() => {
          // cellRef.endEdit();
        }, 200);
      }
    },
    handleBaiFangWZ(row, value) {
      const list = this.list.filter((item) => item.bangFangWZID === value);
      if (list.length > 1) {
        this.$message({
          type: 'warning',
          message: '已存在该摆放位置，不能重复添加',
        });
        row.bangFangWZID = '';
        return;
      }
      row.baiFangWZ = this.baiFangWZOptions.find((item) => {
        return item.baiFangWZID == value;
      })?.baiFangWZMC;
    },
  },
};
</script>

<style lang="scss" scoped>
.cancel__button {
  color: #1e88e5;
  background: #ffffff;
  border: 1px solid #c3e5fd;
  border-radius: 4px;
}

.md-table-addbtn {
  display: block;
  margin: 10px auto;
}

.xuKeZheng-table-title {
  font-weight: 500;
  color: #222222;
  font-size: 16px;
  line-height: 22px;
  padding-left: 12px;
  position: relative;
  margin-bottom: 8px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 3px;
    background-color: #1e88e5;
    height: 16px;
  }
}
</style>
