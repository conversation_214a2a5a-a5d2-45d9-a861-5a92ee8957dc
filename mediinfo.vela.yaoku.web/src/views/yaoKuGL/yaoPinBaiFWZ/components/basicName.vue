<template>
  <div :class="prefixClass('yaoPingGG-box')">
    <div :class="prefixClass('yaoPingGG')">
      <div :class="prefixClass('top')">
        <div :class="prefixClass('top-form')">
          <md-input
            v-model="query.BaiFangWZ"
            :class="prefixClass('input__alias')"
            placeholder="输入摆放位置搜索"
            @keyup.enter="searchHandle('')"
            @lear="searchHandle('')"
            style="width: 200px; margin-right: 8px"
          >
            <template #suffix>
              <i
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
                @click="searchHandle('')"
              ></i>
            </template>
          </md-input>

          <biz-yaopindw
            v-model="query.YaoPinMC"
            placeholder="请输入药品名称选择"
            :showSuffix="true"
            @change="searchHandle"
            ref="bizSearch"
            style="width: 286px"
          ></biz-yaopindw>
        </div>
        <div :class="prefixClass('top_buttons')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="searchHandle('')"
            style="margin-right: 8px"
          >
            刷新</md-button
          >
          <!--<md-button type="primary" :icon="prefixClass('icon-dayinji')" noneBg
            >打印</md-button
          >-->
          <!-- <md-button type="primary"  :icon="prefixClass('icon-daochu')" noneBg
            >导出</md-button
          > -->
        </div>
      </div>
      <div :class="prefixClass('border-table')">
        <div
          :class="prefixClass('table-rap')"
          v-if="activeList.includes('first')"
        >
          <md-table-pro
            :columns="columns"
            :data="dataList"
            height="100%"
            auto-load
            resize
            :onFetch="handleFetch"
            ref="table"
          >
            <template v-slot:baiFangWZList="{ row }">
              <span
                :class="prefixClass('num-tab')"
                v-for="(item, index) in row.baiFangWZList"
                :key="index"
                >{{ item }}</span
              >
            </template>

            <template v-slot:operate="{ row, $index }">
              <md-button
                type="primary"
                noneBg
                style="margin-left: 0px; padding: 0.8px; border-radius: 2px"
                @click="handleEdit(row)"
              >
                编辑
              </md-button>
              <md-button
                type="danger"
                noneBg
                style="margin-left: 10px; padding: 0.8px; border-radius: 2px"
                @click="handleDelete(row, $index)"
              >
                清空
              </md-button>
            </template>
          </md-table-pro>
        </div>
      </div>
    </div>
    <md-YaoPinBaiFWZ ref="YaoPinBaiFWZ"></md-YaoPinBaiFWZ>
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  GetYaoPinBFWZCountByZM,
  GetYaoPinBFWZListByZM,
  QingKongYPBFWZListByYPID,
} from '@/service/yaoPinYK/yaoPinBFWZ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { MdMessageBox } from '@mdfe/medi-ui';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import { debounce } from 'lodash';
import YaoPinBaiFWZ from './YaoPinBaiFWZ-dialog';
export default {
  name: 'basicName',
  props: {
    activeList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      query: {
        YaoPinMC: '',
        BaiFangWZ: '',
        YaoPinLX: getKuCunGLLX(),
        GuiGeLX: 1, //药库搜大规格
      },

      pagination: {
        pageNo: 1,
        pageSize: 10,
      },
      columns: [
        {
          prop: 'yaoPinMC',
          label: '药品名称',
          'min-width': 474,
        },
        { prop: 'biaoZhunBM', label: '标准编码', 'min-width': 218 },
        {
          prop: 'shuRuMa1',
          label: '拼音码',
          'min-width': 146,
          showOverflowTooltip: true,
        },
        {
          prop: 'shuRuMa2',
          label: '五笔码',
          'min-width': 146,
          showOverflowTooltip: true,
        },
        {
          slot: 'baiFangWZList',
          prop: 'baiFangWZList',
          label: '药品摆放位置',
          'min-width': 222,
        },
        { slot: 'operate', label: '操作', 'min-width': 88, fixed: 'right' },
      ],
      dataList: [],
      dataListChangeList: [],
    };
  },
  methods: {
    searchHandle: debounce(function (data) {
      this.query.YaoPinMC = data;
      this.$refs.table.search();
    }, 400),
    async handleFetch({ page, pageSize }, config) {
      let data = {
        BaiFangWZ: this.query.BaiFangWZ,
        YaoPinMC: this.query.YaoPinMC.yaoPinMC,
        YaoPinLX: getKuCunGLLX(),
        GuiGeLX: 1, //药库搜大规格
      };
      await GetYaoPinBFWZListByZM({
        pageIndex: page,
        pageSize: pageSize,
        ...data,
      }).then((res) => {
        this.dataList = res;
      });
      await GetYaoPinBFWZCountByZM(data).then((res) => {
        this.total = res;
      });
      return {
        items: this.dataList,
        total: this.total,
      };
    },
    async handleEdit(row) {
      const res = await this.$refs.YaoPinBaiFWZ.showDialog({
        mode: 'edit',
        name: 'basicName',
        data: row,
      });
      if (res.mode === 'edit') {
        // this.searchHandle('');
      }
    },
    async handleDelete(row, index) {
      if (row.baiFangWZList.length == 0) {
        this.$message({
          message: '药品摆放位置信息已经为空',
          type: 'warning',
        });
        return;
      }
      const flag = await MdMessageBox.confirm(
        '确定清空药品摆放位置信息？',
        '操作提醒！',
        {
          type: 'warning',
        },
      )
        .then(() => {
          return QingKongYPBFWZListByYPID({ yaoPinID: row.yaoPinID });
        })
        .then((response) => {
          this.$message({ message: '清空成功', type: 'success' });
          this.$refs.table.search();
        })
        .catch((error) => {
          if (error !== 'cancel') {
            // this.$message({ message: error.message, type: "error" });
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
  },
  components: {
    'md-table-pro': MdTablePro,
    'md-YaoPinBaiFWZ': YaoPinBaiFWZ,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@import './basic';
</style>
