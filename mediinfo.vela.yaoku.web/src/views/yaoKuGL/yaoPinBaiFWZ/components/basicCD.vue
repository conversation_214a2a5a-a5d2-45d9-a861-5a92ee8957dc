<template>
  <div :class="prefixClass('yaoPingGG-box')">
    <div :class="prefixClass('yaoPingGG')">
      <div :class="prefixClass('top')">
        <div :class="prefixClass('top-form')">
          <md-select
            v-model="query.ZhangBuLBID"
            placeholder="请选择"
            @change="searchHandle('', 1)"
            style="margin-right: 8px; width: 180px"
          >
            <md-option
              v-for="item in options"
              :key="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :value="item.zhangBuLBID"
            ></md-option>
          </md-select>
          <md-input
            v-model="query.BaiFangWZ"
            :class="prefixClass('input__alias')"
            placeholder="输入摆放位置搜索"
            @keyup.enter="searchHandle('', 1)"
            @clear="searchHandle('', 1)"
            style="width: 200px; margin-right: 8px"
          >
            <template #suffix>
              <i
                :class="prefixClass('input__icon icon-seach cursor-pointer')"
                @click="searchHandle('', 1)"
              ></i>
            </template>
          </md-input>
          <biz-yaopindw
            v-model="query.YaoPinMC"
            placeholder="请输入药品名称选择"
            :showSuffix="true"
            @change="searchHandle"
            ref="bizSearch"
            style="width: 286px"
          ></biz-yaopindw>
        </div>
        <div :class="prefixClass('top_buttons')">
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="searchHandle('', 1)"
            style="margin-right: 8px"
            >刷新</md-button
          >
          <!--<md-button type="primary" :icon="prefixClass('icon-dayinji')" noneBg
            >打印</md-button
          >-->
          <!-- <md-button type="primary"  :icon="prefixClass('icon-daochu')" noneBg
            >导出</md-button
          >-->
        </div>
      </div>
      <div :class="prefixClass('border-table')">
        <div
          :class="prefixClass('table-rap')"
          v-if="activeList.includes('third')"
        >
          <md-table-pro
            :columns="columns"
            :data="dataList"
            height="100%"
            :onFetch="handleFetch"
            ref="table"
          >
            <template v-slot:baiFangWZList="{ row }">
              <span
                :class="prefixClass('num-tab')"
                v-for="(item, index) in row.baiFangWZList"
                :key="index"
              >
                {{ item }}
              </span>
            </template>
            <template v-slot:operate="{ row, $index }">
              <md-button
                type="primary"
                noneBg
                style="margin-left: 0px; padding: 0.8px; border-radius: 2px"
                @click="handleEdit(row)"
                >编辑</md-button
              >
              <md-button
                type="danger"
                noneBg
                style="margin-left: 10px; padding: 0.8px; border-radius: 2px"
                @click="handleDelete(row, $index)"
                >清空</md-button
              >
            </template>
          </md-table-pro>
        </div>
      </div>
    </div>
    <md-YaoPinBaiFWZ ref="YaoPinBaiFWZ"></md-YaoPinBaiFWZ>
  </div>
</template>

<script>
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  GetYaoPinBFWZCountByCD,
  GetYaoPinBFWZListByCD,
  QingKongYPBFWZListByJGID,
} from '@/service/yaoPinYK/yaoPinBFWZ';
import formatJiaGe from '@/system/utils/formatJiaGe';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { MdMessageBox } from '@mdfe/medi-ui';
import { debounce } from 'lodash';
import YaoPinBaiFWZ from './YaoPinBaiFWZ-dialog';
export default {
  name: 'basicCD',
  props: {
    options: {
      type: Array,
      default: function () {
        return [];
      },
    },
    activeList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      query: {
        YaoPinMC: {},
        ZhangBuLBID: '',
        BaiFangWZ: '',
      },
      columns: [
        {
          prop: 'yaoPinBM',
          label: '别名',
          'min-width': 177,
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称规格',
          'min-width': 355,
        },
        { prop: 'chanDiMC', label: '产地', 'min-width': 233 },
        { prop: 'baoZhuangDW', label: '单位', 'min-width': 54 },
        {
          prop: 'lingShouJia',
          label: '零售价',
          'min-width': 118,
          align: 'right',
          formatter(row, column, cellValue) {
            cellValue = cellValue || 0;
            return formatJiaGe(cellValue);
          },
        },
        { prop: 'zhangBuLBMC', label: '账簿类别', 'min-width': 88 },
        {
          slot: 'baiFangWZList',
          prop: 'baiFangWZList',
          label: '药品摆放位置',
          'min-width': 222,
        },
        { slot: 'operate', label: '操作', 'min-width': 95, fixed: 'right' },
      ],
      dataList: [],
    };
  },

  mounted() {
    this.$refs.table.search({ pageSize: 100 });
  },
  methods: {
    searchHandle: debounce(function (data, type = 0) {
      if (type === 0) {
        this.query.YaoPinMC = data;
      }
      this.$refs.table.refresh();
    }, 400),
    async handleFetch({ page, pageSize }, config) {
      let data = {
        BaiFangWZ: this.query.BaiFangWZ,
        jiaGeID: this.query.YaoPinMC.jiaGeID,
        ZhangBuLBID: this.query.ZhangBuLBID,
        YaoPinLX: getKuCunGLLX(),
        GuiGeLX: 1, //药库搜大规格
      };
      await GetYaoPinBFWZListByCD({
        pageIndex: page,
        pageSize: pageSize,
        ...data,
      }).then((res) => {
        this.dataList = res;
      });
      await GetYaoPinBFWZCountByCD({ ...data }).then((res) => {
        this.total = res;
      });
      return {
        items: this.dataList,
        total: this.total,
      };
    },
    async handleEdit(row) {
      const res = await this.$refs.YaoPinBaiFWZ.showDialog({
        mode: 'edit',
        name: 'basicCD',
        data: row,
      });
      if (res.mode === 'edit') {
        this.$refs.table.refresh();
      }
    },
    async handleDelete(row, index) {
      if (row.baiFangWZList.length == 0) {
        this.$message({
          message: '药品摆放位置信息已经为空',
          type: 'warning',
        });
        return;
      }
      const flag = await MdMessageBox.confirm(
        '确定清空药品摆放位置信息？',
        '操作提醒！',
        {
          type: 'warning',
        },
      )
        .then(() => {
          return QingKongYPBFWZListByJGID({ jiaGeID: row.jiaGeID });
        })
        .then((response) => {
          this.$message({ message: '清空成功', type: 'success' });
          this.$refs.table.search({ pageSize: 100 });
        })
        .catch((error) => {
          if (error !== 'cancel') {
            // this.$message({ message: error.message, type: "error" });
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
  },
  components: {
    'md-YaoPinBaiFWZ': YaoPinBaiFWZ,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@import './basic';
</style>
