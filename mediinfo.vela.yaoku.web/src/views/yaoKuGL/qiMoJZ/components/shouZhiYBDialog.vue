<template>
  <md-dialog
    title="收支月报"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <md-table
        :columns="columns"
        :data="tableData"
        height="360"
        highlight-current-row
        @cell-dblclick="handleRowClick"
      />
    </div>
    <span slot="footer" class="dialog-footer"></span>
    <da-yin :params="params" :id="id" ref="report" />
  </md-dialog>
</template>

<script>
import { GetJieZhuanDanList } from '@/service/yaoPinYK/qiMoJZ';
import { printByUrl } from '@/system/utils/print';
import DaYin from '@/components/DaYinBB';
export default {
  name: 'shouzhiybdialog',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      id: 'YFXT003',
      params: {
        jieZhuanDID: '1022406240627597312',
      },
      columns: [
        {
          prop: 'id',
          label: '结转单ID',
        },
        {
          prop: 'jieZhuanKSSJ',
          label: '结转开始时间',
        },
        {
          prop: 'jieZhuanJSSJ',
          label: '结转结束时间',
        },
      ],
      tableData: [],
    };
  },
  methods: {
    async showModel() {
      try {
        this.dialogVisible = true;
        this.loading = true;
        this.tableData = await GetJieZhuanDanList();
      } finally {
        this.loading = false;
      }
    },
    // 双击一行 打印
    handleRowClick(row) {
      this.params.jieZhuanDID = row.id;
      printByUrl(this.id, this.params);
    },
    handleClose() {
      this.dialogVisible = false;
    },
  },
  components: {
    DaYin,
  },
};
</script>

<style lang="scss" scoped></style>
