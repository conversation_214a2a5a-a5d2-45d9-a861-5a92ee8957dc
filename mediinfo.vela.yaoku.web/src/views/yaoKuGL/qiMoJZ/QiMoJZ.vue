<template>
  <div
    v-loading="pageLoading"
    element-loading-text="正在加载中..."
    :class="prefixClass('qimojz')"
  >
    <div :class="prefixClass('qimojz-top')">
      <div :class="prefixClass('qimojz-top-left')">
        <div :class="prefixClass('yaofangkcwh-text margin-right-8')">
          结转日期
        </div>
        <md-date-picker
          v-model="query.kaiShiRQ"
          placeholder="开始日期"
          :class="prefixClass('margin-right-8')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          disabled
        />
        <div :class="prefixClass('yaofangkcwh-text margin-right-8')">至</div>
        <md-date-picker
          v-model="query.jieShuRQ"
          placeholder="结束日期"
          :class="prefixClass('margin-right-8')"
          :editable="false"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 204px"
          :disabledDate="disabledDate"
        />
        <md-time-picker
          v-model="query.jieShuSJ"
          style="width: 100px"
          format="HH:mm"
          value-format="HH:mm:ss"
          placeholder="请选择"
          :editable="false"
          @change="handleChangeTime"
        />
      </div>
      <div :class="prefixClass('qimojz-top-right')">
        <md-button
          type="primary"
          :class="prefixClass('margin-right-8')"
          plain
          @click="handleHeDuiZB"
          >核对账本</md-button
        >
        <md-button
          v-if="false"
          type="primary"
          :class="prefixClass('margin-right-8')"
          plain
          @click="handleWeiChaTZ"
          >尾差调整</md-button
        >
        <md-button
          type="primary"
          :class="prefixClass('margin-right-8')"
          plain
          @click="handleChaKanSZYB"
          >查看收支月报</md-button
        >
        <md-button
          type="primary"
          :class="prefixClass('margin-right-8')"
          plain
          @click="handleCancelJieZhuan"
          >取消结转</md-button
        >
        <md-button type="primary" @click="handleJieZhuan">结转</md-button>
      </div>
    </div>
    <div :class="prefixClass('qimojz-main')">
      <md-table-pro
        :columns="columns"
        height="100%"
        :onFetch="handleFetch"
        ref="mdTablePro"
      />
    </div>
    <shouzhiyb-dialog ref="shouZhiYBDialog" />
  </div>
</template>

<script>
import dayjs from 'dayjs';
import shouZhiYBDialog from './components/shouZhiYBDialog';
import { MessageBox } from '@mdfe/stark-app';
import { MdMessageBox } from '@mdfe/medi-ui';

import {
  JieZhuan,
  QuXiaoJZ,
  WeiChaTZ,
  HeDuiZB,
  GetZuiJinJZRQ,
} from '@/service/yaoPinYK/qiMoJZ';
export default {
  name: 'qimojz',
  data() {
    return {
      query: {
        kaiShiRQ: '',
        jieShuRQ: dayjs().format('YYYY-MM-DD'),
        jieShuSJ: dayjs().format('HH:mm:ss'),
      },
      // pickerOptions: {
      //   disabledDate: time => {
      //     return dayjs(time).valueOf() <= dayjs(this.query.kaiShiRQ).valueOf()
      //   }
      // },
      pageLoading: false,
      tableData: [],
      allTableData: [],
      columns: [
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 300,
          formatter: (row) => {
            return row.yaoPinMC + ' ' + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 280,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 108,
        },
        {
          prop: 'jieCunSL',
          label: '结存数量',
          align: 'right',
          width: 148,
          formatter(row) {
            return Number(row.jieCunSL).toFixed(3);
          },
        },
        {
          prop: 'jieCunJE',
          label: '结存金额',
          align: 'right',
          width: 148,
          formatter: (row) => {
            return Number(row.jieCunJE ? row.jieCunJE : 0).toFixed(3);
          },
        },
        {
          prop: 'zhangMianKCSL',
          label: '账面数量',
          align: 'right',
          width: 148,
          formatter(row) {
            return Number(row.zhangMianKCSL).toFixed(3);
          },
        },
        {
          prop: 'zhangMianKCJE',
          label: '账面金额',
          align: 'right',
          width: 148,
          formatter: (row) => {
            return Number(row.zhangMianKCJE ? row.zhangMianKCJE : 0).toFixed(3);
          },
        },
      ],
      pageSize: 10,
      reportUrl: '',
    };
  },
  computed: {
    formattQuery() {
      return {
        KaiShiSJ: this.query.kaiShiRQ,
        JieShuSJ: this.query.jieShuRQ + ' ' + this.query.jieShuSJ,
      };
    },
  },
  created() {
    GetZuiJinJZRQ()
      .then((res) => {
        this.query.kaiShiRQ = res;
      })
      .catch(() => {
        // this.$message({
        //   type: 'error',
        //   message: '获取最近结转时间失败'
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `获取最近结转时间失败`,
          confirmButtonText: '我知道了',
        });
      });
  },
  mounted() {
    this.$refs.mdTablePro.search({ pageSize: 100 });
  },
  methods: {
    disabledDate(time) {
      return dayjs(time).valueOf() <= dayjs(this.query.kaiShiRQ).valueOf();
    },
    getTableData(page, pageSize) {
      return this.allTableData.slice((page - 1) * pageSize, page * pageSize);
    },
    async handleFetch({ page, pageSize }) {
      let items = this.getTableData(page, pageSize);
      let total = this.allTableData.length;
      return {
        items,
        total,
      };
    },
    handleChangeTime(val) {
      if (!val) {
        // this.query.jieShuSJ = '00:00:00'
      }
    },
    //核对账本
    handleHeDuiZB() {
      this.pageLoading = true;
      HeDuiZB(this.formattQuery)
        .then((res) => {
          this.allTableData = res;
          this.$refs.mdTablePro.search({ pageSize: 100 });
          this.$message({
            type: 'success',
            message: '核对账本成功！',
          });
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    //尾差调整
    handleWeiChaTZ() {
      this.pageLoading = true;
      WeiChaTZ(this.formattQuery)
        .then((res) => {
          this.allTableData = res;
          this.$refs.mdTablePro.search({ pageSize: 100 });
          this.$message({
            type: 'success',
            message: '尾差调整成功！',
          });
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    //查看收支月报
    handleChaKanSZYB() {
      this.$refs.shouZhiYBDialog.showModel();
    },
    //取消结转
    handleCancelJieZhuan() {
      this.pageLoading = true;
      QuXiaoJZ(this.formattQuery)
        .then(() => {
          this.$message({
            type: 'success',
            message: '取消结转成功',
          });
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    //结转
    handleJieZhuan() {
      this.pageLoading = true;
      JieZhuan(this.formattQuery)
        .then(() => {
          this.$message({
            type: 'success',
            message: '结转成功',
          });
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
  },
  components: {
    'shouzhiyb-dialog': shouZhiYBDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-qimojz {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background-color: #f5f5f5;
  padding: 8px;
  ::v-deep .#{$md-prefix}-margin-right-8 {
    margin-right: 8px;
  }
  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #ffffff;
    &-left {
      display: flex;
      align-items: center;
      .#{$md-prefix}-yaofangkcwh-text {
        height: 20px;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
      }
    }
    &-right {
      display: flex;
    }
  }
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 8px 8px 8px;
    background-color: #ffffff;
    min-height: 0;
  }
  &-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    background-color: #f5f5f5;
    .#{$md-prefix}-button-size {
      width: 64px;
      height: 30px;
      &:nth-child(n + 2) {
        margin-left: 12px;
      }
    }
  }
  ::v-deep .#{$md-prefix}-date-editor {
    --md-date-editor-width: unset;
  }
}
</style>
