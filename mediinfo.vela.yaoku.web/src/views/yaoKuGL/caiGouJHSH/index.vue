<template>
  <div :class="prefixClass('caigou-content')">
    <div :class="prefixClass('procurement-top')">
      <div :class="prefixClass('procurement-left')">
        <md-date-picker-range-pro
          :class="prefixClass('procurement-date')"
          v-model="query.timeRange"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleSearch"
        >
        </md-date-picker-range-pro>
        <md-select
          v-model="query.zhuangTai"
          :clearable="false"
          :class="prefixClass('procurement-select')"
          @change="handleSearch"
        >
          <md-option label="全部状态" value=""></md-option>
          <md-option
            v-for="item in zhuangTaiOptions"
            :key="item.biaoZhunDM"
            :label="item.biaoZhunMC"
            :value="item.biaoZhunDM"
          >
          </md-option>
        </md-select>
        <md-input
          v-model="query.danJuHao"
          placeholder="输入单据号搜索"
          :suffix-icon="prefixClass('icon-seach cursor-pointer')"
          :class="prefixClass('procurement-input')"
          @keyup.enter.native="handleSearch"
        >
        </md-input>
      </div>
      <md-button
        type="primary"
        :icon="prefixClass('icon-shuaxin')"
        noneBg
        style="margin-left: auto"
        @click="handleSearch"
        >刷新</md-button
      >
    </div>
    <div :class="prefixClass('caigou-table')">
      <md-table-pro
        :columns="columns"
        :onFetch="handleFetch"
        height="100%"
        :stripe="false"
        :class="prefixClass('table')"
        ref="table"
      >
        <template v-slot:caiGouDH="{ row }">
          <md-tooltip
            trigger="hover"
            effect="light"
            :popper-class="prefixClass('his-cai-gou-dan')"
          >
            <template #content>
              <div @click="copy(row.caiGouDH)" :class="prefixClass('fuzhi')">
                复制
              </div>
            </template>
            <span
              :class="prefixClass('caigoudh')"
              @click="handleRowClick($event, row)"
              >{{ row.caiGouDH }}</span
            >
          </md-tooltip>
          <md-tooltip
            trigger="hover"
            effect="dark"
            :popper-class="prefixClass('his-cai-gou-dan')"
          >
            <template #content> 平台上传失败 </template>
            <md-icon
              name="tixing-s"
              color="red"
              v-if="row.shangChuanBZ == 2"
            ></md-icon>
          </md-tooltip>
        </template>
        <template v-slot:zhuangTai="{ row }">
          <md-tag v-if="row.danJuZTDM == 2" type="warning">待审核</md-tag>
          <md-tag v-if="row.danJuZTDM == 3" type="success">已通过</md-tag>
          <md-tag v-if="row.danJuZTDM == 4" type="danger">已拒绝</md-tag>
        </template>
        <template v-slot:operate="{ row }">
          <md-button
            type="text-bg"
            v-if="row.danJuZTDM !== '2'"
            @click="handleChaKan($event, row, 1)"
          >
            查看
          </md-button>
          <md-button
            type="text-bg"
            v-if="row.danJuZTDM == '2'"
            @click="handleChaKan($event, row, 2)"
          >
            审核
          </md-button>
        </template>
      </md-table-pro>
    </div>
    <xiangqiangdan ref="caigoujhshdetail" size="75%" @refresh="handleSearch" />
  </div>
</template>
<script>
import { getShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  GetCaiGouJHDSHXXCount,
  GetCaiGouJHDSHXXList,
} from '@/service/yaoPinYK/caiGouJHSH';
import dayjs from 'dayjs';
import useClipboard from 'vue-clipboard3';
import eventBus from '../../../system/utils/eventbus';
import XiangQingDan from './component/XiangQingDan';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';

export default {
  name: 'CaiGouJHSH',
  data() {
    return {
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      query: {},
      columns: [
        {
          slot: 'caiGouDH',
          prop: 'caiGouDH',
          label: '采购单',
          width: 220,
        },
        {
          slot: 'yaoPinZS',
          prop: 'yaoPinZS',
          label: '药品数',
          align: 'right',
          width: 100,
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          align: 'right',
          minwidth: 120,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          align: 'right',
          minwidth: 120,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },
        {
          prop: 'zhiDanSJ',
          label: '制单日期',
          width: 140,
          formatter: (row, column, cellValue, index) => {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 108,
        },
        {
          prop: 'beiZhu',
          label: '备注',
          width: 180,
          showOverflowTooltip: true,
        },
        {
          prop: 'shenHeRXM',
          label: '审核人',
          width: 108,
          showOverflowTooltip: true,
        },
        {
          prop: 'zhuangTai',
          slot: 'zhuangTai',
          label: '审核状态',
          width: 108,
          align: 'center',
          showOverflowTooltip: true,
        },
        {
          slot: 'operate',
          type: 'operate',
          label: '操作',
          fixed: 'right',
          width: 90,
        },
      ],
      zhuangTaiOptions: [],
    };
  },
  async mounted() {
    this.query = {
      timeRange: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      danJuHao: '', //单据号
      zhuangTai: '',
    };
    this.zhuangTaiOptions = await getShuJuYZYList({
      pageIndex: 1,
      pageSize: 9999,
      shuJuYLBID: 'YP0107',
    });
    this.handleSearch();
    eventBus.$on('handleFetchCaiGou', this.handleSearch);
    const res = await getKuFangSZList(['jinJiaJEXSDWS', 'lingShouJEXSDWS']);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  },
  beforeUnmount() {
    eventBus.$off('handleFetchCaiGou', this.handleSearch);
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          // console.log(err);
        });
    },
    async handleFetch({ page, pageSize }, config) {
      const { danJuHao, timeRange, zhuangTai } = this.query;
      const params = {
        CaiGouDH: danJuHao,
        pageIndex: page,
        danJuZTDM: zhuangTai,
        pageSize: pageSize < 0 ? 1 : pageSize,
      };
      params.zhiDanSJKS = timeRange[0]
        ? dayjs(timeRange[0]).format('YYYY-MM-DD')
        : null;
      params.zhiDanSJJS = timeRange[1]
        ? dayjs(timeRange[1]).format('YYYY-MM-DD')
        : null;
      //获取列表
      const [items, total] = await Promise.all([
        GetCaiGouJHDSHXXList(params),
        GetCaiGouJHDSHXXCount(params),
      ]);
      return { items, total };
    },
    //调用table刷新
    handleSearch() {
      if (this.$refs.table) {
        this.$refs.table.search({ pageSize: 100 });
      } else {
        this.$nextTick(() => {
          this.$refs.table.search({ pageSize: 100 });
        });
      }
    },
    //新增采购计划单
    handleKaiDan() {
      this.$router.push({
        name: 'XinZengCGA',
      });
    },
    //编辑采购计划单
    handleChaKan(e, row, type) {
      e.stopPropagation();
      this.$refs.caigoujhshdetail.openDrawer(row, type);
    },
    //点击采购单号--打开侧滑
    handleRowClick(e, row) {
      const type = row.danJuZTDM == '2' ? 2 : 1;
      e.stopPropagation();
      this.$refs.caigoujhshdetail.openDrawer(row, type);
    },
  },
  components: {
    xiangqiangdan: XiangQingDan,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-fuzhi {
  color: rgb(var(--md-color-6));
}

.#{$md-prefix}-his-cai-gou-dan {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;
  float: left;

  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
.#{$md-prefix}-caigoudh {
  float: left;
  margin-right: 5px;
  cursor: pointer;
  color: rgb(var(--md-color-6));

  &:hover {
    color: rgb(var(--md-color-6));
    text-decoration: underline;
    line-height: 20px;
  }
}

.#{$md-prefix}-caigou-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0 8px;
  min-height: 0;
  min-width: 0;

  .#{$md-prefix}-procurement-top {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .#{$md-prefix}-procurement-left {
    display: flex;
    align-items: center;
  }

  .#{$md-prefix}-procurement-date {
    margin-right: 12px;
    min-width: 250px;
  }

  .#{$md-prefix}-procurement-select {
    margin-right: 12px;
    min-width: 180px;
  }

  .#{$md-prefix}-procurement-input {
    min-width: 260px;
  }

  .#{$md-prefix}-caigou-table {
    flex: 1;
    min-height: 0;
    display: flex;

    .#{$md-prefix}-table {
      flex: 1;
      min-height: 0;
      min-width: 0;
    }
  }

  .#{$md-prefix}-public-span {
    display: inline-block;
    width: 16px;
    height: 16px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: 16px;
    border-radius: 8px;
    margin-right: 4px;
  }

  .#{$md-prefix}-ling {
    background-color: #1385f0;
  }

  .#{$md-prefix}-ji {
    background-color: #ff8600;
  }
}

::v-deep .has-gutter {
  .cell {
    font-weight: bold;
    font-size: 14px;
  }
}

::v-deep .cell {
  color: #222222;
}
</style>
