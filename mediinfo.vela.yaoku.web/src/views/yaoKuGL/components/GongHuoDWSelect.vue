<template>
  <md-select-scroll
    v-model="query.gongHuoDWID"
    v-bind="$attrs"
    :class="[prefixClass('filter-width'), 'gong-huo-d-w-select']"
    style="margin-right: 8px"
    :onFetch="loadMore"
    filterable
    remote
    autoLoad
    placeholder="输入选择供货单位"
    @change="handleSelectChange"
    @focus="handleFocus"
    ref="selectRef"
  >
    <template v-slot="{ options }">
      <md-option
        v-for="item in options"
        :key="item.danWeiID"
        :value="item.danWeiID"
        :label="item.danWeiMC"
      ></md-option>
    </template>
  </md-select-scroll>
</template>

<script>
import { GetGongHuoDWCount, GetGongHuoDWList } from '@/service/yaoPinYK/common';
import MdSelectScroll from '@mdfe/material.select-scroll';
// import MdSelectScroll from '@/views/yaoKuGL/yaoPinRK/components/test';
import { logger } from '@/service/log';
export default {
  name: 'gonghuodw-select',
  props: {
    value: {
      type: String,
      default: '',
    },
    gongHuoDWList: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    value: {
      handler: function (val) {
        this.query['gongHuoDWID'] = val;
      },
      immediate: true,
    },
  },
  data() {
    return {
      loading: false,
      gongHuoDWOptions: [],
      queryString: '',
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      query: {
        gongHuoDWID: '',
        gongHuoDWMC: '',
      },
    };
  },
  // setup(_, { expose }) {
  //   const selectRef = ref(null);
  //   function focus() {
  //     console.log('11111');
  //     return unref(selectRef)?.focus();
  //   }
  //   expose({ focus });
  // },
  async created() {},
  methods: {
    focus() {
      this.$refs.selectRef.focus();
    },
    handleFocus() {},
    async loadMore({ page, pageSize, query }, config) {
      try {
        const params = {
          likeQuery: query.search,
          pageIndex: page,
          pageSize: 999,
        };
        const [items, total] = await Promise.all([
          GetGongHuoDWList(params, config),
          !this.total && GetGongHuoDWCount(params, config),
        ]);
        this.gongHuoDWOptions = this.gongHuoDWOptions.concat(items);
        this.gongHuoDWOptions.forEach((item) => {
          item.gongHuoDWID = item.danWeiID;
          item.gongHuoDWMC = item.danWeiMC;
        });
        this.total = total;
        return {
          items: items,
          total: total ? total : items.length,
        };
      } catch (e) {
        logger.error(e);
        return { items: [], total: 0 };
      }
    },
    // handleRemoteMethod: debounce(async function (queryString) {
    //   this.pageIndex = 1;
    //   this.queryString = queryString;
    //   this.gongHuoDWOptions = [];
    //   this.total = 10;
    //   await this.loadMore();
    // }, 400),
    handleSelectChange(val) {
      if (val) {
        let data = this.gongHuoDWOptions.find(
          (item) => item.gongHuoDWID === val,
        );
        this.query.gongHuoDWMC = data ? data.gongHuoDWMC : '';
      } else this.query.gongHuoDWMC = '';
      this.$emit('update:value', this.query.gongHuoDWID);
      this.$emit('select-change', this.query);
    },
  },
  components: {
    'md-select-scroll': MdSelectScroll,
  },
};
</script>
<style scoped></style>
