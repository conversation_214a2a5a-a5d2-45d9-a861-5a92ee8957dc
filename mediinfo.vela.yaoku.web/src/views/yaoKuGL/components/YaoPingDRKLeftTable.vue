<template>
  <div :class="prefixClass('table-height')">
    <md-table
      :columns="columns"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="loading"
      :data="data"
      :border="false"
      :stripe="false"
      height="100%"
      highlight-current-row
      :header-cell-class-name="
        () => prefixClass('cell-border cell-height-head')
      "
      :cell-class-name="
        () => prefixClass('cell-bg cell-border cell-height-body')
      "
      @current-change="handleTableCurrentChange"
      ref="table"
      @sort-change="handleSortChange"
      :level="level"
      :controlLevel="controlLevel"
      :customLevels="customLevels"
      :control-loading="controlLoading"
      :controlColumnLayout="controlColumnLayout"
      :controlExtraColumns="controlExtraColumns"
      @getNewColumn="getNewColumn"
      @recovery-column="recoveryColumn"
      @control-cancel="controlCancel"
      @level-change="levelChange"
    >
      <template #keyParams="{ row, $index }">
        <div style="display: flex; align-items: center; height: 20px">
          <div :class="prefixClass('table-item-inline')">
            {{ row[keyParams] }}
            <div v-if="row.jiYongBZ" :class="prefixClass('jiyongbz')">急</div>
          </div>
          <md-button
            v-if="zuoFeiButton"
            :class="prefixClass('table-buttons')"
            type="text"
            :icon="prefixClass('icon-shanchu')"
            @click.stop="handleDelete(row, $index)"
          >
            作废
          </md-button>
        </div>
      </template>
    </md-table>
    <div :class="prefixClass('item-inline')">
      <div :class="prefixClass('total-show')">共{{ total }}条</div>
      <md-pagination
        :page-size="pageData.pageSize"
        simple
        layout="prev, jumper, total, next"
        :total="total"
        @current-change="handleCurrentChange"
        :current-page.sync="pageData.pageIndex"
      >
      </md-pagination>
    </div>
  </div>
</template>

<script>
import { MdMessageBox } from '@mdfe/medi-ui';
import { debounce } from 'lodash';
import columnMixin from '@/components/mixin/columnMixin';
import tableData from '@/views/yaoKuGL/yaoPinCK/js/tableData.js';

/**
 * keyParams
 */
export default {
  name: 'yaopingrk-left-table',
  props: {
    /**
     * 绑定编辑按钮的列名，传入列的字段名，
     * 同时columns中  定义
     * {
     *  slot: 'keyParams',
     *  label: '入库单号',
     *  minWidth: 120,
     *  showOverflowTooltip: false
     * },
     */
    keyParams: {
      type: String,
      default: 'id',
    },
    offsetHeight: {
      type: Number,
      default: 280,
    },
    getListData: {
      type: Function,
      default: async (pageIndex, PageSize) => {
        return {
          items: [],
          total: 0,
        };
      },
    },
    zuoFeiButton: {
      type: Boolean,
      default: true,
    },
  },
  mixins: [columnMixin],
  data() {
    return {
      columns: tableData.anRuKuDanColumns,
      offsetWidth: 0,
      loading: false,
      maxHeight: window.innerHeight,
      pageData: {
        pageSize: 0,
        pageIndex: 1,
      },
      total: 0,
      data: [],
      currentRow: null,
      resizeEvent: null,
      sortField: '',
      sortDir: '',
    };
  },
  async mounted() {
    await this.getColumnInit();
    this.$nextTick(() => {
      setTimeout(() => {
        this.computedPageSize();
      }, 300);
    });
    this.resizeEvent = debounce(() => {
      this.computedPageSize();
    }, 100);
    window.addEventListener('resize', this.resizeEvent);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizeEvent);
  },
  methods: {
    handleSortChange({ column, prop, order }) {
      this.sortField = prop;
      if (order === 'ascending') {
        this.sortDir = 'asc';
      } else if (order === 'descending') {
        this.sortDir = 'desc';
      } else {
        this.sortDir = order;
      }
      this.search();
    },
    //页面数据条数计算，没有纵向滚动条
    computedPageSize() {
      //行高32px, 表头高度24px， 表格高度this.$refs.table.$el.clientHeight
      let pageSize = Math.floor(
        (this.$refs.table.$el.clientHeight - 24) / 32.5,
      );
      if (pageSize !== this.pageData.pageSize && pageSize > 0) {
        this.pageData.pageSize = pageSize;
        this.search();
      }
    },
    async search() {
      this.pageData.pageIndex = 1;
      await this.getData();
    },
    async getData() {
      try {
        this.loading = true;
        const { items, total } = await this.getListData(
          this.pageData.pageIndex,
          this.pageData.pageSize,
          {
            sortField: this.sortField,
            sortDir: this.sortDir,
          },
        );
        this.data = items;
        this.total = total;
        if (this.data.length > 0) this.$refs.table.setCurrentRow(this.data[0]);
      } catch (e) {
        // console.log(e);
      } finally {
        this.loading = false;
      }
    },
    handleTableCurrentChange(currentRow) {
      this.currentRow = currentRow;
    },
    handleCurrentChange(val) {
      this.pageData.pageIndex = val;
      this.getData();
    },
    handleDelete(row, $index) {
      MdMessageBox.confirm(
        `该操作将永久删除单号为 '${row[this.keyParams]}' 的数据，是否继续？`,
        '操作提醒！',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      ).then(() => {
        this.$emit('delete-row', row);
      });
    },
    getTableComponent() {
      return this.$refs.table;
    },
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-item-inline {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.#{$md-prefix}-total-show {
  height: 17px;
  color: #666666;
  font-size: 12px;
  line-height: 17px;
}

::v-deep .#{$md-prefix}-cell-height-head {
  //height: 24px;

  .cell {
    //height: 16px;
    color: #666666;
    font-size: 12px;
    //line-height: 16px !important;
  }
}

::v-deep .#{$md-prefix}-cell-height-body {
  height: 32px;
  cursor: pointer;

  .cell {
    color: #222;
    height: 20px;
    font-size: var(--md-font-2);
    line-height: 20px;
  }
}

::v-deep .#{$md-prefix}-cell-bg {
  background: #f5f5f5;
}

// ::v-deep .#{$md-prefix}-cell-border {
//   padding: 0 !important;
//   border-bottom: 2px solid #fff !important;
// }

::v-deep .#{$md-prefix}-base-table:before {
  display: none;
}

.#{$md-prefix}-table-height {
  min-height: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

::v-deep .#{$md-prefix}-base-table {
  display: flex;
  flex-direction: column;

  .#{$md-prefix}-base-table__body-wrapper {
    flex: 1;
  }
}

.#{$md-prefix}-chudan {
  background-image: linear-gradient(180deg, #cfd8e7 0%, #a1afca 100%);
}

.#{$md-prefix}-weichudan {
  background-image: linear-gradient(180deg, #cfd8e7 0%, #a1afca 100%);
}

.#{$md-prefix}-table-item-inline {
  display: flex;
  align-items: center;
}
.#{$md-prefix}-chonghongBZ {
  margin-left: 4px;
  width: 16px;
  height: 16px;
  background-color: #f12933;
  border-radius: 8px;
  color: #ffffff;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
}
.#{$md-prefix}-item-inline {
  display: flex;
  align-items: center;
}
.#{$md-prefix}-jiyongbz {
  margin-left: 4px;
  width: 16px;
  height: 16px;
  background-color: #ff9900;
  border-radius: 8px;
  color: #ffffff;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
}
.#{$md-prefix}-table-buttons {
  display: none;
  position: absolute;
  right: 0;
  z-index: 99;
  padding-right: 8px;
  background: #e2efff !important;
}
::v-deep .#{$md-prefix}-base-table__row:hover {
  .#{$md-prefix}-table-buttons {
    display: inline;
  }
}
::v-deep .#{$md-prefix}-base-table__row {
  > td {
    position: static;
  }
  .cell {
    position: static;
  }
}
</style>
