<template>
  <div class="biz-list">
    <ul class="biz-list__ul" v-show="dataList.length > 0">
      <li
        v-for="item in dataList"
        :key="item[idKey]"
        :class="{ isActive: currentIndex === item[idKey] }"
        class="biz-list__li"
        @click="handleClick(item)"
      >
        <i class="iconfont icondanwei" />
        <span class="biz-list-title">{{ item[titleKey] }}</span>
      </li>
    </ul>
    <div class="biz-list-pagination" v-show="dataList.length > 0">
      <div class="total-show">共{{ total }}条</div>
      <md-pagination
        v-bind="$attrs"
        v-on="$listeners"
        :total="total"
        layout="total, prev, pager,next, jumper"
      >
      </md-pagination>
      <!-- <md-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        /> -->
    </div>
    <div class="biz-list__empty" v-show="dataList.length === 0">
      <img src="@/assets/images/zanWuSJ.svg" alt="" />
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import { MdPagination } from '@mdfe/medi-ui';

export default {
  name: 'biz-list',
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
    total: {
      type: [Number, String],
      defalut: 0,
    },
    idKey: {
      type: String,
      default: 'id',
    },
    titleKey: {
      type: String,
      default: 'title',
    },
  },
  data() {
    return {
      currentIndex: null,
    };
  },
  methods: {
    handleClick(item) {
      const idKey = this.idKey;
      this.currentIndex = item[idKey];
      this.$emit('rowClick', item);
    },
    setCurrentSelect(id) {
      this.currentIndex = id;
    },
  },
  components: {
    'md-pagination': MdPagination,
  },
};
</script>

<style lang="scss" scoped>
.biz-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  &-title {
    font-size: var(--md-font-2);
  }
  &__ul {
    flex: 1;
    min-height: 0;
    overflow: auto;
  }
  &__li {
    box-sizing: border-box;
    height: 32px;
    margin-bottom: 2px;
    padding: 0 4px;
    line-height: 32px;
    font-size: 14px;
    color: #222;
    background: #f5f5f5;
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    i {
      margin-right: 4px;
      background-image: linear-gradient(
        180deg,
        rgba(207, 216, 231, 1) 0%,
        rgba(161, 175, 202, 1) 100%
      );
      -webkit-background-clip: text; //主要用于剪掉文字以外的区域。
      -webkit-text-fill-color: transparent; //设置文本的填充颜色。
    }
    span {
      display: inline-block;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover {
      background-color: rgb(var(--md-color-2));
      i {
        color: #fff;
        background-image: linear-gradient(
          180deg,
          rgb(var(--md-color-2)),
          rgb(var(--md-color-6)) 50%
        );
      }
    }
    &.isActive {
      background-color: rgb(var(--md-color-2));
      i {
        color: #fff;
        background-image: linear-gradient(
          180deg,
          rgb(var(--md-color-2)),
          rgb(var(--md-color-6)) 50%
        );
      }
    }
  }
  &-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .total-show {
      height: 17px;
      color: #666666;
      font-size: 12px;
      line-height: 17px;
    }
    ::v-deep .#{$md-prefix}-pagination__total {
      display: none;
    }
  }
  &__empty {
    display: flex;
    flex: 1;
    min-height: 0;
    flex-direction: column;
    align-items: center;
    img {
      width: 100px;
      height: 100px;
      margin-top: 80px;
    }
    p {
      color: #aaa;
      font-size: 18px;
      margin-top: 12px;
    }
  }
}
</style>
