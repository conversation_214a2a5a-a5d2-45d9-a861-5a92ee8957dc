<template>
  <div :class="prefixClass('CaiJiGL-content')">
    <div :class="prefixClass('procurement-left')">
      <div :class="prefixClass('procurement-top')">
        <div class="flex-sp" :class="prefixClass('margB')" style="width: 100%">
          <md-select
            v-model="query.jiBieBZ"
            placeholder="请选择"
            @change="getLeftSelectList"
            style="width: 100%"
          >
            <md-option
              v-for="item in jiBieList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
        </div>
        <md-select
          v-model="query.leftJiCaiXMID"
          placeholder="请选择"
          style="width: 100%"
          @change="getLeftXiangMu"
        >
          <md-option
            v-for="item in jiCaiSelect"
            :key="item.id"
            :label="item.jiCaiXMMC"
            :value="item.id"
          >
          </md-option>
        </md-select>
      </div>
      <div class="asideBox">
        <md-scrollbar
          v-if="typeList.length > 0"
          :native="false"
          class="Aside-scrollbar"
        >
          <div
            :class="['type-list', typeActive == item.id ? 'active' : '']"
            v-for="item in typeList"
            :key="item.id"
            @click="handleTypeClick(item)"
          >
            <span class="name">{{ item.xiangMuMC }}</span>
            <!-- <span class="edit">
              <md-button
                type="primary"
                :icon="prefixClass('icon-bianji')"
                noneBg
                @click="handleEditType(item)"
              ></md-button
            ></span> -->
          </div>
        </md-scrollbar>
        <md-empty v-else class="Aside-scrollbar empty"></md-empty>
      </div>
    </div>
    <div :class="prefixClass('procurement-right')">
      <div :class="prefixClass('procurement-right-detail')">
        <md-title type="grace" :label="rightDetail.xiangMuMC"></md-title>
        <div class="info">
          <span class="labelName"
            >级别：<span>{{ rightDetail.jiBie }}</span></span
          >
          <span class="labelName"
            >开始日期：<span>{{ rightDetail.kaiShiRQ }}</span></span
          >
          <span class="labelName"
            >结束日期：<span>{{ rightDetail.jieShuRQ }}</span></span
          >
          <span class="labelName"
            >项目总天数：<span>{{ rightDetail.xiangMuZTS }}</span></span
          >
          <span class="labelName"
            >进行天数：<span>{{ rightDetail.jingXingTS }}</span></span
          >
          <span class="labelName"
            >当前应完成进度：<span>{{ rightDetail.wanChengDu }}%</span></span
          >
        </div>
      </div>
      <div :class="prefixClass('procurement-right-top')">
        <div class="searchDate">
          <div>消耗量统计日期</div>
          <md-date-picker-range-pro
            v-model="xiaoHaoLTJRQ"
            style="width: 320px; margin-left: 8px"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
            :clearable="false"
          >
          </md-date-picker-range-pro>
        </div>
        <md-button type="primary" plain @click="handleYuLan(1)">
          导出</md-button
        >
      </div>
      <div :class="prefixClass('CaiJiGL-table')">
        <div style="overflow: hidden; width: 100%">
          <md-table-pro
            :columns="columns"
            :onFetch="handleFetch"
            height="100%"
            highlight-current-row
            :showPagination="false"
            :class="prefixClass('table')"
            @row-click="handleBottomTableData"
            ref="tablePro"
          >
          </md-table-pro>
        </div>
      </div>
      <div :class="prefixClass('CaiJiGL-table')" class="flex-col">
        <div class="flex-sp">
          <md-title label="科室消耗量"></md-title>
          <md-button type="primary" plain @click="handleYuLan('')">
            导出</md-button
          >
        </div>
        <div :class="prefixClass('CaiJiGL-table')">
          <md-table
            :columns="columnsBottom"
            :data="bottomData"
            height="100%"
            :stripe="false"
            :class="prefixClass('table')"
            ref="table"
            :span-method="objectSpanMethod"
          >
          </md-table>
        </div>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="daYinLXID"
      :fileName="'集采药品跟踪导出'"
      :title="'集采药品跟踪预览'"
    />
  </div>
</template>
<script>
import {
  GetJiCaiJHDCount,
  GetJiCaiJHDList,
  ZuoFeiJiCaiJHD,
  GetJiCaiXMList,
  GetJiCaiJHDXHXQ,
  getJiCaiJHDListByJCXMID,
  GetJiCaiJHDKSXHXQ,
} from '@/service/yaoPinYK/JiCaiJHD';
import DaYinDialog from '@/components/DaYinDialog.vue';
import commonData from '@/system/utils/commonData.js';
import { MdFrameset, MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import dayjs from 'dayjs';
import { logger } from '@/service/log';
export default {
  name: 'CaiJiYPGZ',
  data() {
    return {
      params: {},
      daYinLXID: '',
      biaoZhunGGID: '',
      xiaoHaoLTJRQ: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().endOf('month').format('YYYY-MM-DD'),
      ],
      jiCaiSelect: [],
      typeActive: 1,
      typeList: [],
      query: {
        stateList: [],
        xiangMuMC: '',
        jiBieBZ: 0,
        leftJiCaiXMID: '',
      },
      rightDetail: {
        xiangMuMC: '',
        jieShuRQ: '',
        kaiShiRQ: '',
        jiBie: '',
      },
      jiBieList: [
        { label: '全部级别', value: 0 },
        { label: '省级', value: 1 },
        { label: '国家级', value: 2 },
      ],
      columns: [
        {
          type: 'index',
          label: '序号',
          width: 60,
        },
        {
          prop: 'yaoPinLXDM',
          label: '',
          width: 32,
          formatter: (row, column, cellValue, index) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yaoPinMC',
          label: '药品名称',
          minWidth: 200,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 120,
        },
        {
          prop: 'biaoZhunGGXHL',
          label: '标准规格消耗量',
          minWidth: 120,
          align: 'right',
        },
        {
          prop: 'jingPinBZGGXHL',
          label: '竞品标准规格消耗量',
          align: 'right',
          minWidth: 160,
        },
        {
          prop: 'biaoZhunGGRWL',
          label: '标准规格任务量',
          align: 'right',
          minWidth: 120,
        },
        {
          label: '当前已完成进度',
          align: 'right',
          prop: 'wanChengDu',
          formatter: (row, column, cellValue, index) => {
            return cellValue ? cellValue : '0%';
          },
          minWidth: 120,
        },
        {
          prop: 'zongCaiGL',
          label: '总采购量',
          align: 'right',
          minWidth: 80,
        },
        {
          prop: 'jiCaiCGL',
          label: '集采采购量',
          align: 'right',
          minWidth: 100,
        },
        {
          prop: 'jingPinCGL',
          label: '竞品采购量',
          align: 'right',
          minWidth: 100,
        },
        {
          prop: 'jingCaiZB',
          label: '集采占比',
          align: 'right',
          minWidth: 80,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? cellValue + '%' : '0%';
          },
        },
      ],
      bottomData: [],
      columnsBottom: [
        {
          type: 'index',
          label: '序号',
          width: 60,
        },
        {
          prop: 'keShiMC',
          label: '科室',
          width: 160,
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称',
        },
        {
          prop: 'biaoZhunGGXHL',
          label: '标准规格消耗量',
          width: 230,
          align: 'right',
        },
      ],
    };
  },
  mounted() {
    this.getLeftSelectList();
    this.getLeftXiangMu();
  },
  methods: {
    async getLeftSelectList() {
      const { jiBieBZ } = this.query;
      this.jiCaiSelect = await GetJiCaiXMList({
        likeQuery: '',
        jiBieBZ: jiBieBZ == 0 ? '' : jiBieBZ,
      });
      this.getLeftXiangMu();
    },
    // 获取左侧列表 默认选择第一个
    async getLeftXiangMu() {
      try {
        const { leftJiCaiXMID, jiBieBZ } = this.query;
        const data = await getJiCaiJHDListByJCXMID({
          jiCaiXMID: leftJiCaiXMID,
          pageIndex: 1,
          pageSize: 9999,
          jiBieBZ: jiBieBZ,
        });
        this.typeList = data.jiCaiJHDList;
        this.handleTypeClick(
          data.jiCaiJHDList.length > 0 ? data.jiCaiJHDList[0] : '',
        );
      } catch (e) {
        console.error(e);
      }
    },
    async handleFetch({ page, pageSize }, config) {
      try {
        this.bottomData = [];
        this.rightDetail = {};
        if (!this.typeActive) {
          return { items: [], total: 0 };
        }
        const params = {
          xiangMuID: this.typeActive,
          kaiShiSJ: this.xiaoHaoLTJRQ[0],
          jieShuSJ: this.xiaoHaoLTJRQ[1],
        };
        this.bottomData = [];
        const items = await GetJiCaiJHDXHXQ(params);
        this.rightDetail = items;
        this.rightDetail.kaiShiRQ = dayjs(items.kaiShiRQ).format('YYYY-MM-DD');
        this.rightDetail.jieShuRQ = dayjs(items.jieShuRQ).format('YYYY-MM-DD');
        if (items && items.jiCaiBZGGXHList.length > 0) {
          this.$nextTick(() => {
            this.$refs.tablePro
              .getComp('table')
              .setCurrentRow(items.jiCaiBZGGXHList[0]);
            this.handleBottomTableData(items.jiCaiBZGGXHList[0]);
          });
        }
        return {
          items: items.jiCaiBZGGXHList,
          total: items.jiCaiBZGGXHList.length || 0,
        };
      } catch (error) {
        logger.error(error);
      }
    },
    //调用table刷新
    handleSearch() {
      if (this.$refs.tablePro) this.$refs.tablePro.search();
    },
    // 底部表格数据
    async handleBottomTableData(row) {
      try {
        this.biaoZhunGGID = row.biaoZhunGGID;
        this.bottomData = await GetJiCaiJHDKSXHXQ({
          biaoZhunGGID: row.biaoZhunGGID,
          kaiShiSJ: this.xiaoHaoLTJRQ[0],
          jieShuSJ: this.xiaoHaoLTJRQ[1],
        });
      } catch (e) {
        console.error(e);
      }
    },
    //切换级别
    handleTypeClick(item) {
      this.typeActive = item ? item.id : '';
      this.handleSearch();
    },
    //预览
    async handleYuLan(type) {
      this.daYinLXID = type ? 'YKXT028' : 'YKXT029';
      const params = {
        kaiShiSJ: this.xiaoHaoLTJRQ[0],
        jieShuSJ: this.xiaoHaoLTJRQ[1],
      };
      if (type) {
        params.xiangMuID = this.typeActive;
      } else {
        params.biaoZhunGGID = this.biaoZhunGGID;
      }
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    // 编辑级别
    handleEditType(item) {},
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const spanColumns = ['keShiMC'];
      const prop = column.property;
      if (spanColumns.includes(prop) || columnIndex == 0) {
        const currentRow = this.bottomData[rowIndex];
        const prevRow = this.bottomData[rowIndex - 1];

        if (
          prevRow &&
          spanColumns.every((key) => prevRow[key] === currentRow[key])
        ) {
          return [0, 0]; // 合并单元格
        }

        let rowSpan = 1;
        for (let i = rowIndex + 1; i < this.bottomData.length; i++) {
          if (
            spanColumns.every(
              (key) => this.bottomData[i][key] === currentRow[key],
            )
          ) {
            rowSpan++;
          } else {
            break;
          }
        }

        return [rowSpan, 1];
      }
    },
  },
  components: {
    'md-table-pro': MdTablePro,
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$namespace}-CaiJiGLdh {
  float: left;
  margin-right: 5px;
  cursor: pointer;
  color: getCssVar('color-6');

  &:hover {
    color: getCssVar('color-6');
    text-decoration: underline;
    line-height: 20px;
  }
}

.#{$md-prefix}-CaiJiGL-content {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  background: #f0f2f5;

  .#{$md-prefix}-procurement-left {
    width: 240px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 8px;
    box-sizing: border-box;
    background-color: #fff;
    .asideBox {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    .Aside-scrollbar {
      flex: 1;
      min-height: 0;
      margin-top: getCssVar('spacing-3');

      &.empty {
        justify-content: center;
      }

      .type-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin-bottom: getCssVar('spacing-3');
        padding: 0 getCssVar('spacing-3');
        cursor: pointer;

        .name {
          flex: 1;
        }

        .edit {
          display: none;
        }

        &.active {
          background-color: getCssVar('color-2');
        }

        &:hover {
          background-color: getCssVar('color-1');

          .edit {
            display: block;
          }
        }
      }
    }
  }
  .#{$md-prefix}-procurement-right {
    flex: 1;
    overflow: hidden;
    margin: 8px 8px 0 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .flex-sp {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &-top {
      display: flex;
      justify-content: space-between;
      padding: 8px 8px 0 8px;
      box-sizing: border-box;
      background-color: #fff;

      .searchDate {
        flex: 1;
        display: flex;
        align-items: center;
      }
    }
    &-detail {
      width: 100%;
      min-height: 38px;
      background: linear-gradient(270deg, #f2f6fc 0%, #e9f4fe 100%);
      padding: 8px;
      /* padding-bottom: 0; */
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .flex-sp {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .info {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-left: 12px;
        span {
          color: #222;
          /* margin-right: 12px; */
        }
        .labelName {
          color: #666666;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
    .flex-col {
      display: flex;
      flex-direction: column;
    }
  }
  .#{$md-prefix}-procurement-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    .flex-sp {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .#{$md-prefix}-margB {
    margin-bottom: getCssVar('spacing-3');
  }

  .#{$md-prefix}-procurement-date {
    margin-right: 12px;
    /* width: 220px; */
  }

  .#{$md-prefix}-CaiJiGL-table {
    background-color: #fff;
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 8px 8px 0 8px;
    box-sizing: border-box;
    margin-bottom: 8px;
    /* padding-bottom: 8px; */

    .#{$md-prefix}-table {
      flex: 1;
      overflow: hidden;
    }
  }
}

.#{$md-prefix}-CaiJiGL-state {
  width: 4em;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 2px;
  margin: auto;

  &.#{$md-prefix}-yellow {
    background-color: rgba(255, 238, 219, 1);
    color: #ff8600;
  }

  &.#{$md-prefix}-green {
    background-color: rgba(228, 255, 216, 1);
    color: #4ac110;
  }

  &.#{$md-prefix}-red {
    background-color: rgba(255, 235, 235, 1);
    color: #f12933;
  }
}

::v-deep .#{$md-prefix}-button--primary.is-noneBg.#{$md-prefix}-button--small {
  padding: getCssVar('spacing-1');
}
</style>
