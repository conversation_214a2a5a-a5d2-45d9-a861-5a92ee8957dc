<template>
  <div :class="prefixClass('yaoPinBFWZ')">
    <div v-loading="leftloading" :class="prefixClass('yaoPinBFWZ-left')">
      <div :class="prefixClass('yaoPinBFWZ-left-jianSuo')">
        <md-search-input
          v-model="baiFangWZ"
          style="width: 120px"
          placeholder="摆药位置"
          @search="handleBaiFangWZ"
        ></md-search-input>
        <md-button
          :disabled="yiXuanObj.baiFangWZID == '-1'"
          type="primary"
          :icon="prefixClass('icon-xinzeng')"
          noneBg
          @click="handleAdd"
          >摆放位置</md-button
        >
      </div>
      <md-scrollbar
        :native="false"
        style="height: 100%"
        class="kuweizd-tree-scrollbar"
        wrap-class="kuweizd-el-scrollbar__wrap"
        view-class="kuweizd-el-scrollbar__view"
      >
        <div :class="prefixClass('yaoPinBFWZ-left-tree')">
          <md-tree
            :data="baiFangWZData"
            highlight-current
            node-key="baiFangWZID"
            :expand-on-click-node="false"
            :default-expanded-keys="expandKeys"
            ref="tree"
            :props="treeProps"
          >
            <template #default="{ node }">
              <div class="treeItem" @click="handleTreeCurrent(node.data)">
                <div class="treeItem-label">
                  {{ node.data.baiFangWZMC }}
                  <span
                    v-if="
                      node.data.baiFangWZID != '0' &&
                      node.data.baiFangWZID != '-1'
                    "
                  >
                    ({{ node.data.shuLiang }})
                  </span>
                </div>
                <div
                  v-if="
                    node.data.baiFangWZID != '0' &&
                    node.data.baiFangWZID != '-1'
                  "
                  class="treeItem-icon"
                >
                  <md-button
                    type="primary"
                    :icon="prefixClass('icon-bianji')"
                    noneBg
                    @click.stop="handleBianJi(node.data)"
                  ></md-button>
                </div>
              </div>
            </template>
          </md-tree>
        </div>
      </md-scrollbar>
    </div>
    <div :class="prefixClass('yaoPinBFWZ-right')">
      <div :class="prefixClass('yaoPinBFWZ-right-top')">
        <div>
          <md-select
            v-model="zhangBuLB"
            style="margin-right: 8px; width: 180px"
            @change="handleJianSuo"
          >
            <md-option
              v-for="item in zhangBuLBData"
              :key="item.zhangBuLBID"
              :label="item.zhangBuLBMC"
              :value="item.zhangBuLBID"
            ></md-option>
          </md-select>
          <md-search-input
            style="margin-right: 8px; width: 180px"
            v-model="yaoPinWZ"
            placeholder="摆药位置"
            @search="handleJianSuo"
          ></md-search-input>

          <biz-yaopindw
            v-model="yaoPinMC"
            placeholder="药品名称"
            style="width: 305px; margin-right: 8px"
            @change="handleSearch"
          >
          </biz-yaopindw>
          <md-checkbox
            label="仅展示入库药品"
            v-model="ruKuYPBZ"
            :false-label="0"
            :true-label="1"
            @change="handleJianSuo"
          />
        </div>
        <div>
          <md-button
            type="primary"
            :icon="prefixClass('icon-dayinji')"
            noneBg
            @click="handleKuWeiMDY"
            >库位码打印</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-daochu')"
            noneBg
            @click="handleYuLan"
            >预览</md-button
          >
          <md-button
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            @click="handleJianSuo"
            >刷新</md-button
          >
          <md-button
            :disabled="
              yiXuanObj.baiFangWZID == '0' || yiXuanObj.baiFangWZID == '-1'
            "
            type="primary"
            :icon="prefixClass('icon-xinzeng')"
            noneBg
            @click="addYaoPin"
            >药品</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('yaoPinBFWZ-right-table')">
        <md-table-pro
          ref="table"
          :resize="false"
          :autoLoad="false"
          loading="true"
          height="100%"
          :columns="columns"
          :onFetch="handleFetch"
          @sort-change="handleSortChange"
        >
          <template v-slot:yaoPinBFWZ="{ row }">
            <md-overflow-container style="width: 100%">
              <md-overflow-item
                v-for="(item, index) in row.baiFangWZList"
                :key="item.yaoPinBFWZID"
                :index="index"
                :data="item"
                style="margin-right: 4px"
              >
                <span
                  style="background: #f5f5f5; border-radius: 2px; padding: 4px"
                  ><span style="margin-right: 8px">{{ item.baiFangWZ }}</span>
                  <!-- <md-icon style="cursor: pointer"
                    name="cha"
                    @click="handleDleBFWZ(item, row)"></md-icon> -->
                </span>
              </md-overflow-item>
            </md-overflow-container>
          </template>
          <template v-slot:operate="{ row }">
            <md-button type="primary" noneBg @click="handleEditRow(row)"
              >编辑</md-button
            >
          </template>
        </md-table-pro>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="dayinOBj.id"
      :fileName="dayinOBj.fileName"
      :title="dayinOBj.title"
    />
    <baiFangWZDialog
      ref="baiFangWZDialog"
      @init="getBaiFangWZ"
    ></baiFangWZDialog>
    <tianJiaYPDialog
      :zhangBuLBData="zhangBuLBData"
      :weiZhiID="yiXuanObj"
      ref="tianJiaYPDialog"
      @init="handleJianSuo"
    ></tianJiaYPDialog>
    <editDialog ref="editDialog" @handleJianSuo="handleJianSuo" />
  </div>
</template>
<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { logger } from '@/service/log';
import {
  DeleteYaoPinBaiFangWZByIDs,
  GetBaiFangWZTree,
  GetYaoPinBaiFangWZCount,
  GetYaoPinBaiFangWZList,
} from '@/service/yaoPin/yaoPinZD';
import { GetZhangBuLBSelectList } from '@/service/yaoPinYK/yaoPinBFWZ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import {
  MdOverflowContainer,
  MdOverflowItem,
} from '@mdfe/material.overflow-layout';
import { MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import baiFangWZDialog from './components/baiFangWZDialog.vue';
import editDialog from './components/editDialog.vue';
import tianJiaYPDialog from './components/tianJiaYPDialog.vue';

export default {
  name: 'baiFngWZ',
  data() {
    return {
      expandKeys: [],
      params: {},
      leftloading: false,
      baiFangWZ: '',
      baiFangWZData: [],
      treeProps: { label: 'baiFangWZMC', children: 'children' },
      yiXuanObj: {},
      // ---------------
      yaoPinWZ: '',
      yaoPinMC: '',
      jiaGeID: '',
      zhangBuLB: '',
      ruKuYPBZ: 0,
      zhangBuLBData: [],
      yaoPinMCZS: '',
      columns: [
        {
          prop: 'yuanNeiBM',
          label: '院内编码',
          width: 145,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          width: 145,
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称规格',
          minWidth: 215,
        },
        {
          prop: 'chanDiMC',
          label: '产地',
          width: 200,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 60,
        },

        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 60,
          align: 'right',
        },
        {
          prop: 'zhangBuLBMC',
          label: '帐薄类别',
          width: 90,
        },
        {
          slot: 'yaoPinBFWZ',
          prop: 'yaoPinBFWZ',
          label: '药品摆放位置',
          minWidth: 310,
          sortable: 'custom',
        },
        {
          prop: 'xiuGaiSJ',
          label: '库位修改时间',
          formatter: (row, column, cellValue, index) => {
            return cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm')
              : cellValue;
          },
          width: 190,
          sortable: 'custom',
        },
        {
          prop: 'caiGouBZ',
          label: '采购包装',
          width: 90,
        },
        {
          prop: 'shunXuHao',
          label: '顺序号',
          width: 90,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 60,
          fixed: 'right',
        },
      ],
      sortType: 0,
      sortDir: 0,
      // 打印参数
      dayinOBj: {
        id: 'YKXT021',
        fileName: '库位字典单',
        title: '库位字典打印预览',
      },
    };
  },
  mounted() {
    this.getZhangBuLB();
    this.getBaiFangWZ('');
  },
  methods: {
    //升降序
    handleSortChange({ column, prop, order }) {
      if (!order) return;
      this.sortType = prop == 'yaoPinBFWZ' ? 1 : '2';
      this.sortDir = order == 'ascending' ? 1 : 2;
      this.handleJianSuo();
    },
    //编辑
    handleEditRow(row) {
      this.$refs.editDialog.showDialog(row);
    },
    async getZhangBuLB() {
      let res = await GetZhangBuLBSelectList();
      this.zhangBuLBData = [
        { zhangBuLBMC: '全部账簿类别', zhangBuLBID: '' },
        ...res,
      ];
    },
    handleBaiFangWZ(data) {
      this.baiFangWZ = data;
      this.getBaiFangWZ('');
    },

    async getBaiFangWZ(id) {
      try {
        this.leftloading = true;
        this.baiFangWZData = await GetBaiFangWZTree({
          baiFangWZMC: this.baiFangWZ,
          weiSheZBZ: 1,
        });
        this.yiXuanObj = id
          ? { baiFangWZID: id }
          : this.baiFangWZData[0]?.children[0];
        this.expandKeys = [this.yiXuanObj.baiFangWZID];
        if (this.yiXuanObj.baiFangWZID) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.yiXuanObj.baiFangWZID);
            if (id) {
              this.yiXuanObj = this.$refs.tree.getCurrentNode();
            }
            this.handleJianSuo();
          });
          let time = setTimeout(() => {
            let node = document.querySelector('.is-current');
            if (node) {
              this.$nextTick(() => {
                node.scrollIntoView({ block: 'center' });
              });
            }
            clearTimeout(time);
          }, 300);
        }
      } catch (error) {
        logger.error(error);
      } finally {
        this.leftloading = false;
      }
    },
    handleTreeCurrent(node) {
      this.yiXuanObj = node;
      this.handleJianSuo();
    },
    handleBianJi(data) {
      this.$refs.baiFangWZDialog.showDialog(data);
    },
    handleAdd() {
      this.$refs.baiFangWZDialog.showDialog({
        id: '',
        yiXuanObj: this.yiXuanObj,
      });
    },
    /**
     * 库位码打印
     * **/
    async handleKuWeiMDY() {
      // 完善入参，需要把对象{yaoPinMC:'**'}里的yaoPinMC提取出来，接口改用jiageID判断，移除yaoPinMC
      // this.params.yaoPinMC = this.yaoPinMC?.yaoPinMC || null;
      this.dayinOBj = {
        id: 'YKXT033',
        fileName: '库位码打印',
        title: '库位码打印',
      };
      this.$refs.daYinDialog.showModal();
    },
    //预览
    handleYuLan() {
      this.dayinOBj = {
        id: 'YKXT021',
        fileName: '库位字典单',
        title: '库位字典打印预览',
      };
      this.$refs.daYinDialog.showModal();
    },
    // ------------------
    // 查询条件变更搜索
    handleSearch(data) {
      if (data) {
        this.yaoPinMC = {
          yaoPinMC: data.yaoPinMC,
          yaoPinGG: data.yaoPinGG,
          kuCunSL: data.kuCunSL,
          chanDiMC: data.chanDiMC,
          baoZhuangDW: data.baoZhuangDW,
          danJia: data.danJia,
          jiaGeID: data.jiaGeID,
        };
        this.yaoPinMCZS = data.yaoPinMC;
        this.jiaGeID = data.jiaGeID;
      } else {
        this.yaoPinMC = '';
        this.yaoPinMCZS = '';
        this.jiaGeID = '';
      }
      this.handleJianSuo();
    },
    async handleFetch({ page, pageSize }) {
      try {
        let params = {
          baiFangWZID: this.yiXuanObj.baiFangWZID,
          // yaoPinMC: this.yaoPinMCZS,
          jiaGeID: this.yaoPinMC ? this.yaoPinMC.jiaGeID : '',
          zhangBuLBID: this.zhangBuLB,
          ruKuYPBZ: this.ruKuYPBZ,
          baiFangWZ: this.yaoPinWZ,
          YaoPinLX: getKuCunGLLX(),
          GuiGeLX: 1, //药库搜大规格
          pageIndex: page,
          pageSize: pageSize,
          sortType: this.sortType,
          sortDir: this.sortDir,
        };
        this.params = params;
        let items = await GetYaoPinBaiFangWZList(params);
        let total = await GetYaoPinBaiFangWZCount(params);
        this.yiXuanObj.shuLiang = total;
        return { items, total };
      } catch (err) {
        logger.error(err);
      }
    },
    // 刷新列表
    handleJianSuo() {
      this.$refs.table.search({ pageSize: 100 });
    },
    // 加药品
    addYaoPin() {
      this.$refs.tianJiaYPDialog.showDialog();
    },
    // 删除排放位置
    handleDleBFWZ(item, row) {
      MdMessageBox.confirm('确定移除该摆放位置！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await DeleteYaoPinBaiFangWZByIDs({ ids: item.yaoPinBFWZID });
        this.$message({
          type: 'success',
          message: '移除成功',
        });
        this.handleJianSuo();
      });
    },
  },
  components: {
    baiFangWZDialog,
    tianJiaYPDialog,
    editDialog,
    MdOverflowContainer,
    MdOverflowItem,
    'biz-yaopindw': BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-yaoPinBFWZ {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;

  &-left {
    padding: 8px 0;
    width: 224px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;

    &-jianSuo {
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
    }

    &-tree {
      min-height: 0;
      flex: 1;

      .treeItem {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 8px;

        .treeItem-icon {
          display: none;
        }

        &:hover {
          .treeItem-icon {
            display: block;
          }
        }
      }
    }
  }

  &-right {
    flex: 1;
    min-width: 0;
    border: 8px solid #f0f2f5;
    border-bottom: 0;
    display: flex;
    flex-direction: column;
    padding: 8px;

    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-table {
      flex: 1;
      min-height: 0;
      margin-top: 8px;
    }
  }
}
</style>
<style lang="scss">
@use '@mdfe/material.overflow-layout/es/index.scss' as *;
</style>

<style>
.kuweizd-tree-scrollbar {
  overflow-x: auto;
  white-space: nowrap;
  width: 100%;
}

.kuweizd-el-scrollbar__view {
  display: flex;
  width: 100%;
}
</style>
