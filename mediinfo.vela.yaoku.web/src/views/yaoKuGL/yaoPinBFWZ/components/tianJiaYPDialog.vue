<template>
  <md-dialog
    title="添加药品"
    v-model="dialogVisible"
    :content-scrollable="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="1200px"
    height="540px"
  >
    <div :class="prefixClass('xuanYongpage')">
      <div class="left">
        <div class="leftHeader">
          <md-title label="选择药品"></md-title>
          <div>
            <md-select
              v-model="zhangBuLB"
              style="width: 160px"
              @change="search"
            >
              <md-option
                v-for="item in zhangBuLBData"
                :key="item.zhangBuLBID"
                :label="item.zhangBuLBMC"
                :value="item.zhangBuLBID"
              ></md-option>
            </md-select>
            <!-- <md-search-input

              v-model="likeQuery"
              placeholder="药品名称"
              @search="search"
            ></md-search-input> -->
            <md-input
              v-model="likeQuery"
              placeholder="药品名称"
              style="margin-left: 8px; width: 170px"
              @keyup.enter.native="search"
            >
              <i
                slot="suffix"
                :class="[
                  prefixClass('input__icon'),
                  prefixClass('icon-search'),
                ]"
                @click="search"
              ></i>
            </md-input>
          </div>
        </div>
        <div class="tableStyle">
          <md-table-pro
            :columns="columns"
            :autoLoad="false"
            height="100%"
            :onFetch="handleFetch"
            :onAfterFetch="afterFetch"
            ref="leftTable"
            :pagination="{ simple: true, layout: 'prev, jumper, total,next' }"
            @select-all="selectAll"
            @select="selectChanged"
          >
          </md-table-pro>
        </div>
      </div>
      <div class="right" style="margin-left: 8px">
        <div class="leftHeader">
          <md-title label="已选"></md-title>
        </div>
        <div class="tableStyle">
          <md-table
            ref="dragTable"
            :columns="columnsRight"
            :data="tableList"
            height="100%"
          >
            <template v-slot:caozuo="{ row, $index }">
              <md-button
                type="danger"
                :icon="prefixClass('icon-shanchu')"
                noneBg
                @click="onDel(row, $index)"
              ></md-button>
            </template>
          </md-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div>
        <md-button type="primary" plain @click="handleCancel">取消</md-button>
        <md-button type="primary" @click="handleBaoCun"> 保存 </md-button>
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { cloneDeep } from 'lodash';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { logger } from '@/service/log';
import {
  GetYaoPinCDJGListForAddYP,
  GetYaoPinCDJGCountForAddYP,
  SaveYaoPinBaiFangWZPL,
} from '@/service/yaoPin/yaoPinZD';

export default {
  name: 'yaoPinTJDialog',

  data() {
    return {
      dialogVisible: false,
      zhangBuLB: '',
      likeQuery: '',
      list: [], //左
      tableList: [], //右
      columns: [
        {
          type: 'selection',
          align: 'left',
          width: 35,
          selectable: (row) => {
            return row.keXuanBZ !== 1;
          },
        },
        {
          prop: 'yaoPinBM',
          label: '商品名',
          minWidth: 90,
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称规格',
          minWidth: 190,
        },
        {
          prop: 'chanDiMC',
          label: '产地',
          width: 160,
        },
      ],
      columnsRight: [
        {
          prop: 'yaoPinBM',
          label: '商品名',
          minWidth: 90,
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称规格',
          minWidth: 190,
        },
        {
          prop: 'chanDiMC',
          label: '产地',
          width: 160,
        },
        {
          type: 'operate',
          label: '',
          width: 40,
          slot: 'caozuo',
        },
      ],
    };
  },
  props: ['zhangBuLBData', 'weiZhiID'],
  methods: {
    search() {
      this.$refs.leftTable.search();
    },
    async showDialog() {
      try {
        this.dialogVisible = true;
        this.tableList = [];
        this.zhangBuLB = '';
        this.likeQuery = '';
        this.$nextTick(() => {
          this.search();
        });
      } catch (error) {
        logger.error(error);
      }
    },
    async handleFetch({ page, pageSize }) {
      const params = {
        yaoPinMC: this.likeQuery,
        zhangBuLBID: this.zhangBuLB,
        baiFangWZID: this.weiZhiID.baiFangWZID,
        pageSize,
        pageIndex: page,
      };
      let [items, total] = await Promise.all([
        GetYaoPinCDJGListForAddYP(params),
        GetYaoPinCDJGCountForAddYP(params),
      ]);
      this.list = items || [];

      return { items: items || [], total };
    },
    // 全选反选
    selectAll(items) {
      if (items.length > 0) {
        items.forEach((item) => {
          const checked = this.tableList.find(
            (r) => r.jiaGeID === item.jiaGeID,
          );
          if (!checked) {
            this.tableList.push(item);
          }
        });
      } else {
        this.list.forEach((item) => {
          const index = this.tableList.findIndex(
            (n) => n.jiaGeID === item.jiaGeID,
          );
          this.tableList.splice(index, 1);
        });
      }
    },
    //单选反选
    selectChanged(selection, row) {
      const checked = this.tableList.find((r) => r.jiaGeID === row.jiaGeID);
      if (checked) {
        const index = this.tableList.findIndex(
          (n) => n.jiaGeID === row.jiaGeID,
        );
        this.tableList.splice(index, 1);
      } else {
        this.tableList.push(row);
      }
    },
    onDel(row, $index) {
      const leftTable = this.$refs.leftTable.getComp('table');
      let zuiXinRow = this.list.find((e) => e.jiaGeID === row.jiaGeID);
      leftTable.toggleRowSelection(zuiXinRow);
      this.tableList.splice($index, 1);
    },
    afterFetch() {
      this.$nextTick(() => {
        if (this.tableList && this.tableList.length > 0) {
          this.tableList.forEach((row) => {
            const rows = this.list.find((item) => row.jiaGeID === item.jiaGeID);
            if (rows && rows.jiaGeID) {
              const leftTable = this.$refs.leftTable.getComp('table');
              leftTable.toggleRowSelection(rows);
            }
          });
        }
      });
    },
    // 保存分组
    async handleBaoCun() {
      try {
        if (this.tableList.length === 0) {
          MdMessage.warning('请选择药品！');
          return;
        }
        let yiXuanList = cloneDeep(this.tableList);
        yiXuanList = yiXuanList.map((e) => {
          return {
            jiaGeID: e.jiaGeID,
            baiFangWZ: this.weiZhiID.baiFangWZMC,
            bangFangWZID: this.weiZhiID.baiFangWZID,
            weiHuFS: '3',
          };
        });
        await SaveYaoPinBaiFangWZPL(yiXuanList);
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        this.dialogVisible = false;
        this.$emit('init');
      } catch (error) {
        logger.error(error);
      }
    },

    //取消
    async handleCancel() {
      this.dialogVisible = false;
    },
  },
  components: {
    MdMessage,
    MdMessageBox,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-xuanYongpage {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 50%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .right {
    flex: 1;
    min-width: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .leftHeader {
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-bottom: none;
    height: 46px;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
  }

  .tableStyle {
    flex: 1;
    min-height: 0;
  }
}
</style>
