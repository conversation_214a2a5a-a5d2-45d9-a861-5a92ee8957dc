<template>
  <md-dialog
    title="编辑"
    size="small"
    v-model="dialogVisible"
    :content-scrollable="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div :class="prefixClass('editdialog')">
      <md-form
        :model="editForm"
        use-status-icon
        :rules="formRules"
        label-width="110px"
        ref="form"
      >
        <md-form-item label="采购包装" prop="caiGouBZ">
          <md-input v-model="editForm.caiGouBZ" />
        </md-form-item>
        <md-row>
          <md-col :span="12">
            <md-form-item label="两定系数" prop="liangDingXS">
              <md-input v-model="editForm.liangDingXS" disabled />
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="顺序号" prop="shunXuHao" label-width="auto">
              <md-input v-number v-model="editForm.shunXuHao" />
            </md-form-item>
          </md-col>
        </md-row>
      </md-form>

      <div class="tableStyle">
        <md-editable-table-pro
          v-enter
          v-model="editForm.baiFangWZS"
          :columns="columns"
          :new-row="addRow"
          :showDefaultOperate="false"
          :hideAddButton="true"
          auto-focus
          row-key="shunXuHao"
          :autoFill="true"
          height="100%"
          ref="editTable"
          :row-class-name="rowClassName"
        >
          <template v-slot:baiFangWZ="{ row, $index }">
            <md-select
              v-model="row.bangFangWZID"
              @change="handlerBaiFWZ($event, row, $index)"
              filterable
              remote
              :remote-method="filterMethod"
              placeholder="请选择"
              :clearable="false"
            >
              <md-option
                v-for="item in shangJiData"
                :key="item.id"
                :label="item.baiFangWZMC"
                :value="item.baiFangWZID"
              >
              </md-option>
            </md-select>
          </template>
          <template v-slot:operate="{ row, $index }">
            <div
              v-if="editForm.baiFangWZS.length - 1 != $index"
              class="btnstyle"
            >
              <md-button
                type="primary"
                noneBg
                :class="nsIcon.b('tuozhuai')"
              ></md-button>
              <md-button
                type="primary"
                noneBg
                :class="nsIcon.b('shanchu')"
                @click="handleDel($index, row)"
              ></md-button>
            </div>
          </template>
        </md-editable-table-pro>
      </div>
    </div>

    <template #footer>
      <div class="HISHL-dialog-footer-right">
        <md-button type="primary" plain @click="handleCancel">取消</md-button>
        <md-button type="primary" @click="handleBaoCun"> 保存 </md-button>
      </div>
    </template>
  </md-dialog>
</template>

<script>
import {
  GetBaiFangWZSelect,
  GetYiYuanMLByJiaGeID,
  YiYuanMLUpdate,
} from '@/service/yaoPin/yaoPinZD';
import { MdMessage, useNamespace } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
import Sortable from 'sortablejs';
import { logger } from '@/service/log';
export default {
  name: 'editDialog',
  data() {
    return {
      dialogVisible: false,
      editForm: {
        yaoPinBFWZID: '',
        caiGouBZ: '',
        baiFangWZS: [],
        liangDingXS: '',
        shunXuHao: '',
        addBaiFangWZS: [],
        deleteBaiFangWZS: [],
        updateBaiFangWZS: [],
      },
      shangJiData: [],
      columns: [
        {
          slot: 'baiFangWZ',
          label: '摆放位置',
        },
        {
          slot: 'operate',
          label: '',
          width: 55,
        },
      ],
      treeProps: {
        label: 'baiFangWZMC',
        value: 'baiFangWZID',
        children: 'children',
      },
      originBaiFWZ: [],
    };
  },
  setup() {
    const nsIcon = useNamespace('icon');
    return {
      nsIcon,
    };
  },

  methods: {
    async showDialog(row) {
      try {
        this.dialogVisible = true;
        const [form, baiFangWZ] = await Promise.all([
          GetYiYuanMLByJiaGeID({
            jiaGeID: row.jiaGeID,
            yaoPinBFWZID: row.yaoPinBFWZID,
          }),
          GetBaiFangWZSelect(),
        ]);
        this.originBaiFWZ = cloneDeep(baiFangWZ);
        this.shangJiData = baiFangWZ;
        Object.assign(this.editForm, { yaoPinBFWZID: row.yaoPinBFWZID }, form);
        this.addRow();
        this.$nextTick(() => {
          this.initSortable();
        });
      } catch (error) {
        logger.error(error);
      }
    },
    handleDel(index, row) {
      this.editForm.baiFangWZS.splice(index, 1);
      if (row.id) {
        this.editForm.deleteBaiFangWZS.push(row.id);
      }
    },
    filterMethod(query) {
      if (query) {
        this.shangJiData = this.originBaiFWZ.filter((item) => {
          return item.baiFangWZMC
            .toLocaleLowerCase()
            .includes(query.toLocaleLowerCase());
        });
      } else {
        this.shangJiData = cloneDeep(this.originBaiFWZ);
      }
    },
    handlerBaiFWZ(val, row, index) {
      row.baiFangWZ = this.shangJiData.find(
        (fl) => fl.baiFangWZID == val,
      ).baiFangWZMC;
      if (row.id) {
        const isIndex = this.editForm.updateBaiFangWZS.findIndex(
          (fl) => fl.id == row.id,
        );
        if (isIndex == -1) this.editForm.updateBaiFangWZS.push(row);
      } else {
        this.editForm.addBaiFangWZS.push(row);
      }
      if (index == this.editForm.baiFangWZS.length - 1) {
        // this.shangJiData = cloneDeep(this.originBaiFWZ);
        this.addRow();
      }
    },
    addRow() {
      this.editForm.baiFangWZS.push({
        bangFangWZID: '',
        baiFangWZ: '',
        shunXuHao: this.editForm.baiFangWZS.length + 1,
      });
    },
    //拖拽排序
    async initSortable() {
      const el = this.$refs.editTable.$el.querySelectorAll(
        `.${process.env.VUE_APP_NAMESPACE}-base-table__body-wrapper table tbody`,
      )[0];
      this.sortable = Sortable.create(el, {
        sort: true, //是否可进行拖拽排序
        animation: 150,
        handle: `.${this.nsIcon.b('tuozhuai')}`.trim(),
        draggable: '.drag-row', // 允许拖拽的项目类名
        onEnd: (evt) => {
          const targetRow = this.editForm.baiFangWZS.splice(evt.oldIndex, 1)[0];
          this.editForm.baiFangWZS.splice(evt.newIndex, 0, targetRow);
          this.$nextTick(() => {
            this.editForm.baiFangWZS.forEach((item, index) => {
              item.shunXuHao = index + 1;
            });
          });
        },
      });
    },
    rowClassName({ row, rowIndex }) {
      if (rowIndex !== this.editForm.baiFangWZS.length - 1) return 'drag-row';
    },
    // 保存分组
    async handleBaoCun() {
      try {
        const form = cloneDeep(this.editForm);
        form.baiFangWZS.splice(form.baiFangWZS.length - 1, 1);
        form.baiFangWZS.forEach((el) => (el.jiaGeID = form.jiaGeID));
        form.liangDingXSGXBZ = 0;
        await YiYuanMLUpdate(form);
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        this.handleCancel();
        this.$emit('handleJianSuo');
      } catch (error) {
        logger.error(error);
      }
    },
    //取消
    async handleCancel() {
      this.dialogVisible = false;
      this.shangJiData = [];
      this.originBaiFWZ = [];
      this.editForm = {
        caiGouBZ: '',
        baiFangWZS: [],
        liangDingXS: '',
        addBaiFangWZS: [],
        deleteBaiFangWZS: [],
        updateBaiFangWZS: [],
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-editdialog {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .tableStyle {
    flex: 1;
    overflow: hidden;

    .btnstyle {
      display: flex;
      justify-content: space-around;
    }
  }
}
</style>
