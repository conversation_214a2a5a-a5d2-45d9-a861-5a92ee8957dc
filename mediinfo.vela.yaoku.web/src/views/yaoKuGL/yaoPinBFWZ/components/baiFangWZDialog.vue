<template>
  <md-dialog
    :title="baoYaoWZForm.id ? '编辑摆放位置' : '新增摆放位置'"
    size="small"
    v-model="dialogVisible"
    :content-scrollable="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <md-form
      :model="baoYaoWZForm"
      use-status-icon
      :rules="formRules"
      label-width="110px"
      ref="form"
    >
      <md-form-item label="上级名称" prop="shangJiBFWZID">
        <md-tree-select
          v-model="baoYaoWZForm.shangJiBFWZID"
          :data="shangJiData"
          :props="treeProps"
          node-key="id"
          check-strictly
          :render-after-expand="false"
          @change="handleSJ"
        />
      </md-form-item>
      <md-form-item label="摆放位置名称" prop="baiFangWZMC">
        <md-input v-model="baoYaoWZForm.baiFangWZMC" />
      </md-form-item>
      <md-form-item label="顺序号" prop="shunXuHao">
        <md-input
          v-model="baoYaoWZForm.shunXuHao"
          v-number.float="{ min: 1 }"
        />
      </md-form-item>
    </md-form>

    <template #footer>
      <md-button
        v-show="baoYaoWZForm.id"
        type="danger"
        plain
        style="float: left"
        @click="handleDelel"
      >
        作废
      </md-button>
      <div class="HISHL-dialog-footer-right">
        <md-button type="primary" plain @click="handleCancel">取消</md-button>
        <md-button type="primary" @click="handleBaoCun"> 保存 </md-button>
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';

import { logger } from '@/service/log';
import {
  DeleteBaiFangWZ,
  GetBaiFangWZById,
  GetBaiFangWZSXHMax,
  GetBaiFangWZTree,
  SaveBaiFangWZ,
} from '@/service/yaoPin/yaoPinZD';
export default {
  name: 'baiYaoWZDialog',
  data() {
    return {
      dialogVisible: false,
      baoYaoWZForm: { id: '', shangJiBFWZID: [], shunXuHao: '', fenZuMC: '' },
      formRules: {
        baiFangWZMC: [
          {
            required: true,
            message: '请输入摆放位置名称',
            trigger: ['blur'],
          },
        ],
      },
      shangJiData: [],
      treeProps: {
        label: 'baiFangWZMC',
        value: 'baiFangWZID',
        children: 'children',
      },
    };
  },
  methods: {
    async showDialog(row) {
      try {
        this.dialogVisible = true;
        this.shangJiData = await GetBaiFangWZTree({
          baiFangWZMC: this.baiFangWZ,
          weiSheZBZ: 0,
        });
        if (row.id) {
          let data = await GetBaiFangWZById({ id: row.id });
          this.baoYaoWZForm = data;
          this.baoYaoWZForm.shangJiBFWZID = [data.shangJiBFWZID];
        } else {
          const shunXuHao = await GetBaiFangWZSXHMax({
            shangJiBFWZID: row.yiXuanObj.baiFangWZID,
          });
          this.baoYaoWZForm = {
            id: '',
            shangJiBFWZID: [row.yiXuanObj.baiFangWZID],
            shunXuHao: shunXuHao,
            fenZuMC: '',
          };
        }
      } catch (error) {
        logger.error(error);
      }
    },
    // 保存分组
    async handleBaoCun() {
      try {
        const ref = await this.$refs.form.validate();
        if (!ref) return;
        let params = {
          id: this.baoYaoWZForm.id,
          baiFangWZID: this.baoYaoWZForm.id,
          shangJiBFWZID: this.baoYaoWZForm.shangJiBFWZID[0] || '0',
          baiFangWZMC: this.baoYaoWZForm.baiFangWZMC,
          shunXuHao: this.baoYaoWZForm.shunXuHao,
        };
        const obj = await SaveBaiFangWZ(params);
        MdMessage({
          type: 'success',
          message: '保存成功',
        });
        this.dialogVisible = false;
        this.$emit('init', obj.id);
      } catch (error) {
        logger.error(error);
      }
    },
    handleDelel() {
      MdMessageBox.confirm('确定作废？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await DeleteBaiFangWZ({ id: this.baoYaoWZForm.id });
        MdMessage({
          type: 'success',
          message: '作废成功',
        });
        this.$emit('init');
        this.dialogVisible = false;
      });
    },
    async handleSJ(val) {
      const shunXuHao = await GetBaiFangWZSXHMax({
        shangJiBFWZID: val,
      });

      this.baoYaoWZForm.shunXuHao = shunXuHao;
    },
    //取消
    async handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>
