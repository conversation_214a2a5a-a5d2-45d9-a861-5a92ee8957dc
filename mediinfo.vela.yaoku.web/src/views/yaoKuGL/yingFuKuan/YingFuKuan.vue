<template>
  <md-tabs v-model="activeName" :class="prefixClass('tabs-views')">
    <md-tab-pane label="未结算" name="first" lazy>
      <weijiesuan ref="first" />
    </md-tab-pane>
    <md-tab-pane label="已结算" name="second" lazy>
      <yijiesuan ref="second" />
    </md-tab-pane>
  </md-tabs>
</template>

<script>
import WeiJieSuan from './WeiJieSuan.vue';
import YiJieSuan from './YiJieSuan.vue';

export default {
  name: 'yingfukuan',
  data() {
    return {
      activeName: 'first',
    };
  },
  watch: {
    // tab切换时请求数据
    activeName: {
      handler: function (val) {
        switch (val) {
          case 'first':
            if (this.$refs.first) this.$refs.first.handleSearch();
            break;
          case 'second':
            if (this.$refs.second) this.$refs.second.handleSearch();
            break;
        }
      },
    },
  },

  components: {
    weijiesuan: <PERSON><PERSON><PERSON><PERSON>uan,
    yijiesuan: Yi<PERSON>ie<PERSON>uan,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-tabs-views {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
::v-deep .#{$md-prefix}-tabs__header {
  margin-bottom: 0;
}
::v-deep .#{$md-prefix}-tabs__content {
  flex: 1;
  min-height: 0;
  min-width: 0;
  background: #eaeff3;
}
</style>
