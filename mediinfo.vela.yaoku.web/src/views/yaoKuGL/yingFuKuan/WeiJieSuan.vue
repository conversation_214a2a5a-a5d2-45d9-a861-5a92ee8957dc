<template>
  <div :class="prefixClass('frameset')">
    <div :class="prefixClass('frame-pane-box frame-pane-left')">
      <div :class="prefixClass('radio-box')">
        <md-radio-group v-model="showType" @change="handleShowTypeChange">
          <md-radio label="1">按发票</md-radio>
          <md-radio label="2">按供货单位</md-radio>
        </md-radio-group>
        <md-button v-if="isAnFaPiao" type="primary" @click="handleJieSuan"
          >结算</md-button
        >
      </div>
      <div :class="prefixClass('left-content')">
        <div :class="[isShowFilter ? prefixClass('left-filter') : '']">
          <md-date-picker-range-pro
            v-model="query.timeRange"
            :class="
              prefixClass([
                'query-size',
                isAnFaPiao ? 'riqi-width-anfapiao' : '',
              ])
            "
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
            @clear="handleSearch"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :clearable="false"
          >
          </md-date-picker-range-pro>
          <div
            v-show="isAnFaPiao"
            :class="prefixClass('filter-more')"
            @click="isShowFilter = !isShowFilter"
          >
            <i
              :class="
                prefixClass([
                  'icon-class',
                  isShowFilter
                    ? 'icon-shuangshangjiantou'
                    : 'icon-shuangxiajiantou',
                ])
              "
            ></i>
          </div>
          <md-input
            v-model="query.likeQuery"
            v-show="isAnFaPiao"
            :class="prefixClass('query-size')"
            placeholder="输入发票号搜索"
            @keyup.enter.native="handleSearch"
          >
            <i
              :class="prefixClass('input__icon icon-seach')"
              slot="suffix"
              @click="handleSearch"
            ></i>
          </md-input>
          <md-input
            v-model="query.gongHuoDWMC"
            v-show="isAnGongHuoDW"
            :class="prefixClass('query-size')"
            placeholder="输入供货单位搜索"
            @keyup.enter.native="handleSearch"
          >
            <i
              :class="prefixClass('input__icon icon-seach')"
              slot="suffix"
              @click="handleSearch"
            ></i>
          </md-input>
          <div :class="prefixClass('item-inline')">
            <gonghuodw-select
              :value.sync="query.gongHuoDWMC"
              :class="prefixClass('gonghuodw')"
              @keyup.enter.native="handleSearch"
              @select-change="handleChangeGongHuoDW"
            />
          </div>
          <div :class="prefixClass('item-inline left-filter-buttons')">
            <md-button
              :class="prefixClass('button-class')"
              type="primary"
              plain
              @click="clearFilter"
              >重置</md-button
            >
            <md-button
              :class="prefixClass('button-class')"
              type="primary"
              @click="handleFilterSearch"
              >查询</md-button
            >
          </div>
        </div>
        <yaopingrk-left-table
          v-if="isAnFaPiao"
          :columns="faPiaoColumns"
          :get-list-data="getLeftTableData"
          :class="[isShowFilter ? prefixClass('left-filter-more') : '']"
          ref="leftTable"
          @row-click="handleTableRowClick"
          @selection-change="handleSelectionChange"
        />
        <biz-list
          v-else
          v-loading="listLoading"
          :dataList="gongHuoDWList"
          :total="listToal"
          titleKey="gongHuoDWMC"
          idKey="gongHuoDWID"
          :pageSize.sync="gongHuoDWQuery.pageSize"
          :current-page.sync="gongHuoDWQuery.pageIndex"
          @rowClick="handleListRowClick"
          @current-change="handleCurrentChange"
          ref="bizList"
        ></biz-list>
      </div>
    </div>
    <div :class="prefixClass('frame-pane-box frame-pane-right')">
      <div :class="prefixClass('content-top')">
        <div :class="prefixClass('content-top-filters')">
          <md-input
            v-if="showType === '2'"
            style="margin-right: 8px"
            v-model="rightQuery.ruKuDID"
            :class="prefixClass('filter-width')"
            placeholder="输入入库单号搜索"
            @keyup.enter.native="gongHuoDWTableSearch"
          >
            <i
              :class="prefixClass('input__icon icon-seach')"
              slot="suffix"
              @click="gongHuoDWTableSearch"
            />
          </md-input>
          <biz-yaopindw
            v-model="rightQuery.yaoPin"
            :class="prefixClass('yaopin-search')"
            @change="handleDingWei"
            placeholder="输入药品搜索"
            style="width: 240px"
            showSuffix
          />
          <md-button
            v-if="isAnGongHuoDW"
            type="primary"
            :icon="prefixClass('icon-shuaxin')"
            noneBg
            style="margin-right: 55px; margin-top: 3px"
            @click="gongHuoDWTableSearch"
            >刷新</md-button
          >
          <md-button type="primary" v-if="isAnGongHuoDW" @click="handleJieSuan"
            >结算</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('table-box-border')">
        <md-table
          v-loading="anFaPiaoTableLoading"
          v-show="isAnFaPiao"
          :columns="faPiaoRightColumns"
          :data="faPiaoData"
          height="100%"
          highlight-current-row
          row-key="id"
          :stripe="false"
          ref="faPiaoTable"
        >
          <template #yaoPinMCGG="{ row, $index }">
            <span :data-name="'scroll-' + $index">
              {{ row.yaoPinMCGG }}
            </span>
          </template>
        </md-table>
        <md-table-pro
          v-loading="anGongHuoDWTableLoading"
          v-show="isAnGongHuoDW"
          :columns="columns"
          height="100%"
          border
          row-key="id"
          :stripe="false"
          :onFetch="handleFetch"
          @selection-change="handleSelectionChange"
          ref="gongHuoDWTable"
          @sort-change="handleSortChange"
          :pagination="{
            pageSizes: [10, 20, 30, 40, 50, 100, 200, 300, 400],
          }"
          :level="level"
          :controlLevel="controlLevel"
          :customLevels="customLevels"
          :control-loading="controlLoading"
          :controlColumnLayout="controlColumnLayout"
          :controlExtraColumns="controlExtraColumns"
          @getNewColumn="getNewColumn"
          @recovery-column="recoveryColumn"
          @control-cancel="controlCancel"
          @level-change="levelChange"
        >
          <template #caoZuoSZDM="{ row, cellRef }">
            <md-select
              v-model="row.caoZuoSZDM"
              :disabled="row.canShuRu"
              @change="CaoZuoSZChange($event, row, cellRef)"
            >
              <md-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </md-select>
          </template>
          <template #lieMingCheng="{ row }">
            <span :class="prefixClass('lieMingCheng')" style="margin-left: 8px">
              {{ row.lieMingCheng }}
            </span>
          </template>
          <template #bieMing="{ row }">
            <md-input v-model="row.bieMing" placeholder="请填写" clearable />
          </template>
          <!-- :span-method="handleSpanMethod" -->
          <template #zhongYiBZ="{ row }">
            <div v-if="!row.isHeJi" style="text-align: center">
              {{ row.zhongYiBZ == 1 ? '中' : '西' }}
            </div>
            <div v-else>{{ row.chanDiMC }}</div>
          </template>
        </md-table-pro>
      </div>
      <div :class="prefixClass('table-append')" v-show="isAnFaPiao">
        共计：<span>{{ faPiaoData.length }}</span
        >种药品 合计 进价金额：<span>{{ calcJinJiaJE }}</span
        >元
      </div>
      <div :class="prefixClass('table-appendLeft')" v-show="isAnGongHuoDW">
        共<span :class="prefixClass('number')">{{ danJuCount }}</span
        >张单据 合计 进价金额：
        <span :class="prefixClass('number')">{{ gongHuoJinJiaJE }}</span> 元
        进销差额：
        <span :class="prefixClass('number')">{{
          Number(gongHuoLingShouJE - gongHuoJinJiaJE).toFixed(2)
        }}</span>
        元 零售金额：
        <span :class="prefixClass('number')">{{ gongHuoLingShouJE }}</span> 元
      </div>
    </div>
    <jiesuandan-dialog ref="jieSuanDialog" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import Big from 'big.js';

import columnMixin from '@/components/mixin/columnMixin';

import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizList from '@/views/yaoKuGL/components/BizList';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import YaopingrkLeftTable from '@/views/yaoKuGL/components/YaoPingRKLeftTable';

import {
  getWeiJieSFPCount,
  getWeiJieSFPList,
  getWeiJieSGHDWCount,
  getWeiJieSGHDWList,
  getWeiJieSYPMXCountByGHDW,
  getWeiJieSYPMXListByFP,
  getWeiJieSYPMXListByGHDW,
} from '@/service/yaoPinYK/yingFuKuan';
import { logger } from '@/service/log';
import {
  addYingFuKJS,
  addYingFuKJSByRuKuDH,
  getWeiJieSDXX,
  getWeiJieSDXXByRuKuDH,
} from '@/service/yaoPinYK/yingFuKuan';
import JieSuanDialog from './components/JieSuanDanDialog';
import tableData from './components/tableData.js';

function formatNum(num) {
  if (!num) num = 0;
  return Number(new Big(num).times(100).round(0).div(100));
}
export default {
  name: 'weijiesuan-page',
  mixins: [columnMixin],
  data() {
    return {
      anFaPiaoTableLoading: false,
      anGongHuoDWTableLoading: false,
      listLoading: false,
      checkAll: false,
      isIndeterminate: false,
      showType: '1',
      isShowFilter: false,
      query: {
        timeRange: this.getDefaultDateRange(),
        likeQuery: '',
        gongHuoDWMC: '',
      },
      defaultQuery: {
        timeRange: this.getDefaultDateRange(),
        likeQuery: '',
        gongHuoDWMC: '',
      },
      rightQuery: {
        ruKuDID: '',
        yaoPin: {},
      },
      defaultRightQuery: {
        ruKuDID: '',
        yaoPin: '',
      },
      gongHuoDWQuery: {
        pageSize: 20,
        pageIndex: 1,
      },
      selectedGongSi: '',
      selectedArray: [],
      columns: tableData.gongHuoDWColumns, //用来列设置
      faPiaoColumns: tableData.faPiaoColumns,
      faPiaoRightColumns: tableData.faPiaoRightColumns,
      // gongHuoDWColumns: tableData.gongHuoDWColumns,
      faPiaoData: [],
      gongHuoDWData: [],
      gongHuoDWList: [],
      listToal: 0,
      currentSelection: [],
      currentGongHuoDW: null,
      yaoPingDW: {},
      paiXuBZ: null,
      sortDir: null,
      sortField: null,
      gongHuoDWSelectList: [],
      calcJinJiaJE: 0,
      controlExtraColumns: [
        {
          slot: 'bieMing',
          label: '别名',
          cIndex: 5,
        },
      ],
    };
  },
  computed: {
    isAnFaPiao() {
      return this.showType === '1';
    },
    isAnGongHuoDW() {
      return this.showType === '2';
    },
    // calcJinJiaJE() {
    //   let jinE = 0;
    //   this.faPiaoData.forEach((item) => {
    //     jinE = formatNum(+item.jinJiaJE) + jinE;
    //   });
    //   return this.round(jinE);
    // },
    danJuCount() {
      return this.currentSelection.length || 0;
    },
    gongHuoJinJiaJE() {
      let jinE = 0;
      this.currentSelection.forEach((item) => {
        jinE = formatNum(+item.ruKuJJJE) + jinE;
      });
      return this.round(jinE);
    },
    gongHuoLingShouJE() {
      let lingShou = 0;
      this.currentSelection.forEach((item) => {
        lingShou = formatNum(+item.ruKuLSJE) + lingShou;
      });
      return this.round(lingShou);
    },
  },
  async mounted() {
    this.query.timeRange = this.getDefaultDateRange();
    this.defaultQuery.timeRange = this.getDefaultDateRange();
    this.columnsName = 'gongHuoDWColumns';
    await this.getColumnInit();
    // this.gongHuoDWColumns = cloneDeep(this.columns);
  },
  methods: {
    round(num) {
      const n = Number(new Big(num).times(100).round(0).div(100));
      return n.toFixed(2);
    },
    handleDingWei(data) {
      if (!data) {
        this.rightQuery.yaoPin = '';
        this.gongHuoDWTableSearch();
        return;
      }
      const yaoPinMC = data.yaoPinMC;
      this.rightQuery.yaoPin = data;
      let rowIndex = null;
      if (this.showType === '1') {
        rowIndex = this.faPiaoData.findIndex(
          (item) => item.yaoPinMC === yaoPinMC,
        );
        if (rowIndex < 0) {
          MdMessage({
            message: '未找到此药品信息',
            type: 'warning',
          });
          return;
        }
        this.$refs.faPiaoTable.setCurrentRow(this.faPiaoData[rowIndex]);
        this.$refs.faPiaoTable.$el
          .querySelector(`[data-name=scroll-${rowIndex}]`)
          .scrollIntoView();
      } else {
        rowIndex = this.gongHuoDWData.find(
          (item) => item.yaoPinMC === yaoPinMC,
        );
        this.gongHuoDWTableSearch();
      }
    },
    getDefaultDateRange() {
      const year = new Date().getFullYear();
      const month = new Date().getMonth() + 1;
      const currentMonthFirstDay = `${year}-${month}-1`;
      return [
        dayjs(currentMonthFirstDay).format('YYYY-MM-DD'),
        dayjs(new Date()).format('YYYY-MM-DD'),
      ];
    },
    handleListRowClick(row) {
      this.rightQuery = cloneDeep(this.defaultRightQuery);
      this.currentGongHuoDW = row.gongHuoDWID;
      this.gongHuoDWTableSearch();
    },
    gongHuoDWTableSearch() {
      this.$refs.gongHuoDWTable.search({ pageSize: 200 });
    },
    async handleFetch({ page, pageSize }) {
      if (this.showType == '1') return;
      const KaiShiSJ = this.query.timeRange?.[0];
      const JieShuSJ = this.query.timeRange?.[1];
      const YaoPinMC = this.rightQuery.yaoPin
        ? this.rightQuery.yaoPin.yaoPinMC
        : '';
      const params = {
        GongHuoDWID: this.currentGongHuoDW,
        RuKuDID: this.rightQuery.ruKuDID,
        YaoPinMC,
        KaiShiSJ,
        JieShuSJ,
        PageSize: pageSize,
        PageIndex: page,
        sortDir: this.sortDir,
        sortField: this.sortField,
      };
      const [res, total] = await Promise.all([
        getWeiJieSYPMXListByGHDW(params),
        getWeiJieSYPMXCountByGHDW(params),
      ]);
      let gongHuoDWData = [];
      this.gongHuoDWSelectList = cloneDeep(res);
      // res.forEach((item, index) => {
      //   const faPiao = {
      //     faPiaoHM: item.faPiaoHM,
      //     faPiaoJE: item.faPiaoJE,
      //     faPiaoRQ: item.faPiaoRQ,
      //     dangQianIndex: index,
      //   };
      //   item.rowSpanLeng =
      //     index === 0
      //       ? 0 + item.ruKuDMXYFKList.length
      //       : res[index - 1].rowSpanLeng + item.ruKuDMXYFKList.length;
      //   const rowSpanIndex = index === 0 ? 0 : res[index - 1].rowSpanLeng;
      //   const rowSpanNum = item.ruKuDMXYFKList.length;
      //   item.ruKuDMXYFKList.forEach((val) => {
      //     val = { ...val, ...faPiao, rowSpanIndex, rowSpanNum };
      //     gongHuoDWData.push(val);
      //   });
      // });
      // this.gongHuoDWData = gongHuoDWData;
      // console.log(gongHuoDWData, 'gongHuoDWData');
      return {
        items: res,
        total,
      };
    },
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 4) {
        const rowSpanNum = row.rowSpanNum;
        const rowSpanIndex = row.rowSpanIndex;
        if (rowIndex === rowSpanIndex) {
          return {
            rowspan: rowSpanNum,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    handleSelectionChange(selection) {
      this.gongHuoDWSelectList = [];
      this.currentSelection = selection;
      for (let item of selection) {
        this.gongHuoDWData.forEach((fl) => {
          if (fl.dangQianIndex == item.dangQianIndex) {
            this.gongHuoDWSelectList.push(fl);
          }
        });
      }
    },
    //升降序
    handleSortChange({ column, prop, order }) {
      if (!order) return;
      this.paiXuBZ = order == 'ascending' ? 1 : 2;
      this.sortField = prop;
      this.sortDir = order == 'ascending' ? 'asc' : 'desc';
      this.gongHuoDWTableSearch();
    },
    clearSelection() {
      this.selection = [];
    },
    handleCurrentChange() {},

    handleTableRowClick(row) {
      this.rightQuery = cloneDeep(this.defaultRightQuery);
      this.anFaPiaoTableLoading = true;
      row = row || this.faPiaoData[0];
      this.calcJinJiaJE = row.faPiaoJE || 0;
      if (!row) return;
      const params = {
        faPiaoHM: row.faPiaoHM,
      };
      getWeiJieSYPMXListByFP(params)
        .then((res) => {
          this.faPiaoData = res;
        })
        .catch((e) => {
          this.faPiaoData = [];
        })
        .finally(() => {
          this.anFaPiaoTableLoading = false;
        });
    },
    async handleJieSuan() {
      const data = this.currentSelection;
      //判断是否选择发票
      if (data.length === 0) {
        MdMessage({
          message: '请勾选要结算的发票',
          type: 'warning',
        });
        return;
      }
      //判断勾选的是否是同一个供货单位的
      const firstId = data[0].gongHuoDWID;
      const flag = data.every((item) => {
        return item.gongHuoDWID === firstId;
      });
      if (!flag) {
        MdMessage({
          message: '请选择相同供货单位的发票',
          type: 'warning',
        });
        return;
      }
      await MdMessageBox.confirm('确认结算吗？', '操作提醒！', {
        confirmButtonText: '好的',
        cancelButtonText: '取消',
        type: 'warning',
      });
      let gongHuoDWXX = {};
      if (this.isAnFaPiao) {
        const faPiaoHMs = data.map((item) => item.faPiaoHM).join(',');
        getWeiJieSDXX({ faPiaoHMs })
          .then((res) => {
            if (res == null) {
              MdMessage({
                type: 'warning',
                message: '发票不存在',
              });
              throw new Error('发票不存在');
            }
            gongHuoDWXX = res || {};
            if (gongHuoDWXX.jinJiaJE) {
              gongHuoDWXX.jinJiaJE = Number(gongHuoDWXX.jinJiaJE).toFixed(3);
            }
            this.onJieSuanMethod(data, gongHuoDWXX);
          })
          .catch((x) => {});
      } else {
        const ruKuDHList = data.map((item) => item.ruKuDH).join();
        getWeiJieSDXXByRuKuDH({ ruKuDHList })
          .then((res) => {
            if (res == null) {
              MdMessage({
                type: 'warning',
                message: '发票不存在',
              });
              throw new Error('发票不存在');
            }
            gongHuoDWXX = res || {};
            if (gongHuoDWXX.jinJiaJE) {
              gongHuoDWXX.jinJiaJE = Number(gongHuoDWXX.jinJiaJE).toFixed(3);
            }
            this.onJieSuanMethod(data, gongHuoDWXX);
          })
          .catch((x) => {});
      }
    },
    onJieSuanMethod(data, gongHuoDWXX) {
      const RuKuDHs = data.map((item) => item.ruKuDH);
      const faPiaoHMs = data.map((item) => item.faPiaoHM);
      const params = {
        gongHuoDWID: gongHuoDWXX.danWeiID,
        gongHuoDWMC: gongHuoDWXX.danWeiMC,
        kaiHuYH: gongHuoDWXX.kaiHuYH,
        danWeiZH: gongHuoDWXX.danWeiZH,
        jieSuanSJ: this.currentDate, //结算时间
        beiZhu: this.remark,
        JieSuanDH: gongHuoDWXX.jieSuanDH, //结算单号
        KaiShiSJ: this.query.timeRange[0] ? this.query.timeRange[0] : null,
        JieShuSJ: this.query.timeRange ? this.query.timeRange[1] : null,
      };
      if (this.isAnFaPiao) {
        params.faPiaoHMs = faPiaoHMs;
        addYingFuKJS(params)
          .then((res) => {
            MdMessage({
              type: 'success',
              message: '结算成功',
            });
            this.handleSearch();
          })
          .catch((e) => {
            logger.error(e);
          });
      } else {
        params.RuKuDHs = RuKuDHs;
        addYingFuKJSByRuKuDH(params)
          .then((res) => {
            MdMessage({
              type: 'success',
              message: '结算成功',
            });
            this.handleSearch();
          })
          .catch((e) => {
            logger.error(e);
          });
      }
    },
    handleSearch() {
      this.currentSelection = [];
      if (this.showType === '1') {
        this.$refs.leftTable.search();
      }
      if (this.showType === '2') {
        this.getWeiJieSGHDWList();
      }
    },
    handleChangeGongHuoDW(data) {
      this.query.gongHuoDWMC = data.gongHuoDWMC;
    },
    handleZhiDan() {},
    async getLeftTableData(pageIndex, pageSize) {
      const query = this.query;
      const KaiShiSJ = query.timeRange?.[0];
      const JieShuSJ = query.timeRange?.[1];
      const params = {
        KaiShiSJ,
        JieShuSJ,
        FaPiaoHM: query.likeQuery,
        GongHuoDWMC: query.gongHuoDWMC,
        JieSuanBZ: 0,
        PageSize: pageSize,
        PageIndex: pageIndex,
      };
      const [items, total] = await Promise.all([
        getWeiJieSFPList(params),
        getWeiJieSFPCount(params),
      ]);

      if (total > 0) {
        this.handleTableRowClick(items[0]);
      } else {
        this.faPiaoData = [];
      }
      return Promise.resolve({
        items,
        total,
      });
    },
    getWeiJieSGHDWList() {
      this.listLoading = true;
      const query = this.query;
      const KaiShiSJ = query.timeRange?.[0];
      const JieShuSJ = query.timeRange?.[1];
      const params = {
        GongHuoDWMC: query.gongHuoDWMC,
        JieSuanBZ: 0,
        PageSize: this.gongHuoDWQuery.pageSize,
        PageIndex: this.gongHuoDWQuery.pageIndex,
        KaiShiSJ,
        JieShuSJ,
      };

      getWeiJieSGHDWList(params)
        .then((res) => {
          this.gongHuoDWList = res;
          if (res.length > 0) {
            const row = res[0];
            this.$refs.bizList.setCurrentSelect(row.gongHuoDWID);
            this.handleListRowClick(row);
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
      getWeiJieSGHDWCount(params).then((res) => {
        this.listToal = res;
      });
    },
    initSelection() {
      this.selectedArray = [];
      this.selectedGongSi = '';
    },
    checkChoosed(row) {
      if (
        this.selectedArray.length !== 0 &&
        this.selectedGongSi !== row.chanDiMC
      ) {
        MdMessageBox.confirm('请选择相同供货单位的药品入库！', '操作提醒！', {
          showCancelButton: false,
          confirmButtonText: '好的',
          type: 'warning',
        });
        return false;
      } else {
        this.selectedGongSi = row.chanDiMC;
        return true;
      }
    },
    handleShowTypeChange(val) {
      this.isShowFilter = false;
      this.query = cloneDeep(this.defaultQuery);
      this.rightQuery = cloneDeep(this.defaultRightQuery);
      this.handleSearch();
    },
    clearFilter() {
      this.query = cloneDeep(this.defaultQuery);
    },
    handleFilterSearch() {
      this.isShowFilter = false;
      this.handleSearch();
    },
  },
  components: {
    'yaopingrk-left-table': YaopingrkLeftTable,
    'biz-list': BizList,
    'jiesuandan-dialog': JieSuanDialog,
    'biz-yaopindw': BizYaoPinDW,
    'gonghuodw-select': GongHuoDWSelect,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-table-box-border {
  flex: 1;
  min-height: 0;
  ::v-deep .caozuo-icon {
    transform: translateY(3px);
  }
}

::v-deep .#{$md-prefix}-heji-row {
  background-color: #f2f9f9;

  > td {
    border-right: 0;
    border-left: 0;

    .cell {
      height: 20px;
      font-weight: 600;
      color: #377777;
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.#{$md-prefix}-content-top {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  position: relative;

  &-filters {
    width: 100%;
    display: flex;
    .#{$md-prefix}-filter-width {
      width: 240px;
    }
    .#{$md-prefix}-button {
      position: absolute;
      right: 0;
    }
  }

  &-buttons {
    display: flex;
  }
}
.#{$md-prefix}-frameset {
  display: flex;
  flex: 1;
  min-height: 0;
}
.#{$md-prefix}-frame-pane-right {
  margin: 8px;
  padding: 8px;
  flex: 1;
  min-height: 0;
  min-width: 0;
}

.#{$md-prefix}-frame-pane-left {
  height: 100%;
  // width: 280px;
  width: 300px;
}

.#{$md-prefix}-frame-pane-box {
  background: #fff;
  display: flex;
  flex-direction: column;
}

.#{$md-prefix}-radio-box {
  height: 36px;
  display: flex;
  align-items: center;
  // background-color: #edf6fd;
  background-color: rgb(var(--md-color-1));
  padding: 0 8px;
  justify-content: space-between;
}

.#{$md-prefix}-radio-text {
  height: 20px;
  color: #222222;
  font-size: 14px;
  line-height: 20px;
}

.#{$md-prefix}-left-content {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.#{$md-prefix}-query-size {
  width: 100%;
  height: 30px;
  margin-bottom: 8px;
}

.#{$md-prefix}-riqi-width-anfapiao {
  width: calc(100% - 38px);
}

.#{$md-prefix}-filter-more {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 30px;
  height: 28px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
}

.#{$md-prefix}-icon-class {
  font-size: 16px;
}

.#{$md-prefix}-item-inline {
  display: none;
}

.#{$md-prefix}-left-filter {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 999;
  padding: 8px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .#{$md-prefix}-item-inline-label {
      width: 62px;
      height: 20px;
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      text-align: right;
    }

    &-input {
      margin-left: 8px;
      width: calc(100% - 68px);
    }
    .#{$md-prefix}-gonghuodw {
      margin-right: 0 !important;
    }
  }

  .#{$md-prefix}-left-filter-buttons {
    margin-top: 8px;
    .#{$md-prefix}-button-class {
      width: 64px;
      height: 30px;
      margin-left: 8px;
    }
  }
}

.#{$md-prefix}-left-filter-more {
  margin-top: 76px;
}
.#{$md-prefix}-table-append {
  text-align: right;
  color: #aaaaaa;
  font-size: 14px;
  line-height: 20px;
  box-sizing: border-box;
  padding: 8px;
  span {
    color: #000;
    font-weight: bold;
  }
}
.#{$md-prefix}-table-appendLeft {
  position: absolute;
  bottom: 16px;
  text-align: left;
  color: #aaaaaa;
  font-size: 14px;
  line-height: 20px;
  box-sizing: border-box;
  padding: 8px;
  span {
    color: #000;
    font-weight: bold;
  }
}
</style>
