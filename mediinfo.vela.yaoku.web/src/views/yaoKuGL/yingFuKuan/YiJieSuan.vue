<template>
  <div :class="prefixClass('yijiesuan-box')">
    <div :class="prefixClass('yijiesuan-content-top')">
      <div :class="prefixClass('yijiesuan-content-top-filters')">
        <md-date-picker-range-pro
          v-model="query.timeRange"
          style="margin-right: 8px; width: 250px"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        >
        </md-date-picker-range-pro>
        <md-select
          v-model="query.xiaoZhangBZ"
          style="margin-right: 8px; width: 170px"
          @change="handleSearch()"
        >
          <md-option
            v-for="item in xiaoZhangBZOptionis"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </md-select>
        <gonghuodw-select
          :class="prefixClass('filter-width')"
          style="margin-right: 8px"
          placeholder="输入选择供货单位"
          @change="handleSearch"
          @select-change="handleChangeGongHuoDW"
        />
        <md-input
          v-model="query.likeQuery"
          :class="prefixClass('filter-width')"
          style="margin-right: 8px"
          placeholder="输入结算号/发票号搜索"
          @keyup.enter.native="handleSearch"
        >
          <i
            :class="prefixClass('input__icon icon-seach')"
            slot="suffix"
            @click="handleSearch"
          />
        </md-input>
      </div>
      <div :class="prefixClass('yijiesuan-content-top-buttons')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          @click="handleSearch"
        >
          刷新</md-button
        >
        <md-button
          type="primary"
          noneBg
          :icon="prefixClass('icon-dayinji')"
          @click="handlePiLiangDY"
          >批量打印</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('yijiesuan-table-box')">
      <md-table-pro
        :columns="columns"
        :onFetch="handleFetch"
        :row-class-name="tableRowClassName"
        height="100%"
        :autoLoad="false"
        border
        :stripe="false"
        @select="handleSelectRow"
        @select-all="handleSelectRow"
        ref="table"
      >
        <template #jieSuanDH="{ row, $index }">
          <md-tooltip
            effect="light"
            trigger="hover"
            :popper-class="prefixClass('rukudantip')"
          >
            <template #content>
              <div @click="copy(row.jieSuanDH)">复制</div>
            </template>
            <md-link
              :always-underline="false"
              :class="prefixClass('rukudh')"
              @click="handleRowClick($event, row, $index)"
              >{{ row.jieSuanDH }}</md-link
            >
          </md-tooltip>
        </template>
        <template #yaoPinMX="{ row }">
          <biz-tag-list :list="row.yaoPinMX" label-key="yaoPinMC">
          </biz-tag-list>
        </template>
        <template #chuKuDan="{ row }">
          <div :class="prefixClass('item-inline')">
            {{ row.chuKuDan }}
            <div :class="prefixClass('chonghongBZ')" v-if="row.chongHongBZ">
              冲
            </div>
          </div>
        </template>
        <template #xiaoZhangBZ="{ row }">
          <i
            v-show="row.xiaoZhangBZ"
            class="iconfont icongou"
            style="color: #1e88e5"
          />
          <md-button
            type="text-bg"
            v-show="!row.xiaoZhangBZ"
            @click.stop="handleXiaoZhang(row)"
          >
            销账
          </md-button>
        </template>
        <template #operate="{ row }">
          <md-button type="text-bg" @click.stop="handleChongDa(row)">
            重打
          </md-button>
          <md-button
            type="text-bg"
            v-show="!row.xiaoZhangBZ"
            @click.stop="handleQuXiaoJS(row)"
          >
            取消结算
          </md-button>
        </template>
      </md-table-pro>
      <div :class="prefixClass('yijiesuan-zong')">
        <div :class="prefixClass('yijiesuan-zong-item')">
          <div>合计 结算金额：</div>
          <div>{{ heJiXX.jieSuanZJE }}</div>
          <div>元</div>
        </div>
        <div :class="prefixClass('yijiesuan-zong-item')">
          <div>零售金额：</div>
          <div>{{ heJiXX.lingShouZJE }}</div>
          <div>元</div>
        </div>
      </div>
    </div>
    <yaopinfkd-drawer ref="yaoPinFKDDrawer" @da-yin="handleDaYin" />
    <dayin-dialog ref="daYinDialog" />
    <PiLiangDYDialog ref="PiLiangDYDialog" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';

import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
import BizTagList from '@/components/BizTagList';
import {
  getJieSuanDXX,
  getJieSuanDanCount,
  getJieSuanDanList,
  quXiaoJieSuanDan,
  xiaoZhangJieSuanDan,
} from '@/service/yaoPinYK/yingFuKuan';
import { printByUrl } from '@/system/utils/print';
import GongHuoDWSelect from '@/views/yaoKuGL/components/GongHuoDWSelect';
import useClipboard from 'vue-clipboard3';
import DaYinDialog from './components/DaYinDialog.vue';
import PiLiangDYDialog from './components/PiLiangDYDialog.vue';
import YaoPinFKDDrawer from './components/YaoPinFKDDrawer';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
export default {
  name: 'yijiesuan-page',
  data() {
    return {
      lingShouJEXSDW: 2,
      jinJiaJEXSDW: 2,
      currentTableIndex: null,
      query: {
        timeRange: this.getDefaultDateRange(),
        gongHuoDWMC: '',
        likeQuery: '',
        xiaoZhangBZ: '',
      },
      gongHuoDWOptions: [
        {
          gongHuoDWID: '1',
          gongHuoDWMC: '测试',
        },
      ],
      columns: [
        {
          type: 'selection',
        },
        {
          slot: 'jieSuanDH',
          label: '结算单号',
          width: 112,
        },
        {
          prop: 'gongHuoDWMC',
          label: '供货单位',
          minWidth: 180,
          showOverflowTooltip: true,
        },
        {
          prop: 'faPiaoHM',
          label: '发票号',
          minWidth: 118,
        },
        {
          prop: 'jieSuanJE',
          label: '结算金额',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            if (!cellValue) return '0.00';
            return formatJiaGe_2(cellValue, this.lingShouJEXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            if (!cellValue) return '0.00';
            return formatJiaGe_2(cellValue, this.lingShouJEXSDW);
          },
        },
        {
          prop: 'jieSuanSJ',
          label: '结算日期',
          width: 160,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'xiaoZhangSJ',
          label: '销账日期',
          width: 160,
          formatter: (row, column, cellValue, index) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'jieSuanRXM',
          label: '结算人',
          width: 120,
        },
        {
          slot: 'xiaoZhangBZ',
          label: '销账',
          width: 70,
          align: 'center',
        },
        {
          slot: 'operate',
          label: '操作',
          width: 125,
        },
      ],
      selectedRows: [],
      heJiXX: {
        jieSuanZJE: 0,
        lingShouZJE: 0,
      },
      xiaoZhangBZOptionis: [
        { value: '', label: '全部' },
        { value: '0', label: '未销账' },
        { value: '1', label: '已销账' },
      ],
    };
  },
  async mounted() {
    this.query.timeRange = this.getDefaultDateRange();
    this.handleSearch();
    const res = await getKuFangSZList(['jinJiaJEXSDWS', 'lingShouJEXSDWS']);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  },
  methods: {
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
            duration: 2000,
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    getDefaultDateRange() {
      const year = new Date().getFullYear();
      const month = new Date().getMonth() + 1;
      const currentMonthFirstDay = `${year}-${month}-1`;
      return [
        dayjs(currentMonthFirstDay).format('YYYY-MM-DD'),
        dayjs(new Date()).format('YYYY-MM-DD'),
      ];
    },
    handleQuXiaoJS(row) {
      MdMessageBox.confirm(
        `确定取消结算<span style="color:#1e88e5;margin:0 8px">${row.jieSuanDH}</span>？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning',
        },
      ).then(() => {
        const params = {
          jieSuanDID: row.id,
        };
        quXiaoJieSuanDan(params)
          .then((res) => {
            MdMessage({
              message: '取消结算成功',
              type: 'success',
            });
            this.handleSearch();
          })
          .catch((e) => {
            MdMessage({
              message: e,
              type: 'success',
            });
          });
      });
    },
    handleDateRangeChange(val) {
      this.handleSearch();
    },
    handleSelectChange() {
      this.handleSearch();
    },
    // 批量打印
    handlePiLiangDY() {
      if (this.selectedRows.length) {
        this.$refs.PiLiangDYDialog.showModal(this.selectedRows);
      } else {
        this.$message.warning('请至少选择一条数据');
      }
    },
    handleSelectRow(selections) {
      this.selectedRows = selections;
    },
    handleSearch() {
      this.$refs.table.search({ pageSize: 100 });
    },
    handleChangeGongHuoDW(data) {
      this.query.gongHuoDWMC = data.gongHuoDWMC;
    },
    async handleDaYin(row) {
      const params = {
        jieSuanDID: row.id,
      };
      await printByUrl('YKXT007', params);
    },
    async handleChongDa(row) {
      this.$refs.daYinDialog.showModal({
        data: row,
      });
    },
    handleXiaoZhang(row) {
      MdMessageBox.confirm(
        `确定将此结算单<span style="color:#1e88e5;margin:0 8px">${row.jieSuanDH}</span>销帐？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning',
        },
      ).then(() => {
        const params = {
          jieSuanDID: row.id,
        };
        xiaoZhangJieSuanDan(params)
          .then((res) => {
            MdMessage({
              message: '销账成功',
              type: 'success',
            });
            this.handleSearch();
          })
          .catch((e) => {
            // Message.error('e')
            MdMessageBox.confirm({
              title: '系统消息',
              type: 'error',
              message: e.message,
              confirmButtonText: '我知道了',
            });
          });
      });
    },
    async handleFetch({ page, pageSize }) {
      const jieSuanSJKS = this.query.timeRange?.[0];
      const jieSuanSJJS = this.query.timeRange?.[1];
      const params = {
        xiaoZhangBZ: this.query.xiaoZhangBZ,
        jieSuanSJKS,
        jieSuanSJJS,
        gongHuoDWMC: this.query.gongHuoDWMC,
        jieSuanID: this.query.likeQuery,
        pageSize: pageSize,
        pageIndex: page,
      };
      const items = await getJieSuanDanList(params);
      const total = await getJieSuanDanCount(params);
      let jieSuanZJE = 0;
      let lingShouZJE = 0;
      items.forEach((item) => {
        lingShouZJE = lingShouZJE + (item.lingShouJE || 0);
      });
      this.heJiXX.jieSuanZJE = Number(total.jieSuanJEHZ).toFixed(
        this.lingShouJEXSDW,
      );
      this.heJiXX.lingShouZJE = Number(lingShouZJE).toFixed(
        this.lingShouJEXSDW,
      );
      return {
        items: items,
        total: total.count,
      };
    },
    async handleRowClick(e, row, index) {
      e.stopPropagation();
      this.currentTableIndex = index;
      const data = await getJieSuanDXX({ jieSuanDID: row.id });
      data.jieSuanSJ = dayjs(data.jieSuanSJ).format('YYYY-MM-DD HH:mm');
      this.$refs.yaoPinFKDDrawer.openDrawer({
        data,
        jieSuanJE: row.jieSuanJE,
      });
    },
    tableRowClassName({ row, rowIndex }) {
      let className = '';
      if (rowIndex === this.currentTableIndex) {
        className = 'row-height';
      } else {
        className = '';
      }
      return className;
    },
  },
  components: {
    'biz-tag-list': BizTagList,
    'yaopinfkd-drawer': YaoPinFKDDrawer,
    'dayin-dialog': DaYinDialog,
    'gonghuodw-select': GongHuoDWSelect,
    PiLiangDYDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-yijiesuan-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .#{$md-prefix}-yijiesuan-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    &-filters {
      display: flex;

      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }
  ::v-deep .row-height {
    background-color: rgb(var(--md-color-2));
  }

  .#{$md-prefix}-yijiesuan-table-box {
    position: relative;
    flex: 1;
    height: 0;
    box-sizing: border-box;
    overflow: hidden;
    .#{$md-prefix}-yijiesuan-zong {
      position: absolute;
      left: 2px;
      bottom: 10px;
      display: flex;
      &-item {
        display: flex;
        margin-right: 8px;
        & > div:nth-child(1),
        & > div:nth-child(3) {
          color: #aaa;
        }
        & > div:nth-child(2) {
          font-weight: 600;
        }
      }
    }
  }
  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
  }
  .#{$md-prefix}-chonghongBZ {
    margin-left: 4px;
    width: 16px;
    height: 16px;
    background-color: #f12933;
    border-radius: 8px;
    color: #ffffff;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
  }
}
.#{$md-prefix}-rukudh {
  cursor: pointer;
  color: rgb(var(--md-color-6));
  &:hover {
    color: rgb(var(--md-color-6));
    line-height: 20px;
    ::v-deep .#{$md-prefix}-link--inner {
      text-decoration: underline !important;
    }
    // &:after {
    //   display: none;
    // }
  }
}
// ::v-deep .#{$md-prefix}-base-table__body-wrapper {
//   height: 505px;
//   flex: unset;
// }
</style>
<style lang="scss">
.#{$md-prefix}-rukudantip {
  min-width: 30px;
  color: rgb(var(--md-color-6));
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
