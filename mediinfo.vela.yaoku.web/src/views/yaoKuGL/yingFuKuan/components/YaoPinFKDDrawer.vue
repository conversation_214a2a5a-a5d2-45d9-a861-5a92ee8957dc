<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('shouliqldrawer')"
    :modalClass="prefixClass('yingfukuandrawermodal')"
    @closed="handlerClose"
  >
    <div :class="prefixClass('drawer__alias')">
      <div :class="prefixClass('ypfkd-title')">
        <span :class="prefixClass('title-left')"> 药品付款单 </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleDaYin"
              noneBg
            >
              打印
            </md-button>
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <div :class="prefixClass('content-zhuangtai')">
          <md-descriptions direction="horizontal" label-align="left">
            <md-descriptions-item label="供货单位：" :column-percent="33">
              {{ data.gongHuoDWMC }}
            </md-descriptions-item>
            <md-descriptions-item label="单据号：" :column-percent="66">
              {{ data.id }}
            </md-descriptions-item>
            <md-descriptions-item label="验收人：" :column-percent="33">
              {{ data.jieSuanRXM }}
            </md-descriptions-item>
            <md-descriptions-item label="编制部门：" :column-percent="33">
              {{ data.weiZhiMC }}
            </md-descriptions-item>
            <md-descriptions-item label="编制日期：" :column-percent="33">
              {{ data.jieSuanSJ }}
            </md-descriptions-item>
          </md-descriptions>
        </div>
        <div :class="prefixClass('yaopinmx')">药品明细</div>
        <div :class="prefixClass('table-wrap')">
          <md-table
            :data="calaTableData"
            :columns="columns"
            :stripe="false"
            height="100%"
            :span-method="arraySpanMethod"
            :row-class-name="rowCssName"
          >
          </md-table>
        </div>
      </div>
    </div>
  </md-drawer>
</template>

<script>
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
import dayjs from 'dayjs';
export default {
  name: 'yaopinfk-detail-drawer',
  props: {
    size: { type: [String, Number], default: '75%' },
  },
  data() {
    return {
      drawer: false,
      title: '',
      shiFouJY: 0,
      zhuangTai: 0,
      columns: [
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 35,
          // align: 'center',
          formatter: (row, column, cellValue) => {
            if (cellValue === '总合计') return cellValue;
            return cellValue ? cellValue.slice(0, 1) : '';
          },
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 100,
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 108,
          formatter: (row, column, cellValue) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'yaoPinMCGG',
          label: '药品名称与规格',
          minWidth: 250,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          width: 200,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 52,
        },
        {
          prop: 'ruKuSL',
          label: '数量',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return Number(cellValue)?.toFixed(3);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          width: 100,
          formatter: (row, column, cellValue) => {
            return formatJiaGe_2(cellValue);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return formatJiaGe_2(cellValue);
          },
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 108,
          formatter: (row, column, cellValue) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'ruKuDH',
          label: '入库单号',
          width: 100,
        },
      ],
      danJuHao: '',
      data: {},
      calaTableData: [],
    };
  },
  // computed: {
  //   calaTableData() {
  //     let tableData = this.data.ruKuDMXDtos || [];
  //     // if (tableData.length === 0) return [];
  //     let jinJiaJE = 0;
  //     let lingShouJE = 0;
  //     tableData.forEach((item) => {
  //       jinJiaJE += Number(item.jinJiaJE) || 0;
  //       // lingShouJE += +itemLingShouJE;
  //     });
  //     tableData.push({
  //       yaoPinLXMC: '总合计',
  //       jinJiaJE,
  //       lingShouJE,
  //     });
  //     return tableData;
  //   },
  // },
  methods: {
    rowCssName({ row, rowIndex }) {
      const tableData = this.calaTableData;
      if (rowIndex === tableData.length - 1) {
        return this.prefixClass('heJiRow');
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      const tableData = this.calaTableData;
      if (rowIndex === tableData.length - 1) {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 7,
          };
        } else if (columnIndex === 7 || columnIndex === 8) {
          return {
            rowspan: 1,
            colspan: 1,
          };
        } else if (columnIndex === 9) {
          return {
            rowspan: 1,
            colspan: 3,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    //打开
    async openDrawer(option) {
      console.log(option, 'option');

      this.drawer = true;
      let jinJiaJE = 0;
      let lingShouJE = 0;
      try {
        this.data = option.data;

        option.data.ruKuDMXDtos &&
          option.data.ruKuDMXDtos.forEach((el) => {
            jinJiaJE += el.jinJiaJE;
            lingShouJE += el.lingShouJE;
          });
        this.calaTableData = option.data.ruKuDMXDtos;
        this.calaTableData.push({
          yaoPinLXMC: '总合计',
          jinJiaJE: option.jieSuanJE,
          lingShouJE,
        });
      } catch (e) {}
    },
    handleDaYin() {
      this.$emit('da-yin', this.data);
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
    handlerClose() {
      this.$emit('closed');
    },
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-yingfukuandrawermodal {
  position: initial !important;
}
</style>
<style lang="scss" scoped>
@import '~@/assets/styles/mixin.scss';

.#{$md-prefix}-shouliqldrawer {
  position: absolute;
  right: 0;
  top: 0;

  .#{$md-prefix}-drawer__alias {
    display: flex;
    flex-direction: column;
    height: 100%;

    .#{$md-prefix}-ypfkd-title {
      display: flex;
      justify-content: space-between;
      // background: #f0f5fb;
      background-color: rgb(var(--md-color-1));
      height: 36px;
      line-height: 36px;

      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;

        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 16px;
          height: 16px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
        }
      }

      .#{$md-prefix}-title-toolbar {
        margin-right: 18px;
      }

      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }

    .#{$md-prefix}-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      padding: 0 8px 8px 8px;

      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }

      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }

      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
        // height: 56px;.
        margin: 8px 0;
        // background-color: #f5f5f5;
        // border-radius: 4px;
        // padding-left: 10px;
      }

      .#{$md-prefix}-zhuangtaitag {
        display: inline-block;
        width: 58px;
        height: 24px;
        margin: 3px 8px 0 0;
        border-radius: 2px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;

        &.#{$md-prefix}-shouli {
          background-color: #e2efff;
          color: #1e88e5;
        }

        &.#{$md-prefix}-jujue {
          background-color: #f5f5f5;
          color: #999999;
        }
      }

      .#{$md-prefix}-description {
        display: flex;

        &-item {
          line-height: 20px;
          min-height: 20px;
          font-size: 14px;
          color: #333;
          padding: 5px 0;

          &__label {
            color: #666;
            margin-left: 8px;
          }

          &__content {
            padding-left: 5px;

            &.#{$md-prefix}-fontWeight {
              font-weight: bold;
            }

            .#{$md-prefix}-content-color {
              color: #666;
              font-weight: normal;
            }
          }
        }
      }

      .#{$md-prefix}-yaopinmx {
        flex-shrink: 0;
        position: relative;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        height: 36px;
        padding-left: 10px;
        font-size: 16px;
        color: #222;
        font-weight: bold;
        @include titleTrim($left: 0px, $width: 3px, $height: 16px);
      }

      .#{$md-prefix}-table-wrap {
        display: flex;
        flex: 1;
        min-height: 0;
        height: 0;
        overflow: hidden;
      }
    }
  }
}

.#{$md-prefix}-pos-bottom-direction {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 98%;
  justify-content: space-between;
}

::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}

::v-deep .#{$md-prefix}-descriptions__body {
  background-color: #f5f5f5;
  padding: 8px;
}

::v-deep .#{$md-prefix}-descriptions-item {
  color: #222;
}

::v-deep .#{$md-prefix}-descriptions-item {
  font-size: 14px;
}

::v-deep .#{$md-prefix}-heJiRow {
  background-color: #f5f5f5;
  font-weight: bold;

  & > td:nth-child(1) > div {
    width: 100% !important;
  }
}
</style>
