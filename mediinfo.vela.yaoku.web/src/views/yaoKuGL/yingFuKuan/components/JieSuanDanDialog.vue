<template>
  <div :class="prefixClass('jieSuanDan')">
    <md-dialog
      title="结算"
      v-model="dialogVisible"
      :before-save="handleSave"
      :body-loading="loading"
      @close="closeDialog"
    >
      <div :class="prefixClass('jieSuanDan-wrap')">
        <div :class="prefixClass('wrap-title')">
          <div
            :class="prefixClass('wrap-title-name')"
            style="
              position: absolute;
              top: 0px;
              text-align: center;
              width: 100%;
            "
          >
            结算单
          </div>
          <div
            :class="wrap - title - No"
            style="
              position: absolute;
              top: 5px;
              float: right;
              text-align: right;
              color: #000000;
              width: 100%;
            "
          >
            NO：{{ gongHuoDWXX.jieSuanDH }}
          </div>
        </div>
        <div :class="prefixClass('wrap-content')" style="margin-top: 40px">
          <div :class="prefixClass('wrap-content__p')">
            请付：{{ gongHuoDWXX.danWeiMC }}
          </div>
          <div :class="prefixClass('wrap-content__p')">
            <table style="border-spacing: 0">
              <tbody>
                <tr>
                  <td>药款：</td>
                  <td style="width: 60px">人民币</td>
                  <td>{{ gongHuoDWXX.jinJiaJEDX }}</td>
                </tr>
                <tr>
                  <td></td>
                  <td>￥</td>
                  <td>{{ gongHuoDWXX.jinJiaJE }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div :class="prefixClass('wrap-content__p')">
            开户行：{{ gongHuoDWXX.kaiHuYH }}
          </div>
          <p :class="prefixClass('wrap-content__p')">
            账号：{{ gongHuoDWXX.danWeiZH }}
          </p>
          <br />
          <p :class="prefixClass('wrap-content__p')">
            入库日期：{{ gongHuoDWXX.zhidanSJs }}
          </p>
          <p :class="prefixClass('wrap-content__p')">
            入库单号：{{ gongHuoDWXX.ruKuDHs }}
          </p>
          <p :class="prefixClass('wrap-content__p')">
            <label>备注：</label>
            <md-input
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
              :autosize="{ minRows: 1, maxRows: 4 }"
              v-model="remark"
              :class="prefixClass('border-none')"
            >
            </md-input>
          </p>
          <br />
          <p :class="prefixClass('wrap-content__p')">
            <span :class="prefixClass('wrap-content_span')">主任：</span>
            <span :class="prefixClass('wrap-content_span')">会计：</span>
            <span :class="prefixClass('float-r')"
              >结算时间：{{ currentDate }}</span
            >
          </p>
        </div>
      </div>
    </md-dialog>
  </div>
</template>

<script>
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import {
  addYingFuKJS,
  getGongHuoDWXX,
  getWeiJieSDXX,
} from '@/service/yaoPinYK/yingFuKuan';
export default {
  name: 'jiesuandan',
  data() {
    return {
      dialogVisible: false,
      faPiaoData: [],
      loading: false,
      gongHuoDWXX: {},
      currentDate: dayjs(new Date()).format('YYYY-MM-DD'),
      remark: '',
    };
  },
  methods: {
    showDialog(option) {
      this.loading = true;
      const faPiaoHMs = option.data.map((item) => item.faPiaoHM).join(',');
      this.faPiaoInfo = option.data;
      getWeiJieSDXX({ faPiaoHMs })
        .then((res) => {
          if (res == null) {
            MdMessage({
              type: 'warning',
              message: '发票不存在',
            });
            throw new Error('发票不存在');
          }
          this.gongHuoDWXX = res || {};
          this.dialogVisible = true;
          if (this.gongHuoDWXX.jinJiaJE) {
            this.gongHuoDWXX.jinJiaJE = Number(
              this.gongHuoDWXX.jinJiaJE,
            ).toFixed(3);
          }
        })
        .catch((x) => {
          this.reject();
        })
        .finally(() => {
          this.loading = false;
        });
      this.faPiaoData = option.data;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeDialog() {
      this.dialogVisible = false;
      this.remark = '';
      this.reject();
    },
    handleSave() {
      this.loading = true;
      const gongHuoDWXX = this.gongHuoDWXX;
      const faPiaoHMs = this.faPiaoInfo.map((item) => item.faPiaoHM);
      const params = {
        gongHuoDWID: gongHuoDWXX.danWeiID,
        gongHuoDWMC: gongHuoDWXX.danWeiMC,
        kaiHuYH: gongHuoDWXX.kaiHuYH,
        danWeiZH: gongHuoDWXX.danWeiZH,
        jieSuanSJ: this.currentDate, //结算时间
        beiZhu: this.remark,
        faPiaoHMs: faPiaoHMs,
        JieSuanDH: gongHuoDWXX.jieSuanDH, //结算单号
      };
      addYingFuKJS(params)
        .then((res) => {
          MdMessage({
            type: 'success',
            message: '结算成功',
          });
          this.resolve();
          this.dialogVisible = false;
        })
        .catch((e) => {
          MdMessage({
            type: 'warning',
            message: e,
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-jieSuanDan-wrap {
  width: 100%;
  min-height: 330px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  padding: 12px 24px;
  .#{$md-prefix}-wrap-title {
    position: relative;
    margin-bottom: 15px;
    .#{$md-prefix}-wrap-title-name {
      font-size: 18px;
      color: #222222;
      font-weight: bold;
      text-align: center;
      float: left;
      width: 100%;
    }
    .#{$md-prefix}-wrap-title-No {
      color: #222222;
      font-size: 14px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .#{$md-prefix}-wrap-content__p {
    color: #222222;
    font-size: 14px;
    line-height: 24px;
    display: flex;
    .#{$md-prefix}-wrap-content_span {
      margin-right: 70px;
    }
    .#{$md-prefix}-float-r {
      float: right;
      margin-left: auto;
    }
    label {
      flex-shrink: 0;
      line-height: 28px;
    }
    .#{$md-prefix}-border-none {
      ::v-deep textarea {
        border: none;
      }
    }
  }
}
</style>
