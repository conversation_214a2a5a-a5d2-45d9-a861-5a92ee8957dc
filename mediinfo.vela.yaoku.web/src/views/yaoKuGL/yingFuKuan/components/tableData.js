import formatJiaGe from '@/system/utils/formatJiaGe';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
import dayjs from 'dayjs';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
let jinJiaXSDW = '';
let lingShouXSDW = '';
let jinJiaJEXSDW = 2;
let lingShouJEXSDW = 2;
const canShu = async () => {
  try {
    const res = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'lingShouJEXSDWS',
      'lingShouJXSDWS',
      'jinJiaXSDWS',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJXSDWS') {
          lingShouXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  } catch (error) {}
  //如果是中药库
  const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
  // 判断进价零售价是否设置了值，没有则赋默认值
  jinJiaXSDW = jinJiaXSDW ? jinJiaXSDW : xiaoShuDianWS;
  lingShouXSDW = lingShouXSDW ? lingShouXSDW : xiaoShuDianWS;
};
canShu();
export default {
  faPiaoColumns: [
    {
      type: 'selection',
      // width: 24,
      showOverflowTooltip: false,
    },
    {
      prop: 'faPiaoHM',
      label: '发票号',
      minWidth: 118,
    },
    {
      prop: 'gongHuoDWMC',
      label: '供货单位',
      width: 120,
      showOverflowTooltip: true,
    },
    {
      prop: 'faPiaoJE',
      label: '发票金额',
      width: 110,
      formatter: (row, column, cellValue, index) => {
        return Number(cellValue).toFixed(3);
      },
    },
    {
      prop: 'faPiaoRQ',
      label: '日期',
      width: 100,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '-';
      },
    },
  ],
  faPiaoRightColumns: [
    {
      prop: 'yaoPinLXMC',
      label: '',
      width: 35,
      align: 'center',
      formatter: (row, column, cellValue) => {
        return cellValue ? cellValue.slice(0, 1) : '';
      },
    },
    {
      slot: 'yaoPinMCGG',
      label: '药品名称与规格',
      minWidth: 200,
      showOverflowTooltip: true,
    },
    {
      prop: 'chanDiMC',
      label: '产地名称',
      minWidth: 150,
      showOverflowTooltip: true,
    },
    {
      prop: 'baoZhuangDW',
      label: '单位',
      width: 60,
    },
    {
      prop: 'ruKuSL',
      label: '数量',
      width: 100,
      align: 'right',
      formatter: (row, column, cellValue) => {
        return Number(cellValue).toFixed(3);
      },
    },
    {
      prop: 'jinJia',
      label: '进价',
      width: 80,
      align: 'right',
      formatter: (row, column, cellValue) => {
        return formatJiaGe_2(cellValue, jinJiaXSDW);
      },
    },
    {
      prop: 'jinJiaJE',
      label: '进价金额',
      width: 110,
      align: 'right',
      formatter: (row, column, cellValue) => {
        return formatJiaGe_2(cellValue, jinJiaJEXSDW);
      },
    },
    {
      prop: 'shengChanPH',
      label: '批号',
      width: 120,
    },
    {
      prop: 'yaoPinXQ',
      label: '有效期',
      width: 100,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      prop: 'jiZhangSJ',
      label: '入库日期',
      width: 110,
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      prop: 'ruKuDH',
      label: '入库单号 ',
      width: 100,
      showOverflowTooltip: true,
    },
    {
      prop: 'beiZhu',
      label: '备注',
      width: 110,
      showOverflowTooltip: true,
    },
  ],
  gongHuoDWColumns: [
    {
      type: 'selection',
      showOverflowTooltip: false,
    },
    {
      prop: 'faPiaoHM',
      label: '发票号码',
      width: 138,
      field: true,
    },
    // {
    //   prop: 'faPiaoRQ',
    //   label: '发票日期',
    //   formatter: (row, column, cellValue) => {
    //     return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
    //   },
    //   width: 112,
    // },
    // {
    //   prop: 'faPiaoJE',
    //   label: '发票金额',
    //   width: 100,
    //   formatter: (row, column, cellValue) => {
    //     return Number(cellValue).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'yaoPinLXMC',
    //   label: '',
    //   width: 32,
    //   formatter: (row, column, cellValue) => {
    //     return cellValue ? cellValue.slice(0, 1) : '';
    //   },
    // },
    // {
    //   prop: 'yaoPinMCGG',
    //   label: '药品名称与规格',
    //   minWidth: 240,
    //   showOverflowTooltip: true,
    // },
    // {
    //   prop: 'chanDiMC',
    //   label: '产地名称',
    //   width: 160,
    //   showOverflowTooltip: true,
    // },
    // {
    //   prop: 'baoZhuangDW',
    //   label: '单位',
    //   width: 60,
    // },
    // {
    //   prop: 'ruKuSL',
    //   label: '数量',
    //   align: 'right',
    //   width: 100,
    //   formatter: (row, column, cellValue) => {
    //     return Number(cellValue).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'jinJia',
    //   label: '进价',
    //   align: 'right',
    //   width: 100,
    //   formatter: (row, column, cellValue) => {
    //     return formatJiaGe(cellValue);
    //   },
    // },
    // {
    //   prop: 'jinJiaJE',
    //   label: '进价金额',
    //   align: 'right',
    //   width: 100,
    //   formatter: (row, column, cellValue) => {
    //     return Number(cellValue).toFixed(3);
    //   },
    // },
    // {
    //   prop: 'shengChanPH',
    //   label: '生产批号',
    //   width: 120,
    // },
    // {
    //   prop: 'yaoPinXQ',
    //   label: '药品效期',
    //   width: 100,
    //   formatter: (row, column, cellValue) => {
    //     return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
    //   },
    // },
    {
      prop: 'jiZhangSJ',
      label: '入库日期',
      // minWidth: 110,
      field: true,
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      prop: 'ruKuDH',
      label: '入库单号',
      // minWidth: 110,
      field: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'ruKuJJJE',
      label: '入库进价金额',
      align: 'right',
      // minWidth: 130,
      field: true,
      formatter: (row, column, cellValue) => {
        return formatJiaGe_2(cellValue, jinJiaJEXSDW);
      },
      showOverflowTooltip: true,
    },
    {
      prop: 'jinXiaoCJ',
      label: '进销差价',
      align: 'right',
      // minWidth: 110,
      field: true,
      formatter: (row, column, cellValue) => {
        return formatJiaGe_2(row.jinXiaoCJ, jinJiaJEXSDW);
      },
      showOverflowTooltip: true,
    },
    {
      prop: 'ruKuLSJE',
      label: '入库零售金额',
      align: 'right',
      // minWidth: 110,
      field: true,
      formatter: (row, column, cellValue) => {
        return formatJiaGe_2(cellValue, lingShouJEXSDW);
      },
      showOverflowTooltip: true,
    },
    {
      prop: 'ruKuXZ',
      label: '入库性质',
      // minWidth: 110,
      showOverflowTooltip: true,
      field: true,
      control: true,
      fieldDisabled: true,
    },
  ],
};
