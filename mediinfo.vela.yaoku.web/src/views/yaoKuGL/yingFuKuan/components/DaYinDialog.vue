<template>
  <md-dialog
    title="打印"
    width="1100px"
    height="640px"
    :body-loading="loading"
    :append-to-body="false"
    v-model="dialogVisible"
    :before-close="handleCancel"
  >
    <div :class="prefixClass('dialog-box')" style="">
      <div :class="prefixClass('dialog-left')">
        <md-scrollbar :native="false" style="height: 100%">
          <md-checkbox-group v-model="checkedDaYinDan" :inline="false">
            <div v-for="item in daYinDanOptions" :key="item.biaoZhunDM">
              <md-checkbox
                v-if="item.shiFouXS === 1"
                :label="item.biaoZhunDM"
                :class="{ active: item.biaoZhunDM === activeDaYinDM }"
                @change="handlecheckDaYinDan(item)"
              >
                <span
                  style="display: block; width: 100%"
                  @click.prevent="handleLabel(item)"
                  >{{ item.biaoZhunMC }}</span
                >
                <md-icon size="14" name="youjiantou-s" class="icon-you" />
              </md-checkbox>
            </div>
          </md-checkbox-group>
        </md-scrollbar>
      </div>
      <div :class="prefixClass('dialog-right')">
        <span class="right-top">
          打印份数
          <md-input
            v-model.trim="number"
            v-number="{}"
            class="fenshu"
          ></md-input>
        </span>

        <div :class="prefixClass('dialog-right-content')">
          <!-- <div v-show="!hasData" :class="prefixClass('nodata')">
            <img src="@/assets/images/weiJianDang.svg" alt="暂无数据…" />
            <span>暂无内容</span>
          </div> -->
          <dayin-baobiao
            v-if="dialogVisible"
            :headers="headers"
            :params="params"
            :id="id"
            ref="baobiao"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <md-button
          type="primary"
          plain
          :disabled="loading"
          @click="handleCancel"
          >取消</md-button
        >
        <md-button type="primary" :loading="loading" @click="handleSave"
          >打印</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import DayinBaobiao from '@/components/DaYinBB';
import { getUserInfo } from '@/system/utils/local-cache';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { cloneDeep, isEmpty } from 'lodash';

const initTableData = () => {
  return {
    YKXT007: {
      id: 'YKXT007',
      data: [],
      params: { biaoTi: '结算单', jieSuanDID: '' },
    },
    YKXT022: {
      id: 'YKXT022',
      data: [],
      params: { biaoTi: '药品验收汇总单', jieSuanDID: '' },
    },
  };
};
const initData = () => {
  return [
    {
      biaoZhunMC: '结算单',
      biaoZhunDM: 'YKXT022',
      daYinLXID: 'YKXT022',
      daYinBT: '结算单',
      shunXuHao: 1,
      shiFouXS: 1,
    },
    {
      biaoZhunMC: '药品验收汇总单',
      biaoZhunDM: 'YKXT007',
      daYinLXID: 'YKXT007',
      daYinBT: '药品验收汇总单',
      shunXuHao: 2,
      shiFouXS: 1,
    },
  ];
};
export default {
  name: '',
  data() {
    return {
      number: 1,
      headers: {},
      dialogVisible: false,
      loading: false,
      checkedDaYinDan: [], //选中的打印单
      daYinDanOptions: initData(),
      daYinDDM: '', //当前打印单
      daYinDMC: '',
      params: null,
      id: '', //默认预览打印的id
      filterTableData: initTableData(),
      activeDaYinDM: '',
      paramsData: {},
    };
  },
  computed: {},
  created() {
    this.headers = getUserInfo([
      'WeiZhiID',
      'WeiZhiMC',
      'KeShiID',
      'KeShiMC',
      'BingQuID',
      'BingQuMC',
      'JiGouID',
      'JiGouMC',
      'ShuRuMLX',
      'CaiDanID',
    ]);
  },
  mounted() {
    this.bingQuOptions = [];
  },
  methods: {
    //隐藏显示弹框
    async showModal(data) {
      this.dialogVisible = true;
      this.number = 1;
      this.paramsData = cloneDeep(data.data);
      //初始化数据 左侧选中清空
      this.checkedDaYinDan = [];
      this.handleLabel(this.daYinDanOptions[0]);
    },
    //点击选中打印单
    handlecheckDaYinDan(item) {
      this.daYinDDM = item.biaoZhunDM;
      this.daYinDMC = item.biaoZhunMC;
      this.changeDaYinDM(0);
    },
    //点击列表
    changeDaYinDM(value) {
      let selectData = this.filterTableData[this.daYinDDM];
      let shenQingDIDs = selectData.data.map((item) => {
        return item.id;
      });
      var sqd = shenQingDIDs.join('|');
      let params = {};
      if (this.daYinDDM === 'YKXT007') {
        params = {
          jieSuanDID: this.paramsData.id,
        };
      }
      if (this.daYinDDM === 'YKXT022') {
        params = {
          jieSuanDID: this.paramsData.id,
        };
      }
      if (value) {
        params.biaoTi = selectData.params.biaoTi;
        this.id = selectData.id;
        this.params = params;
      }
      this.filterTableData[this.daYinDDM].params = params;
    },
    handleLabel(item) {
      this.activeDaYinDM = item.biaoZhunDM;
      this.daYinDDM = item.biaoZhunDM;
      this.daYinDMC = item.biaoZhunMC;
      this.changeDaYinDM(1);
    },

    handleSave() {
      if (!isEmpty(this.checkedDaYinDan)) {
        MdMessageBox.confirm('确认打印？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            //选中的打印单
            this.checkedDaYinDan.forEach((i) => {
              if (this.filterTableData[i].params) {
                if (i == 'YKXT007' || i == 'YKXT022') {
                  this.id = this.filterTableData[i].id;
                  this.params = this.filterTableData[i].params;
                  this.$refs.baobiao.print(Number(this.number));
                  // printByUrl(
                  //   this.filterTableData[i].id,
                  //   this.filterTableData[i].params,
                  //   this.number,
                  // );
                }
              }
            });
            this.dialogVisible = false;
          })
          .catch(() => {});
      } else {
        MdMessage({
          message: '请选择打印单',
          type: 'warning',
        });
      }
      //this.resolve(this.checked)
    },
    handleCancel() {
      this.dialogVisible = false;
      this.filterTableData = initTableData();
    },
  },
  components: {
    'dayin-baobiao': DayinBaobiao,
  },
};
</script>

<style scoped lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;
::v-deep .mediinfo-vela-yaoku-web-scrollbar__view {
  height: 100%;
  box-sizing: border-box;
}

.mediinfo-vela-yaoku-web-dialog-box {
  display: flex;
  height: 544px;
  padding: 0 8px;
  box-sizing: border-box;

  .right-top {
    display: flex;
    align-items: baseline;
  }
  .fenshu {
    width: 100px;
    margin-left: var(--md-spacing-3);
    margin-bottom: var(--md-spacing-2);
  }

  .mediinfo-vela-yaoku-web-dialog-left {
    flex: 0 0 auto;
    width: 250px;
    margin-right: 8px;
    border: 1px solid;
    border-color: #dddddd;

    .mediinfo-vela-yaoku-web-checkbox-group {
      margin-right: -1px;
    }

    .mediinfo-vela-yaoku-web-checkbox {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 12px;

      ::v-deep .mediinfo-vela-yaoku-web-checkbox__input {
        top: 0;
      }

      ::v-deep .mediinfo-vela-yaoku-web-checkbox__label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        width: 0;

        .icon-you {
          display: none;
          color: getCssVar('color-6');
        }
      }
    }

    .mediinfo-vela-yaoku-web-checkbox.active {
      // background-color: #b8ddff;
      background-color: getCssVar('color-2');

      ::v-deep .mediinfo-vela-yaoku-web-checkbox__label {
        .icon-you {
          display: block;
          color: getCssVar('color-6');
        }
      }
    }
  }

  .mediinfo-vela-yaoku-web-dialog-right {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;

    &-top {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    &-content {
      flex: 1 1 auto;
      background-color: #f5f5f5;
    }
  }
}
</style>
