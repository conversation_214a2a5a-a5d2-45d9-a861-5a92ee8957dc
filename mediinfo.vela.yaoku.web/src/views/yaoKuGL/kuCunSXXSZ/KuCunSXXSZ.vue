<template>
  <div :class="prefixClass('KuCunSXXSZ-wrap')">
    <md-tabs v-model="activeName" @tab-click="handleTabsChange">
      <!-- <md-tab-pane label="维护到药品规格" name="0" lazy>

      </md-tab-pane> -->
      <md-tab-pane label="维护到药品产地" name="1" lazy> </md-tab-pane>
    </md-tabs>

    <div :class="prefixClass('tables-wrap')">
      <div :class="prefixClass('yaoPingGG-box')">
        <div :class="prefixClass('yaoPingGG')">
          <div :class="prefixClass('top')">
            <md-form :model="query" label-width="110px" ref="searchForm" inline>
              <md-form-item label="药品类型" labelWidth="72px">
                <md-select
                  v-model="query.YaoPinLXDM"
                  placeholder="全部"
                  ref="yaoPinLXSelete"
                  @change="searchHandle"
                  style="width: 219px"
                  clearable
                >
                  <md-option
                    v-for="item in yaoPinLXOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </md-option>
                </md-select>
              </md-form-item>
              <md-form-item>
                <div :class="prefixClass('input__alias')">
                  <biz-yaopindw
                    v-model="tableData[activeName].keyWord"
                    placeholder="请输入药品名称选择"
                    :showSuffix="true"
                    type="kcsxx"
                    :weiZhiID="weiZhiID"
                    @change="handleChangeStr"
                    ref="bizSearch"
                  ></biz-yaopindw>
                </div>
              </md-form-item>
            </md-form>
            <div :class="prefixClass('top_buttons')">
              <md-button
                type="primary"
                :icon="prefixClass('icon-shuaxin')"
                noneBg
                @click="searchHandle"
                style="margin-right: 8px"
              >
                刷新</md-button
              >
              <md-button
                :class="prefixClass('changYongAN')"
                type="primary"
                :loading="loading"
                plain
                style="margin-right: 8px"
                @click="handlePLSC"
              >
                按消耗量设置
              </md-button>
              <md-button
                :class="prefixClass('changYongAN')"
                type="primary"
                :loading="loading"
                @click="handleSaveData"
              >
                保存
              </md-button>
            </div>
          </div>

          <div :class="prefixClass('border-table')">
            <div :class="prefixClass('table-rap')">
              <md-editable-table-pro
                v-table-enter
                v-model="tableData[activeName].dataList"
                :columns="tableData[activeName].columns"
                :data="tableData[activeName].dataList"
                height="100%"
                ref="table"
                :class="prefixClass('KuCunSXXSZ-table')"
                :cell-class-name="tableCellClassName"
                :showDefaultOperate="false"
                :hideAddButton="true"
                v-loading="tableLoading"
                element-loading-text="正在加载中..."
                addPosition="start"
                @selection-change="selectionChange"
                @sort-change="handleSortChange"
              >
                <template #yaoPinMC="{ row }">
                  <p :class="prefixClass('paddingBox')">{{ row.yaoPinBM }}</p>
                </template>
                <template #chanDiMC="{ row }">
                  <p :class="prefixClass('paddingBox')">{{ row.chanDiMC }}</p>
                </template>
                <template #baoZhuangDW="{ row }">
                  <p :class="prefixClass('paddingBox')">
                    {{ row.baoZhuangDW }}
                  </p>
                </template>
                <template #yaoPinMCGG="{ row }">
                  <p :class="prefixClass('paddingBox')">
                    {{ row.yaoPinMC }} {{ row.yaoPinGG }}
                  </p>
                </template>
                <template #guiGeID="{ row }">
                  <p :class="prefixClass('paddingBox')">{{ row.guiGeID }}</p>
                </template>
                <template #jiaGeID="{ row }">
                  <p :class="prefixClass('paddingBox')">{{ row.jiaGeID }}</p>
                </template>
                <template #kuCunSL="{ row }">
                  <p :class="prefixClass('paddingBox')">{{ row.kuCunSL }}</p>
                </template>
                <template #kuCunSX="{ row, $index }">
                  <md-input
                    :class="prefixClass('kuCun-inputNum')"
                    v-model="row.kuCunSX"
                    @change="handleInput($index, 'kuCunSX')"
                    v-number.float="{ decimal: xiaoShuDWS, min: 0 }"
                    ref="kuCunSX"
                  ></md-input>
                </template>
                <template v-slot:kuCunXX="{ row, $index }">
                  <md-input
                    :class="prefixClass('kuCun-inputNum')"
                    v-model="row.kuCunXX"
                    @change="handleInput($index, 'kuCunXX')"
                    v-number.float="{ decimal: xiaoShuDWS, min: 0 }"
                    ref="kuCunXX"
                  ></md-input>
                </template>
                <template v-slot:qingLingGDSL="{ row, $index }">
                  <md-input
                    :class="prefixClass('kuCun-inputNum')"
                    v-model="row.qingLingGDSL"
                    @change="handleInput($index, 'qingLingGDSL')"
                    v-number.float="{ decimal: 0, min: 0 }"
                    ref="qingLingGDSL"
                  ></md-input>
                </template>
                <template v-slot:caiGouGDSL="{ row, $index }">
                  <md-input
                    :class="prefixClass('kuCun-inputNum')"
                    v-model="row.caiGouGDSL"
                    @change="handleInput($index, 'caiGouGDSL')"
                    v-number.float="{ decimal: 0, min: 0 }"
                    ref="caiGouGDSL"
                  ></md-input>
                </template>
                <template v-slot:zuiXiaoKDL="{ row, $index }">
                  <md-input
                    :class="prefixClass('kuCun-inputNum')"
                    v-model="row.zuiXiaoKDL"
                    @change="handleInput($index, 'zuiXiaoKDL')"
                    v-number.float="{ decimal: 0, min: 0 }"
                    ref="zuiXiaoKDL"
                    :clearable="false"
                  ></md-input>
                </template>
              </md-editable-table-pro>
            </div>

            <div :class="prefixClass('pagination-wrap')">
              <md-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tableData[activeName].pagination.PageIndex"
                :page-sizes="[10, 20, 50, 100, 500, 1000]"
                :page-size="tableData[activeName].pagination.PageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableData[activeName].pagination.total"
              >
              </md-pagination>
            </div>
          </div>
        </div>
      </div>
      <AnKuCunXXSCDialog ref="anXiaoHLSZ" @getList="getList" />
    </div>
  </div>
</template>

<script>
import lyra from '@mdfe/lyra';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  AddYaoPinCLKZ,
  GetYaoPinCDJGSXSZCount,
  GetYaoPinCDJGSXSZList,
  GetYaoPinGGSXSZCount,
  GetYaoPinGGSXSZList,
} from '@/service/yaoPinYK/KuCunSXXSZ';
import commonData from '@/system/utils/commonData';
import { getWeiZhiID, getWeiZhiMC } from '@/system/utils/local-cache';
import { MdPagination } from '@mdfe/medi-ui';
import { debounce } from 'lodash';
import AnKuCunXXSCDialog from './ccomponents/AnXiaoHaoLDialog.vue';
export default {
  name: 'KuCunSXXSZ-wrap',
  data() {
    return {
      selections: [],
      activeName: '1',
      loading: false,
      query: {
        LikeQuery: '',
        YaoPinLXDM: '',
      },
      pagination: {
        PageIndex: 1,
        PageSize: 100,
        total: 0,
      },
      queryShow: [
        {
          label: '药品类型',
          key: 'label',
          valueKey: 'yaoPinLXDM',
          options: 'yaoPinLXOptions',
        },
      ],
      yaoPinLXOptions: commonData.yaoPinLBArr,
      dataList: [],
      dataListChangeList: [],
      weiZhiID: '',
      xiaoShuDWS: 3,
      tableData: [
        {
          columns: [
            {
              type: 'selection',
              width: 40,
              align: 'center',
              showOverflowTooltip: false,
            },
            {
              slot: 'yaoPinMCGG',
              prop: 'yaoPinGG',
              label: '药品名称规格',
              'min-width': 412,
              showOverflowTooltip: true,
            },
            {
              slot: 'baoZhuangDW',
              prop: 'baoZhuangDW',
              label: '单位',
              'min-width': 96,
            },
            {
              slot: 'guiGeID',
              prop: 'guiGeID',
              label: '规格ID',
              'min-width': 169,
            },
            {
              slot: 'kuCunSL',
              prop: 'kuCunSL',
              label: '库存数量',
              'min-width': 169,
            },
            {
              slot: 'kuCunSX',
              prop: 'kuCunSX',
              label: '库存上限',
              'min-width': 99,
              // formatter: row => {
              //   return row.kuCunSX ? row.kuCunSX : 0
              // }
            },
            {
              slot: 'kuCunXX',
              prop: 'kuCunXX',
              label: '库存下限',
              'min-width': 99,
              // formatter: row => {
              //   return row.kuCunXX ? row.kuCunXX : 0
              // }
            },
            {
              slot: 'qingLingGDSL',
              prop: 'qingLingGDSL',
              label: '请领固定数量',
              'min-width': 125,
              // formatter: row => {
              //   return row.qingLingGDSL ? row.qingLingGDSL : 0
              // }
            },
            {
              slot: 'caiGouGDSL',
              prop: 'caiGouGDSL',
              label: '采购固定数量',
              'min-width': 125,
              // formatter: row => {
              //   return row.caiGouGDSL ? row.caiGouGDSL : 0
              // }
            },
            {
              slot: 'zuiXiaoKDL',
              prop: 'zuiXiaoKDL',
              label: '最小开单量',
              'min-width': 125,
              // formatter: row => {
              //   return row.caiGouGDSL ? row.caiGouGDSL : 0
              // }
            },
          ],
          dataList: [],
          pagination: { PageIndex: 1, PageSize: 100, total: 0 },
          keyWord: '',
        },
        {
          columns: [
            {
              type: 'selection',
              width: 40,
              align: 'center',
              showOverflowTooltip: false,
            },
            // {
            //   slot: 'yaoPinMC',
            //   prop: 'yaoPinBM',
            //   label: '通用名',
            //   'min-width': 191
            // },
            {
              slot: 'yaoPinMCGG',
              prop: 'yaoPinGG',
              label: '药品名称规格',
              'min-width': 300,
              showOverflowTooltip: true,
            },
            {
              slot: 'chanDiMC',
              prop: 'chanDiMC',
              label: '产地',
              'min-width': 210,
            },
            {
              slot: 'baoZhuangDW',
              prop: 'baoZhuangDW',
              label: '单位',
              'min-width': 54,
            },
            {
              slot: 'jiaGeID',
              prop: 'jiaGeID',
              label: '价格ID',
              'min-width': 90,
            },
            {
              prop: 'baiFangWZ',
              label: '摆放位置',
              'min-width': 100,
              type: 'text',
              sortable: 'custom',
              hidden: false,
            },
            {
              slot: 'kuCunSL',
              prop: 'kuCunSL',
              label: '库存数量',
              'min-width': 90,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
            },
            {
              slot: 'kuCunSX',
              prop: 'kuCunSX',
              label: '库存上限',
              'min-width': 98,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
              // formatter: row => {
              //   return row.kuCunSX ? row.kuCunSX : 0
              // }
            },
            {
              slot: 'kuCunXX',
              prop: 'kuCunXX',
              label: '库存下限',
              'min-width': 98,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
              // formatter: row => {
              //   return row.kuCunXX ? row.kuCunXX : 0
              // }
            },
            {
              slot: 'qingLingGDSL',
              prop: 'qingLingGDSL',
              label: '请领固定数量',
              'min-width': 123,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
              // formatter: row => {
              //   return row.qingLingGDSL ? row.qingLingGDSL : 0
              // }
            },
            {
              slot: 'caiGouGDSL',
              prop: 'caiGouGDSL',
              label: '采购固定数量',
              'min-width': 122,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
              // formatter: row => {
              //   return row.caiGouGDSL ? row.caiGouGDSL : 0
              // }
            },
            {
              slot: 'zuiXiaoKDL',
              prop: 'zuiXiaoKDL',
              label: '最小开单量',
              'min-width': 122,
              formatter: (row, column, cellValue, index) => {
                return Math.abs(Number(cellValue)).toFixed(this.xiaoShuDWS);
              },
            },
          ],
          dataList: [],
          pagination: { PageIndex: 1, PageSize: 100, total: 0 },
          keyWord: '',
          cssPrefix: null,
        },
      ],
      tableLoading: false,
      baiFangWZOrderDir: '',
    };
  },
  PageIndex: 1,
  PageSize: 100,
  total: 0,
  mounted() {
    this.getList();
    this.getInitPageSize();
    if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
      this.xiaoShuDWS = 2;
    }
  },
  async created() {
    // WeiZhiLXDM = 3 药房 WeiZhiLXDM = 4 药库
    const { WeiZhiLXDM } = lyra.getShareDataSync();
    this.tableData[1].columns[5].hidden = WeiZhiLXDM == 4 ? false : true;
    this.getZiDianSJ();
    this.getDangQianWZID();
    document.addEventListener('keyup', this.handleKeyupSearch);
  },
  beforeDestroy() {
    document.removeEventListener('keyup', this.handleKeyupSearch);
  },
  methods: {
    //升降序
    handleSortChange({ column, prop, order }) {
      if (!order) return;
      // this.sortType = prop == 'baiFangWZ' ? 1 : '2';
      this.baiFangWZOrderDir = order == 'ascending' ? 1 : 2;
      let pagination = this.tableData[this.activeName].pagination;
      pagination.PageIndex = 1;
      this.getList();
    },
    getInitPageSize: debounce(function () {
      const cssPrefix = process.env.VUE_APP_NAMESPACE;
      this.$nextTick(() => {
        const tableBody = this.$refs.table.$el.querySelector(
          `.${cssPrefix}-base-table__body-wrapper`,
        );
        const tableBodyHeight = tableBody.clientHeight;
        this.tableData[0].pagination.PageSize = 100;
        // this.tableData[0].pagination.PageSize =
        //   this.tableData[1].pagination.PageSize = Math.floor(
        //     tableBodyHeight / 33,
        //   );
        this.getList();
      });
    }, 500),
    async handleTabsChange() {
      this.dataListChangeList = [];
      if (
        (!this.tableData[this.activeName].keyWord ||
          this.tableData[this.activeName].keyWord.length == 0) &&
        this.tableData[this.activeName].dataList.length == 0
      ) {
        await this.getList();
      }
      await this.$nextTick;
      this.$refs.table.$refs.table.doLayout();
    },
    async handleChangeStr(data) {
      let id = '';
      //if (this.activeName == 0) {
      id = data.jiaGeID; //获取上下限使用大规格获所有
      // } else {
      //   id = data.jiaGeID
      // }
      this.query.LikeQuery = id;
      // await this.getList()
      await this.searchHandle();
    },
    tableCellClassName({ columnIndex }) {
      let index = 3;
      if (this.activeName == 0) {
        index = 3;
      } else {
        index = 5;
      }
      if (columnIndex < index) {
        return this.prefixClass('KuCunSXXSZ-edit-cell');
      }
      return '';
    },
    //table选中事件
    selectionChange(selection) {
      this.selections = selection;
    },
    searchHandle: debounce(function () {
      let pagination = this.tableData[this.activeName].pagination;
      pagination.PageIndex = 1;
      this.baiFangWZOrderDir = '';
      this.$refs.table.invokeTableMethod('clearSort');
      this.getList();
    }, 500),
    async handleCurrentChange(page) {
      let pagination = this.tableData[this.activeName].pagination;
      pagination.PageIndex = page;
      await this.handleSaveData();
    },
    async handleSizeChange(size) {
      let pagination = this.tableData[this.activeName].pagination;
      pagination.PageSize = size;
      await this.handleSaveData();
    },
    // 获取位置ID
    getDangQianWZID() {
      this.weiZhiID = decodeURIComponent(getWeiZhiID());
    },
    handleInput(index, str) {
      let curData = this.tableData[this.activeName].dataList[index];
      let changeData = this.dataListChangeList;
      //清空以后变为空
      if ((str == 'kuCunSX' || str == 'kuCunXX') && curData[str].length == 0) {
        if (str == 'kuCunSX') {
          curData['kuCunSX'] = null;
        }
        if (str == 'kuCunXX') {
          curData['kuCunXX'] = null;
        }
      } else {
        // if (curData[str].length == 0) {
        //     curData[str] = 0
        //  }
        if (str == 'qingLingGDSL' && curData[str].length == 0) {
          curData['qingLingGDSL'] = null;
        }
        if (str == 'caiGouGDSL' && curData[str].length == 0) {
          curData['caiGouGDSL'] = null;
        }
        if (str == 'zuiXiaoKDL' && curData[str].length == 0) {
          curData['zuiXiaoKDL'] = 0;
        }
        if (
          str == 'kuCunSX' &&
          curData.kuCunXX &&
          Number(curData.kuCunXX) > Number(curData.kuCunSX)
        ) {
          this.$message.warning('库存下限不能大于库存上限');
          curData.kuCunXX = 0;
          return;
        }
        if (
          str == 'kuCunXX' &&
          curData.kuCunSX &&
          Number(curData.kuCunXX) > Number(curData.kuCunSX)
        ) {
          this.$message.warning('库存下限不能大于库存上限');
          curData.kuCunXX = 0;
          return;
        }
      }

      if (changeData && changeData.length == 0) {
        changeData.push(curData);
      } else {
        let findIndeX = changeData.findIndex(
          (item) =>
            item.yaoPinID == curData.yaoPinID &&
            item.guiGeID == curData.guiGeID,
        );
        if (findIndeX == -1) {
          changeData.push(curData);
        }
      }
    },
    getZiDianSJ() {
      let daiMaList = ['YP0005'];
      let optionKeys = ['yaoPinLXOptions'];
      getYaoPinShuJuYZYList(daiMaList).then((res) => {
        res.forEach((item, index) => {
          let options = [];
          item.zhiYuList.forEach((item1) => {
            let data = {
              label: item1.biaoZhunMC,
              value: item1.biaoZhunDM,
            };
            options.push(data);
          });
          this[optionKeys[index]] = options;
        });
      });
    },
    //保存
    handleSaveData() {
      if (this.dataListChangeList.length == 0) {
        this.getList();
        return;
      }
      this.dataListChangeList.map((res) => {
        if (!res.jiaGeID) {
          res.jiaGeID = '';
        }
        if (!res.guiGeID) {
          res.guiGeID = '';
        }
        // if (Number(res.kuCunSX) == 0) {
        //   res.kuCunSX = '1';
        // }
        if (res.caiGouGDSL == '') {
          res.caiGouGDSL = null;
        }
        if (res.qingLingGDSL == '') {
          res.qingLingGDSL = null;
        }
        if (res.kuCunSX == '') {
          res.kuCunSX = null;
        }
        if (res.kuCunXX == '') {
          res.kuCunXX = null;
        }
      });
      this.loading = true;
      AddYaoPinCLKZ(this.dataListChangeList)
        .then((res) => {
          this.$message({ message: '保存成功', type: 'success' });
          this.dataListChangeList = [];
          this.getList();
        })
        .finally((e) => {
          this.loading = false;
        });
    },
    async getList() {
      let pagination = this.tableData[this.activeName].pagination;
      let data = {
        PageIndex: pagination.PageIndex,
        PageSize: pagination.PageSize,
        ...this.query,
        baiFangWZOrderDir: this.baiFangWZOrderDir,
      };
      this.tableLoading = true;
      if (this.activeName == 0) {
        GetYaoPinGGSXSZList(data).then((res) => {
          this.tableData[0].dataList = res;
          this.tableLoading = false;
        });
        GetYaoPinGGSXSZCount(this.query).then((res) => {
          this.tableData[0].pagination.total = res;
        });
      } else {
        GetYaoPinCDJGSXSZList(data).then((res) => {
          this.tableData[1].dataList = res;
          this.tableLoading = false;
        });
        GetYaoPinCDJGSXSZCount(this.query).then((res) => {
          this.tableData[1].pagination.total = res;
        });
      }
    },
    //按消耗量
    handlePLSC() {
      this.$refs.anXiaoHLSZ.showModal(this.selections);
    },
  },
  components: {
    'md-pagination': MdPagination,
    'biz-yaopindw': BizYaoPinDW,
    AnKuCunXXSCDialog,
  },
};
</script>
<style lang="scss" scoped>
.#{$md-prefix}-KuCunSXXSZ-wrap {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.#{$md-prefix}-tables-wrap {
  flex: 1;
  border: 8px solid #f0f2f5;
  height: 100%;
  overflow: hidden;
}

::v-deep .#{$md-prefix}-KuCunSXXSZ-wrap .#{$md-prefix}-tabs__header {
  margin-bottom: 0px;
}

.#{$md-prefix}-select__alias {
  width: 140px;
  height: 30px;
  margin-right: 8px;
}

.#{$md-prefix}-changYongAN {
  min-width: 62px;
}

.#{$md-prefix}-top {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .#{$md-prefix}-top-form {
    display: flex;

    .#{$md-prefix}-form-jigoutree {
      width: 260px;
      margin-right: 8px;
    }
  }

  &_buttons {
    display: flex;
    justify-content: flex-start;
    height: 30px;
  }
}

.#{$md-prefix}-icon-right {
  margin-right: 4px;
}

.#{$md-prefix}-yaoPingGG-box {
  height: 100%;
  background: #eaeff3;
}

.#{$md-prefix}-yaoPingGG {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .#{$md-prefix}-border-table {
    flex: 1;
    padding: 0 8px 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .#{$md-prefix}-pagination-wrap {
      text-align: right;
    }

    .#{$md-prefix}-table-rap {
      flex: 1;
      overflow: hidden;
    }
  }
}

::v-deep .#{$md-prefix}-base-table__body-wrapper {
  height: 100%;
  overflow: auto;
}

::v-deep .#{$md-prefix}-editable-table__container {
  height: 100%;
}

::v-deep
  .#{$md-prefix}-kuCun-inputNum
  .#{$md-prefix}-input.#{$md-prefix}-input--suffix
  .#{$md-prefix}-input__inner {
  padding-left: 8px;
  text-align: left;
  height: 32px !important;
  line-height: 32px !important;
}

::v-deep .#{$md-prefix}-input-number {
  width: 100%;
}

::v-deep .#{$md-prefix}-KuCunSXXSZ-table {
  height: 100%;
}

::v-deep .#{$md-prefix}-KuCunSXXSZ-table .#{$md-prefix}-KuCunSXXSZ-edit-cell {
  background: #f5f5f5;
}

p.#{$md-prefix}-paddingBox {
  padding: 6px 8px;
  box-sizing: border-box;
  // overflow: hidden;
}
.#{$md-prefix}-border-table .#{$md-prefix}-pagination {
  justify-content: flex-end;
}
</style>
