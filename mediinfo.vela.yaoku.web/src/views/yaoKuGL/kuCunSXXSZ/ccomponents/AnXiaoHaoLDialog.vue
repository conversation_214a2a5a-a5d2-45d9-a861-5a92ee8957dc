<template>
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="560px"
    height="300px"
    title="按消耗量设置"
    saveText="保存"
    :isShowYY="false"
    @submit="handleSave(2)"
    @handleAllAccept="handleSave(1)"
  >
    <div class="flex-kcsxx dialogDIV">
      <md-form v-loading="loading" use-status-icon label-width="65px">
        <md-form-item label="消耗时间">
          <md-input
            v-model="formModel.xiaoHaoTS"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @change="handleInput"
            :clearable="false"
          >
            <template #suffix>
              <i class="md-input__icon icon-text">天内</i>
            </template>
          </md-input>
        </md-form-item>
        <md-form-item label="消耗日期">
          <div class="flex-kcsxx">
            <md-date-picker
              v-model="formModel.xiaoHaoKSSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleKaiShiSJ"
            ></md-date-picker>
            <span class="padding-r-l">-</span>
            <md-date-picker
              v-model="formModel.xiaoHaoJSSJ"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleJieShuSJ"
            ></md-date-picker>
          </div>
        </md-form-item>
      </md-form>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import BlueDialog from '@/components/blue-dialog/index.vue';
import { getZhangBuLBSelectList } from '@/service/xiTongSZ/zhangBuLBWH';
import { GetYaoPinBFWZSelect } from '@/service/yaoPin/yaoPinZD';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { UpdateYaoPinXXBYXHL } from '@/service/yaoPinYK/KuCunSXXSZ';
import { MdMessage } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
const formModelInit = () => {
  return {
    xiaoHaoTS: 3,
    xiaoHaoKSSJ: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
    xiaoHaoJSSJ: dayjs().format('YYYY-MM-DD'),
  };
};
export default {
  name: '',
  data() {
    return {
      selections: [], //表格选中的数据
      visibleDialog: false,
      loading: false,
      zhangBuLBOptions: [],
      duLiFLOptions: [],
      formModel: formModelInit(),
      resolve: null,
      reject: null,
      baiFangWZList: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    showModal(selections) {
      this.visibleDialog = true;
      this.selections = selections;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    //消耗时间事件
    handleInput(val) {
      if (val) {
        this.formModel.xiaoHaoKSSJ = dayjs()
          .subtract(Number(val) - 1, 'day')
          .format('YYYY-MM-DD');
      }
    },
    // 消耗开始事件
    handleKaiShiSJ(val) {
      if (val) {
        const xiaoHaoKSSJ = val.replace(/-/g, '');
        const xiaoHaoJSSJ = this.formModel.xiaoHaoJSSJ.replace(/-/g, '');
        if (Number(xiaoHaoKSSJ) > Number(xiaoHaoJSSJ)) {
          this.$message({
            message: '开始时间不能大于当前时间！',
            type: 'warning',
          });
          return;
        }
        this.formModel.xiaoHaoTS =
          Number(xiaoHaoJSSJ) - Number(xiaoHaoKSSJ) + 1;
      } else {
        this.formModel.xiaoHaoTS = null;
      }
    },
    // 消耗结束时间事件
    handleJieShuSJ(val) {
      if (val) {
        const xiaoHaoKSSJ = this.formModel.xiaoHaoKSSJ.replace(/-/g, '');
        const xiaoHaoJSSJ = val.replace(/-/g, '');
        if (Number(xiaoHaoKSSJ) > Number(xiaoHaoJSSJ)) {
          this.$message({
            message: '结束时间不能小于开始时间！',
            type: 'warning',
          });
          return;
        }
        this.formModel.xiaoHaoTS =
          Number(xiaoHaoJSSJ) - Number(xiaoHaoKSSJ) + 1;
      } else {
        this.formModel.xiaoHaoTS = null;
      }
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    init() {
      getYaoPinShuJuYZYList(['YP0006']).then((res) => {
        this.duLiFLOptions = res[0].zhiYuList;
      });
      getZhangBuLBSelectList().then((res) => {
        this.zhangBuLBOptions = res;
      });
    },
    searchBaiFangWZ(val) {
      if (val == '' || val == undefined) {
        this.baiFangWZList = [];
        return;
      }
      this.getBaiFangWZList(val);
    },
    // 获取摆放位置列表
    async getBaiFangWZList(val) {
      try {
        if (val.length == 0) return;
        const res = await GetYaoPinBFWZSelect({
          baiFangWZ: val,
        });
        if (res) {
          this.baiFangWZList = res;
        }
        this.baiFangWZList = res;
        this.$forceUpdate();
      } catch (e) {}
    },
    //1是全部，2是保存
    handleSave(val) {
      if (val === 2 && this.selections.length === 0) {
        MdMessage.warning('请勾选数据！');
        return;
      }
      const params = {
        ...this.formModel,
        baoCunLX: val,
        yaoPinMXList:
          val === 2
            ? this.selections.map((m) => {
                return {
                  jiaGeID: m.jiaGeID,
                  guiGeID: m.guiGeID,
                  yaoPinID: m.yaoPinID,
                };
              })
            : [],
      };
      this.loading = true;
      UpdateYaoPinXXBYXHL(params)
        .then((res) => {
          this.resolve(res);
          MdMessage.success('保存成功！');
          this.closeModal();
          this.$emit('getList');
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
::v-deep .#{$md-prefix}-scrollbar__view {
  display: flex;
  justify-content: center;
}
::v-deep .#{$md-prefix}-form-item--status-icon-inline {
  padding-right: 0px;
}

.dialogDIV {
  padding-top: 20px;
  width: 400px;
}
.flex-kcsxx {
  display: flex;
  align-items: center;
}

.padding-r-l {
  padding: 0 8px;
}

.icon-text {
  font-style: normal;
}
</style>
