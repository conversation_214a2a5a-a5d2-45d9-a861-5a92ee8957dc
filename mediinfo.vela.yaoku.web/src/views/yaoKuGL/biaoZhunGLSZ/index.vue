<template>
  <div class="HISYK-TPNYPWH">
    <div class="HISYK-TPNYPWH-header">
      <span class="mr-8">药品属性</span>
      <md-select
        v-model="query.yaoPinSX"
        placeholder="请选择"
        style="width: 200px"
        class="mr-8"
        filterable
        :clearable="false"
        :class="prefixClass('procurement-date')"
        @change="handleSearch"
      >
        <md-option
          v-for="item in yaoPinSXList"
          :key="item.biaoZhunDM"
          :label="item.biaoZhunMC"
          :value="item.biaoZhunDM"
        >
        </md-option>
      </md-select>
      <span class="mr-8">状态</span>
      <md-select
        v-model="query.sheZhiZT"
        class="mr-8"
        style="width: 160px"
        :class="prefixClass('procurement-date')"
        placeholder="选择状态"
        :clearable="false"
        @change="handleSearch"
      >
        <md-option value="0" label="全部"></md-option>
        <md-option value="1" label="已设置"></md-option>
        <md-option value="2" label="未设置"></md-option>
      </md-select>
      <BizYaoPinDW
        v-model="query.likeQuery"
        placeholder="请输入药品名称"
        class="ypmc mr-8"
        style="width: 220px"
        @change="handleChangeYP"
      />
      <md-checkbox v-model="onlyJingPin" @change="handleSearch"
        >仅展示竞品</md-checkbox
      >
      <md-button
        type="primary"
        :icon="prefixClass('icon-dayinji')"
        noneBg
        style="margin-right: 8px; float: right"
        @click="handleYuLan"
        >预览</md-button
      >
    </div>
    <div class="HISYK-TPNYPWH-container" v-loading="fenLeiLoading">
      <div class="HISYK-TPNYPWH-Aside">
        <md-table-pro
          :columns="leftcolumns"
          autoLoad
          height="100%"
          highlight-current-row
          :onFetch="handleFetch"
          ref="leftTable"
          @row-click="handleRowClick"
          :pagination="{ simple: true, layout: 'prev, jumper, total,next' }"
        >
        </md-table-pro>
      </div>
      <div class="HISYK-TPNYPWH-main" v-loading="rightLoading">
        <div class="yaopindetail">
          <md-title
            label="标准规格"
            type="grace"
            style="margin-bottom: 6px"
          ></md-title>
          <span class="mr-8">标准规格</span>
          <md-input width="220px" v-model="yaoPinDetail.yaoPinGG"> </md-input>
        </div>
        <div class="YaoPintitle">
          <md-title label="药品明细" type="grace"></md-title>
        </div>
        <div class="TPNYPWH-main-content">
          <md-editable-table-pro
            v-table-enter
            v-model="yaoPinDetail.jiCaiYPGGList"
            row-key="id"
            height="100%"
            :columns="columns"
            auto-fill
            auto-focus
            hideAddButton
            id="mdEditTable"
            ref="mdEditTable"
            :showDefaultOperate="false"
          >
            <template #zhuanHuanBi="{ $index }">
              <md-input
                v-number.float="{ min: 0 }"
                v-model="yaoPinDetail.jiCaiYPGGList[$index].zhuanHuanBi"
              ></md-input>
            </template>
          </md-editable-table-pro>
        </div>
        <div class="HISYK-TPNYPWH-footer">
          <md-button class="mr-8 btnWidth" @click="handleCancel"
            >取消</md-button
          >
          <md-button class="btnWidth" type="primary" @click="handleSave"
            >保存</md-button
          >
        </div>
      </div>
    </div>
    <dayin-dialog
      ref="daYinDialog"
      :params="params"
      :id="'YKXT031'"
      :fileName="'标准规格'"
      :title="'标准规格打印预览'"
    />
  </div>
</template>

<script>
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  getJiCaiYPFZList,
  GetJiCaiYPFZCount,
  GetJiCaiYPGGList,
  SaveJiCaiYPGGList,
} from '@/service/yaoPinYK/JiCaiJHD';
import {
  MdMessage,
  MdMessageBox,
  useNamespace,
  MdTooltip,
} from '@mdfe/medi-ui';
import { h } from 'vue';
import { cloneDeep } from 'lodash';
import { MdEditableTablePro } from '@mdfe/medi-ui-pro';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import { focusEditTableDom } from './focusEditTable.js';
import DaYinDialog from '@/components/DaYinDialog.vue';

export default {
  name: 'BiaoZhunGLSZ',
  data() {
    return {
      onlyJingPin: false,
      params: {},
      leftcolumns: [
        {
          prop: 'yaoPinMC',
          label: '药品名称',
        },
        {
          prop: 'chanDiMC',
          label: '产地',
        },
      ],
      query: {
        yaoPinSX: '',
        sheZhiZT: '0',
        likeQuery: {},
      },
      currentRow: {},
      yaoPinSXList: [],
      fenLeiLoading: false,
      columns: [
        {
          label: '药品名称与规格',
          prop: 'yaoPinMCYGG',
          width: 270,
          formatter(v) {
            if (v.yaoPinMC) {
              return v.yaoPinMC + ' ' + v.yaoPinGG;
            } else {
              return '';
            }
          },
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          label: '包装量',
          prop: 'baoZhuangSL',
          width: 80,
          type: 'text',
          showOverflowTooltip: false,
        },
        {
          label: '最小单位',
          prop: 'zuiXiaoDW',
          width: 80,
          type: 'text',
          showOverflowTooltip: false,
        },
        {
          label: '包装单位',
          prop: 'baoZhuangDW',
          width: 80,
          type: 'text',
          showOverflowTooltip: false,
        },
        {
          label: '剂型',
          prop: 'jiXingMC',
          width: 80,
          type: 'text',
          showOverflowTooltip: false,
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          type: 'text',
          minWidth: 200,
          showOverflowTooltip: true,
        },
        {
          slot: 'zhuanHuanBi',
          prop: 'zhuanHuanBi',
          width: 140,
          formatter(v) {
            return v.zhuanHuanBi;
          },
          renderHeader: () => {
            return h(
              'div',
              {
                style:
                  'display: flex;justify-content: space-between;align-items: center;',
              },
              [
                h('div', [
                  h('span', { style: { color: 'red' } }, '*'),
                  h('span', '转换比'),
                ]),
                h(
                  MdTooltip,
                  { effect: 'light' },
                  {
                    content: () =>
                      h(
                        'span',
                        { style: 'color: #333;font-size:12px;' },
                        '药品最小规格与标准规格的比值',
                      ),
                    default: () =>
                      h('md-icon', {
                        class: 'mediinfo-vela-yaoku-web-icon-tixing-s',
                        style: 'color:#aaa;cursor:pointer;margin-left:4px',
                      }),
                  },
                ),
              ],
            );
          },
        },
      ],
      originData: [],
      yaoPinDetail: {
        yaoPinGG: '',
        jiCaiYPGGList: [],
      },
      rightLoading: false,
    };
  },
  setup() {
    const nsIcon = useNamespace('icon');
    const nsYP = useNamespace('yaopin');
    return {
      nsIcon,
      nsYP,
    };
  },
  async mounted() {
    const data = await getYaoPinShuJuYZYList(['YP0001']);
    this.yaoPinSXList = data[0].zhiYuList;
    this.yaoPinSXList.unshift({
      biaoZhunDM: '',
      biaoZhunMC: '全部',
    });
  },
  methods: {
    // 获取左侧列表
    async handleFetch({ page, pageSize }, config) {
      const params = {
        yaoPinLXDM: 1,
        yaoPinSXDM: this.query.yaoPinSX,
        queryLike: '',
        yaoPinID: this.query.likeQuery.yaoPinID,
        chanDiID: this.query.likeQuery.chanDiID,
        sheZhiZT: this.query.sheZhiZT,
        onlyJingPin: this.onlyJingPin,
        pageIndex: page,
        pageSize,
      };
      const [items, total] = await Promise.all([
        getJiCaiYPFZList(params),
        GetJiCaiYPFZCount(params),
      ]);
      this.$nextTick(() => {
        this.$refs.leftTable.getComp('table').setCurrentRow(items[0]);
        this.handleRowClick(items[0]);
      });
      return { items, total };
    },
    handleSearch() {
      this.$refs.leftTable.search();
    },
    handleYuLan() {
      this.params = {
        yaoPinLXDM: 1,
        yaoPinSXDM: this.query.yaoPinSX,
        queryLike: this.query.likeQuery.yaoPinMC,
        sheZhiZT: this.query.sheZhiZT,
        onlyJingPin: this.onlyJingPin,
        pageIndex: 1,
        pageSize: 999,
      };
      this.$refs.daYinDialog.showModal();
    },
    //左侧点击事件
    handleRowClick(row) {
      if (row.fenZuID == this.currentRow.fenZuID) return;
      this.currentRow = row;
      this.handleRightData(row);
    },
    handleChangeYP(val) {
      this.query.likeQuery = val;
      this.handleSearch();
    },
    // 获取右侧详情
    async handleRightData(row) {
      try {
        this.rightLoading = true;
        this.yaoPinDetail = await GetJiCaiYPGGList({
          chanDiID: row.chanDiID,
          yaoPinID: row.yaoPinID,
        });
      } catch (e) {
        console.error(e);
      } finally {
        this.rightLoading = false;
      }
    },
    async handleSave() {
      try {
        this.rightLoading = true;
        const yaoPinDetail = cloneDeep(this.yaoPinDetail);
        // if (!yaoPinDetail.yaoPinGG) {
        //   MdMessageBox.confirm(``, '请设置标准规格！', {
        //     confirmButtonText: '我知道了',
        //     showCancelButton: false,
        //     type: 'warning',
        //   });
        //   return;
        // }
        yaoPinDetail.biaoZhunGGBZ = 1;
        // 判断表格是否有空数据
        const isEmptyIndex = yaoPinDetail.jiCaiYPGGList.findIndex(
          (fl) => !fl.zhuanHuanBi,
        );

        if (isEmptyIndex > -1) {
          await MdMessageBox.confirm(``, '请填写药品转换比！', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            type: 'warning',
          });
          focusEditTableDom({
            rowIndex: isEmptyIndex,
            columns: this.columns,
            key: 'zhuanHuanBi',
          });
          return;
        }
        await SaveJiCaiYPGGList(yaoPinDetail);
        MdMessage.success('保存成功！');
        this.handleRightData(this.currentRow);
      } catch (e) {
        console.error(e);
      } finally {
        this.rightLoading = false;
      }
    },
    async handleCancel() {
      await MdMessageBox.confirm(`确定要取消吗？`, '操作提醒！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      this.handleRightData(this.currentRow);
    },
  },
  components: {
    'md-editable-table-pro': MdEditableTablePro,
    BizYaoPinDW,
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-TPNYPWH {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  &-header {
    background-color: #fff;
    padding: getCssVar('spacing-3');
  }

  &-container {
    flex: 1;
    display: flex;
    min-height: 0;
    justify-content: space-between;
    background-color: #f0f2f5;
    padding: getCssVar('spacing-3');
    overflow: hidden;
  }

  &-Aside {
    display: flex;
    flex-direction: column;
    width: 320px;
    background-color: #fff;
    margin-right: getCssVar('spacing-3');
    overflow: hidden;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;

    .Aside-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .Aside-scrollbar {
      flex: 1;
      min-height: 0;
      margin-top: getCssVar('spacing-3');

      &.empty {
        justify-content: center;
      }

      .type-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin-bottom: getCssVar('spacing-3');
        padding: 0 getCssVar('spacing-3');
        cursor: pointer;

        .name {
          flex: 1;
        }

        .edit {
          display: none;
        }

        &.active {
          background-color: getCssVar('color-2');
        }

        &:hover {
          background-color: getCssVar('color-1');

          .edit {
            display: block;
          }
        }
      }
    }
  }

  &-main {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    overflow: hidden;
    .yaopindetail {
      height: 76px;
      background: linear-gradient(270deg, #f2f6fc 0%, #e9f4fe 100%);
      margin: 8px;
      padding: 8px;
      box-sizing: border-box;
    }
    .YaoPintitle {
      padding: 12px 8px 0px;
      box-sizing: border-box;
    }
    .TPNYPWH-main-content {
      flex: 1;
      min-height: 0;
      padding: getCssVar('spacing-3');
      box-sizing: border-box;
      overflow: hidden;

      ::v-deep .#{$namespace}-form-item--status-icon-inline {
        padding-right: unset;

        .#{$namespace}-form-item__content {
          justify-content: center;
        }
      }

      ::v-deep
        .#{$namespace}-table--edit
        .#{$namespace}-base-table__body
        .#{$namespace}-input__inner {
        border: unset;
      }

      ::v-deep .#{$namespace}-select .#{$namespace}-input__wrapper:hover {
        border-color: transparent;
      }
    }
  }

  &-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: getCssVar('spacing-3');
    background-color: getCssVar('color-1');
    .btnWidth {
      width: 72px;
    }
  }

  .mr-8 {
    margin-right: getCssVar('spacing-3');
  }
}
</style>
