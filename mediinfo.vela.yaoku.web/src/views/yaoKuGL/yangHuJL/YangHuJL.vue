<template>
  <div :class="prefixClass('yijiesuan-box')">
    <div :class="prefixClass('yijiesuan-content-top')">
      <div :class="prefixClass('yijiesuan-content-top-filters')">
        <md-date-picker-range-pro
          v-model="query.timeRange"
          range-separator="/"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-right: 8px; width: 250px"
          @change="handleDateRangeChange"
        >
        </md-date-picker-range-pro>
        <md-select
          v-model="query.yangHuLXDM"
          style="width: 200px; margin-right: 8px"
          :clearable="false"
          @change="onChangeYHLX"
        >
          <md-option
            v-for="item in yaoHuLXOptions"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          ></md-option>
        </md-select>
        <md-input
          v-model="query.danJuHao"
          placeholder="输入单据号搜索"
          :class="prefixClass('filter-width')"
          style="margin-right: 8px"
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <i
              :class="[prefixClass('input__icon'), prefixClass('icon-seach')]"
              @click="handleSearch"
            />
          </template>
        </md-input>
      </div>
      <div :class="prefixClass('yijiesuan-content-top-buttons')">
        <md-button
          type="primary"
          :icon="prefixClass('icon-shuaxin')"
          noneBg
          style="margin-right: 8px"
          @click="handleSearch"
          >刷新</md-button
        >
        <md-button
          type="primary"
          :icon="prefixClass('icon-xinzeng')"
          :class="prefixClass('kaidan-button')"
          noneBg
          @click="handleAdd"
          >开单</md-button
        >
      </div>
    </div>
    <div :class="prefixClass('yijiesuan-table-box')">
      <md-table-pro
        :columns="columns"
        :loading="loading"
        element-loading-text="正在加载中..."
        :onFetch="handleFetch"
        :row-class-name="tableRowClassName"
        border
        :stripe="false"
        ref="table"
      >
        <template v-slot:yangHuDan="{ row, $index }">
          <div :class="prefixClass('item-inline')">
            <md-tooltip
              trigger="hover"
              effect="light"
              :popperClass="prefixClass('yanghudantip')"
            >
              <template #content>
                <div @click="copy(row.yangHuDan)">复制</div>
              </template>
              <span
                :class="prefixClass('yanghudh')"
                @click="handleRowClick($event, row, $index)"
                >{{ row.yangHuDan }}</span
              >
            </md-tooltip>
          </div>
        </template>
        <template v-slot:yaoPinMXMCList="{ row }">
          <biz-tag-list :list="row.yaoPinMCXSList" label-key="yaoPinMC">
          </biz-tag-list>
        </template>
        <template #operate="{ row }">
          <md-button type="primary" noneBg @click.stop="handleEdit(row)">
            编辑
          </md-button>
          <md-button type="danger" noneBg @click.stop="handleDelete(row)">
            作废
          </md-button>
        </template>
      </md-table-pro>
      <yanghuxq-drawer ref="yangHuXQDrawer" size="60%" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import { MdMessageBox, MdMessage } from '@mdfe/medi-ui';
import BizTagList from '@/components/BizTagList';
import eventBus from '@/system/utils/eventbus';
import { logger } from '@/service/log';
import {
  GetYangHuDanList,
  GetYangHuDanCount,
  ZuoFeiYangHuDan,
} from '@/service/yaoPinYF/yangHuJL';

import YangHuXQDrawer from './components/YangHuXQDrawer';
import useClipboard from 'vue-clipboard3';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
export default {
  name: 'yanghujl',
  data() {
    return {
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      value1: new Date(),
      loading: false,
      currentTableIndex: null,
      yaoHuLXOptions: [],
      query: {
        // timeRange: this.getDefaultDateRange(),
        // danJuHao: '',
      }, //搜索
      columns: [
        {
          slot: 'yangHuDan',
          label: '养护单',
          width: 130,
        },
        {
          prop: 'yangHuLXMC',
          label: '养护类型',
          width: 130,
        },
        {
          prop: 'yaoPinShu',
          label: '药品数',
          width: 100,
          align: 'right',
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额(元)',
          width: 110,
          align: 'right',
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaJEXSDW);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额(元)',
          width: 110,
          align: 'right',
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.lingShouJEXSDW);
          },
        },
        {
          slot: 'yaoPinMXMCList',
          label: '药品明细',
          minWidth: 400,
        },
        {
          prop: 'zhiDanRQ',
          label: '制单日期',
          width: 100,
          formatter: (row, cell, cellValue) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'zhiDanRen',
          label: '制单人',
          width: 110,
        },
        {
          slot: 'operate',
          label: '操作',
          width: 95,
          fixed: 'right',
        },
      ],
    };
  },
  async created() {
    //监听是否需要刷新列表 用于新增养护记录后
    eventBus.$on('yangHuJL:refresh', this.handleSearch);
    const res = await getKuFangSZList(['jinJiaJEXSDWS', 'lingShouJEXSDWS']);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        }
      });
    }
  },
  mounted() {
    this.query = {
      timeRange: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      danJuHao: '', //单据号
      yangHuLXDM: '0',
      yangHuLXMC: '全部养护类型',
    };
    getYaoPinShuJuYZYList(['YP0123']).then((res) => {
      this.yaoHuLXOptions = res[0].zhiYuList.map((item1) => {
        return {
          label: item1.biaoZhunMC,
          value: item1.biaoZhunDM,
        };
      });
      this.yaoHuLXOptions.unshift({
        value: '0',
        label: '全部养护类型',
      });
    });
    this.handleSearch();
  },
  beforeDestroy() {
    //页面刷新 取消监听
    eventBus.$off('yangHuJL:refresh');
  },
  methods: {
    onChangeYHLX(val) {
      this.query.yangHuLXMC = this.yaoHuLXOptions.find(
        (fl) => fl.value == val,
      ).label;
      this.handleSearch();
    },
    copy(content) {
      const { toClipboard } = useClipboard();
      toClipboard(content)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
        })
        .catch((err) => {
          MdMessageBox({
            title: '系统消息',
            type: 'error',
            message: `复制失败`,
            confirmButtonText: '我知道了',
          });
        });
    },
    //点击编辑 跳转致编辑页面
    handleEdit(row) {
      this.$router.push({
        name: 'YangHuDBJ',
        query: {
          title: '养护单-' + row.yangHuDan,
          id: row.yangHuDan,
        },
      });
      // const query = {
      //   name: 'YangHuDBJ',
      //   title: '养护单-' + row.yangHuDan,
      //   id: row.yangHuDan,
      // }
      // this.$router.push({ path: '/YangHuDXZ', query })
    },
    //点击作废
    async handleDelete(row) {
      try {
        //消息提示是否作废
        await MdMessageBox.confirm('确定要作废该数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        //点击确认之后调用作废接口
        const params = {
          YangHuDan: row.yangHuDan,
        };
        this.loading = true;
        await ZuoFeiYangHuDan(params);
        //作废成功 消息提示
        MdMessage({
          type: 'success',
          message: '作废成功!',
        });
        //刷新页面数据
        this.handleSearch();
      } catch (error) {
        console.error(error);
        logger.error(error);
      } finally {
        this.loading = false;
      }
    },
    //获取默认时间范围 本月初 至 今天
    //todo 取后端默认当前时间 加计时器 默认跑
    getDefaultDateRange() {
      return [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ];
    },
    //时间范围选择后重新检索页面数据
    handleDateRangeChange() {
      this.handleSearch();
    },
    //检索页面数据
    handleSearch() {
      if (this.$refs.table) this.$refs.table.search({ pageSize: 100 });
    },
    //点击新增按钮跳转至新增请领
    handleAdd() {
      this.$router.push('/YangHuDXZ');
    },
    //请求列表数据的方法
    async handleFetch({ page, pageSize }, config) {
      const KaiShiSJ = this.query.timeRange?.[0];
      const JieShuSJ = this.query.timeRange?.[1];
      const params = {
        KaiShiSJ,
        JieShuSJ,
        yangHuLXDM: this.query.yangHuLXDM,
        yangHuLXMC: this.query.yangHuLXMC,
        YangHuDan: this.query.danJuHao,
        pageIndex: page,
        pageSize: pageSize,
      };

      const [items, total] = await Promise.all([
        GetYangHuDanList(params, config),
        GetYangHuDanCount(params, config),
      ]);
      return {
        items: items,
        total: total,
      };
    },
    //点击某一行 展开侧滑
    handleRowClick(e, row, index) {
      e.stopPropagation();
      this.currentTableIndex = index;
      this.$refs.yangHuXQDrawer.openDrawer(row);
    },
    tableRowClassName({ row, rowIndex }) {
      let className = '';
      if (rowIndex === this.currentTableIndex) {
        className = 'row-height';
      } else {
        className = '';
      }
      return className;
    },
    // handleCopySuccess() {
    //   this.$message({
    //     message: '复制成功',
    //     type: 'success',
    //     duration: 2000,
    //   });
    // },
    // handleCopyError() {
    //   MdMessageBox({
    //     title: '系统消息',
    //     type: 'error',
    //     message: `复制失败`,
    //     confirmButtonText: '我知道了',
    //   });
    // },
  },
  components: {
    'biz-tag-list': BizTagList,
    'md-table-pro': MdTablePro,
    'yanghuxq-drawer': YangHuXQDrawer,
  },
};
</script>

<style lang="scss">
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-yanghudantip {
  min-width: 30px;
  color: getCssVar('color-6');
  padding: 4px 11px;

  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-yijiesuan-box {
  background: #fff;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;

  .#{$md-prefix}-yijiesuan-content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-filters {
      display: flex;

      .#{$md-prefix}-filter-width {
        width: 200px;
      }
    }

    &-buttons {
      display: flex;
    }
  }

  ::v-deep .row-height {
    background-color: getCssVar('color-2');
  }

  .#{$md-prefix}-yijiesuan-table-box {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    &-jiajiicon {
      display: inline-block;
      color: #fff;
      font-size: 12px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      background-color: #ff9900;
      border-radius: 2px;
      text-align: center;
      margin-left: 8px;
    }
  }

  .#{$md-prefix}-item-inline {
    display: flex;
    align-items: center;
  }

  .#{$md-prefix}-chonghongBZ {
    margin-left: 4px;
    width: 16px;
    height: 16px;
    background-color: #f12933;
    border-radius: 8px;
    color: #ffffff;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
  }
}

::v-deep .#{$md-prefix}-link.is-underline:hover::after {
  bottom: 1px;
}

.#{$md-prefix}-zhuangtai-tag {
  display: inline-block;
  width: 58px;
  height: 20px;
  border-radius: 2px;
  font-size: 14px;
  line-height: 20px;
  text-align: center;

  &.#{$md-prefix}-yiShouLi {
    background-color: #e2efff;
    color: #1e88e5;
  }

  &.#{$md-prefix}-yiJuJue {
    background-color: #f5f5f5;
    color: #999999;
  }

  &.#{$md-prefix}-weiShouLi {
    background-color: #fff5e5;
    color: #ff9900;
  }
}

.#{$md-prefix}-yanghudh {
  cursor: pointer;
  color: getCssVar('color-6');

  &:hover {
    color: getCssVar('color-6');
    font-size: getCssVar('font-2');
    text-decoration: underline;
    line-height: 20px;
  }
}
</style>
