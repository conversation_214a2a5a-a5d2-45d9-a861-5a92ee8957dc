<template>
  <bmis-blue-dialog
    v-model:visible="visibleDialog"
    width="560px"
    height="330px"
    title="库存>0生成"
    saveText="生成"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @submit="handleSave"
    @close="handleCancel"
  >
    <md-form v-loading="loading" label-width="80px" use-status-icon>
      <md-row>
        <md-col :span="12">
          <md-form-item label="药品类型" prop="yaoPinLXDM">
            <md-select v-model="formModel.yaoPinLXDM">
              <md-option
                v-for="item in yaoPinLXOptions"
                :value="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :key="item.biaoZhunDM"
              ></md-option>
            </md-select>
          </md-form-item>
        </md-col>
        <md-col :span="12">
          <md-form-item label="失效天数" prop="shiXiaoTS">
            <md-input
              v-model="formModel.shiXiaoTS"
              oninput="value=value.replace(/^0|[^0-9]/g, '')"
              :clearable="false"
            >
              <template #suffix>
                <i class="md-input__icon icon-text">天内</i>
              </template>
            </md-input>
          </md-form-item>
        </md-col>
      </md-row>
      <md-col :span="12">
        <md-form-item label="积压天数" prop="jiYaTS">
          <md-input
            v-model="formModel.jiYaTS"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            :clearable="false"
          >
            <template #suffix>
              <i class="md-input__icon icon-text">天内</i>
            </template>
          </md-input>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="摆放位置" prop="baiFangWZ">
          <md-select
            filterable
            remote
            multiple
            v-model="formModel.baiFangWZ"
            :remote-method="searchBaiFangWZ"
          >
            <md-option
              v-for="(option, index) in baiFangWZList"
              :key="option.baiFangWZ"
              :label="option.baiFangWZ"
              :value="option.baiFangWZ"
            ></md-option>
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="毒理分类" prop="duLiFLDMList">
          <md-select v-model="formModel.duLiFLDMList" multiple>
            <md-option
              v-for="item in duLiFLOptions"
              :key="item.biaoZhunDM"
              :label="item.biaoZhunMC"
              :value="item.biaoZhunDM"
            />
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="药品属性" prop="yaoPinSXList">
          <md-select
            v-model="formModel.yaoPinSXList"
            placeholder="全部属性"
            filterable
            multiple
            remote
            collapse-tags
            reserve-keyword
            :remote-method="fetchYaoPinSX"
          >
            <md-option
              v-for="item in yaoPinSXOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
        </md-form-item>
      </md-col>
      <md-col :span="24">
        <md-form-item label="入库日期">
          <md-date-picker-range-pro
            v-model="formModel.timeRange"
            range-separator="/"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          >
          </md-date-picker-range-pro>
        </md-form-item>
      </md-col>
    </md-form>
  </bmis-blue-dialog>
</template>

<script>
import BlueDialog from '@/components/blue-dialog/index.vue';
import { PiLiangSC } from '@/service/yaoPinYK/yaoKuPC';
import {
  getYaoPinShuJuYZYList,
  getShuJuYZYList,
} from '@/service/yaoPin/yeWuZD';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import { GetYaoPinBFWZSelect } from '@/service/yaoPin/yaoPinZD';
import { ShengChengDaYuLYP } from '@/service/yaoPinYF/yangHuJL';
const formModelInit = () => {
  return {
    baiFangWZ: [], //摆放位置
    duLiFLDMList: [], //毒理分类
    yaoPinSXList: [],
    yaoPinLXDM: '',
    shiXiaoTS: '',
    jiYaTS: '',
    timeRange: [],
  };
};
export default {
  name: '',
  data() {
    return {
      visibleDialog: false,
      loading: false,
      duLiFLOptions: [],
      yaoPinLXOptions: [],
      formModel: formModelInit(),
      resolve: null,
      reject: null,
      baiFangWZList: [],
      yaoPinSXOptions: [],
    };
  },
  mounted() {
    this.init();
    this.fetchYaoPinSX('');
  },
  methods: {
    showModal() {
      this.formModel = formModelInit();
      this.visibleDialog = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    init() {
      getYaoPinShuJuYZYList(['YP0006']).then((res) => {
        this.duLiFLOptions = res[0].zhiYuList;
      });
      getYaoPinShuJuYZYList(['YP0005']).then((res) => {
        this.yaoPinLXOptions = res[0].zhiYuList;
      });
    },
    fetchYaoPinSX(query) {
      getShuJuYZYList({
        pageIndex: 1,
        pageSize: 9999,
        likeQuery: query,
        shuJuYLBID: 'YP0001',
      }).then((res) => {
        this.yaoPinSXOptions = res.map((m) => {
          return {
            label: m.biaoZhunMC,
            value: m.biaoZhunDM,
          };
        });
      });
    },
    searchBaiFangWZ(val) {
      if (val == '' || val == undefined) {
        this.baiFangWZList = [];
        return;
      }
      this.getBaiFangWZList(val);
    },
    // 获取摆放位置列表
    async getBaiFangWZList(val) {
      try {
        if (val.length == 0) return;
        const res = await GetYaoPinBFWZSelect({
          baiFangWZ: val,
        });
        if (res) {
          this.baiFangWZList = res;
        }
        this.baiFangWZList = res;
        this.$forceUpdate();
      } catch (e) {}
    },
    // 点击取消按钮
    handleCancel() {
      this.reject('点击取消关闭弹窗');
    },
    handleSave() {
      const {
        yaoPinLXDM,
        baiFangWZ,
        duLiFLDMList,
        yaoPinJG,
        shiXiaoTS,
        jiYaTS,
        yaoPinSXList,
        timeRange,
      } = this.formModel;
      const params = {
        yaoPinLXDM,
        shiXiaoTS: Number(shiXiaoTS),
        jiYaTS: Number(jiYaTS),
        yaoPinSXList,
        baiFangWZList: baiFangWZ,
        duLiFLDMList: duLiFLDMList,
        jieShuSJ: timeRange[1] ? timeRange[1] + ' 23:59:50' : '',
        kaiShiSJ: timeRange[0] ? timeRange[0] + ' 00:00:00' : '',
      };
      this.loading = true;
      ShengChengDaYuLYP(params)
        .then((res) => {
          this.resolve(res);
          this.closeModal();
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
</style>
