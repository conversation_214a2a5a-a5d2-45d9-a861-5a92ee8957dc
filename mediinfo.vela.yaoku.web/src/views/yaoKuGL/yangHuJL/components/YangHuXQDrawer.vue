<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :with-header="false"
    :modal-class="prefixClass('yangHuCustom')"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('yanghuxqdrawer')"
    ref="yanghuxqdrawer"
  >
    <div :class="prefixClass('rukudan-drawer')" v-loading="pageLoading">
      <div :class="prefixClass('yanghuXQ-title')">
        <span :class="prefixClass('title-left')">
          {{ title }}
        </span>
        <div :class="prefixClass('title-right')">
          <span :class="prefixClass('title-toolbar')">
            <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              noneBg
              @click="handleYuLan"
              >预览</md-button
            >
            <!--             <md-button
              type="primary"
              :icon="prefixClass('icon-dayinji')"
              @click="handleDaYin"
              noneBg
              >打印
            </md-button> -->
          </span>
          <span :class="prefixClass('title-close')" @click="closeDrawer">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('content')">
        <div :class="prefixClass('content-zhuangtai')">
          <div :class="prefixClass('content-description-item')">
            <label>养护类型：</label><span>{{ rowData.yangHuLXMC }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>养护日期：</label
            ><span>{{ formatDate(rowData.yangHuRQ) }}</span>
          </div>
          <div :class="prefixClass('content-description-item')">
            <label>备注：</label><span>{{ rowData.beiZhu }}</span>
          </div>
        </div>
        <md-table :columns="colums" :data="rowData.yaoPinMX" height="100%">
        </md-table>
        <div :class="prefixClass('description pos-bottom-direction')">
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">制单:</span>
            <span :class="prefixClass('description-item__content')">
              {{ rowData.zhiDanRXM + ' ' + formatDate(rowData.zhiDanSJ) }}
            </span>
          </div>
          <div :class="prefixClass('description-item')">
            <span :class="prefixClass('description-item__label')">共计:</span>
            <span :class="prefixClass('description-item__content fontWeight')">
              {{ rowData.yaoPinZS }}
              <span :class="prefixClass('content-color')">种药品</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >合计 进价金额:</span
            >
            <span
              :class="
                prefixClass([
                  'description-item__content fontWeight',
                  fuShuXS ? 'chonghong-number__color' : '',
                ])
              "
            >
              {{ formatJinE(rowData.jinJiaJEHJ) }}
              <span :class="prefixClass('content-color')">元</span>
            </span>
            <span :class="prefixClass('description-item__label')"
              >零售金额:</span
            >
            <span
              :class="
                prefixClass([
                  fuShuXS ? 'chonghong-number__color' : '',
                  'description-item__content fontWeight',
                ])
              "
            >
              {{ formatJinE(rowData.lingShouJEHJ) }}
              <span :class="prefixClass('content-color')">元</span></span
            >
          </div>
        </div>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="params"
        :id="'YKXT012'"
        :fileName="'养护记录单'"
        :title="'养护记录单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import DaYinDialog from '@/components/DaYinDialog.vue';
import { GetYangHuDanXX } from '@/service/yaoPinYF/yangHuJL';
import { GetCanShuZhi } from '@/system/utils/canShu';
import { yaoFangZDJZTimeShow } from '@/system/utils/formatDate';
import { getWeiZhiMC, getKuCunGLLX } from '@/system/utils/local-cache';
import { add } from '@/system/utils/mathComputed';
import { printByUrl } from '@/system/utils/print';
import { MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import { logger } from '@/service/log';
export default {
  name: 'yanghujl-drawer',
  props: {
    size: { type: [String, Number], default: '75%' },
  },
  async created() {
    let xiaoShu;
    try {
      const params = {
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '3', //0表示关闭，1表示开启
        gongNengID: '0',
      };
      xiaoShu = await GetCanShuZhi(params);
    } catch (error) {
      logger.error(error);
    } finally {
      this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 3 : xiaoShu;
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  mounted() {
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  data() {
    return {
      xiaoShuDianWS: 3,
      isZhongYaoKXS: 5,
      id: 'YKXT008',
      params: {
        pageIndex: 1,
        pageSize: 9999,
        ShiXiaoJSY: '',
        ShiXiaoQSY: '',
        ShiXiaoTS: 30,
        youXiaoBZ: '',
      },
      drawer: false,
      pageLoading: false,
      title: '',
      rowData: {},
      colums: [
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'yaoPinLXMC',
          label: '',
          width: 32,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return row.yaoPinLXMC ? row.yaoPinLXMC.slice(0, 1) : '';
          },
        },
        {
          label: '药品名称与规格',
          slot: 'yaoPinMCYGG',
          prop: 'yaoPinMCYGG',
          formatter: (row, column, cellValue, index) => {
            if (row.yaoPinMC) {
              return row.yaoPinMC + ' ' + row.yaoPinGG;
            } else {
              return '';
            }
          },
          minWidth: 300,
        },
        {
          prop: 'ruKuCDMC',
          label: '入库产地',
          minWidth: 150,
          type: 'text',
          showOverflowTooltip: true,
          hidden: true,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 150,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'shengChanQY',
          label: '生产企业',
          minWidth: 150,
          type: 'text',
          showOverflowTooltip: true,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 50,
          type: 'text',
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          width: 120,
          type: 'text',
        },
        {
          prop: 'shengChanRQ',
          label: '生产日期',
          minWidth: 150,
          type: 'text',
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 120,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            if (!cellValue) return '';
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'shuLiang',
          label: '数量',
          width: 100,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'baoZhuangZLMC',
          label: '包装质量',
          width: 90,
          formatter: (row, column, cellValue, index) => {
            return row.baoZhuangZLMC;
          },
        },
        {
          prop: 'yangHuJGMC',
          label: '养护结果',
          width: 90,
          formatter: (row, column, cellValue, index) => {
            return row.yangHuJGMC;
          },
        },
        {
          slot: 'chuLiYJMC',
          label: '处理意见',
          width: 90,
          formatter: (row, column, cellValue, index) => {
            return row.chuLiYJMC;
          },
        },
        {
          slot: 'cunChuTJ',
          prop: 'cunChuTJDM',
          label: '存储条件',
          width: 90,
          startMode: 'click',
          endMode: 'custom',
          formatter: (row, column, cellValue, index) => {
            return row.cunChuTJMC;
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.isZhongYaoKXS);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
        {
          prop: 'lingShouJia',
          label: '零售价',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.isZhongYaoKXS);
          },
        },
        {
          prop: 'lingShouJE',
          label: '零售金额',
          width: 120,
          align: 'right',
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.xiaoShuDianWS);
          },
        },
      ],
      data: [],
      fuShuXS: false,
      kuCunLX: getKuCunGLLX(),
    };
  },
  watch: {
    kuCunLX: {
      handler: function (val) {
        this.changeColumsHidden(
          'ruKuCDMC',
          val.indexOf('3') > -1 ? false : true,
        );
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    // 修改列的隐藏属性
    changeColumsHidden(prop, val) {
      const index = this.colums.findIndex((item) => item.prop === prop);
      if (index !== -1) {
        this.colums[index].hidden = val;
      }
    },
    //预览
    async handleYuLan() {
      const params = {
        yangHuDan: this.rowData.yangHuDan,
      };
      this.params = params;
      this.$refs.daYinDialog.showModal();
    },
    //打印
    async handleDaYin() {
      try {
        this.pageLoading = true;
        const params = {
          yangHuDan: this.rowData.yangHuDan,
        };
        await printByUrl('YKXT012', params);
        this.$message({
          type: 'success',
          message: '打印成功！',
        });
      } catch (e) {
        // this.$message({
        //   type: 'error',
        //   message: e.message,
        // })
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: e.message,
          confirmButtonText: '我知道了',
        });
      } finally {
        this.pageLoading = false;
      }
    },
    //格式化金额
    formatJinE(jinE) {
      if (!jinE) jinE = 0;
      return Math.abs(Number(jinE)).toFixed(this.xiaoShuDianWS);
    },
    //结算金额
    jiSuanJE(data) {
      let lingShouJEHJ = 0;
      let jinJiaJEHJ = 0;
      data.forEach((item) => {
        lingShouJEHJ = add(lingShouJEHJ, Number(item.lingShouJE));
        jinJiaJEHJ = add(jinJiaJEHJ, Number(item.jinJiaJE));
      });
      this.rowData.lingShouJEHJ = lingShouJEHJ;
      this.rowData.jinJiaJEHJ = jinJiaJEHJ;
      this.rowData.yaoPinZS = data.length;
    },
    //时间格式化
    formatDate(date) {
      return yaoFangZDJZTimeShow(date);
    },
    //关闭
    handleClickBodyCloseDrawer(e) {
      if (!this.$refs.yanghuxqdrawer.$el.contains(e.target)) {
        this.closeDrawer();
      }
    },
    //打开
    async openDrawer(option) {
      this.pageLoading = true;
      try {
        this.drawer = true;
        const params = {
          YangHuDan: option.yangHuDan,
        };
        this.rowData = await GetYangHuDanXX(params);
        this.title = `详情 - ${this.rowData.yangHuDan}`;
        this.jiSuanJE(this.rowData.yaoPinMX);
      } finally {
        this.pageLoading = false;
      }
      return new Promise((resolve, reject) => {
        this.finish = resolve;
        this.reject = reject;
      });
    },
    //关闭
    closeDrawer() {
      this.drawer = false;
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
  },
};
</script>

<style lang="scss">
.#{$md-prefix}-yangHuCustom {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
}
</style>

<style lang="scss" scoped>
.#{$md-prefix}-yanghuxqdrawer {
  position: absolute;
  right: 0;
  top: 0;
  left: inherit;
  width: 80%;
  .#{$md-prefix}-rukudan-drawer {
    display: flex;
    flex-direction: column;
    height: 100%;
    .#{$md-prefix}-yanghuXQ-title {
      display: flex;
      justify-content: space-between;
      background: #f0f5fb;
      height: 36px;
      line-height: 36px;
      .#{$md-prefix}-title-left {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin-left: 9px;
        .#{$md-prefix}-jiyongtag {
          display: inline-block;
          vertical-align: middle;
          width: 20px;
          height: 20px;
          border-radius: 2px;
          line-height: 20px;
          margin: 0 0 2px 5px;
          background-color: #ff9900;
          color: #ffffff;
          font-size: 12px;
          text-align: center;
        }
        .#{$md-prefix}-chonghongBZ {
          width: 16px;
          height: 16px;
          background-color: #f12933;
          border-radius: 8px;
          color: #ffffff;
          font-size: 12px;
          text-align: center;
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 2px 5px;
          line-height: 14px;
        }
      }
      .#{$md-prefix}-title-toolbar {
        margin-right: 18px;
      }
      .#{$md-prefix}-title-close {
        i {
          font-size: 14px;
          float: right;
          margin-right: 12px;
          margin-top: 11px;
          color: #aaaaaa;
          cursor: pointer;
        }
      }
    }
    .#{$md-prefix}-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      // margin-top: 10px;
      padding: 0 8px 8px 8px;
      &-description-item {
        margin-top: 12px;
        margin-bottom: 12px;
        &:nth-last-child(n + 1) {
          margin-right: 48px;
        }
        > label {
          height: 20px;
          color: #666666;
          font-size: 14px;
          line-height: 20px;
        }
        > span {
          height: 20px;
          color: #222222;
          font-size: 14px;
          line-height: 20px;
        }
      }

      .#{$md-prefix}-shifoujy {
        text-align: center;
        color: #ff9900;
      }
      .#{$md-prefix}-bihuan {
        text-align: center;
        color: #1e88e5;
        cursor: pointer;
      }
      .#{$md-prefix}-content-zhuangtai {
        display: flex;
        width: 100%;
      }
      .#{$md-prefix}-description {
        display: flex;
        &-item {
          line-height: 20px;
          min-height: 20px;
          font-size: 14px;
          color: #333;
          padding: 5px 0;
          &__label {
            color: #666;
            margin-left: 8px;
          }
          &__content {
            padding-left: 5px;
            &.#{$md-prefix}-fontWeight {
              font-weight: bold;
            }
            .#{$md-prefix}-content-color {
              color: #666;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
}
.#{$md-prefix}-pos-bottom-direction {
  justify-content: space-between;
}
::v-deep .#{$md-prefix}-descriptions {
  // margin-bottom: 8px;
  flex: 1;
}
::v-deep .#{$md-prefix}-description-item {
  color: #222;
}
::v-deep .#{$md-prefix}-description-item {
  font-size: 14px;
}
::v-deep .#{$md-prefix}-drawer__body {
  min-height: 0;
}
.#{$md-prefix}-chonghong-number__color {
  color: #f12933;
}
::v-deep .#{$md-prefix}-chonghong-number__color .cell {
  color: #f12933 !important;
}
</style>
