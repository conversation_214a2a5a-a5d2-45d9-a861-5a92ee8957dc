<template>
  <div :class="prefixClass('inventory-container')">
    <div v-loading="loading" :class="prefixClass('add-inventory')">
      <div :class="prefixClass('add-inventory-header')">
        <div :class="prefixClass('add-inventory-header__action')">
          <div :class="prefixClass('add-inventory-header__action__left')">
            <span :class="prefixClass('title')"
              ><i class="iconfont icondanju"></i>养护记录单</span
            >
            <md-tooltip
              class="item"
              effect="dark"
              :content="tiShiMC"
              placement="top"
              v-if="tiShiMC"
            >
              <biz-yaopindw
                v-model="query.yaoPin"
                valueKey="yaoPinMC"
                labelKey="yaoPinMC"
                showSuffix
                style="width: 200px; background: #fff; border-radius: 4px"
                type="yh"
                :guiGeLX="1"
                @change="handleDingWei"
              />
            </md-tooltip>
            <biz-yaopindw
              v-model="query.yaoPin"
              valueKey="yaoPinMC"
              labelKey="yaoPinMC"
              showSuffix
              style="width: 200px; background: #fff; border-radius: 4px"
              type="yh"
              :guiGeLX="1"
              @change="handleDingWei"
              v-else
            />
            <md-select
              v-model="yaoPinLXDM"
              placeholder="全部类型"
              style="width: 120px; margin-left: 8px"
            >
              <md-option
                v-for="item in yaoPinLXOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </md-option>
            </md-select>
            <md-button
              type="primary"
              plain
              style="margin-left: 8px"
              @click="handleAnKuCXXSC"
              >按库存>0生成</md-button
            >
          </div>
          <div :class="prefixClass('add-inventory-header__action__right')">
            <md-button
              v-if="id"
              type="primary"
              noneBg
              :icon="prefixClass('icon-dayinji')"
              @click="handlePrint"
              >打印
            </md-button>
            <md-button
              type="danger"
              :icon="prefixClass('icon-shanchuwap')"
              noneBg
              @click="handleDelete"
              >删除
            </md-button>
            <md-button type="primary" style="width: 72px" @click="handleSave"
              >保存</md-button
            >
          </div>
        </div>
        <div :class="prefixClass('add-inventory-header__note')">
          <div :class="prefixClass('box')" style="width: 284px">
            <label>养护类型</label>
            <md-select
              v-model="query.yangHuLXDM"
              class="margin-right-8"
              :clearable="false"
              @change="
                (val) =>
                  (query.yangHuLXMC = yaoHuLXOptions.find(
                    (fl) => fl.value == val,
                  ).label)
              "
            >
              <md-option
                v-for="item in yaoHuLXOptions"
                :value="item.value"
                :label="item.label"
                :key="item.value"
              ></md-option>
            </md-select>
          </div>
          <div :class="prefixClass('box')" style="width: 284px">
            <label>养护日期</label>
            <md-date-picker
              v-model="query.yangHuRQ"
              placeholder="请选择日期"
              :class="prefixClass('margin-right-8')"
              :editable="false"
              :clearable="false"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              @change="handleChangeDate"
            />
          </div>
          <div :class="prefixClass('box')" style="flex: 1">
            <label>备注</label>
            <md-input v-model="query.beiZhu" placeholder="请输入"></md-input>
          </div>
        </div>
      </div>
      <div :class="prefixClass('add-inventory-body')">
        <!-- :addButtonDisabled="addButtonDisabled" -->
        <!-- :dataid="`mdEditTable${id}`" -->
        <vxe-table
          :data="allTableData"
          height="100%"
          border
          show-overflow
          keep-source
          ref="xTable"
          :column-config="{ resizable: true }"
          :custom-config="{ mode: 'popup', storage: true }"
          :toolbarConfig="{
            custom: true,
          }"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: false,
            autoClear: false,
            beforeEditMethod: false,
          }"
          :sort-config="{ remote: true }"
          :scroll-y="{ enabled: true }"
          :row-config="{ isHover: true }"
          :highlight-current-row="true"
          :cell-class-name="cellClassName"
          @checkbox-change="checkboxChange"
          @checkbox-all="selectAllCheckboxChange"
          @sort-change="sortChangeEvent"
        >
          <vxe-column type="checkbox" width="45" align="center"> </vxe-column>
          <vxe-column width="40" field="yaoPinLXMC">
            <template #default="{ row, $index }">
              <span :data-name="'scroll' + $index">{{
                row.yaoPinLXMC ? row.yaoPinLXMC.slice(0, 1) : ''
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="baiFangWZ"
            title="摆放位置"
            width="100"
            class="baiFangWZ"
          >
            <template #default="{ row }">
              {{ row.baiFangWZ }}
            </template>
          </vxe-column>
          <vxe-column
            field="yaoPinXX"
            title="药品名称与规格"
            width="301"
            :edit-render="{ autofocus: '.md-input__inner' }"
          >
            <template #default="{ row }">{{ row.yaoPinZC }}</template>
            <template #edit="{ row, $index }">
              <biz-yaopindw
                v-model="row.yaoPinXX"
                labelKey="yaoPinZC"
                style="width: 100%"
                type="yh"
                :guiGeLX="1"
                @change="handleTableYaoPinDWChange($event, row, $index)"
              ></biz-yaopindw>
            </template>
          </vxe-column>
          <vxe-column
            field="ruKuCDMC"
            title="入库产地"
            width="200"
            v-if="kuCunLX.indexOf('3') > -1"
          >
            <template #default="{ row }">
              {{ row.ruKuCDMC }}
            </template>
          </vxe-column>
          <vxe-column field="chanDiMC" title="产地名称" width="150" v-else>
            <template #default="{ row }">
              {{ row.chanDiMC }}
            </template>
          </vxe-column>

          <vxe-column field="shengChanQY" title="生产企业" width="200">
            <template #default="{ row }">
              {{ row.shengChanQY }}
            </template>
          </vxe-column>
          <vxe-column field="baoZhuangDW" title="单位" width="50">
            <template #default="{ row }">
              {{ row.baoZhuangDW }}
            </template>
          </vxe-column>
          <vxe-column field="shengChanPH" title="生产批号" width="120">
            <template #default="{ row }">
              {{ row.shengChanPH }}
            </template>
          </vxe-column>
          <vxe-column field="shengChanRQ" title="生产日期" width="150">
            <template #default="{ row }">
              {{ formatYPXQ(row.shengChanRQ) }}
            </template>
          </vxe-column>
          <vxe-column field="yaoPinXQ" title="药品效期" width="120">
            <template #default="{ row }">
              {{ formatYPXQ(row.yaoPinXQ) }}
            </template>
          </vxe-column>
          <vxe-column field="shuLiang" title="数量" width="100" align="right">
            <template #default="{ row }">
              {{ Number(row.shuLiang).toFixed(3) }}
            </template>
          </vxe-column>
          <vxe-column
            field="yangHuLY"
            title="养护理由"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.yangHuLYMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.yangHuLYDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleYangHuLY(row, $event)"
              >
                <md-option
                  v-for="item in yangHuLYOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>
          <vxe-column
            field="jianChaNR"
            title="检查内容"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">
              {{ row.jianChaNR }}
            </template>
            <template #edit="{ row }">
              <md-input v-model="row.jianChaNR" placeholder="请输入"></md-input>
            </template>
          </vxe-column>
          <!-- <vxe-column
            field="yangHuFFDM"
            title="养护方法"
            width="90"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.yangHuFFMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.yangHuFFDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleYangHuFF(row, $event)"
              >
                <md-option
                  v-for="item in yangHuFFOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>   -->
          <vxe-column
            field="baoZhuangZLDM"
            title="包装质量"
            width="90"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.baoZhuangZLMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.baoZhuangZLDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleBaoZhuangZL(row, $event)"
              >
                <md-option
                  v-for="item in baoZhuangZLOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>
          <vxe-column
            field="yangHuJGDM"
            title="养护结果"
            width="90"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.yangHuJGMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.yangHuJGDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleYangHuJG(row, $event)"
              >
                <md-option
                  v-for="item in yangHuJGOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>
          <vxe-column
            field="chuLiYJDM"
            title="处理意见"
            width="90"
            :edit-render="{}"
          >
            <template #default="{ row }">
              {{ row.chuLiYJMC }}
            </template>
            <template #edit="{ row }">
              <span v-if="row.yangHuJGDM === '1'">{{ row.chuLiYJMC }}</span>
              <md-select
                v-else
                v-model="row.chuLiYJDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleChuLiYJ(row, $event)"
              >
                <md-option
                  v-for="item in chuLiYJOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>
          <!-- <vxe-column
            field="yangHuLY"
            title="养护理由"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.yangHuLYMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.yangHuLYDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleYangHuLY(row, $event)"
              >
                <md-option
                  v-for="item in yangHuLYOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column> -->
          <vxe-column
            field="cunChuTJDM"
            title="存储条件"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">{{ row.cunChuTJMC }}</template>
            <template #edit="{ row }">
              <md-select
                v-model="row.cunChuTJDM"
                placeholder="请选择"
                @visible-change="selectChange($event)"
                @change="handleCunChuTJ(row, $event)"
              >
                <md-option
                  v-for="item in cunChuTJOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </md-option>
              </md-select>
            </template>
          </vxe-column>
          <vxe-column field="jinJia" title="进价" width="88" align="right">
            <template #default="{ row }">
              {{ formatJJ(row.jinJia) }}
            </template>
          </vxe-column>
          <vxe-column
            field="jinJiaJE"
            title="进价金额"
            width="120"
            align="right"
          >
            <template #default="{ row }">
              {{ formatJJJE(row.jinJiaJE) }}
            </template>
          </vxe-column>
          <vxe-column
            field="lingShouJia"
            title="零售价"
            width="88"
            align="right"
          >
            <template #default="{ row }">
              {{ formatLSJ(row.lingShouJia) }}
            </template>
          </vxe-column>
          <vxe-column
            field="lingShouJE"
            title="零售金额"
            width="120"
            align="right"
          >
            <template #header>
              <span>零售金额</span>
              <md-icon
                class="lie-shezhi"
                name="shezhi"
                @click="handleClickLieSheZ"
              />
            </template>
            <template #default="{ row }">
              {{ formatLSJE(row.lingShouJE) }}
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div :class="prefixClass('add-inventory-footer')">
        <div :class="prefixClass('add-inventory-footer__info left')">
          <span>制单：</span>
          <span :class="prefixClass('info__name color-222')">{{
            zhiDanRXM
          }}</span>
          <span :class="prefixClass('info__time color-222')">{{
            zhiDanSJ
          }}</span>
        </div>
      </div>
    </div>
    <biz-yaopinph ref="bizYaoPinPH" />
    <KuCunDialog ref="KuCunDialog" />
  </div>
</template>

<script>
import { MdMessage, MdMessageBox, MdPagination } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
import KuCunDialog from './KuCunDialog';
import eventBus from '@/system/utils/eventbus';
import { yaoFangZDJZTimeShow } from '@/system/utils/formatDate';
import { formatMoney } from '@/system/utils/formatMoney';
import {
  getWeiZhiMC,
  getYongHuID,
  getYongHuXM,
  getKuCunGLLX,
} from '@/system/utils/local-cache';
import { printByUrl } from '@/system/utils/print';
import { makePY } from '@/system/utils/wubi-pinyin.js';
import { cloneDeep, isEqual } from 'lodash';
import { sortBy } from 'lodash-es';

import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
import {
  GetYangHuDanXX,
  SaveYangHuDan,
  ShengChengDaYuLYP,
} from '@/service/yaoPinYF/yangHuJL';
import { logger } from '@/service/log';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinPH from '@/components/YaoKu/BizYaoPinPH';
import { GetCanShuZhi } from '@/system/utils/canShu';
const newRow = () => {
  return {
    baiFangWZ: '',
    yangHuDID: '',
    yaoPinLXDM: '',
    yaoPinLXMC: '',
    jiaGeID: '',
    yaoPinMC: '',
    yaoPinZC: '',
    yaoPinGG: '',
    yaoPinXX: '',
    baoZhuangDW: '',
    chanDiID: '',
    chanDiMC: '',
    shuLiang: 0,
    shengChanPH: '',
    yaoPinXQ: '',
    jinJia: 0,
    jinJiaJE: 0,
    lingShouJia: 0,
    lingShouJE: 0,
    // yangHuFFDM: '1',
    // yangHuFFMC: '正常',
    baoZhuangZLDM: '1',
    baoZhuangZLMC: '正常',
    yangHuJGDM: '1',
    yangHuJGMC: '合格',
    chuLiYJDM: '',
    chuLiYJMC: '',
    yangHuLYDM: '2',
    yangHuLYMC: '近效',
    cunChuTJDM: '1',
    cunChuTJMC: '常温',
    jianChaNR: '',
  };
};
export default {
  name: 'xinzengyhd',
  inject: ['viewManager'],
  data() {
    return {
      loading: false,
      id: '',
      yaoFangZDJZTimeShow: yaoFangZDJZTimeShow, //药房制单时间
      query: {
        yaoPin: {},
        beiZhu: '',
        yangHuRQ: dayjs().format('YYYY-MM-DD'),
        yangHuLXDM: '',
        yangHuLXMC: '',
      }, //搜索
      yaoHuLXOptions: [],
      tiShiMC: '',
      yaoPinLXDM: '', //药品类型代码
      yaoPinLXOptions: [], //药品类型
      // yangHuFFOptions: [], //养护方法
      yangHuLYOptions: [], //养护理由
      baoZhuangZLOptions: [], //包装质量
      yangHuJGOptions: [], //养护结果
      chuLiYJOptions: [], //处理意见
      tableData: [],
      deleteList: [],
      selection: [],
      zhiDanRXM: getYongHuXM(),
      zhiDanRID: getYongHuID(),
      zhiDanSJ: dayjs().format('YYYY-MM-DD'),
      selections: [],
      editTableData: [],
      // currentPage: 1,
      // pageSize: 20,
      allTableData: [newRow()],
      temporary: [newRow()],
      tableBodyEle: null,
      xiaoShuDianWS: 3,
      isZhongYaoKXS: 5,
      kuCunLX: getKuCunGLLX(),
    };
  },
  computed: {
    //新增一行禁用
    addButtonDisabled() {
      // let len = this.allTableData.length
      // if (len == 0) return false
      // if (this.allTableData[len - 1].yaoPinMC) return false
      return true;
    },
  },
  async created() {
    this.initData();
    let xiaoShu = 0;
    try {
      xiaoShu = await GetCanShuZhi({
        canShuMC: '库房管理_小数点位数',
        canShuMRZ: '3', //0表示关闭，1表示开启
        gongNengID: '0',
      });
    } finally {
      this.xiaoShuDianWS = !xiaoShu || xiaoShu == 0 ? 3 : xiaoShu;
      this.isZhongYaoKXS = this.xiaoShuDianWS;
      if (decodeURIComponent(getWeiZhiMC()).indexOf('中药') > -1) {
        this.isZhongYaoKXS = 5;
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.id = this.$route.query.id;
      this.editInit();
    });
  },
  methods: {
    // 点击列设置按钮
    handleClickLieSheZ() {
      this.$refs.xTable.openCustom();
    },
    cellClassName({ row, column }) {
      const textFields = [
        'yaoPinLXMC',
        'baiFangWZ',
        'chanDiMC',
        'baoZhuangDW',
        'shengChanPH',
        'yaoPinXQ',
        'shuLiang',
        'jinJia',
        'jinJiaJE',
        'lingShouJia',
        'lingShouJE',
        'shengChanRQ',
        'shengChanQY',
      ];
      if (textFields.includes(column.field)) {
        return 'is-text';
      }
    },
    formatYPMCYGG(row) {
      if (row.jiaGeID) {
        return row.yaoPinMC + ' ' + row.yaoPinGG;
      } else {
        return '';
      }
    },
    formatYPXQ(cellValue) {
      if (!cellValue) return '';
      return dayjs(cellValue).format('YYYY-MM-DD');
    },
    formatJJ(cellValue) {
      cellValue = cellValue || 0;
      return formatMoney(cellValue, this.isZhongYaoKXS);
    },
    formatJJJE(cellValue) {
      cellValue = cellValue || 0;
      return Number(cellValue).toFixed(this.xiaoShuDianWS);
    },
    formatLSJ(cellValue) {
      cellValue = cellValue || 0;
      return formatMoney(cellValue, this.isZhongYaoKXS);
    },
    formatLSJE(cellValue) {
      cellValue = cellValue || 0;
      return Number(cellValue).toFixed(this.xiaoShuDianWS);
    },
    handleChangeDate(data) {
      this.zhiDanSJ = data;
    },
    async initData() {
      this.loading = true;
      try {
        //先获取下拉数据
        let daiMaList = [
          'YP0005',
          'YP0009',
          //'YP0015',
          'YP0016',
          'YP0090',
          'YP0091',
          'YP0092',
          'YP0123',
        ];
        let optionKeys = [
          'yaoPinLXOptions',
          'cunChuTJOptions',
          // 'yangHuFFOptions',
          'yangHuLYOptions',
          'baoZhuangZLOptions',
          'yangHuJGOptions',
          'chuLiYJOptions',
          'yaoHuLXOptions',
        ];
        getYaoPinShuJuYZYList(daiMaList).then((res) => {
          res.forEach((item, index) => {
            let options = [];
            item.zhiYuList.forEach((item1) => {
              let data = {
                label: item1.biaoZhunMC,
                value: item1.biaoZhunDM,
              };
              options.push(data);
            });
            this[optionKeys[index]] = options;
            if (optionKeys[index] == 'yaoHuLXOptions') {
              this.query.yangHuLXDM = this.yaoHuLXOptions[0].value;
              this.query.yangHuLXMC = this.yaoHuLXOptions[0].label;
            }
          });
        });
      } catch (error) {
        logger.error(error);
      } finally {
        this.loading = false;
      }
    },
    //键盘事件处理
    handleTableEnter({ length, activeIndex, callback }) {
      //键盘翻页
      if ((activeIndex + 1) % length == 0) {
        this.newTableRow();
        callback({});
        return;
      }
      callback();
    },
    //编辑初始化
    editInit() {
      if (!this.id) return;
      this.loading = true;
      // this.$nextTick(() => {
      // const tableRef = this.$refs.mdEditTable.getTableRef();
      // const tableBodyHeight = tableRef.$el.querySelector(
      //   `.${process.env.VUE_APP_NAMESPACE}-base-table__body-wrapper`,
      // ).clientHeight;
      // });
      const params = {
        YangHuDan: this.id,
      };
      GetYangHuDanXX(params)
        .then((res) => {
          this.query.yangHuRQ = res.yangHuRQ;
          this.query.beiZhu = res.beiZhu;
          this.query.yangHuLXDM = res.yangHuLXDM;
          this.query.yangHuLXMC = res.yangHuLXMC;
          res.yaoPinMX.forEach((item) => {
            item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
            item.yaoPinXX = {
              baiFangWZ: item.baiFangWZ, //摆放位置
              yaoPinMC: item.yaoPinMC, //药品名称
              yaoPinLXDM: item.yaoPinLXDM, //药品类型DM
              yaoPinLXMC: item.yaoPinLXMC, //药品类型MC
              jiaGeID: item.jiaGeID,
              yaoPinGG: item.yaoPinGG,
              baoZhuangDW: item.baoZhuangDW, //单位
              chanDiID: item.chanDiID,
              chanDiMC: item.chanDiMC,
            };
          });
          this.zhiDanSJ = dayjs(res.yangHuRQ).format('YYYY-MM-DD');
          this.editTableData = cloneDeep(res.yaoPinMX);
          this.allTableData = res.yaoPinMX;
          this.temporary = cloneDeep(res.yaoPinMX);
          this.newTableRow();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //表格药品定位change
    async handleTableYaoPinDWChange(data, row, index) {
      //清空直接恢复为默认值
      try {
        if (!data) {
          //清空
          this.resetTableRow(row);
          return;
        } else {
          this.loading = true;
          row.yaoPinMC = data.yaoPinMC; //药品名称
          row.yaoPinLXDM = data.yaoPinLXDM; //药品类型DM
          row.yaoPinLXMC = data.yaoPinLXMC; //药品类型MC
          row.jiaGeID = data.jiaGeID;
          row.yaoPinGG = data.yaoPinGG;
          row.yaoPinZC = row.yaoPinMC + ' ' + row.yaoPinGG;
          row.baoZhuangDW = data.baoZhuangDW; //单位
          row.chanDiID = data.chanDiID;
          row.chanDiMC = data.chanDiMC;
          const res = await this.$refs.bizYaoPinPH.show({
            yaoPinMC: data.yaoPinMC,
            jiaGeID: data.jiaGeID,
          });
          row.baiFangWZ = res.baiFangWZ;
          row.lingShouJE = res.lingShouJia * res.kuCunSL;
          row.lingShouJia = res.lingShouJia;
          row.jinJia = res.jinJia;
          row.jinJiaJE = res.jinJia * res.kuCunSL;
          row.shuLiang = res.kuCunSL;
          row.shengChanPH = res.shengChanPH;
          row.yaoPinXQ = this.formatYPXQ(res.yaoPinXQ);
          row.yaoPinXX = data;
          row.shengChanQY = res.shengChanQY;
          row.shengChanRQ = res.shengChanRQ;
          row.ruKuCDMC = res.ruKuCDMC;
          this.temporary = cloneDeep(this.allTableData);
          this.newTableRow();
          // cellRef.endEdit();
        }
      } catch {
        this.resetTableRow(row);
      } finally {
        this.loading = false;
      }
    },
    //删除
    handleDelete() {
      if (!(Array.isArray(this.selections) && this.selections.length > 0)) {
        MdMessage.warning('至少选中一条');
        return;
      }
      MdMessageBox.confirm('确定删除当前选中的药品?', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // this.clearHeightLight()
          this.allTableData = this.selections.reduce((prev, item) => {
            let index = prev.findIndex((i) => {
              return (
                i.jiaGeID == item.jiaGeID &&
                i.shengChanPH == item.shengChanPH &&
                i.jinJia == item.jinJia &&
                i.yaoPinXQ == item.yaoPinXQ
              );
            });
            //编辑状态存储id
            if (index > -1) {
              prev.splice(index, 1);
            }
            return prev;
          }, this.allTableData);
          //处理末尾删除时，数据被清空则回退一页
          if (
            this.allTableData.length == 0 ||
            this.allTableData[this.allTableData.length - 1].yaoPinMC
          ) {
            this.newTableRow();
          }
          MdMessage.success('删除成功');
        })
        .catch(() => {
          MdMessage.info('已取消删除');
        });
    },
    async handleSave(type) {
      const tableData = cloneDeep(this.allTableData);
      const tableLen = tableData.length;
      if (tableLen == 1) {
        MdMessage.warning('养护列表为空！');
        return;
      }
      this.loading = true;
      //判断是否有空值
      let nullIndex = this.allTableData.findIndex((item, index) => {
        if (index == tableLen - 1) return false;
        return !item.yaoPinMC;
      });
      if (nullIndex > -1) {
        this.clearHeightLight();
        const { page, domIndex } = this.tableDomHeightLight(nullIndex);
        MdMessage.error(`第${domIndex + 1}条,药品名称为空`);
        this.loading = false;
        return;
      }
      //删除最后一个
      tableData.splice(tableLen - 1, 1);
      //新增
      if (!this.id) {
        //新增参数处理
        const params = {
          yangHuLXDM: this.query.yangHuLXDM,
          yangHuLXMC: this.query.yangHuLXMC,
          yangHuRQ: this.query.yangHuRQ,
          beiZhu: this.query.beiZhu,
          addList: tableData,
          updateList: [],
          deleteList: [],
        };
        try {
          //新增养护单
          const result = await SaveYangHuDan(params);
          MdMessage.success('保存成功!');
          //刷新列表页 并关闭标签页
          eventBus.$emit('yangHuJL:refresh');
          this.closeTab();
        } catch (err) {
          logger.error(err);
          // MdMessage.warning(err);s
        } finally {
          this.loading = false;
        }
      } else {
        tableData.forEach((el) => {
          el.yaoPinXQ = this.formatYPXQ(el.yaoPinXQ);
        });
        //编辑养护单
        const params = {
          yangHuDan: this.id,
          yangHuRQ: this.query.yangHuRQ,
          beiZhu: this.query.beiZhu,
          yangHuLXDM: this.query.yangHuLXDM,
          yangHuLXMC: this.query.yangHuLXMC,
          addList: [],
          updateList: [],
          deleteList: [],
        };
        params.addList = tableData.filter((item) => {
          if (!item.id) {
            return item;
          }
        });
        params.deleteList = this.editTableData.reduce((prev, item) => {
          let flag = tableData.some((i) => i.id === item.id);
          if (!flag) {
            prev.push(item.id);
          }
          return prev;
        }, []);
        params.updateList = tableData.filter((item) => {
          return this.editTableData.some(
            (i) => i.id === item.id && !isEqual(item, i),
          );
        });
        try {
          const result = await SaveYangHuDan(params);
          MdMessage.success('保存成功!');
          //刷新列表页 并关闭标签页
          eventBus.$emit('yangHuJL:refresh');
          this.closeTab();
        } catch (err) {
          logger.error(err);
        } finally {
          this.loading = false;
        }
      }
    },

    //关闭Tab
    closeTab() {
      let closeTabKey =
        this.$route.query.id || this.viewManager.currentPage.name;
      this.viewManager.close(closeTabKey);
    },
    //定位
    async handleDingWei(data) {
      if (!data) return;
      if (!(Array.isArray(this.allTableData) && this.allTableData.length > 0)) {
        MdMessage.warning('养护单列表为空,不能定位！');
        return;
      }
      try {
        this.loading = true;
        this.clearHeightLight();
        const result = await this.$refs.bizYaoPinPH.show({
          yaoPinMC: data.yaoPinMC,
          jiaGeID: data.jiaGeID,
        });
        this.tiShiMC = data.yaoPinMC;
        let index = this.allTableData.findIndex((item) => {
          return (
            item.jiaGeID == result.jiaGeID &&
            item.shengChanPH == result.shengChanPH &&
            item.jinJia == result.jinJia
          );
        });
        if (index == -1) {
          MdMessage.warning('未找到该药品');
          return;
        }
        this.tableDomHeightLight(index);
      } finally {
        this.loading = false;
      }
    },
    //获取dom,清除高亮
    clearHeightLight() {
      //获取table DOM
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(`.vxe-table--body`);
      }
      // 清除高亮
      const rowList = Array.from(
        this.tableBodyEle.querySelectorAll(`.vxe-body--row`),
      );
      rowList.forEach((item) => {
        item.classList.remove('heightLight');
      });
    },
    //dom定位
    tableDomHeightLight(index) {
      let pageIndex = parseInt(index);
      let page = pageIndex > 0 ? pageIndex + 1 : 1;
      let domIndex = index;
      //获取高亮dom节点
      const childDom =
        this.tableBodyEle.querySelectorAll(`.vxe-body--row`)[domIndex];
      this.$nextTick(() => {
        this.$refs.xTable.setCurrentRow(this.allTableData[domIndex]);
      });
      childDom.classList.add('heightLight');
      this.loading = false;
      return { page, domIndex };
    },
    //重置当前row
    resetTableRow(row) {
      const resetRow = newRow();
      for (let key in row) {
        if (resetRow[key] !== void 0) {
          row[key] = resetRow[key];
        } else {
          delete row[key];
        }
      }
    },
    //新增一行
    newTableRow() {
      let isFull = this.allTableData.every((item) => item.yaoPinMC);
      if (!isFull) return;
      this.allTableData.push(newRow());
      // this.clearHeightLight()
    },
    //table选中事件
    selectionChange(selection) {
      this.selections = selection;
    },
    checkboxChange() {
      this.selections = this.$refs.xTable.getCheckboxRecords();
    },
    selectAllCheckboxChange(checked) {
      this.selections = checked.records;
    },
    sortChangeEvent({ column, property, order }) {
      if (order) {
        //删除最后一个空白行
        if (!this.allTableData[this.allTableData.length - 1].jiaGeID) {
          this.allTableData.splice(this.allTableData.length - 1, 1);
        }
        //按照首字母排序
        this.allTableData = sortBy(this.allTableData, (item) => {
          //判断第一个字符是不是数字
          const baiFangWZ = item.baiFangWZ ? item.baiFangWZ : '-';
          const pinyin = makePY(baiFangWZ);
          if (pinyin) return pinyin.toLocaleUpperCase();
        });
        if (order === 'desc') {
          this.allTableData.reverse();
        }
        this.$nextTick(() => {
          this.allTableData.push(newRow());
        });
      }
    },

    /**
     * 延时关闭上一节点， 供下拉选择时，回车事件获取表格输入定位。
     * @param value
     * @param cellRef
     */
    selectChange(value, cellRef) {
      if (!value) {
        setTimeout(() => {
          // cellRef.endEdit();
        }, 200);
      }
    },
    handleYangHuFF(row, value) {
      row.yangHuFFMC = this.yangHuFFOptions.find((item) => {
        return item.value == value;
      })?.label;
    },
    handleBaoZhuangZL(row, value) {
      row.baoZhuangZLMC = this.baoZhuangZLOptions.find((item) => {
        return item.value == value;
      })?.label;
    },
    handleYangHuJG(row, value) {
      row.yangHuJGMC = this.yangHuJGOptions.find((item) => {
        return item.value == value;
      })?.label;
      if (row.yangHuJGDM === '1') {
        row.chuLiYJMC = '';
        row.chuLiYJDM = '';
      }
    },
    handleChuLiYJ(row, value) {
      row.chuLiYJMC = this.chuLiYJOptions.find((item) => {
        return item.value == value;
      })?.label;
    },
    handleYangHuLY(row, value) {
      row.yangHuLYMC = this.yangHuLYOptions.find((item) => {
        return item.value == value;
      })?.label;
    },
    handleCunChuTJ(row, value) {
      row.cunChuTJMC = this.cunChuTJOptions.find((item) => {
        return item.value == value;
      })?.label;
    },
    //按库存>0生成
    async handleAnKuCXXSC() {
      try {
        this.loading = true;
        let data = await this.$refs.KuCunDialog.showModal();
        // data = await ShengChengDaYuLYP({ yaoPinLXDM: this.yaoPinLXDM });
        data.forEach((item) => {
          item.yaoPinZC = item.yaoPinMC + ' ' + item.yaoPinGG;
          item.yaoPinXQ = this.formatYPXQ(item.yaoPinXQ);
          item.yaoPinXX = {
            yaoPinMC: item.yaoPinMC, //药品名称
            yaoPinLXDM: item.yaoPinLXDM, //药品类型DM
            yaoPinLXMC: item.yaoPinLXMC, //药品类型MC
            jiaGeID: item.jiaGeID,
            yaoPinGG: item.yaoPinGG,
            baoZhuangDW: item.baoZhuangDW, //单位
            chanDiID: item.chanDiID,
            chanDiMC: item.chanDiMC,
          };
        });
        this.allTableData = data;
        this.temporary = cloneDeep(data);
        //清除高亮
        this.newTableRow();
        this.clearHeightLight();
      } catch (error) {
        logger.error(error);
      } finally {
        this.loading = false;
      }
    },
    //打印
    async handlePrint() {
      const params = {
        yangHuDan: this.id,
      };
      try {
        this.loading = true;
        await printByUrl('YKXT012', params);
        MdMessage.success('打印成功！');
      } catch (e) {
        MdMessageBox({
          title: '系统消息',
          type: 'error',
          message: `打印失败！`,
          confirmButtonText: '我知道了',
        });
        // Message.error('打印失败！')
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    // 'md-virtualized-editable-table': VirtualizedEditableTable,
    // 'md-editable-table-pro':MdEditableTablePro,
    'biz-yaopindw': BizYaoPinDW,
    'md-pagination': MdPagination,
    'biz-yaopinph': BizYaoPinPH,
    KuCunDialog,
  },
};
</script>

<style lang="scss" scoped>
/* @use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *; */

::v-deep .vxe-table--render-default .vxe-header--column {
  background-color: rgb(var(--md-color-1));
}
::v-deep .vxe-header--column {
  padding: 0px !important;
}
::v-deep .vxe-header--column .vxe-cell {
  padding: 7px !important;
}
::v-deep .vxe-body--column {
  height: 33px !important;
}
::v-deep(.vxe-table .vxe-cell--sort) {
  vertical-align: sub;
}

/* 控制列设置弹窗的高度 */

.lie-shezhi {
  cursor: pointer;
  margin-left: 30px;
  color: rgb(var(--md-color-6));
}

.heightLight {
  > td {
    background: #e2efff;
  }
}

.#{$md-prefix}-box {
  display: flex;
  align-items: center;

  &:last-child {
    margin-left: 8px;
  }
}

.#{$md-prefix}-inventory-container {
  height: 100%;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  background-color: #eaeff3;
  padding: 8px;
}

::v-deep .select-select .mediinfo-vela-yaoku-web-bmis-input__inner,
::v-deep
  .mediinfo-vela-yaoku-web-bmis-select
  .mediinfo-vela-yaoku-web-bmis-input__tag {
  text-overflow: ellipsis;
  white-space: nowrap; // 设置段落文本不换行(不换行才有可能行溢出)；
  overflow: hidden; // 关闭滚动条，超出部分隐藏；
}

.#{$md-prefix}-add-inventory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;

  &-header {
    flex-shrink: 0;

    &__action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      // background-color: #edf6fd;
      background-color: getCssVar('color-1');
      padding: 0 8px;

      &__left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        // background-color: #e3ecf3;
        border-radius: 4px;

        .#{$md-prefix}-yaopin-search {
          width: 180px;
        }

        .#{$md-prefix}-title {
          font-weight: 600;
          color: getCssVar('color-8');
          font-size: 16px;
          line-height: 28px;
          margin: 0 8px;

          &::before {
            top: 6px;
          }

          i {
            font-size: 18px;
          }
        }

        .#{$md-prefix}-more-action {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          height: 30px;
          background-color: #ffffff;
          border-radius: 4px;
          margin-left: 4px;
          cursor: pointer;

          &:hover {
            background: #e6f7ff;
          }
        }
      }

      &__right {
        .#{$md-prefix}-button {
          margin-left: 8px;

          &.#{$md-prefix}-button--text {
            margin-right: 4px;
          }
        }
      }
    }

    &__note {
      display: flex;
      height: 47px;
      align-items: center;
      box-sizing: border-box;
      padding: 0 8px;

      .margin-right-8 {
        margin-right: 8px;
      }

      label {
        margin: 0 8px;
        flex-shrink: 0;
        font-size: 14px;
      }

      label:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  &-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    padding: 0 8px;

    ::v-deep .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .#{$md-prefix}-table.#{$md-prefix}-table--edit {
        min-height: 0;
        display: flex;
        flex-direction: column;
      }

      .#{$md-prefix}-base-table {
        flex-shrink: 0;

        td.td-text.is-center {
          padding-left: 0px;
        }
      }

      .#{$md-prefix}-base-table__body-wrapper {
        overflow: auto;
        flex: 1;

        .#{$md-prefix}-base-table__row.#{$md-prefix}-heightLight {
          > td {
            background: #e2efff;
          }
        }
      }

      .cell {
        .#{$md-prefix}-date-editor {
          width: 100%;
        }
      }
    }

    ::v-deep .#{$md-prefix}-pagination {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;
    }
  }

  &-footer {
    // position: absolute;
    // bottom: 9px;
    justify-content: space-between;
    padding: 8px;
    flex-shrink: 0;
    line-height: 20px;
    font-size: 14px;

    &__info {
      display: flex;
      justify-content: flex-end;

      .#{$md-prefix}-info__name {
        margin-right: 8px;
      }

      .#{$md-prefix}-margin-right-12 {
        margin-right: 12px;
      }

      &.#{$md-prefix}-right {
        span {
          color: #aaa;
        }
      }
    }

    span {
      color: #666666;

      &.#{$md-prefix}-color-222 {
        color: #222222;
      }

      &.#{$md-prefix}-font-bold {
        font-weight: 600;
        color: #222222;
      }
    }
  }
}
</style>
