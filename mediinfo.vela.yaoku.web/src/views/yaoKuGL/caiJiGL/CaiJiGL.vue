<template>
  <div :class="prefixClass('CaiJiGL-content')">
    <div :class="prefixClass('procurement-left')">
      <div :class="prefixClass('procurement-top')">
        <div class="flex-sp" :class="prefixClass('margB')">
          <md-select
            v-model="query.jiBieBZ"
            placeholder="请选择"
            :class="prefixClass('procurement-date')"
            @change="getLeftXiangMu"
          >
            <md-option
              v-for="item in jiBieList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </md-option>
          </md-select>
          <md-button
            type="primary"
            noneBg
            :icon="prefixClass('icon-xinzeng')"
            @click="handleAdd('')"
            >新增</md-button
          >
        </div>
        <md-input
          v-model="query.leftQuery"
          placeholder="请输入项目名称"
          :suffix-icon="prefixClass('icon-seach cursor-pointer')"
          @change="getLeftXiangMu"
          @clear="getLeftXiangMu"
        >
        </md-input>
      </div>
      <div class="asideBox">
        <md-scrollbar
          v-if="typeList.length > 0"
          :native="false"
          class="Aside-scrollbar"
        >
          <div
            :class="['type-list', typeActive == item.id ? 'active' : '']"
            v-for="item in typeList"
            :key="item.id"
            @click="handleTypeClick(item)"
          >
            <span class="name">{{ item.jiCaiXMMC }}</span>
            <span class="edit" v-show="item.id !== 1">
              <md-button
                type="primary"
                :icon="prefixClass('icon-bianji')"
                noneBg
                @click="handleAdd(item)"
              ></md-button
            ></span>
          </div>
        </md-scrollbar>
        <md-empty v-else class="Aside-scrollbar empty"></md-empty>
      </div>
    </div>
    <div :class="prefixClass('procurement-right')" v-show="typeActive">
      <div
        :class="prefixClass('procurement-right-detail')"
        v-if="typeActive !== 1"
      >
        <div class="flex-sp">
          <md-title type="grace" :label="xiangMuDetail.jiCaiXMMC"></md-title>
          <md-button
            type="primary"
            noneBg
            :icon="prefixClass('icon-fujian')"
            @click="handleFuJian"
            >查看附件</md-button
          >
        </div>
        <div class="info">
          <span class="labelName"
            >级别：<span>{{ xiangMuDetail.jiBie }}</span></span
          >
          <span class="labelName"
            >纳入品种数量：<span>{{ xiangMuDetail.naRuPZSL }}</span></span
          >
          <span class="labelName"
            >中选品种数量：<span>{{ xiangMuDetail.zhongXuanPZSL }}</span></span
          >
          <span class="labelName"
            >流标品种数量：<span>{{ xiangMuDetail.liuBiaoPZSL }}</span></span
          >
          <span class="labelName"
            >我院涉及品种数量：<span>{{
              xiangMuDetail.woYuanSJPZSL
            }}</span></span
          >
          <span class="labelName"
            >有任务量品种数量：<span>{{
              xiangMuDetail.youRenWLPZSL
            }}</span></span
          >
        </div>
      </div>
      <div :class="prefixClass('procurement-right-top')">
        <span>
          <md-select
            v-model="query.stateList"
            style="width: 240px"
            :class="prefixClass('procurement-date')"
            placeholder="选择状态"
            @change="handleSearch"
          >
            <md-option value="0" label="全部状态"></md-option>
            <md-option value="1" label="未开始"></md-option>
            <md-option value="2" label="在期"></md-option>
            <md-option value="3" label="过期"></md-option>
          </md-select>
          <md-input
            v-if="typeActive == 1"
            width="220px"
            v-model="query.likeQuery"
            placeholder="请输入项目名称或轮次名称"
            :suffix-icon="prefixClass('icon-seach cursor-pointer')"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
          </md-input>
          <md-input
            v-if="typeActive != 1"
            width="220px"
            v-model="query.jiCaiXMMC"
            placeholder="请输入项目名称"
            :class="prefixClass('procurement-date')"
            :suffix-icon="prefixClass('icon-seach cursor-pointer ')"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
          </md-input>
          <md-input
            v-if="typeActive != 1"
            width="220px"
            v-model="query.xiangMuMC"
            placeholder="请输入轮次名称"
            :suffix-icon="prefixClass('icon-seach cursor-pointer')"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
          </md-input>
          <biz-yaopindw
            v-if="typeActive == 1"
            v-model="yaoPingObj"
            showSuffix
            :class="prefixClass('input-seach')"
            @change="handleYaoPin($event)"
            placeholder="药品名称"
            style="width: 240px; margin-left: 8px"
          >
          </biz-yaopindw>
        </span>
        <md-button
          v-if="typeActive == 1"
          type="primary"
          plain
          @click="handleXianLiang"
        >
          竞品限量设置</md-button
        >
        <md-button
          v-if="typeActive != 1"
          type="primary"
          :icon="prefixClass('icon-jia')"
          @click="handleKaiDan"
        >
          集采轮次</md-button
        >
      </div>
      <div :class="prefixClass('CaiJiGL-table')">
        <md-table-pro
          :columns="columns"
          :onFetch="handleFetch"
          height="100%"
          :stripe="false"
          highlight-current-row
          :class="prefixClass('table')"
          ref="table"
        >
          <template v-slot:xiangMuMC="{ row }">
            <span
              :class="prefixClass('CaiJiGLdh')"
              @click="handleRowClick($event, row)"
              >{{ row.xiangMuMC }}</span
            >
          </template>
          <template v-slot:zhuangTaiMC="{ row }">
            <div
              v-if="row.zhuangTaiDM == 1"
              :class="prefixClass(['CaiJiGL-state', 'yellow'])"
            >
              {{ row.zhuangTaiMC }}
            </div>
            <div
              v-if="row.zhuangTaiDM == 2"
              :class="prefixClass(['CaiJiGL-state', 'green'])"
            >
              {{ row.zhuangTaiMC }}
            </div>
            <div
              v-if="row.zhuangTaiDM == 3"
              :class="prefixClass(['CaiJiGL-state', 'red'])"
            >
              {{ row.zhuangTaiMC }}
            </div>
          </template>

          <template v-slot:operate="{ row }">
            <md-button
              type="primary"
              size="small"
              noneBg
              @click="handleRowClick($event, row)"
            >
              查看
            </md-button>
            <md-button
              type="primary"
              size="small"
              noneBg
              @click="handleEdit(row)"
            >
              编辑
            </md-button>
            <md-button
              type="primary"
              size="small"
              noneBg
              @click="handleDelete(row)"
            >
              作废
            </md-button>
          </template>
        </md-table-pro>
      </div>
    </div>
    <div v-show="!typeActive" :class="prefixClass('procurement-empty')">
      <md-empty class="Aside-scrollbar empty"></md-empty>
    </div>
    <xiangqiangdan ref="CaiJiGLdetail" size="75%" />
    <AddJiCaiXM ref="AddJiCaiXM" @search="getLeftXiangMu" />
    <FuJianDialog ref="FuJianDialog" />
    <XianLiang ref="XianLiang" @search="getLeftXiangMu" />
  </div>
</template>
<script>
import {
  GetJiCaiJHDCount,
  GetJiCaiJHDList,
  ZuoFeiJiCaiJHD,
  getJiCaiXMList,
  getJiCaiJHDListByJCXMID,
  getJiCaiJHDCountByJCXMID,
} from '@/service/yaoPinYK/JiCaiJHD';
import { MdFrameset, MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { MdTablePro } from '@mdfe/medi-ui-pro';
import dayjs from 'dayjs';
import XiangQingDan from './components/XiangQingDan';
import { logger } from '@/service/log';
import AddJiCaiXM from './components/AddJiCXMDialog.vue';
import FuJianDialog from './components/FuJianDialog.vue';
import XianLiang from './components/XianLiangSZDialog';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
const initQuery = () => {
  return {
    jiBieBZ: 0,
    leftQuery: '',
    stateList: '0',
    likeQuery: '',
    jiCaiXMMC: '',
    xiangMuMC: '',
  };
};
export default {
  name: 'CaiJiGL',
  data() {
    return {
      yaoPingObj: '',
      leftCurrentRow: {},
      typeActive: 1,
      typeMC: '',
      typeList: [],
      xiangMuDetail: {},
      query: initQuery(),
      jiBieList: [
        { label: '全部级别', value: 0 },
        { label: '省级', value: 1 },
        { label: '国家级', value: 2 },
      ],
      columns: [
        {
          prop: 'jiCaiXMMC',
          label: '项目名称',
          minWidth: 220,
          hidde: false,
        },
        {
          slot: 'xiangMuMC',
          prop: 'xiangMuMC',
          label: '轮次名称',
          minWidth: 220,
        },
        {
          prop: 'jiBieBZ',
          label: '级别',
          width: 80,
          formatter: (row, column, cellValue, index) => {
            return cellValue == 1 ? '省级' : '国家级';
          },
        },
        {
          prop: 'kaiShiRQ',
          label: '开始日期',
          width: 180,
          sortable: true,
          formatter: (row, column, cellValue, index) => {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'jieShuRQ',
          label: '结束日期',
          width: 180,
          sortable: true,
          formatter: (row, column, cellValue, index) => {
            return dayjs(cellValue).format('YYYY-MM-DD');
          },
        },
        {
          prop: 'zhiDanSJ',
          label: '制单时间',
          width: 180,
          formatter: (row, column, cellValue, index) => {
            return dayjs(cellValue).format('YYYY-MM-DD HH:mm');
          },
        },

        {
          prop: 'zhiDanRXM',
          label: '制单人',
          width: 130,
        },
        {
          prop: 'zhuangTaiMC',
          slot: 'zhuangTaiMC',
          label: '状态',
          width: 100,
          align: 'center',
        },
        {
          slot: 'operate',
          type: 'operate',
          label: '操作',
          fixed: 'right',
          width: 120,
        },
      ],
    };
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val.path === '/CaiJiGL' || val.path === '/caiJiGL') {
          this.handleSearch();
        }
      },
    },
    // 监听当前选中项目级别是否为全部
    typeActive: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.columns[0].hidden = val == 1 ? false : true;
      },
    },
  },
  mounted() {
    this.getLeftXiangMu();
  },
  methods: {
    // 获取左侧列表 默认选择第一个
    async getLeftXiangMu() {
      try {
        const { leftQuery, jiBieBZ } = this.query;
        const data = await getJiCaiXMList({
          likeQuery: leftQuery,
          jiBieBZ: jiBieBZ == 0 ? '' : jiBieBZ,
        });
        this.typeList = data;
        if (data.length > 0) {
          data[0].id = 1;
        }
        const row =
          this.leftCurrentRow && this.leftCurrentRow.id
            ? this.leftCurrentRow
            : data.length > 0
              ? data[0]
              : '';
        this.handleTypeClick(row);
      } catch (e) {
        console.error(e);
      }
    },
    async handleFetch({ page, pageSize }, config) {
      try {
        if (!this.typeActive) {
          this.xiangMuDetail = {};
          return { items: [], total: 0 };
        }
        const { stateList, jiBieBZ, jiCaiXMMC, xiangMuMC, likeQuery } =
          this.query;
        const params = {
          jiCaiXMID: this.typeActive == 1 ? '' : this.typeActive,
          stateList: stateList,
          pageIndex: page,
          pageSize: pageSize,
          jiBieBZ: jiBieBZ,
          likeQuery: this.typeActive == 1 ? likeQuery : '',
          jiCaiXMMC: this.typeActive == 1 ? '' : jiCaiXMMC,
          xiangMuMC: this.typeActive == 1 ? '' : xiangMuMC,
          yaoPinMC: this.yaoPingObj ? this.yaoPingObj.yaoPinMC : '',
        };

        const [items, total] = await Promise.all([
          getJiCaiJHDListByJCXMID(params),
          getJiCaiJHDCountByJCXMID(params),
        ]);
        this.xiangMuDetail = items;
        return { items: items.jiCaiJHDList, total };
      } catch (error) {
        logger.error(error);
      }
    },
    //调用table刷新
    handleSearch() {
      if (this.$refs.table) this.$refs.table.search({ pageSize: 100 });
    },
    handleYaoPin(data) {
      this.yaoPingObj = data;
      this.handleSearch();
    },
    // 新增/编辑项目级别
    async handleAdd(row) {
      const res = await this.$refs.AddJiCaiXM.showModal(row);
      if (res && res !== 1) {
        this.leftCurrentRow = res;
      }
      this.getLeftXiangMu();
    },
    // 附件
    async handleFuJian() {
      const res = await this.$refs.FuJianDialog.showModal(this.xiangMuDetail);
      await this.getLeftXiangMu();
      this.handleTypeClick(this.xiangMuDetail);
    },
    //切换级别
    handleTypeClick(item) {
      this.typeActive = item ? item.id : '';
      this.typeMC = item ? item.jiCaiXMMC : '';
      this.query.likeQuery = '';
      this.query.jiCaiXMMC = '';
      this.query.xiangMuMC = '';
      this.handleSearch();
    },
    // 竞品限量弹窗
    handleXianLiang() {
      this.$refs.XianLiang.showModal();
    },
    //新增采购计划单
    handleKaiDan() {
      this.$router.push({
        name: 'XinZengCJ',
        query: {
          jiCaiXMMC: this.typeMC,
          jiCaiXMID: this.typeActive,
          jiBieBZ: this.xiangMuDetail.jiBieBZ,
        },
      });
    },
    //编辑采购计划单
    handleEdit(row) {
      this.$router.push({
        name: 'caiJi',
        query: {
          title: '集采项目-' + row.xiangMuMC,
          id: row.id,
          xiangMuMC: row.xiangMuMC,
          jiCaiXMMC: row.jiCaiXMMC,
          jiCaiXMID: this.typeActive,
          kaiShiRQ: row.kaiShiRQ,
        },
      });
    },
    //点击采购单号--打开侧滑
    handleRowClick(e, row) {
      e.stopPropagation();
      this.$refs.CaiJiGLdetail.openDrawer(row);
      this.$refs.table.getComp('table').setCurrentRow(row);
    },
    //作废集采计划单
    async handleDelete(row) {
      const flag = await MdMessageBox.confirm('是否确定作废？', '操作提醒！', {
        type: 'warning',
      })
        .then(() => {
          return ZuoFeiJiCaiJHD({ id: row.id });
        })
        .then((response) => {
          MdMessage({ message: '作废成功', type: 'success' });
          this.handleSearch();
        })
        .catch((error) => {
          if (error !== 'cancel') {
            MdMessageBox({
              title: '系统消息',
              type: 'error',
              message: error.message,
              confirmButtonText: '我知道了',
            });
          }
        });
    },
  },
  components: {
    'md-table-pro': MdTablePro,
    'md-frameset': MdFrameset,
    xiangqiangdan: XiangQingDan,
    AddJiCaiXM,
    FuJianDialog,
    XianLiang,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$namespace}-CaiJiGLdh {
  float: left;
  margin-right: 5px;
  cursor: pointer;
  color: getCssVar('color-6');

  &:hover {
    color: getCssVar('color-6');
    text-decoration: underline;
    line-height: 20px;
  }
}

.#{$md-prefix}-CaiJiGL-content {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  background: #f0f2f5;

  .#{$md-prefix}-procurement-left {
    width: 240px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 8px;
    box-sizing: border-box;
    background-color: #fff;
    .asideBox {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    .Aside-scrollbar {
      flex: 1;
      min-height: 0;
      margin-top: getCssVar('spacing-3');

      &.empty {
        justify-content: center;
      }

      .type-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin-bottom: getCssVar('spacing-3');
        padding: 0 getCssVar('spacing-3');
        cursor: pointer;

        .name {
          flex: 1;
        }

        .edit {
          display: none;
        }

        &.active {
          background-color: getCssVar('color-2');
        }

        &:hover {
          background-color: getCssVar('color-1');

          .edit {
            display: block;
          }
        }
      }
    }
  }
  .#{$md-prefix}-procurement-right {
    flex: 1;
    overflow: hidden;
    margin: 8px 8px 0 8px;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    &-top {
      display: flex;
      justify-content: space-between;
      padding: 8px 8px 0 8px;
      box-sizing: border-box;
    }
    &-detail {
      width: 100%;
      height: 66px;
      background: linear-gradient(270deg, #f2f6fc 0%, #e9f4fe 100%);
      padding: 8px;
      padding-bottom: 0;
      box-sizing: border-box;
      overflow: hidden;
      .flex-sp {
        display: flex;
        justify-content: space-between;
      }
      .info {
        margin: 4px 18px 0 18px;
        display: flex;
        /* margin-top: 4px; */
        justify-content: space-between;
        span {
          color: #222;
          /* margin-right: 20px; */
          /* box-sizing: border-box; */
        }
        .labelName {
          color: #666666;
        }
      }
    }
  }
  .#{$md-prefix}-procurement-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    .flex-sp {
      display: flex;
      justify-content: space-between;
    }
  }

  .#{$md-prefix}-procurement-empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .#{$md-prefix}-margB {
    margin-bottom: getCssVar('spacing-3');
  }

  .#{$md-prefix}-procurement-date {
    margin-right: 12px;
    /* width: 220px; */
  }

  .#{$md-prefix}-CaiJiGL-table {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 8px 8px 0 8px;
    box-sizing: border-box;
    /* padding-bottom: 8px; */

    .#{$md-prefix}-table {
      flex: 1;
      overflow: hidden;
    }
  }
}

.#{$md-prefix}-CaiJiGL-state {
  width: 4em;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 2px;
  margin: auto;

  &.#{$md-prefix}-yellow {
    background-color: rgba(255, 238, 219, 1);
    color: #ff8600;
  }

  &.#{$md-prefix}-green {
    background-color: rgba(228, 255, 216, 1);
    color: #4ac110;
  }

  &.#{$md-prefix}-red {
    background-color: rgba(255, 235, 235, 1);
    color: #f12933;
  }
}

::v-deep .#{$md-prefix}-button--primary.is-noneBg.#{$md-prefix}-button--small {
  padding: getCssVar('spacing-1');
}
</style>
