<template>
  <md-dialog
    title="竞品限量设置"
    v-model="visibleDialog"
    :before-save="handleSave"
    :body-loading="loading"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    size="medium"
    @close="closeModal"
  >
    <div class="xiangliangszdialog">
      <div class="xiangliangszdialog-header">
        <md-title label="竞品限量设置" class="mar-r-8"></md-title>
        <md-switch
          v-model="form.shiFouQY"
          :activeValue="1"
          :inactiveValue="0"
          size="small"
        ></md-switch>
      </div>
      <div class="neiRong">
        集采药品(中选品规)的任务未完成时，限制量为前
        <md-input
          v-model="form.weiWanCXLYF"
          class="widthInput"
          v-number="{ min: 1 }"
        >
        </md-input
        >个月集采药品(中选品规)月均使用量的
        <md-input
          v-model="form.weiWanCXLBFB"
          class="widthInput"
          v-number.float="{ decimal: 2, min: 0 }"
        >
          <template #suffix> % </template>
        </md-input>
      </div>
      <div class="neiRong">
        集采药品(中选品规)的任务完成后，限制量为前
        <md-input
          v-model="form.yiWanCXLYF"
          class="widthInput"
          v-number="{ min: 1 }"
        ></md-input
        >个月集采药品(中选品规)月均使用量的
        <md-input
          v-model="form.yiWanCXLBFB"
          class="widthInput"
          v-number.float="{ decimal: 2, min: 0 }"
        >
          <template #suffix> % </template>
        </md-input>
      </div>
    </div>

    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="mode == 'edit'"
          :class="prefixClass('normal__button')"
          plain
          type="danger"
          style="float: left"
          @click="handleDelete"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="closeModal"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { h } from 'vue';
import { MdMessage } from '@mdfe/medi-ui';
import { GetJiCaiXLKZSZ, SaveJiCaiXLKZSZ } from '@/service/yaoPinYK/JiCaiJHD';
export default {
  name: 'xiangliangszdialog',
  data() {
    return {
      visibleDialog: false,
      loading: false,
      form: {
        shiFouQY: 1,
        weiWanCXLYF: '',
        weiWanCXLBFB: '',
        yiWanCXLYF: '',
        yiWanCXLBFB: '',
      },
    };
  },
  methods: {
    async showModal(val) {
      this.visibleDialog = true;
      this.form = await GetJiCaiXLKZSZ();
    },
    closeModal() {
      this.visibleDialog = false;
    },
    async handleSave() {
      await SaveJiCaiXLKZSZ({
        ...this.form,
        shiFouQY: this.form.shiFouQY ? 1 : 0,
      });
      MdMessage.success('保存成功！');
      this.closeModal();
      this.$emit('search');
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}
.xiangliangszdialog {
  .mar-r-8 {
    margin-right: 8px;
  }
  .widthInput {
    width: 74px;
    margin: 0 8px;
  }
  .xiangliangszdialog-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 16px;
  }
  .neiRong {
    margin-bottom: 8px;
    margin-left: 8px;
  }
}
</style>
