<template>
  <md-dialog
    title="集采项目"
    v-model="visibleDialog"
    :before-save="handleSave"
    :body-loading="loading"
    width="1000px"
    height="540px"
    @close="closeModal"
  >
    <div class="formDiv">
      <md-form
        v-loading="loading"
        :model="formModel"
        :rules="formRules"
        label-width="140px"
        use-status-tooltip-icon
        ref="form"
      >
        <md-row>
          <md-col :span="12">
            <md-form-item label="项目名称" prop="jiCaiXMMC">
              <md-input v-model="formModel.jiCaiXMMC"></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="级别" prop="jiBieBZ">
              <md-radio-group
                v-model="formModel.jiBieBZ"
                border
                style="width: 100%"
              >
                <md-radio :label="1">省级</md-radio>
                <md-radio :label="2">国家级</md-radio>
              </md-radio-group>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="纳入品种数量" prop="naRuPZSL">
              <md-input
                v-model="formModel.naRuPZSL"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="中选品种数量" prop="zhongXuanPZSL">
              <md-input
                v-model="formModel.zhongXuanPZSL"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="流标品种数量" prop="liuBiaoPZSL">
              <md-input
                v-model="formModel.liuBiaoPZSL"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="我院涉及品种数量" prop="woYuanSJPZSL">
              <md-input
                v-model="formModel.woYuanSJPZSL"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="有任务量品种数量" prop="youRenWLPZSL">
              <md-input
                v-model="formModel.youRenWLPZSL"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="12">
            <md-form-item label="顺序号" prop="shunXuHao">
              <md-input
                v-model="formModel.shunXuHao"
                v-number.float="{ min: 0, decimal: 0 }"
              ></md-input>
            </md-form-item>
          </md-col>
          <md-col :span="24">
            <md-form-item label="项目附件">
              <md-upload
                :file-list="formModel.xiangMuFJ"
                class="upload-demo"
                listPosition="top"
                :headers="headers"
                action="/mediinfo-lyra-nengli/api/v1.0/cunchu/wenjian/uploadfile"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                multiple
                :on-success="handleAvatarSuccess"
                @download="handledownload"
                isDownload
                isRemove
              >
                <div>
                  <md-button type="primary" plain>点击上传</md-button>
                  <span
                    style="color: #aaaaaa; font-size: 14px; padding-left: 8px"
                    >附件支持 .doc .xls .pdf .jpg .png格式</span
                  >
                </div>
              </md-upload>
            </md-form-item>
          </md-col>
        </md-row>
      </md-form>
    </div>

    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="mode == 'edit'"
          :class="prefixClass('normal__button')"
          plain
          type="danger"
          style="float: left"
          @click="handleDelete"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="closeModal"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { getAuthClient } from '@mdfe/auth';
import { devToken, devJiGouXX } from '@/service/sys/login.js';
import { inMicroApp } from '@mdfe/stark-app';
import { MdMessageBox, MdMessage } from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
import { SaveJiCaiXM, ZuoFeiJiCaiXM } from '@/service/yaoPinYK/JiCaiJHD';
import { getUserInfo } from '@/system/utils/local-cache';
const formModelInit = () => {
  return {
    id: null,
    jiCaiXMMC: null,
    xiangMuFJ: [],
    jiBieBZ: '',
    jiBie: '',
    naRuPZSL: '',
    shunXuHao: 0,
    youRenWLPZSL: '',
    woYuanSJPZSL: '',
    liuBiaoPZSL: '',
    zhongXuanPZSL: '',
  };
};
export default {
  name: 'addjicaixm',
  data() {
    return {
      headers: {},
      formRules: {
        jiCaiXMMC: [
          { required: true, message: '请选择审批日期', trigger: 'change' },
        ],
        jiBieBZ: [
          { required: true, message: '请选择审批日期', trigger: 'change' },
        ],
      },
      mode: 'add',
      visibleDialog: false,
      loading: false,
      formModel: formModelInit(),
      resolve: null,
      reject: null,
    };
  },
  async mounted() {
    //开发环境用本地写死的假token
    let Authorization = null;
    let userInfoHeaders = null;
    if (process.env.NODE_ENV === 'development' && !inMicroApp) {
      Authorization = devToken;
      userInfoHeaders = devJiGouXX;
    } else {
      //生产环境拿lyra给的
      const auth = getAuthClient();
      Authorization = await auth.getAuthorizationHeaderValue();
      userInfoHeaders = getUserInfo([
        'WeiZhiID',
        'WeiZhiMC',
        'KeShiID',
        'KeShiMC',
        'BingQuID',
        'BingQuMC',
        'JiGouID',
        'JiGouMC',
        'ShuRuMLX',
      ]);
    }
    this.headers = {
      Authorization,
      ...userInfoHeaders,
    };
  },
  methods: {
    async showModal(val) {
      this.visibleDialog = true;
      const obj = cloneDeep(val);
      await this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
      this.mode = obj ? 'edit' : 'add';
      if (obj) {
        obj.xiangMuFJ = obj.xiangMuFJ ? JSON.parse(obj.xiangMuFJ) : [];
      }
      this.formModel = obj ? cloneDeep(obj) : formModelInit();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    //删除
    handleRemove(file, fileList) {
      this.formModel.xiangMuFJ = fileList;
    },
    //头像上传
    handleAvatarSuccess(res, file) {
      this.formModel.xiangMuFJ.push({
        storageDownloadPath: file.response.data[0].storageDownloadPath,
        name: file.response.data[0].originFileName,
      });
    },
    handledownload(val) {
      const myUrl = val.response.data[0].storageDownloadPath;
      this.downloadFile(myUrl, val.name);
    },
    downladBase64(ssr) {
      let base64 = this.getBase64Type('doc') + ssr; //类型拼接后端给的不完整base64
      let docName = '联众智慧Lyra数据集规范';
      this.downloadFileByBase64(base64, docName);
    },
    //根据文件后缀 获取base64前缀不同 拼接完整的base64
    getBase64Type(type) {
      switch (type) {
        case 'txt':
          return 'data:text/plain;base64,';
        case 'doc':
          return 'data:application/msword;base64,';
        case 'docx':
          return 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,';
        case 'xls':
          return 'data:application/vnd.ms-excel;base64,';
        case 'xlsx':
          return 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,';
        case 'pdf':
          return 'data:application/pdf;base64,';
        case 'pptx':
          return 'data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,';
        case 'ppt':
          return 'data:application/vnd.ms-powerpoint;base64,';
        case 'png':
          return 'data:image/png;base64,';
        case 'jpg':
          return 'data:image/jpeg;base64,';
        case 'gif':
          return 'data:image/gif;base64,';
        case 'svg':
          return 'data:image/svg+xml;base64,';
        case 'ico':
          return 'data:image/x-icon;base64,';
        case 'bmp':
          return 'data:image/bmp;base64,';
      }
    },
    //将完整的base64转换为blob
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    // * desc: 下载参数入口
    // * @param base64  ：返回数据的blob对象或链接
    // * @param fileName  ：下载后文件名标记
    downloadFileByBase64(base64, fileName) {
      var myBlob = this.dataURLtoBlob(base64);
      var myUrl = URL.createObjectURL(myBlob);
      this.downloadFile(myUrl, fileName);
    },
    // * desc: 下载方法
    // * @param url  ：返回数据的blob对象或链接
    // * @param fileName  ：下载后文件名标记
    downloadFile(url, name) {
      // console.log('url==',url)
      var a = document.createElement('a');
      a.setAttribute('href', url);
      a.setAttribute('download', name);
      a.setAttribute('target', '_blank');
      let clickEvent = document.createEvent('MouseEvents');
      clickEvent.initEvent('click', true, true);
      a.dispatchEvent(clickEvent);
      MdMessage({ message: '下载成功', type: 'success' });
    },
    closeModal() {
      this.$refs.form.resetFields();
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },

    async handleDelete() {
      await MdMessageBox.confirm('确定作废该数据?', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      await ZuoFeiJiCaiXM({ id: this.formModel.id });
      this.closeModal();
      MdMessage({ message: '作废成功', type: 'success' });
      this.resolve(1);
    },
    async handleSave() {
      try {
        const result = await this.$refs.form.validate();
        if (!result) return;
        const formModel = cloneDeep(this.formModel);
        formModel.jiBie = formModel.jiBieBZ == 1 ? '省级' : '国家级';
        formModel.xiangMuFJ = formModel.xiangMuFJ
          ? JSON.stringify(formModel.xiangMuFJ)
          : '';
        const res = await SaveJiCaiXM(formModel);
        this.closeModal();
        MdMessage({ message: '保存成功', type: 'success' });
        this.resolve(res);
      } catch (e) {
        console.error(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.formDiv {
  padding: 0px 44px 12px 20px;
}
.#{$md-prefix}-yaopin-select {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
.upload-demo {
  width: 100%;
}
::v-deep .#{$md-prefix}-upload-list {
  width: 100%;
  margin-bottom: 8px;

  li {
    width: 100%;
    margin-right: 0;
    margin-top: 8px;
  }
}
</style>
