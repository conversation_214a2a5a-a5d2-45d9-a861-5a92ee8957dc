<template>
  <md-dialog
    title="任务量设置"
    v-model="visibleDialog"
    :before-save="handleSave"
    :body-loading="loading"
    size="medium"
    @close="closeModal"
  >
    <div>
      <md-table
        :data="tableData"
        :columns="columns"
        highlight-current-row
        height="100%"
        ref="mdTablePro"
        @current-change="currentChange"
      >
        <template #renwuL="{ row }">
          <md-input v-model="row.renwuL" v-number="{}" placeholder="请填写" />
        </template>
      </md-table>
    </div>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="mode == 'edit'"
          :class="prefixClass('normal__button')"
          plain
          type="danger"
          style="float: left"
          @click="handleDelete"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="closeModal"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { h } from 'vue';
import { MdMessageBox } from '@mdfe/medi-ui';
export default {
  name: 'linshiyysqdialog',
  data() {
    return {
      visibleDialog: false,
      loading: false,
      resolve: null,
      reject: null,
      tableData: [],
      columns: [
        {
          label: '药品名称与规格',
          prop: 'shengChanPH',
          renderHeader: ({ column }) => {
            return h(
              'div',
              { class: this.prefixClass('require') },
              column.label,
            );
          },
          disabled: true,
        },
        {
          label: '产地名称',
          prop: 'yaoPinXQ',
        },
        {
          label: '分组任务量',
          prop: 'renwuL',
          slot: 'renwuL',
          renderHeader: ({ column }) => {
            return h(
              'div',
              { class: this.prefixClass('require') },
              column.label,
            );
          },
        },
      ],
    };
  },
  methods: {
    async showModal(val) {
      this.visibleDialog = true;
      await this.$nextTick(() => {
        this.$refs.form.resetFields();
      });

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeModal() {
      this.visibleDialog = false;
    },
    async handleSave() {},
  },
};
</script>

<style lang="scss" scoped>
::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}
</style>
