<template>
  <bmis-blue-dialog
    v-model:visible="visible"
    width="432px"
    height="240px"
    title="导入集采批次"
    @submit="handleSave"
    appendToBody
    :closeOnClickModal="false"
    save-text="确定"
  >
    <div :class="prefixClass('anKuCXXSC-dialog')">
      <span>集采批次</span>
      <md-cascader
        v-if="visible"
        :options="options"
        :props="props"
        style="width: 80%"
        :show-all-levels="false"
        @change="handleChange"
      >
      </md-cascader>
    </div>
  </bmis-blue-dialog>
</template>

<script>
import {
  GetJiCaiXMDRList,
  ImportJiCaiXMJHDXQ,
} from '@/service/yaoPinYK/JiCaiJHD';
import BlueDialog from '@/components/blue-dialog/index.vue';
import { MdInput, MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import dayjs from 'dayjs';
export default {
  name: 'ankucunxx-dialog',
  data() {
    return {
      visible: false,
      time: [],
      day: 0, //默认显示3天
      options: [],
      xiangMuID: [],
      props: {
        value: 'jiCaiXMJHDID',
        label: 'jiCaiXMJHDMC',
        children: 'jiCaiXMJHDDRList',
      },
    };
  },
  async mounted() {
    this.options = await GetJiCaiXMDRList();
  },
  methods: {
    handleChange(value) {
      this.xiangMuID = value[1];
    },
    async handleSave() {
      try {
        await MdMessageBox.confirm(
          '导入后会覆盖表格数据，确定导入嘛?',
          '操作提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          },
        );
        const res = await ImportJiCaiXMJHDXQ({ id: this.xiangMuID });
        this.resolve(res);
        this.visible = false;
        MdMessage.success('导入成功！');
      } catch (e) {
        console.error(e);
      }
    },
    showModel() {
      this.visible = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
  },
  components: {
    'bmis-blue-dialog': BlueDialog,
    'md-input': MdInput,
  },
};
</script>

<style scoped lang="scss">
.#{$md-prefix}-anKuCXXSC-dialog {
  margin-top: 24px;
  padding-right: 20px;
  box-sizing: border-box;

  div {
    //line-height: 45px;
    display: flex;
    align-items: center;
  }

  &__time {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  span {
    // display: block;
    // width: 170px;
    margin-left: 20px;
    margin-right: 8px;
  }

  // ::v-deep .#{$md-prefix}-input {
  //   width: 140px;
  // }
}
</style>
