<template>
  <md-dialog
    title="集采项目"
    v-model="visibleDialog"
    :before-save="handleSave"
    :body-loading="loading"
    size="medium"
    @close="closeModal"
  >
    <md-form
      v-loading="loading"
      :model="formModel"
      :rules="formRules"
      label-width="140px"
      use-status-icon
      ref="form"
    >
      <md-row>
        <md-col :span="24">
          <md-form-item label="项目附件">
            <md-upload
              :file-list="formModel.xiangMuFJ"
              class="upload-demo"
              listPosition="top"
              :headers="headers"
              action="/mediinfo-lyra-nengli/api/v1.0/cunchu/wenjian/uploadfile"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :on-success="handleAvatarSuccess"
              @download="handledownload"
              isDownload
              isRemove
            >
              <div>
                <md-button type="primary" plain>点击上传</md-button>
                <span style="color: #aaaaaa; font-size: 14px; padding-left: 8px"
                  >附件支持 .doc .xls .pdf .jpg .png格式</span
                >
              </div>
            </md-upload>
          </md-form-item>
        </md-col>
      </md-row>
    </md-form>
    <template v-slot:footer>
      <div :class="prefixClass('dialog-footer')">
        <md-button
          v-if="mode == 'edit'"
          :class="prefixClass('normal__button')"
          plain
          type="danger"
          style="float: left"
          @click="handleDelete"
          >作 废</md-button
        >
        <md-button :class="prefixClass('normal__button')" @click="closeModal"
          >取 消</md-button
        >
        <md-button
          :class="prefixClass('normal__button')"
          type="primary"
          :loading="loading"
          @click="handleSave"
          >保 存</md-button
        >
      </div>
    </template>
  </md-dialog>
</template>

<script>
import { getUserInfo } from '@/system/utils/local-cache';
import { getAuthClient } from '@mdfe/auth';
import { devToken, devJiGouXX } from '@/service/sys/login.js';
import { inMicroApp } from '@mdfe/stark-app';
import { cloneDeep } from 'lodash';
import { MdMessage } from '@mdfe/medi-ui';
import { SaveJiCaiXM } from '@/service/yaoPinYK/JiCaiJHD';
const formModelInit = () => {
  return {
    id: null,
    xiangMuFJ: '',
  };
};
export default {
  name: 'linshiyysqdialog',
  data() {
    return {
      headers: {},
      mode: 'add',
      visibleDialog: false,
      loading: false,
      formModel: formModelInit(),
      resolve: null,
      reject: null,
    };
  },
  async mounted() {
    //开发环境用本地写死的假token
    let Authorization = null;
    let userInfoHeaders = null;
    if (process.env.NODE_ENV === 'development' && !inMicroApp) {
      Authorization = devToken;
      userInfoHeaders = devJiGouXX;
    } else {
      //生产环境拿lyra给的
      const auth = getAuthClient();
      Authorization = await auth.getAuthorizationHeaderValue();
      userInfoHeaders = getUserInfo([
        'WeiZhiID',
        'WeiZhiMC',
        'KeShiID',
        'KeShiMC',
        'BingQuID',
        'BingQuMC',
        'JiGouID',
        'JiGouMC',
        'ShuRuMLX',
      ]);
    }
    this.headers = {
      Authorization,
      ...userInfoHeaders,
    };
  },
  methods: {
    async showModal(val) {
      const obj = cloneDeep(val);
      this.visibleDialog = true;
      await this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
      obj.xiangMuFJ = obj.xiangMuFJ ? JSON.parse(obj.xiangMuFJ) : [];
      this.formModel = obj;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    closeModal() {
      this.visibleDialog = false;
      this.formModel = formModelInit();
    },
    async handleSave() {
      const formModel = cloneDeep(this.formModel);
      formModel.xiangMuFJ = JSON.stringify(formModel.xiangMuFJ);
      await SaveJiCaiXM(formModel);
      this.closeModal();
      MdMessage({ message: '保存成功', type: 'success' });
      this.resolve(1);
    },
    //头像上传
    handleAvatarSuccess(res, file) {
      this.formModel.xiangMuFJ.push({
        storageDownloadPath: file.response.data[0].storageDownloadPath,
        name: file.response.data[0].originFileName,
      });
    },
    handledownload(val) {
      const myUrl = val.response.data[0].storageDownloadPath;
      this.downloadFile(myUrl, val.name);
    },
    // * desc: 下载方法
    // * @param url  ：返回数据的blob对象或链接
    // * @param fileName  ：下载后文件名标记
    downloadFile(url, name) {
      // console.log('url==',url)
      var a = document.createElement('a');
      a.setAttribute('href', url);
      a.setAttribute('download', name);
      a.setAttribute('target', '_blank');
      let clickEvent = document.createEvent('MouseEvents');
      clickEvent.initEvent('click', true, true);
      a.dispatchEvent(clickEvent);
      MdMessage({ message: '下载成功', type: 'success' });
    },
  },
};
</script>

<style lang="scss" scoped>
.#{$md-prefix}-yaopin-select {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
.#{$md-prefix}-display-flex {
  display: flex;
  .#{$md-prefix}-checkbox {
    margin-left: 17px;
    flex-shrink: 0;
  }
  .#{$md-prefix}-form-item {
    flex: 1;
  }
}
.#{$md-prefix}-normal__button {
  width: 64px;
  height: 30px;
}
.upload-demo {
  width: 100%;
}
::v-deep .#{$md-prefix}-upload-list {
  width: 100%;
  margin-bottom: 8px;

  li {
    width: 100%;
    margin-right: 0;
    margin-top: 8px;
  }
}
</style>
