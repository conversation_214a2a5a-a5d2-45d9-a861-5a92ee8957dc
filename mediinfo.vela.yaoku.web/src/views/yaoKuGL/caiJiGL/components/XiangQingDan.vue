<template>
  <md-drawer
    v-model="drawer"
    direction="rtl"
    :modal-class="prefixClass('caijiCustom')"
    :with-header="false"
    :modal="false"
    :append-to-body="false"
    :size="size"
    :class="prefixClass('shouLiQLDrawer')"
    @close="drawerClose"
    ref="shouLiQLDrawer"
  >
    <div
      v-loading="loading"
      :element-loading-text="loadingText"
      class="drawer-container"
    >
      <div :class="prefixClass('details-top')">
        <div :class="prefixClass('details-left')">
          {{ title }}
        </div>
        <div :class="prefixClass('details-right')">
          <md-button
            type="primary"
            plain
            noneBg
            :icon="prefixClass('icon-daochu')"
            class="btn"
            @click="handleDaoChu"
            >预览</md-button
          >
          <span :class="prefixClass('title-close')" @click="drawerClose">
            <md-icon name="cha" />
          </span>
        </div>
      </div>
      <div :class="prefixClass('note-txt')">
        <div>
          <span
            v-if="showInfo.zhuangTaiDM == 1"
            :class="prefixClass(['CaiJiGL-state', 'yellow'])"
          >
            {{ showInfo.zhuangTaiMC }}
          </span>
          <span
            v-if="showInfo.zhuangTaiDM == 2"
            :class="prefixClass(['CaiJiGL-state', 'green'])"
          >
            {{ showInfo.zhuangTaiMC }}
          </span>
          <span
            v-if="showInfo.zhuangTaiDM == 3"
            :class="prefixClass(['CaiJiGL-state', 'red'])"
          >
            {{ showInfo.zhuangTaiMC }}
          </span>
        </div>
        <div>
          <span :class="prefixClass('sub-title')">级别：</span
          >{{ showInfo.jiBie || '-' }}
        </div>
        <div>
          <span :class="prefixClass('sub-title')">开始日期:</span
          >{{ getNodeDay(showInfo.kaiShiRQ) }}
        </div>
        <div>
          <span :class="prefixClass('sub-title')">结束日期:</span
          >{{ getNodeDay(showInfo.jieShuRQ) }}
        </div>
        <div>
          <span :class="prefixClass('sub-title')">制单:</span
          >{{ showInfo.zhiDanRXM }}&nbsp;&nbsp;{{
            getNodeDay(showInfo.zhiDanSJ)
          }}
        </div>
      </div>
      <div :class="prefixClass('details-table')">
        <md-table
          :data="tableData"
          :columns="columns"
          highlight-current-row
          height="100%"
          @row-click="handleTableClick"
          :class="prefixClass('demo-table')"
          ref="tableData"
        >
          <template v-slot:yaoPinMCShow="{ row }">
            <YaoPinShow
              :styleData="row.xianShiXX || {}"
              :yaoPinMC="row.yaoPinMC + ' ' + row.yaoPinGG"
            />
            <!-- {{ row.yaoPinGG }} -->
          </template>
        </md-table>
      </div>
      <md-title label="竞品药品" style="margin: 0 0 8px 8px"></md-title>
      <div :class="prefixClass(' bottomTable')">
        <md-table
          :data="tableDataBottom"
          :columns="columnsBottom"
          height="100%"
          :class="prefixClass('demo-table')"
        >
        </md-table>
      </div>
      <dayin-dialog
        ref="daYinDialog"
        :params="paramsDC"
        :id="'YKXT020'"
        :fileName="'集采详情单'"
        :title="'集采详情单打印预览'"
      />
    </div>
  </md-drawer>
</template>

<script>
import { h } from 'vue';
import DaYinDialog from '@/components/DaYinDialog.vue';
import YaoPinShow from '@/components/YaoPinShow.vue';
import formatJiaGe_2 from '@/system/utils/formatJiaGe_2';
import { GetYaoPinTSSX } from '@/service/yaoPinYF/common';
import { GetJiCaiJHDXQ } from '@/service/yaoPinYK/JiCaiJHD';
import commonData from '@/system/utils/commonData';
import { MdDrawer, MdTable, MdTooltip } from '@mdfe/medi-ui';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import dayjs from 'dayjs';
export default {
  name: 'xiangqingdan',
  props: {
    size: { type: [String, Number], default: '75%' },
  },
  data() {
    return {
      jinJiaXSDW: '',
      tableDataBottom: [],
      caiGouDID: '',
      loading: false,
      params: {},
      paramsDC: {},
      loadingText: '正在加载中...',
      drawer: false, //侧滑状态
      showInfo: {},
      tableData: [], //表格数据
      title: '', //侧滑名称
      columns: [
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'yaoPinLXMC',
          minWidth: 45,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          label: '药品名称与规格',
          slot: 'yaoPinMCShow',
          minWidth: 249,
          // formatter: (row) => {
          //   return row.yaoPinMC + ' ' + row.yaoPinGG;
          // },
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          minWidth: 65,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          width: 68,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.jinJiaXSDW);
          },
        },
        {
          prop: 'heTongLiang',
          label: '合同量',
          minWidth: 86,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue);
          },
        },
        {
          prop: 'biaoZhunGGMC',
          label: '标准规格',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'zhuanHuanBi',
          width: 100,
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              {
                style:
                  'display: flex;justify-content: space-between;align-items: center;',
              },
              [
                h('div', [h('span', '转换比')]),
                h(
                  MdTooltip,
                  { effect: 'light' },
                  {
                    content: () =>
                      h(
                        'span',
                        { style: 'color: #333;font-size:12px;' },
                        '药品最小规格与标准规格的比值',
                      ),
                    default: () =>
                      h('md-icon', {
                        class: 'mediinfo-vela-yaoku-web-icon-tixing-s',
                        style: 'color:#aaa;cursor:pointer;margin-left:4px',
                      }),
                  },
                ),
              ],
            );
          },
        },
        {
          prop: 'biaoZhunGGRWL',
          label: '标准规格任务量',
          minWidth: 120,
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          minWidth: 60,
        },
        {
          prop: 'yiRuKL',
          label: '已入库量',
          align: 'right',
          minWidth: 90,
        },
        {
          prop: 'xiaoHaoLiang',
          label: '消耗量',
          align: 'right',
          width: 94,
        },
        {
          prop: 'biaoZhunGGXHL',
          label: '标准规格消耗量',
          align: 'right',
          width: 134,
        },
        {
          prop: 'shengYuSYL',
          label: '剩余使用量',
          align: 'right',
          width: 114,
          formatter: (row, column, cellValue, index) => {
            return row.heTongLiang ? cellValue : '';
          },
        },
        {
          prop: 'yuanNeiBM',
          label: '院内编码',
          align: 'right',
          width: 114,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          width: 114,
        },
      ],
      columnsBottom: [
        {
          prop: 'yaoPinLXMC',
          minWidth: 45,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          label: '竞品类型',
          prop: 'jingPinLXMC',
        },
        {
          prop: 'yaoPinMC',
          label: '药品名称与规格',
          minWidth: 249,
          formatter: (row) => {
            return row.yaoPinMC + ' ' + row.yaoPinGG;
          },
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          minWidth: 65,
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          width: 68,
          formatter: (row, column, cellValue, index) => {
            return formatJiaGe_2(cellValue, this.jinJiaXSDW);
          },
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          minWidth: 60,
        },
        {
          prop: 'biaoZhunGGMC',
          label: '标准规格',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          prop: 'zhuanHuanBi',
          width: 100,
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              {
                style:
                  'display: flex;justify-content: space-between;align-items: center;',
              },
              [
                h('div', [h('span', '转换比')]),
                h(
                  MdTooltip,
                  { effect: 'light' },
                  {
                    content: () =>
                      h(
                        'span',
                        { style: 'color: #333;font-size:12px;' },
                        '药品最小规格与标准规格的比值',
                      ),
                    default: () =>
                      h('md-icon', {
                        class: 'mediinfo-vela-yaoku-web-icon-tixing-s',
                        style: 'color:#aaa;cursor:pointer;margin-left:4px',
                      }),
                  },
                ),
              ],
            );
          },
        },
        {
          prop: 'yiRuKL',
          label: '已入库量',
          align: 'right',
          minWidth: 90,
        },
        {
          prop: 'xiaoHaoLiang',
          label: '消耗量',
          align: 'right',
          width: 94,
        },
        {
          prop: 'yuanNeiBM',
          label: '院内编码',
          align: 'right',
          width: 114,
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          width: 114,
        },
      ],
      xianShiXX: [],
    };
  },
  computed: {
    /**
     * 计算表格合计进价和合计零售价以及药品总数
     */
    totalData() {
      var jinJiaJE = 0;
      var lingShouJE = 0;
      this.tableData.forEach((item) => {
        jinJiaJE += item.jinJiaJE;
        lingShouJE += item.lingShouJE;
      });
      return {
        jinJiaJE: Number(jinJiaJE).toFixed(3),
        lingShouJE: Number(lingShouJE).toFixed(3),
      };
    },
  },
  async mounted() {
    // 点击页面事件绑定
    window.addEventListener('click', this.handleClickBodyCloseDrawer);
    const res = await getKuFangSZList(['jinJiaXSDWS']);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleClickBodyCloseDrawer);
  },
  methods: {
    /**
     * 打开侧滑
     */
    async openDrawer(data) {
      try {
        this.drawer = true;
        this.loading = true;
        this.caiGouDID = data.id;
        var params = {
          id: data.id,
        };
        const result = await GetJiCaiJHDXQ(params);
        this.showInfo = result;
        this.title = `详情 - ${data.xiangMuMC}`;
        this.tableData = result.caiJiDMXList;
        const row = this.tableData.length > 0 ? this.tableData[0] : {};
        this.$refs.tableData.setCurrentRow(row);
        await this.getXianShiXX(this.tableData);
        const xianShiXX = this.xianShiXX;
        this.tableData.forEach((item) => {
          item.xianShiXX = xianShiXX[item.jiaGeID] || {};
        });
        this.tableDataBottom = this.tableData[0].jiCaiJPYPList || [];
      } finally {
        this.loading = false;
      }
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        res = await GetYaoPinTSSX({
          jiaGeIDList,
          xianShiLXDM: '1',
        });
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      this.xianShiXX = { ...this.xianShiXX, ...xianShiXX };
    },
    /**
     * 关闭侧滑
     */
    drawerClose() {
      this.drawer = false;
    },
    //导出
    handleDaoChu() {
      const params = {
        id: this.caiGouDID,
      };
      this.paramsDC = params;
      this.$refs.daYinDialog.showModal();
    },
    // 点击页面事件
    handleClickBodyCloseDrawer(e) {
      // if (!this.$refs.shouLiQLDrawer.$el.contains(e.target)) {
      //   this.drawerClose()
      // }
      this.drawerClose();
    },
    //药品选择行事件
    handleTableClick(row) {
      this.tableDataBottom = row.jiCaiJPYPList;
    },
    //预览
    async handleYuLan() {
      const params = {
        id: this.caiGouDID,
      };
      this.params = params;
    },
    getNodeDay(value) {
      if (!value) return '';
      return dayjs(value).format('YYYY-MM-DD');
    },
  },
  components: {
    'dayin-dialog': DaYinDialog,
    YaoPinShow,
  },
};
</script>
<style lang="scss">
.#{$md-prefix}-caijiCustom {
  height: unset !important;
  top: 75px !important;
}

.#{$md-prefix}-shouLiQLDrawer {
  width: 80% !important;

  .#{$md-prefix}-drawer__body {
    height: 100%;

    .drawer-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
}
</style>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-details-table {
  display: flex;
  flex: 1;
  min-height: 0;
  margin: 0 8px 8px;
}

.#{$md-prefix}-bottomTable {
  height: 30%;
  margin: 0 8px 8px;
  box-sizing: border-box;
}

.#{$md-prefix}-note-txt {
  color: #222222;
  font-size: 14px;
  line-height: 35px;
  padding: 0 8px;

  > div {
    display: inline-block;
    margin-right: 12px;
  }

  .#{$md-prefix}-sub-title {
    color: #666;
    margin-right: 4px;
  }
}

.#{$md-prefix}-details-top {
  height: 36px;
  // background-color: #f0f5fb;
  background-color: getCssVar('color-1');
  display: flex;
  justify-content: space-between;
  padding: 0 8px;

  .#{$md-prefix}-details-left {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    line-height: 35px;
    flex: 3;
  }

  .#{$md-prefix}-details-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    margin: 0 10px;
    line-height: 35px;
    color: #1e88e5;
    font-size: 14px;
    cursor: pointer;

    .btn {
      margin-right: 8px;
    }
  }
}

.#{$md-prefix}-title-close {
  color: #666;
}

.#{$md-prefix}-CaiJiGL-state {
  display: inline-block;
  width: 4em;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 2px;
  margin: auto;

  &.#{$md-prefix}-yellow {
    background-color: rgba(255, 238, 219, 1);
    color: #ff8600;
  }

  &.#{$md-prefix}-green {
    background-color: rgba(228, 255, 216, 1);
    color: #4ac110;
  }

  &.#{$md-prefix}-red {
    background-color: rgba(255, 235, 235, 1);
    color: #f12933;
  }
}
</style>
