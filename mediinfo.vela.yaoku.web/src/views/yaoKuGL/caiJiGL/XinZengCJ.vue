<template>
  <div :class="prefixClass('procurement')">
    <div :class="prefixClass('procurement-content')">
      <div :class="prefixClass('procurement-top')">
        <div :class="prefixClass('procurement-left')">
          <div :class="prefixClass('procurement-left-lt')">
            <biz-yaopindw
              v-model="yaoPingDW"
              showSuffix
              :class="prefixClass('input-seach')"
              @change="handleDingWei($event)"
            >
            </biz-yaopindw>
            <md-popover
              v-model="popoverValue"
              placement="bottom"
              width="260"
              trigger="click"
              :class="prefixClass('pop-over')"
            >
            </md-popover>
          </div>
        </div>
        <div :class="prefixClass('procurement-right')">
          <!-- <md-button
            type="primary"
            :class="prefixClass('main5')"
            :icon="prefixClass('icon-fuzhi')"
            noneBg
            @click="handleRenWuL"
            >任务量设置</md-button
          > -->
          <md-button
            type="primary"
            :class="prefixClass('main5')"
            :icon="prefixClass('icon-fuzhi')"
            noneBg
            @click="handleDaoRu"
            >导入</md-button
          >
          <md-button
            type="danger"
            :class="prefixClass('main5')"
            :icon="prefixClass('icon-shanchuwap')"
            noneBg
            @click="handleDelete"
            >删除</md-button
          >
          <md-button
            type="primary"
            :class="prefixClass('save-btn')"
            @click="handleSave"
            >保存</md-button
          >
        </div>
      </div>
      <div :class="prefixClass('note')">
        <div :class="prefixClass('note-item')" style="width: 22%">
          <span :class="prefixClass('note-title require')">项目名称</span>
          <md-input
            v-model="jiCaiXMMC"
            placeholder="请输入"
            disabled
            :class="prefixClass('input-note')"
          ></md-input>
        </div>
        <div :class="prefixClass('note-item')" style="width: 20%">
          <span :class="prefixClass('note-title require')">轮次名称</span>
          <md-input
            v-model="xiangMuMC"
            placeholder="请输入"
            :class="prefixClass('input-note')"
          ></md-input>
        </div>
        <div :class="prefixClass('note-item')" style="width: 20%">
          <span class="demonstration" :class="prefixClass('note-title require')"
            >开始时间</span
          >
          <md-date-picker
            v-model="KaiShiRQ"
            style="flex: 1"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
          >
          </md-date-picker>
        </div>
        <div
          :class="prefixClass('note-item')"
          style="width: 18%; margin-left: 8px"
        >
          <span class="demonstration" :class="prefixClass('note-title require')"
            >结束时间</span
          >
          <md-date-picker
            v-model="JieShuRQ"
            style="flex: 1"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
          >
          </md-date-picker>
        </div>

        <div :class="prefixClass('note-item')" style="width: 20%">
          <span class="demonstration" :class="prefixClass('note-jibie')"
            >备注</span
          >
          <md-input
            v-model="beiZhu"
            placeholder="请输入"
            :class="prefixClass('input-note')"
          ></md-input>
        </div>
      </div>
      <div :class="prefixClass('procurement-table')">
        <md-editable-table-pro
          v-loading="loading"
          v-table-enter
          v-model="tableData"
          height="100%"
          highlight-current-row
          row-key="id"
          auto-focus
          :columns="columns"
          :showDefaultOperate="false"
          :hide-add-button="true"
          :maxLength="9999"
          :row-class-name="tableRowClassName"
          :cell-style="cellStyle"
          id="caiGouTable"
          ref="mdEditTable"
          @row-click="handleTableClick"
          @selection-change="selectionChange"
        >
          <template #previewYPMC="{ row }">
            <YaoPinShow
              v-if="row.yaoPinMC != ''"
              :styleData="row.xianShiXX || {}"
              :yaoPinMC="row.yaoPinMC + ' ' + row.yaoPinGG"
            />
          </template>
          <template #yaoPinMCYGG="{ row, $index, cellRef }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              jiCaiYPBZ="1"
              type="jc"
              label-key="yaoPinZC"
              :class="prefixClass('yaopin-select')"
              @change="handleTableYaoPinDWChange($event, row, $index)"
              @blur="cellRef.endEdit()"
            >
            </biz-yaopindw>
          </template>

          <template #jiCaiZH="{ row }">
            <md-input v-model="row.jiCaiZH" placeholder="请填写" />
          </template>
          <template #biaoZhunGGMC="{ row }">
            <md-input v-model="row.biaoZhunGGMC" placeholder="请填写" />
          </template>
          <template #zhuanHuanBi="{ row }">
            <md-input
              v-number.float="{ min: 0 }"
              v-model="row.zhuanHuanBi"
            ></md-input>
          </template>
          <template #biaoZhunGGRWL="{ row }">
            <md-input
              v-model="row.biaoZhunGGRWL"
              v-number="{}"
              placeholder="请填写"
            />
          </template>
        </md-editable-table-pro>
      </div>
      <div :class="prefixClass('procurement-table')">
        <md-title label="竞品药品" style="margin: 0 0 8px 8px"></md-title>
        <md-editable-table-pro
          class="jingping-table"
          v-loading="loading"
          v-table-enter="handleTableEnter"
          auto-focus
          :autoFill="true"
          v-model="jingPinYPtableData"
          height="100%"
          row-key="id"
          :columns="columnsBottom"
          :showDefaultOperate="false"
          :hide-add-button="true"
          :maxLength="9999"
          :row-class-name="tableRowClassName"
          :cell-style="cellStyle"
          id="caiGouTable"
          ref="mdEditJingPinYPTable"
          @selection-change="selectionJingPinYP"
        >
          <template #previewYPMC="{ row }">
            <YaoPinShow
              v-if="row.yaoPinMC != ''"
              :styleData="row.xianShiXX || {}"
              :yaoPinMC="row.yaoPinMC + ' ' + row.yaoPinGG"
            />
          </template>
          <template #yaoPinMCYGG="{ row, $index, cellRef }">
            <biz-yaopindw
              v-model="row.yaoPinMCYGG"
              :class="prefixClass('yaopin-select')"
              :paiChuJGIDList="currentTableID"
              label-key="yaoPinZC"
              type="jcjp"
              @change="handleTableJingPinYPDWChange($event, row, $index)"
              @blur="cellRef.endEdit()"
            >
            </biz-yaopindw>
          </template>
          <template #jingPinLXDM="{ row, $index }">
            <md-select
              v-model="row.jingPinLXDM"
              @change="handleYaoPinLX($event, row, $index)"
            >
              <md-option
                v-for="item in jingPinLXOptions"
                :key="item.biaoZhunDM"
                :label="item.biaoZhunMC"
                :value="item.biaoZhunDM"
              />
            </md-select>
          </template>
          <template #biaoZhunGGMC="{ row }">
            <md-input v-model="row.biaoZhunGGMC" placeholder="请填写" />
          </template>
          <template #zhuanHuanBi="{ row, $index }">
            <md-input
              v-number.float="{ min: 0 }"
              v-model="row.zhuanHuanBi"
              @change="handleYaoPinZHB($event, row, $index)"
            ></md-input>
          </template>
        </md-editable-table-pro>
      </div>
    </div>
    <DaoRuDialog ref="DaoRuDialog" />
    <!-- <RenWuLSZDialog ref="RenWuLSZDialog" /> -->
  </div>
</template>
<script>
import { MdMessage, MdMessageBox, MdTooltip } from '@mdfe/medi-ui';
import { MdEditableTablePro } from '@mdfe/medi-ui-pro';
import { cloneDeep, isEqual } from 'lodash';
import { h } from 'vue';

import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import YaoPinShow from '@/components/YaoPinShow.vue';
import { GetYaoPinTSSX } from '@/service/yaoPinYF/common';
import {
  AddJiCaiJHD,
  GetJiCaiJHDXQ,
  GetYaoPinCDJGXX,
  UpdateJiCaiJHD,
  GetJiCaiYPBZGGXX,
} from '@/service/yaoPinYK/JiCaiJHD';
import commonData from '@/system/utils/commonData';
import { focusEditTableDom } from '@/system/utils/focusEditTable';
import { focusEditTableDomV2 } from '@/system/utils/focusEditTableV2';
import { yaoKuZDJZTimeShow } from '@/system/utils/formatDate';
import DaoRuDialog from './components/DaoRuDialog.vue';
import RenWuLSZDialog from './components/RenWuLSZDialog';
import { getYaoPinShuJuYZYList } from '@/service/yaoPin/yeWuZD';
const initData = () => {
  return {
    yaoPinLXDM: '', //药品类型代码
    yaoPinLXMC: '', //药品类型名称
    chanDiMC: null, //产地名称
    yaoPinMC: '', //药品名称
    yaoPinGG: '', // 药品规格
    yaoPinMCYGG: {}, //药品名称与规格
    jiaGeID: '', //价格ID
    baoZhuangDW: '', // 包装单位
    jiXingMC: '',
    biaoZhunGGRWL: '',
    yuanNeiBM: '',
    zhangBuLBID: '',
    zhangBuLBMC: '',
    jinJia: '',
    jingPinLXDM: '',
    jingPinLXMC: '',
  };
};
export default {
  name: 'XinZengCJ',
  inject: ['viewManager'],
  data() {
    return {
      beiZhu: '',
      cssPrefix: process.env.VUE_APP_NAMESPACE,
      jingPinYPSelection: [],
      jiBieBZ: 2,
      KaiShiRQ: '',
      jiCaiXMMC: '',
      jiCaiXMID: '',
      xiangMuMC: '',
      JieShuRQ: '',
      loading: false,
      popoverValue: false, //控制popover显隐
      yaoPingDW: '', //药品定位
      tableData: [initData()], // 表格数据
      columns: [
        {
          type: 'selection',
          width: 54,
          selectable: (row, index) => {
            return this.tableData.length !== index + 1 && !row.ruKuMXDID;
          },
        },
        {
          label: '序号',
          type: 'index',
        },
        {
          prop: 'yaoPinLXDM',
          type: 'text',
          width: 32,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yuanNeiBM',
          type: 'text',
          label: '院内编码',
          width: 90,
        },
        {
          previewSlot: 'previewYPMC', //预览插槽
          slot: 'yaoPinMCYGG',
          label: '药品名称与规格',
          width: 301,
          endMode: 'custom',
          startMode: 'click',
          // formatter: (row) => {
          //   if (row.yaoPinMC && row.yaoPinGG) {
          //     return row.yaoPinMC + ' ' + row.yaoPinGG;
          //   }
          //   return '';
          // },
          renderHeader: ({ column }) => {
            return h(
              'div',
              { class: this.prefixClass('require') },
              column.label,
            );
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          width: 80,
          type: 'text',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 90,
          type: 'text',
          align: 'right',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 90,
          type: 'text',
        },
        {
          slot: 'jiCaiZH',
          prop: 'jiCaiZH',
          label: '两定组号',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          slot: 'biaoZhunGGMC',
          prop: 'biaoZhunGGMC',
          label: '标准规格',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          slot: 'zhuanHuanBi',
          prop: 'zhuanHuanBi',
          width: 140,
          formatter(v) {
            return v.zhuanHuanBi;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              {
                style:
                  'display: flex;justify-content: space-between;align-items: center;',
              },
              [
                h('div', [
                  h('span', { style: { color: 'red' } }, '*'),
                  h('span', '转换比'),
                ]),
                h(
                  MdTooltip,
                  { effect: 'light' },
                  {
                    content: () =>
                      h(
                        'span',
                        { style: 'color: #333;font-size:12px;' },
                        '药品最小规格与标准规格的比值',
                      ),
                    default: () =>
                      h('md-icon', {
                        class: 'mediinfo-vela-yaoku-web-icon-tixing-s',
                        style: 'color:#aaa;cursor:pointer;margin-left:4px',
                      }),
                  },
                ),
              ],
            );
          },
        },
        {
          slot: 'biaoZhunGGRWL',
          prop: 'biaoZhunGGRWL',
          label: '标准规格任务量',
          width: 160,
          formatter: (row, column, cellValue, index) => {
            const num = cellValue ? cellValue : 0;
            return Number(num).toFixed(2);
          },
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          width: 114,
          type: 'text',
        },
      ],
      columnsBottom: [
        {
          type: 'selection',
          width: 54,
          selectable: (row, index) => {
            return this.jingPinYPtableData.length !== index + 1 && row.jiaGeID;
          },
        },
        {
          label: '竞品类型',
          slot: 'jingPinLXDM',
        },
        {
          prop: 'yaoPinLXDM',
          type: 'text',
          width: 32,
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'yuanNeiBM',
          type: 'text',
          label: '院内编码',
          width: 120,
        },
        {
          // prop: 'yaoPinMCYGG',
          previewSlot: 'previewYPMC', //预览插槽
          slot: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 301,
          endMode: 'custom',
          startMode: 'click',
          // formatter: (row) => {
          //   if (row.yaoPinMC && row.yaoPinGG) {
          //     return row.yaoPinMC + ' ' + row.yaoPinGG;
          //   }
          //   return '';
          // },
          renderHeader: ({ column }) => {
            return h(
              'div',
              { class: this.prefixClass('require') },
              column.label,
            );
          },
          disabled: ({ row }) => {
            return !!row.ruKuMXDID;
          },
        },
        {
          prop: 'jiXingMC',
          label: '剂型',
          width: 120,
          type: 'text',
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 200,
          type: 'text',
        },
        {
          prop: 'jinJia',
          label: '进价',
          width: 90,
          type: 'text',
          align: 'right',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 120,
          type: 'text',
        },
        {
          slot: 'biaoZhunGGMC',
          prop: 'biaoZhunGGMC',
          label: '标准规格',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return cellValue;
          },
        },
        {
          slot: 'zhuanHuanBi',
          prop: 'zhuanHuanBi',
          width: 140,
          formatter(v) {
            return v.zhuanHuanBi;
          },
          renderHeader: ({ column, _self }) => {
            return h(
              'div',
              {
                style:
                  'display: flex;justify-content: space-between;align-items: center;',
              },
              [
                h('div', [
                  h('span', { style: { color: 'red' } }, '*'),
                  h('span', '转换比'),
                ]),
                h(
                  MdTooltip,
                  { effect: 'light' },
                  {
                    content: () =>
                      h(
                        'span',
                        { style: 'color: #333;font-size:12px;' },
                        '药品最小规格与标准规格的比值',
                      ),
                    default: () =>
                      h('md-icon', {
                        class: 'mediinfo-vela-yaoku-web-icon-tixing-s',
                        style: 'color:#aaa;cursor:pointer;margin-left:4px',
                      }),
                  },
                ),
              ],
            );
          },
        },
        {
          prop: 'shengPingTBM',
          label: '省平台ID',
          width: 114,
          type: 'text',
        },
      ],
      jingPinYPtableData: [initData()], //竞品药品
      searchFormData: {
        LikeQuery: '',
        PageSize: 10,
        PageIndex: 1,
      },
      selections: [], //选中的行
      editTableData: [], // 编辑初始化信息
      delTableList: [], //被删除的表格数据
      tableBodyEle: null, //表格tbody节点
      cloneTableData: [],
      currentTableID: '',
      xianShiXX: [],
      jingPinLXOptions: [],
    };
  },

  async mounted() {
    this.editInit();
    const data = await getYaoPinShuJuYZYList(['YP0120']);
    this.jingPinLXOptions = data[0].zhiYuList;
  },
  watch: {
    'jingPinYPtableData.length': {
      handler: function (val) {
        if (Number(val) > 1) {
          const obj = this.tableData.find(
            (fl) => fl.jiaGeID === this.currentTableID,
          );
          obj.jiCaiJPYPList = this.jingPinYPtableData.filter(
            (el) => el.jiaGeID,
          );
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /**
     * 编辑初始化操作,根据传进来的id,来获取采购详情单
     */
    async editInit() {
      this.id = this.$route.query.id;
      this.jiCaiXMMC = this.$route.query.jiCaiXMMC;
      this.jiCaiXMID = this.$route.query.jiCaiXMID;
      this.jiBieBZ = this.$route.query.jiBieBZ;
      if (!this.id) return;
      try {
        this.loading = true;
        var params = {
          id: this.id,
        };
        const result = await GetJiCaiJHDXQ(params);
        //赋值
        this.xiangMuMC = result.xiangMuMC;
        this.KaiShiRQ = result.kaiShiRQ;
        this.JieShuRQ = result.jieShuRQ;
        result.caiJiDMXList &&
          result.caiJiDMXList.forEach((m) => {
            m.jiCaiJPYPList.length > 0 &&
              m.jiCaiJPYPList.forEach((el) => {
                el.yaoPinMCYGG = {
                  jiaGeID: el.jiaGeID,
                  yaoPinMC: el.yaoPinMC,
                  yaoPinGG: el.yaoPinGG,
                  baoZhuangLiang: el.baoZhuangLiang,
                  baoZhuangDW: el.baoZhuangDW,
                  chanDiID: el.chanDiID,
                  chanDiMC: el.chanDiMC,
                  jiXingID: el.jiXingID,
                  jiXingMC: el.jiXingMC,
                  ruKuLiang: el.ruKuLiang,
                  yaoPinLXDM: el.yaoPinLXDM,
                  yaoPinLXMC: el.yaoPinLXMC,
                  kuCunSL: 0,
                };
              });
          });

        this.tableData =
          result.caiJiDMXList &&
          result.caiJiDMXList.map((m) => {
            return {
              ...m,
              yaoPinMCYGG: {
                jiaGeID: m.jiaGeID,
                yaoPinMC: m.yaoPinMC,
                yaoPinGG: m.yaoPinGG,
                chanDiMC: m.chanDiMC,
                chanDiID: m.chanDiID,
                jinJia: m.jinJia,
                baoZhuangDW: m.baoZhuangDW,
                baoZhuangLiang: m.baoZhuangLiang,
                kuCunSL: m.kuCunSL,
              },
              deleteJiCaiJPYPList: [],
            };
          });

        const xianShiXX = await this.getXianShiXX(this.tableData);
        this.tableData.forEach((item) => {
          item.xianShiXX = xianShiXX[item.jiaGeID] || {};
          item.yaoPinMCYGG.xianShiXX = item.xianShiXX;
        });
        this.editTableData = cloneDeep(this.tableData);
        this.tableData.push(initData());
        if (this.tableData.length > 1) {
          //设置选中行，默认第一行
          this.$refs.mdEditTable.invokeTableMethod(
            'setCurrentRow',
            this.tableData[0],
          );
          this.currentTableID = this.tableData[0].jiaGeID;

          const arr =
            this.tableData[0].jiCaiJPYPList.length > 0
              ? this.tableData[0].jiCaiJPYPList
              : [];
          const xianShiXXJingPin = await this.getXianShiXX(arr);
          arr.forEach((item) => {
            item.xianShiXX = xianShiXXJingPin[item.jiaGeID] || {};
            item.yaoPinMCYGG.xianShiXX = item.xianShiXX;
          });
          this.jingPinYPtableData = arr;
          this.jingPinYPtableData.push(initData());
        }
      } finally {
        this.loading = false;
      }
    },
    // 获取药品显示信息
    async getXianShiXX(data) {
      const list = data.filter((item) => item.jiaGeID);
      const xianShiKeys = Object.keys(this.xianShiXX);
      let jiaGeIDList = [];
      list.forEach((item) => {
        if (!xianShiKeys.includes(item.jiaGeID)) {
          jiaGeIDList.push(item.jiaGeID);
        }
      });

      if (jiaGeIDList.length === 0) return;
      let isError = false;
      let res = null;
      try {
        res = await GetYaoPinTSSX({
          jiaGeIDList,
          xianShiLXDM: '1',
        });
      } catch (e) {
        isError = true;
      }
      if (isError) return;
      const xianShiXX = {};
      if (res.length === 0) {
        jiaGeIDList.forEach((item) => {
          xianShiXX[item] = {};
        });
      } else {
        res.forEach((item) => {
          xianShiXX[item.jiaGeID] = item;
        });
      }

      return { ...this.xianShiXX, ...xianShiXX };
    },
    /**
     * 选择药品change事件
     */
    async handleTableYaoPinDWChange(data, row, index) {
      if (!data) {
        Object.assign(row, initData());
        this.currentTableID = '';
        return;
      }
      this.$refs.mdEditTable.invokeTableMethod('setCurrentRow', row);
      //判断是否有重复的
      const isRepeat = this.tableData.some((item, i) => {
        if (index == i) return false; //排除自身
        return item.jiaGeID == data.jiaGeID;
      });
      //判断是不是选择了同一条数据
      if (this.tableData[index].jiaGeID == data.jiaGeID) {
        return;
      }

      if (isRepeat) {
        MdMessage.warning('药品不可以重复添加');
        Object.assign(row, initData());
        this.$refs.mdEditTable._current();
        return;
      } else {
        this.currentTableID = data.jiaGeID;
        var params = {
          jiaGeID: data.jiaGeID,
        };
        let yaoPinBZGGXX = await GetJiCaiYPBZGGXX(params);
        var yaoPinCDJGXX = await GetYaoPinCDJGXX(params);
        const assingData = {
          biaoZhunGGID: yaoPinBZGGXX.biaoZhunGGID,
          biaoZhunGGMC: yaoPinBZGGXX.biaoZhunGGMC,
          zhuanHuanBi: yaoPinBZGGXX.zhuanHuanBi,
          shengPingTBM: data.shengPingTBM,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          jiaGeID: data.jiaGeID,
          yaoPinMC: data.yaoPinMC,
          yaoPinGG: data.yaoPinGG,
          chanDiMC: data.chanDiMC,
          chanDiID: data.chanDiID,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          jiXingMC: yaoPinCDJGXX.jiXingMC,
          jiXingID: yaoPinCDJGXX.jiXingID,
          zuiXiaoDW: yaoPinCDJGXX.zuiXiaoDW,
          zhangBuLBID: data.zhangBuLBID,
          zhangBuLBMC: data.zhangBuLBMC,
          jiCaiZH: '',
          biaoZhunGGRWL: '',
          jinJia: data.jinJia,
          lingShouJia: data.danJia,
          kuCunSL: data.kuCunSL,
          yuanNeiBM: data.yuanNeiBM,
          xianShiXX: data.xianShiXX,
          yaoPinMCYGG: {
            xianShiXX: data.xianShiXX,
            jiaGeID: data.jiaGeID,
            yaoPinMC: data.yaoPinMC,
            yaoPinGG: data.yaoPinGG,
            chanDiMC: data.chanDiMC,
            chanDiID: data.chanDiID,
            jinJia: data.jinJia,
            baoZhuangDW: data.baoZhuangDW,
            baoZhuangLiang: data.baoZhuangLiang,
            kuCunSL: data.kuCunSL,
            yuanNeiBM: data.yuanNeiBM,
            zhangBuLBID: data.zhangBuLBID,
            zhangBuLBMC: data.zhangBuLBMC,
            yaoPinLXDM: data.yaoPinLXDM,
            yaoPinLXMC: data.yaoPinLXMC,
          },
        };

        Object.assign(row, assingData);
        if (index === this.tableData.length - 1) {
          this.tableData.push(initData());
        }
      }
      // this.$refs.mdEditTable.toNext(3 * index + 1);
    },
    handleYaoPinZHB(val, row, index) {
      this.jingPinYPtableData[index].isChangeYPLX = true;
    },
    handleYaoPinLX(val, row, index) {
      this.jingPinYPtableData[index].isChangeYPLX = true;
      this.jingPinYPtableData[index].jingPinLXMC = this.jingPinLXOptions.find(
        (fl) => fl.biaoZhunDM == val,
      ).biaoZhunMC;
    },
    //竞品药品change事件
    async handleTableJingPinYPDWChange(data, row, index) {
      if (!data) {
        Object.assign(row, initData());
        return;
      }
      //判断是否有重复的
      const isRepeat = this.jingPinYPtableData.some((item, i) => {
        if (index == i) return false; //排除自身
        return item.jiaGeID == data.jiaGeID;
      });

      if (isRepeat) {
        MdMessage.warning('药品不可以重复添加');
        Object.assign(row, initData());
        this.$refs.mdEditJingPinYPTable._current();
        return;
      } else {
        var params = {
          jiaGeID: data.jiaGeID,
        };
        if (
          this.jingPinYPtableData[index].jiaGeID !== data.jiaGeID &&
          this.jingPinYPtableData[index].id
        ) {
          const tableIndex = this.tableData.findIndex(
            (fl) => fl.jiaGeID === this.currentTableID,
          );
          this.tableData[tableIndex].deleteJiCaiJPYPList.push(
            this.jingPinYPtableData[index].id,
          );
        }

        let yaoPinBZGGXX = await GetJiCaiYPBZGGXX(params);
        var yaoPinCDJGXX = await GetYaoPinCDJGXX(params);
        const assingData = {
          biaoZhunGGID: yaoPinBZGGXX.biaoZhunGGID,
          biaoZhunGGMC: yaoPinBZGGXX.biaoZhunGGMC,
          zhuanHuanBi: yaoPinBZGGXX.zhuanHuanBi,
          shengPingTBM: data.shengPingTBM,
          yaoPinLXDM: data.yaoPinLXDM,
          yaoPinLXMC: data.yaoPinLXMC,
          jiaGeID: data.jiaGeID,
          yaoPinMC: data.yaoPinMC,
          yaoPinGG: data.yaoPinGG,
          chanDiMC: data.chanDiMC,
          chanDiID: data.chanDiID,
          baoZhuangDW: data.baoZhuangDW,
          baoZhuangLiang: data.baoZhuangLiang,
          jiXingMC: yaoPinCDJGXX.jiXingMC,
          jiXingID: yaoPinCDJGXX.jiXingID,
          zuiXiaoDW: yaoPinCDJGXX.zuiXiaoDW,
          zhangBuLBID: data.zhangBuLBID,
          zhangBuLBMC: data.zhangBuLBMC,
          heTongLiang: '',
          jinJia: data.jinJia,
          lingShouJia: data.danJia,
          kuCunSL: data.kuCunSL,
          yuanNeiBM: data.yuanNeiBM,
          mingXiJGID: this.currentTableID,
          xianShiXX: data.xianShiXX,
          yaoPinMCYGG: {
            xianShiXX: data.xianShiXX,
            jiaGeID: data.jiaGeID,
            yaoPinMC: data.yaoPinMC,
            yaoPinGG: data.yaoPinGG,
            chanDiMC: data.chanDiMC,
            chanDiID: data.chanDiID,
            jinJia: data.jinJia,
            baoZhuangDW: data.baoZhuangDW,
            baoZhuangLiang: data.baoZhuangLiang,
            kuCunSL: data.kuCunSL,
          },
        };

        Object.assign(row, assingData);
        delete this.jingPinYPtableData[index].id;
        if (index === this.jingPinYPtableData.length - 1) {
          this.jingPinYPtableData.push(initData());
        }
      }
      // console.log(this.$refs.mdEditJingPinYPTable._current(), '11111111111');
      // this.$refs.mdEditJingPinYPTable.toNext(index + 1);
      this.$refs.mdEditJingPinYPTable.toNext(index);
    },
    handleTableEnter({ length, activeIndex, callback }) {
      let index = activeIndex;
      if (activeIndex > this.jingPinYPtableData.length) index = length - 1;
      if ((index + 1) % length == 0) {
        this.jingPinYPtableData.push(initData());
        callback({});
        return;
      }
      callback();
    },
    formatDates(date) {
      return yaoKuZDJZTimeShow(date);
    },
    async handleDaoRu() {
      const result = await this.$refs.DaoRuDialog.showModel();
      //赋值
      this.xiangMuMC = result.xiangMuMC;
      this.KaiShiRQ = result.kaiShiRQ;
      this.JieShuRQ = result.jieShuRQ;
      this.jiBieBZ = result.jiBieBZ;
      result.caiJiDMXList &&
        result.caiJiDMXList.forEach((m) => {
          m.jiCaiJPYPList.length > 0 &&
            m.jiCaiJPYPList.forEach((el) => {
              el.yaoPinMCYGG = {
                jiaGeID: el.jiaGeID,
                yaoPinMC: el.yaoPinMC,
                yaoPinGG: el.yaoPinGG,
                baoZhuangLiang: el.baoZhuangLiang,
                baoZhuangDW: el.baoZhuangDW,
                chanDiID: el.chanDiID,
                chanDiMC: el.chanDiMC,
                jiXingID: el.jiXingID,
                jiXingMC: el.jiXingMC,
                ruKuLiang: el.ruKuLiang,
                yaoPinLXDM: el.yaoPinLXDM,
                yaoPinLXMC: el.yaoPinLXMC,
                kuCunSL: 0,
              };
            });
        });

      this.tableData =
        result.caiJiDMXList &&
        result.caiJiDMXList.map((m) => {
          return {
            ...m,
            yaoPinMCYGG: {
              jiaGeID: m.jiaGeID,
              yaoPinMC: m.yaoPinMC,
              yaoPinGG: m.yaoPinGG,
              chanDiMC: m.chanDiMC,
              chanDiID: m.chanDiID,
              jinJia: m.jinJia,
              baoZhuangDW: m.baoZhuangDW,
              baoZhuangLiang: m.baoZhuangLiang,
              kuCunSL: m.kuCunSL,
            },
            deleteJiCaiJPYPList: [],
          };
        });

      const xianShiXX = await this.getXianShiXX(this.tableData);
      this.tableData.forEach((item) => {
        item.xianShiXX = xianShiXX[item.jiaGeID] || {};
        item.yaoPinMCYGG.xianShiXX = item.xianShiXX;
      });
      this.editTableData = cloneDeep(this.tableData);
      this.tableData.push(initData());
      if (this.tableData.length > 1) {
        //设置选中行，默认第一行
        this.$refs.mdEditTable.invokeTableMethod(
          'setCurrentRow',
          this.tableData[0],
        );
        this.currentTableID = this.tableData[0].jiaGeID;

        const arr =
          this.tableData[0].jiCaiJPYPList.length > 0
            ? this.tableData[0].jiCaiJPYPList
            : [];
        const xianShiXXJingPin = await this.getXianShiXX(arr);
        arr.forEach((item) => {
          item.xianShiXX = xianShiXXJingPin[item.jiaGeID] || {};
          item.yaoPinMCYGG.xianShiXX = item.xianShiXX;
        });
        this.jingPinYPtableData = arr;
        this.jingPinYPtableData.push(initData());
      }
    },
    // 任务量
    handleRenWuL() {
      this.$refs.RenWuLSZDialog.showModal();
    },
    /**
     * 删除采购计划单
     */
    handleDelete() {
      if (this.selections.length === 0 && this.jingPinYPSelection.length == 0) {
        return MdMessage.warning('至少选中一条!');
      }
      MdMessageBox.confirm('确定删除当前选中的药品?', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        //原有的被删除存储起来,新增的直接被删除
        //新增的一行后删除 jiageid为空 tableLength控制不删除最后一行空行
        // const tableLength = this.tableData.length
        //如果勾选了竞品药品点击删除

        this.tableData = this.selections.reduce((prev, item) => {
          let index = prev.findIndex((i) => {
            return i.jiaGeID == item.jiaGeID;
          });
          if (index > -1) {
            if (prev[index].id) {
              this.delTableList.push(prev[index]);
            }
            if (this.currentTableID == prev[index].jiaGeID) {
              this.currentTableID = '';
              this.handleTableClick(this.tableData[0]);
            }
            prev.splice(index, 1);
          }
          return prev;
        }, this.tableData);

        if (this.jingPinYPSelection.length > 0) {
          const tableIndex = this.tableData.findIndex(
            (fl) => fl.jiaGeID === this.currentTableID,
          );
          if (tableIndex > -1) {
            this.tableData[tableIndex].deleteJiCaiJPYPList =
              this.tableData[tableIndex].deleteJiCaiJPYPList &&
              this.tableData[tableIndex].deleteJiCaiJPYPList.length > 0
                ? this.tableData[tableIndex].deleteJiCaiJPYPList
                : [];
            this.jingPinYPSelection.forEach((el, index) => {
              if (el.id) {
                this.tableData[tableIndex].deleteJiCaiJPYPList.push(el.id);
              } else {
                this.tableData[tableIndex].jiCaiJPYPList.splice(index, 1);
              }
            });
          }
          this.jingPinYPtableData = this.jingPinYPSelection.reduce(
            (prev, item) => {
              let index = prev.findIndex((i) => {
                return (
                  i.jiaGeID == item.jiaGeID && i.itemIndex == item.itemIndex
                );
              });
              if (index > -1) {
                prev.splice(index, 1);
              }
              return prev;
            },
            this.jingPinYPtableData,
          );

          this.$refs.mdEditTable.invokeTableMethod('clearSelection');
          this.$refs.mdEditJingPinYPTable.invokeTableMethod('clearSelection');
        }
      });
    },
    /**
     * table选中触发的事件
     */
    selectionChange(selection) {
      this.selections = selection;
    },
    //竞品药品选中checkbox
    selectionJingPinYP(selection) {
      this.jingPinYPSelection = selection;
    },
    tableRowClassName({ row, rowIndex }) {
      row.itemIndex = rowIndex;
    },
    //药品选择行事件
    handleTableClick(row) {
      if (this.tableData.length > 1) {
        this.jingPinYPtableData = [];
        this.currentTableID = row.jiaGeID;
        this.$refs.mdEditTable.invokeTableMethod('setCurrentRow', row);
        this.jingPinYPtableData = cloneDeep(row.jiCaiJPYPList) || [];
        this.jingPinYPtableData.push(initData());
      }
    },

    /**
     * 保存采购计划单
     */
    async handleSave() {
      //判断列表是否有空值
      let invalidRowIndex = this.tableData.findIndex((item, index) => {
        if (index == this.tableData.length - 1) return false;
        return !item.jiaGeID || !item.zhuanHuanBi;
      });
      let jingPinInvalidRowIndex = this.jingPinYPtableData.findIndex(
        (item, index) => {
          if (index == this.jingPinYPtableData.length - 1) return false;
          return !item.zhuanHuanBi;
        },
      );
      const invalidProp = [
        { value: 'jiaGeID', label: '药品名称', key: 'yaoPinMCYGG' },
        { value: 'zhuanHuanBi', label: '转换比', key: 'zhuanHuanBi' },
        // { value: 'heTongLiang', label: '合同量', key: 'heTongLiang' },
      ];
      const jingPinInvalidProp = [
        { value: 'zhuanHuanBi', label: '转换比', key: 'zhuanHuanBi' },
      ];
      if (jingPinInvalidRowIndex > -1) {
        for (let item of jingPinInvalidProp) {
          if (!this.jingPinYPtableData[jingPinInvalidRowIndex][item.value]) {
            focusEditTableDomV2({
              rowIndex: jingPinInvalidRowIndex,
              columns: this.columnsBottom,
              key: item.key,
            });
            return MdMessage.error(
              `第${jingPinInvalidRowIndex + 1}行,${item.label}为空`,
            );
          }
        }
        return;
      }
      if (invalidRowIndex > -1) {
        for (let item of invalidProp) {
          if (!this.tableData[invalidRowIndex][item.value]) {
            focusEditTableDom({
              rowIndex: invalidRowIndex,
              columns: this.columns,
              key: item.key,
            });
            return MdMessage.error(
              `第${invalidRowIndex + 1}行,${item.label}为空`,
            );
          }
        }
        return;
      }
      if (!this.KaiShiRQ) {
        MdMessage.warning('请选择开始时间');
        return;
      }
      if (!this.JieShuRQ) {
        MdMessage.warning('请选择结束时间');
        return;
      }
      if (!this.jiCaiXMMC) {
        MdMessage.warning('请输入项目名称');
        return;
      }
      if (!this.xiangMuMC) {
        MdMessage.warning('请输入轮次名称');
        return;
      }
      var kaiShiSJ = this.formatDates(this.KaiShiRQ);
      var jieShuSJ = this.formatDates(this.JieShuRQ);
      if (kaiShiSJ > jieShuSJ) {
        MdMessage.warning('开始时间不能大于结束时间');
        return;
      }
      try {
        this.loading = true;
        const cloneTableData = cloneDeep(this.tableData);
        cloneTableData.splice(cloneTableData.length - 1, 1);
        const params = {
          jiCaiXMMC: this.jiCaiXMMC,
          jiCaiXMID: this.jiCaiXMID,
          xiangMuMC: this.xiangMuMC,
          KaiShiRQ: this.KaiShiRQ,
          JieShuRQ: this.JieShuRQ,
          jiBieBZ: this.jiBieBZ,
          beiZhu: this.beiZhu,
        };
        if (!this.id) {
          params.caiJiDMXList = cloneTableData.map((m) => {
            return {
              ...m,
              addJiCaiJPYPList: m.jiCaiJPYPList || [],
              deleteJiCaiJPYPList: [],
            };
          });
          if (params.caiJiDMXList.length > 0) {
            params.caiJiDMXList.forEach((item, index) => {
              item.shunXuHao = index; //添加顺序号参数
            });
          }
          if (params.caiJiDMXList.length == 0) {
            MdMessage.warning('集采明细不可为空！');
            return;
          }
          await AddJiCaiJHD(params);
        } else {
          params.id = this.id;
          params.addcaiJiDMXList = cloneTableData.filter((item) => {
            item.addJiCaiJPYPList = item.jiCaiJPYPList || [];
            return !item.id;
          });
          params.updateCaiGouDMXList = this.tableData.filter((item) => {
            return this.editTableData.some(
              (i) => i.id === item.id && !isEqual(item, i),
            );
          });
          params.updateCaiGouDMXList.forEach((el, index) => {
            el.shunXuHao = index; //添加顺序号参数
            el.deleteJiCaiJPYPList = el.deleteJiCaiJPYPList || [];
            // 把新增的药品和修改过药品类型的筛选出来
            el.addJiCaiJPYPList = el.jiCaiJPYPList.filter(
              (fl) => fl.isChangeYPLX || !fl.id,
            );
          });
          params.deleteCaiJiDMXList = this.delTableList.map((item) => item.id);
          await UpdateJiCaiJHD(params);
        }
        this.$message({
          type: 'success',
          message: '保存成功',
        });
        this.closeTab();
      } catch (err) {
      } finally {
        this.loading = false;
      }
    },
    /**
     * 用于保存成功关闭当前tab页面
     */
    closeTab() {
      let closeTabKey = this.viewManager.currentPage.name;
      this.viewManager.close(closeTabKey);
      this.$router.push({
        name: 'CaiJiGL',
      });
    },
    /**
     * 药品定位
     */
    async handleDingWei(data) {
      if (!data) return;
      if (!(Array.isArray(this.tableData) && this.tableData.length > 1)) {
        MdMessage.warning('采购单列表为空,不能定位！');
        return;
      }
      //先清除上次高亮
      this.clearHeightLight();
      let index = this.tableData.findIndex((item) => {
        return item.yaoPinMC == data.yaoPinMC;
      });
      if (index == -1) {
        return MdMessage.warning('未找到该药品');
      } else {
        //获取高亮dom节点
        if (!this.tableBodyEle) {
          this.tableBodyEle = document.querySelector(
            `#caiGouTable .${this.cssPrefix}-base-table__body-wrapper`,
          );
        }
        const childDom = this.tableBodyEle.querySelectorAll(
          `.${this.cssPrefix}-base-table__row`,
        )[index];
        childDom.classList.add(this.prefixClass('heightLight'));
        childDom.classList.add('current-row');
        childDom.scrollIntoView();
      }
    },
    /**
     * 清除table高亮
     */
    clearHeightLight() {
      const rowList = Array.from(
        document.querySelectorAll(
          `#caiGouTable .${this.cssPrefix}-base-table__row`,
        ),
      );
      rowList.forEach((item) => {
        item.classList.remove(this.prefixClass('heightLight'));
        item.classList.remove('current-row');
      });
    },
    cellStyle({ row }) {
      if (row.ruKuMXDID) {
        return { backgroundColor: '#f5f5f5' };
      }
    },
  },
  components: {
    'md-editable-table-pro': MdEditableTablePro,
    'biz-yaopindw': BizYaoPinDW,
    YaoPinShow,
    DaoRuDialog,
    RenWuLSZDialog,
  },
};
</script>
<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-select-div {
  // height: 150px;
  overflow-y: auto;
  width: 200px;
}

.#{$md-prefix}-procurement {
  height: 100%;
  background: #eaeff3;
  padding: 8px;
  box-sizing: border-box;
}

.#{$md-prefix}-procurement-content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .#{$md-prefix}-procurement-top {
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // background-color: #edf6fd;
    background-color: getCssVar('color-1');

    .#{$md-prefix}-procurement-left {
      display: flex;
      align-items: center;

      .#{$md-prefix}-procurement-left-lt {
        height: 38px;
        width: 322px;
        // margin-right: 8px;
        // background-color: #e3ecf3;
        border-radius: 4px;
        float: left;
        justify-content: center;
        align-content: center;
        box-sizing: border-box;
        padding-top: 4px;

        .#{$md-prefix}-omti {
          float: left;
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          // background-color: #f3f7fa;
          background-color: #fff;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 4px;
        }

        .#{$md-prefix}-caigouJH-title {
          float: left;
          color: getCssVar('color-8');
          font-size: 16px;
          font-weight: bold;
          line-height: 28px;
          // padding-left: 23px;
          margin: 0 8px;
          position: relative;

          i {
            font-size: 18px;
          }
        }

        .#{$md-prefix}-input-seach {
          width: 180px;
          // background-color: #f3f7fa;
          background: #fff;
          float: left;
          border-radius: 4px;
        }
      }

      .#{$md-prefix}-procurement-left-rt {
        display: flex;
        justify-content: flex-start;
        margin-left: 8px;
        overflow: hidden;

        span {
          float: left;
          height: 30px;
          line-height: 30px;
          margin-right: 8px;
          padding: 0 8px;
          background-color: #ffffff;
          border: 1px solid #c3e5fd;
          border-color: getCssVar('color-6');
          border-radius: 4px;
          // color: #1e88e5;
          color: getCssVar('color-6');
          font-size: 14px;
          cursor: pointer;

          &:hover {
            background-color: getCssVar('color-1');
          }
        }
      }
    }

    .#{$md-prefix}-procurement-right {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 28px;

      .#{$md-prefix}-main5 {
        margin: 0 2.5px;
      }

      span {
        color: #1e88e5;
        font-size: 14px;
        margin-left: 5px;
        line-height: 38px;

        i {
          margin-right: 5px;
        }
      }

      .#{$md-prefix}-save-btn {
        margin-left: 8px;
        height: 30px;
      }
    }
  }

  .#{$md-prefix}-procurement-table {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .#{$md-prefix}-editable-table {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      z-index: 99;
      padding: 0 8px 8px;

      ::v-deep .#{$md-prefix}-editable-table__container {
        height: 100% !important;
      }
    }
  }

  .#{$md-prefix}-note {
    display: flex;
    justify-content: flex-start;
    margin: 7px 0;
    padding-left: 8px;
    box-sizing: border-box;
    &-item {
      display: flex;
      box-sizing: border-box;
      /* width: 50%; */
    }

    .#{$md-prefix}-note-title {
      line-height: 30px;
      color: #333333;
      font-size: 14px;
      width: 6em;
      padding-right: 8px;
      box-sizing: border-box;
    }

    .#{$md-prefix}-note-jibie {
      line-height: 30px;
      color: #333333;
      font-size: 14px;
      width: 3em;
      margin-left: 8px;
    }

    .#{$md-prefix}-input-note {
      flex: 1;
      padding-right: 10px;
      box-sizing: border-box;
    }
  }
}

.#{$md-prefix}-drop-down-pop {
  .#{$md-prefix}-top-pop {
    display: flex;
    align-items: center;
    overflow: hidden;
    box-sizing: border-box;

    .#{$md-prefix}-item-inline-label {
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      line-height: 30px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .#{$md-prefix}-select {
      flex: 1;
    }

    .#{$md-prefix}-gonghuo-header {
      width: 195px;
      float: left;
    }

    .#{$md-prefix}-select-input {
      float: left;
      width: 175px;
      box-sizing: border-box;
    }
  }

  .#{$md-prefix}-pop-btn {
    text-align: right;
    margin-top: 10px;
  }
}

.#{$md-prefix}-icondanju {
  position: absolute;
  left: 0;
  top: 4px;
}

.#{$md-prefix}-caigou-footer {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  flex-shrink: 0;
  line-height: 20px;
  font-size: 14px;

  &__info {
    display: flex;

    .#{$md-prefix}-info__name {
      margin-right: 8px;
    }

    .#{$md-prefix}-margin-right-12 {
      margin-right: 12px;
    }

    &.#{$md-prefix}-right {
      span {
        color: #aaa;
      }
    }
  }

  span {
    color: #666666;

    &.#{$md-prefix}-color-222 {
      color: #222222;
    }

    &.#{$md-prefix}-font-bold {
      font-weight: 600;
      color: #222222;
    }
  }
}

::v-deep .#{$md-prefix}-base-table__body-wrapper {
  overflow: auto;
  flex: 1;

  .#{$md-prefix}-base-table__row.#{$md-prefix}-heightLight {
    > td {
      background: #e2efff;
    }
  }
}

::v-deep .#{$md-prefix}-base-table-column--selection {
  text-align: center;
}

::v-deep .#{$md-prefix}-require:before {
  margin-right: 4px;
  color: #f12933;
  content: '*';
}

::v-deep .#{$md-prefix}-date-editor.#{$md-prefix}-input {
  width: 220px;
}
</style>
