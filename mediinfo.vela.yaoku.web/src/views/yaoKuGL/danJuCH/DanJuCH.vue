<template>
  <div :class="prefixClass('rushRed')">
    <div :class="prefixClass('rushRed-header')">
      <div :class="prefixClass('rushRed-header__action')">
        <div :class="prefixClass('rushRed-header__action__radio')">
          <md-radio-group v-model="query.chuRuKBZ" @change="handleChange">
            <md-radio :label="0">出库</md-radio>
            <md-radio :label="1">入库</md-radio>
          </md-radio-group>
        </div>
        <div :class="prefixClass('rushRed-header__action__input')">
          <span :class="prefixClass('danJuHao-title')">单据号</span>
          <md-input
            v-model="query.danJuHao"
            placeholder="请输入单据号"
            width="214px"
            :class="prefixClass('change-input')"
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <i
                :class="prefixClass('icon-seach  input__icon cursor-pointer')"
                @click="handleSearch"
              ></i>
            </template>
          </md-input>
        </div>
      </div>
      <div v-show="isEmpty" :class="prefixClass('rushRed-header__show')">
        <div :class="prefixClass('rushRed-header__show__item')">
          <span :class="prefixClass('item__label')">供货单位：</span>
          <span :class="prefixClass('item__value')">{{
            chongHongData.gongHuoDWMC || '-'
          }}</span>
        </div>
        <div :class="prefixClass('rushRed-header__show__item')">
          <span :class="prefixClass('item__label')"
            >{{ query.chuRuKBZ == 1 ? '入库' : '出库' }}日期：</span
          >
          <span class="item__value">{{ chuRuKRQ || '-' }}</span>
        </div>
        <div :class="prefixClass('rushRed-header__show__item')">
          <span :class="prefixClass('item__label')">备注：</span>
          <span :class="prefixClass('item__value')">{{
            chongHongData.beiZhu || '-'
          }}</span>
        </div>
      </div>
    </div>
    <div v-loading="loading" :class="prefixClass('rushRed-body')">
      <div v-if="isEmpty" :class="prefixClass('rushRed-main')">
        <div :class="prefixClass('rushRed-main__header')">
          <div :class="prefixClass('rushRed-main__header__search')">
            <biz-yaopindw
              v-model="yaoPingDW"
              :class="prefixClass('yaopin-search')"
              showSuffix
              :showXiaoGuiGeYP="showXiaoGuiGeYP"
              @change="handleDingWei"
            />
          </div>
          <div :class="prefixClass('rushRed-main__header__action')">
            <md-button
              type="danger"
              :icon="prefixClass('icon-shanchuwap')"
              noneBg
              @click="handleDelete"
              >删除</md-button
            >
            <md-button type="primary" plain @click="handleChongHong"
              >冲红</md-button
            >
            <md-button type="primary" @click="handleChongHong('jiZhang')"
              >冲红并记账</md-button
            >
          </div>
        </div>
        <div :class="prefixClass('rushRed-main__body')">
          <md-editable-table-pro
            v-table-enter
            v-model="tableData"
            :columns="columns"
            height="100%"
            :showDefaultOperate="false"
            :maxLength="9999"
            :hideAddButton="true"
            ref="editableTable"
            id="chongHongTable"
            :autoFill="true"
            @selection-change="tableSelectChange"
          >
            <template #chongHongSL="{ row }">
              <md-input
                v-number.float="{}"
                v-model="row.chongHongSL"
                @change="handleChongHongSL(row)"
              />
            </template>
          </md-editable-table-pro>
        </div>
        <div :class="prefixClass('rushRed-main__footer')">
          <div :class="prefixClass('rushRed-main__footer__info left')">
            <span>制单：</span>
            <span :class="prefixClass('info__name color-222')">{{
              yongHuXM
            }}</span>
            <span :class="prefixClass('info__time color-222')">{{
              currentTime
            }}</span>
          </div>
          <div :class="prefixClass('rushRed-main__footer__info right')">
            <div :class="prefixClass('margin-right-12')">
              <span>共计：</span>
              <span :class="prefixClass('font-bold')">{{
                tableData.length
              }}</span>
              <span>种药品</span>
            </div>
            <div :class="prefixClass('margin-right-12')">
              <span style="margin-right: 8px">合计</span>
              <span>进价金额：</span>
              <span :class="prefixClass('font-bold')">{{ totalJiaJiaJE }}</span>
              <span>元</span>
            </div>
            <div>
              <span>零售金额：</span>
              <span :class="prefixClass('font-bold')">{{
                totalLingShouJE
              }}</span>
              <span>元</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else :class="prefixClass('rushRed-notPage nodata')">
        <img src="@/assets/images/wuChongHong.png" alt="请先选择冲红单据" />
        <span>请先选择冲红单据</span>
      </div>
    </div>
    <biz-yaopinph ref="bizYaoPinPH" />
  </div>
</template>

<script>
import { add, multiply } from '@/system/utils/mathComputed';
import { MdMessage, MdMessageBox } from '@mdfe/medi-ui';
import { MdEditableTablePro } from '@mdfe/medi-ui-pro';
import dayjs from 'dayjs';
import { getKuCunGLLX } from '@/system/utils/local-cache';
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import BizYaoPinPH from '@/components/YaoKu/BizYaoPinPH';
import { logger } from '@/service/log';
import {
  ChongHongChuKuDan,
  ChongHongJZChuKuDan,
  ChongHongJZRuKuDan,
  ChongHongRuKuDan,
  GetYaoPinCKDForCH,
  GetYaoPinRKDForCH,
} from '@/service/yaoPinYK/danJuCH';
import commonData from '@/system/utils/commonData';
import { getYongHuID, getYongHuXM } from '@/system/utils/local-cache';
import { getKuFangSZList } from '@/service/yaoPin/YaoPinZDJCSJ';
const chongHongDataInit = () => {
  return {
    id: '',
    gongHuoDWMC: '',
    beiZhu: '',
    ruKuDH: '',
    ruKuRQ: '',
    chuKuRQ: '',
    chuKuDH: '',
    ruKuDID: '',
    chuKuDID: '',
  };
};
export default {
  name: 'danjuch',
  data() {
    return {
      jinJiaXSDW: '',
      jinJiaJEXSDW: 2,
      lingShouJEXSDW: 2,
      query: {
        chuRuKBZ: 1, // 0 出库 | 1 入库
        danJuHao: '',
      },
      loading: false,
      isEmpty: false,
      yongHuXM: getYongHuXM(),
      yongHuID: getYongHuID(),
      currentTime: dayjs().format('YYYY-MM-DD'),
      tableData: [],
      tableSelection: [],
      columns: [
        {
          label: '',
          type: 'selection',
          align: 'center',
          width: 48,
        },
        {
          prop: 'yaoPinLX',
          label: '',
          align: 'center',
          width: 36,
          type: 'text',
          formatter: (row) => {
            return commonData.yaoPinLB[row.yaoPinLXDM]?.tag;
          },
        },
        {
          prop: 'faPiaoHM',
          label: '发票号码',
          width: 122,
          type: 'text',
        },
        {
          prop: 'faPiaoRQ',
          label: '发票日期',
          width: 108,
          type: 'text',
          formatter(row) {
            return row.faPiaoRQ ? dayjs(row.faPiaoRQ).format('YYYY-MM-DD') : '';
          },
        },
        {
          prop: 'yaoPinMCYGG',
          label: '药品名称与规格',
          minWidth: 274,
          type: 'text',
          formatter(row) {
            return row.yaoPinMC + row.yaoPinGG;
          },
        },
        {
          prop: 'chanDiMC',
          label: '产地名称',
          minWidth: 169,
          type: 'text',
        },
        {
          prop: 'baoZhuangDW',
          label: '单位',
          width: 50,
          align: 'center',
          type: 'text',
        },
        {
          prop: 'chuKuSL',
          label: '出库数量',
          align: 'right',
          minWidth: 75,
          type: 'text',
          formatter(row) {
            return Number(row.chuKuSL).toFixed(3);
          },
        },
        {
          prop: 'jinJia',
          label: '进价',
          align: 'right',
          minWidth: 65,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'jinJiaJE',
          label: '进价金额',
          align: 'right',
          minWidth: 100,
          type: 'text',
          formatter: (row, column, cellValue, index) => {
            return Number(cellValue).toFixed(this.jinJiaXSDW);
          },
        },
        {
          prop: 'keChongHSL',
          label: '可冲红数量',
          align: 'right',
          type: 'text',
          minWidth: 100,
        },
        {
          prop: 'shengChanPH',
          label: '生产批号',
          type: 'text',
          minWidth: 116,
        },
        {
          prop: 'yaoPinXQ',
          label: '药品效期',
          width: 108,
          type: 'text',
          formatter(row) {
            return dayjs(row.yaoPinXQ).format('YYYY-MM-DD');
          },
        },
        {
          slot: 'chongHongSL',
          label: '冲红数量',
          align: 'right',
          fixed: 'right',
          width: 100,
          className: this.prefixClass('cell-color-red'),
          startMode: 'dblclick',
          formatter: (row) => {
            return row.chongHongSL;
          },
        },
        {
          prop: 'chongHongJE',
          label: '冲红金额',
          align: 'right',
          fixed: 'right',
          type: 'text',
          className: this.prefixClass('cell-color-red'),
          width: 110,
          formatter: (row, column, cellValue, index) => {
            if (row.chongHongSL && row.jinJia) {
              return multiply(row.chongHongSL, row.jinJia).toFixed(
                this.jinJiaJEXSDW,
              );
            }
            return '0.00';
          },
        },
      ],
      chongHongData: chongHongDataInit(),
      yaoPingDW: '',
      tableBodyEle: '',
      showXiaoGuiGeYP: false,
    };
  },
  computed: {
    chuRuKRQ() {
      if (this.query.chuRuKBZ == 0) {
        return this.chongHongData.chuKuRQ;
      } else {
        return this.chongHongData.ruKuRQ;
      }
    },
    totalJiaJiaJE() {
      return this.tableData
        .reduce((prev, item) => {
          return prev + item.jinJiaJE;
        }, 0)
        .toFixed(this.jinJiaJEXSDW);
    },
    totalLingShouJE() {
      return this.tableData
        .reduce((prev, item) => {
          return prev + item.lingShouJE;
        }, 0)
        .toFixed(this.lingShouJEXSDW);
    },
  },
  async created() {
    this.handleChange(this.query.chuRuKBZ);
    const res = await getKuFangSZList([
      'jinJiaJEXSDWS',
      'jinJiaXSDWS',
      'lingShouJEXSDWS',
      'ShiFouYXXGGRK',
    ]);
    if (res.length > 0) {
      res.forEach((el) => {
        if (el.xiangMuDM == 'jinJiaJEXSDWS') {
          this.jinJiaJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'jinJiaXSDWS') {
          this.jinJiaXSDW = el.xiangMuZDM ? el.xiangMuZDM : '';
        } else if (el.xiangMuDM == 'lingShouJEXSDWS') {
          this.lingShouJEXSDW = el.xiangMuZDM ? el.xiangMuZDM : 2;
        } else if (el.xiangMuDM == 'ShiFouYXXGGRK') {
          this.showXiaoGuiGeYP = el.xiangMuZDM == 1 ? true : false;
        }
      });
    }
    //如果是中药库
    const xiaoShuDianWS = getKuCunGLLX().indexOf('3') > -1 ? 5 : 3;
    // 判断进价零售价是否设置了值，没有则赋默认值
    this.jinJiaXSDW = this.jinJiaXSDW ? this.jinJiaXSDW : xiaoShuDianWS;
  },
  methods: {
    //查询列表
    handleSearch() {
      const { danJuHao, chuRuKBZ } = this.query;
      if (!danJuHao) {
        this.$message({
          type: 'warning',
          message: '请输入单据号！',
        });
        return;
      }
      this.tableData = [];
      this.chongHongData = chongHongDataInit();
      this.loading = true;
      const query = {
        DanJuH: this.query.danJuHao,
      };
      if (chuRuKBZ == 0) {
        this.getChuKuList(query);
      } else if (chuRuKBZ == 1) {
        this.getRuKuList(query);
      }
    },
    //获取出库列表
    getChuKuList(query) {
      GetYaoPinCKDForCH(query)
        .then((res) => {
          this.isEmpty = true;
          //设置默认的冲红数量为可冲红数量
          res.chuKuDMXForCHList.forEach((item) => {
            item.chongHongSL = item.keChongHSL;
          });
          Object.assign(this.chongHongData, res);
          this.tableData = res.chuKuDMXForCHList;
          this.chongHongData.chuKuRQ = dayjs(this.chongHongData.chuKuRQ).format(
            'YYYY-MM-DD',
          );
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取入库列表
    getRuKuList(query) {
      GetYaoPinRKDForCH(query)
        .then((res) => {
          this.isEmpty = true;
          //设置默认的冲红数量为可冲红数量
          res.ruKuDMXForCHList.forEach((item) => {
            item.chongHongSL = item.keChongHSL;
          });
          Object.assign(this.chongHongData, res);
          this.tableData = res.ruKuDMXForCHList;
          this.chongHongData.ruKuRQ = dayjs(this.chongHongData.ruKuRQ).format(
            'YYYY-MM-DD',
          );
        })
        .catch((error) => {
          console.error(error);
          logger.error(error);
          // MdMessageBox.confirm(`${error.message}`, '业务错误', {
          //  confirmButtonText: '我知道了',
          //  showCancelButton: false,
          //  type: 'error',
          // })
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //出入库修改
    handleChange(val) {
      if (val == 0) {
        this.columns.splice(7, 1, {
          prop: 'chuKuSL',
          label: '出库数量',
          align: 'right',
          minWidth: 75,
          type: 'text',
          formatter(row) {
            return Number(row.chuKuSL).toFixed(3);
          },
        });
      } else {
        this.columns.splice(7, 1, {
          prop: 'ruKuSL',
          label: '入库数量',
          align: 'right',
          minWidth: 85,
          type: 'text',
          formatter(row) {
            return Number(row.ruKuSL).toFixed(3);
          },
        });
      }
      this.clearModal();
    },
    //table 选中变化事件
    tableSelectChange(selection) {
      this.tableSelection = selection;
    },
    //冲红操作
    handleChongHong(type) {
      if (Array.isArray(this.tableData) && this.tableData.length == 0) {
        MdMessageBox.confirm('单据无数据，不可冲红！', '操作提醒！', {
          confirmButtonText: '好的',
          showCancelButton: false,
          type: 'warning',
        });
        return;
      }
      this.loading = true;
      if (this.query.chuRuKBZ == 0) {
        this.handleChuKuDCH(type);
      } else if (this.query.chuRuKBZ == 1) {
        this.handleRuKuDCH(type);
      }
    },
    //入库单冲红
    async handleRuKuDCH(type) {
      //参数处理
      const params = {
        id: this.chongHongData.id,
        ruKuDH: this.chongHongData.ruKuDH,
        yaoPinZS: this.tableData.length,
        zhiDanSJ: this.currentTime,
        zhiDanRID: this.yongHuID,
        zhiDanRXM: this.yongHuXM,
      };

      params.jinJiaJE = this.tableData.reduce((prev, item) => {
        return add(prev, multiply(item.chongHongSL, item.jinJia));
      }, 0);

      params.lingShouJE = this.tableData.reduce((prev, item) => {
        return add(prev, multiply(item.chongHongSL, item.lingShouJia));
      }, 0);
      params.jinJiaJE = Number(params.jinJiaJE).toFixed(6);
      params.lingShouJE = Number(params.lingShouJE).toFixed(6);
      params.ruKuDanMXCHList = this.tableData.map((item) => {
        return {
          ruKuDMXID: item.ruKuDMXID,
          jiaGeID: item.jiaGeID,
          chongHongSL: +item.chongHongSL,
          chongHongJE: multiply(item.chongHongSL, item.jinJia),
        };
      });
      let errorFlag = false;
      let ruKuDID = null;
      try {
        ruKuDID = await ChongHongRuKuDan(params);
        MdMessage.success('冲红成功');
        errorFlag = true;
        this.clearModal();
        if (type == 'jiZhang') {
          await ChongHongJZRuKuDan({ ruKuDID: ruKuDID });
          MdMessage.success('记账成功');
          this.$router.push({
            name: 'YaoPinRK',
            query: {
              showType: 'third',
              isChongHong: '2',
              id: ruKuDID,
            },
          });
        } else {
          this.$router.push({
            name: 'YaoPinRK',
            query: {
              showType: 'second',
              isChongHong: '1',
              id: ruKuDID,
            },
          });
        }
      } catch (err) {
        if (errorFlag) {
          this.$router.push({
            name: 'YaoPinRK',
            query: {
              showType: 'second',
              isChongHong: '1',
              id: ruKuDID,
            },
          });
        }
      } finally {
        this.loading = false;
      }
    },
    //出库单冲红
    async handleChuKuDCH(type) {
      const params = {
        id: this.chongHongData.id,
        chuKuDH: this.chongHongData.chuKuDH,
        yaoPinZS: +this.tableData.length,
        zhiDanSJ: this.currentTime,
        zhiDanRID: this.yongHuID,
        zhiDanRXM: this.yongHuXM,
      };
      params.jinJiaJE = this.tableData.reduce((prev, item) => {
        return add(prev, multiply(item.chongHongSL, item.jinJia));
      }, 0);

      params.lingShouJE = this.tableData.reduce((prev, item) => {
        return add(prev, multiply(item.chongHongSL, item.lingShouJia));
      }, 0);

      params.chuKuDanMXCHList = this.tableData.map((item) => {
        return {
          chuKuDMXID: item.chuKuDMXID,
          jiaGeID: item.jiaGeID,
          chongHongSL: +item.chongHongSL,
          chongHongJE: multiply(item.chongHongSL, item.jinJia),
        };
      });
      let errorFlag = false;
      let chuKuDID = null;
      try {
        chuKuDID = await ChongHongChuKuDan(params);
        MdMessage.success('冲红成功');
        errorFlag = true;
        this.clearModal();
        if (type == 'jiZhang') {
          await ChongHongJZChuKuDan({ chuKuDID: chuKuDID });
          MdMessage.success('记账成功');
          this.$router.push({
            name: 'YaoPinCK',
            query: {
              showType: 'third',
              isChongHong: '2',
              id: chuKuDID,
            },
          });
        } else {
          this.$router.push({
            name: 'YaoPinCK',
            query: {
              showType: 'second',
              isChongHong: '1',
              id: chuKuDID,
            },
          });
        }
      } catch (err) {
        if (errorFlag) {
          this.$router.push({
            name: 'YaoPinCK',
            query: {
              showType: 'second',
              isChongHong: '1',
              id: chuKuDID,
            },
          });
        }
      } finally {
        this.loading = false;
      }
    },
    //删除
    handleDelete() {
      if (
        Array.isArray(this.tableSelection) &&
        this.tableSelection.length == 0
      ) {
        this.$message({
          type: 'warning',
          message: '至少选中一条记录',
        });
        return;
      }
      this.tableData = this.tableSelection.reduce((prev, item) => {
        let index = prev.findIndex((i) => {
          if (this.query.chuRuKBZ == 1) {
            return i.ruKuDMXID == item.ruKuDMXID;
          } else {
            return i.chuKuDMXID == item.chuKuDMXID;
          }
        });
        if (index > -1) {
          prev.splice(index, 1);
        }
        return prev;
      }, this.tableData);
      this.$message({
        type: 'success',
        message: '删除成功',
      });
    },
    //清空
    clearModal() {
      this.chongHongData = chongHongDataInit();
      this.tableData = [];
      this.isEmpty = false;
    },

    handleChongHongSL(row) {
      if (row.chongHongSL <= 0) {
        this.$message({
          type: 'warning',
          message: '冲红数量必须大于0,请重新输入!',
        });
        row.chongHongSL = null;
      } else if (row.chongHongSL > row.keChongHSL) {
        this.$message({
          type: 'warning',
          message: '冲红数量不能大于可冲红数量!',
        });
        row.chongHongSL = row.keChongHSL;
      }
    },
    //定位
    async handleDingWei(data) {
      if (!data) return;
      if (!(Array.isArray(this.tableData) && this.tableData.length > 0)) {
        this.$message({
          type: 'warning',
          message: '冲红列表为空,不能定位！',
        });
        return;
      }

      try {
        //清除高亮
        this.clearHeightLight();
        const result = await this.$refs.bizYaoPinPH.show({
          yaoPinMC: data.yaoPinMC,
          jiaGeID: data.jiaGeID,
        });

        let index = this.tableData.findIndex((item) => {
          return (
            item.jiaGeID == data.jiaGeID &&
            item.shengChanPH == result.shengChanPH
          );
        });
        if (index == -1) {
          this.$message({
            type: 'warning',
            message: '未找到该药品',
          });
          return;
        }
        this.tableDomHeightLight(index);
      } catch (err) {
        // console.log('err', err);
      }
    },
    //dom定位
    tableDomHeightLight(index) {
      //获取高亮dom节点
      const childDom = this.tableBodyEle?.querySelectorAll(
        `.mediinfo-vela-yaoku-web-base-table__row`,
      )[index];
      if (childDom) {
        childDom.classList.add(this.prefixClass('heightLight'));
        this.tableBodyEle.scrollTop = 9999;
      }
    },
    //清除高亮
    clearHeightLight() {
      //获取table DOM
      if (!this.tableBodyEle) {
        this.tableBodyEle = document.querySelector(
          `#chongHongTable .mediinfo-vela-yaoku-web-base-table__body-wrapper`,
        );
      }
      // 清除高亮
      const rowList = Array.from(
        this.tableBodyEle.querySelectorAll(
          `.mediinfo-vela-yaoku-web-base-table__row`,
        ),
      );
      rowList.forEach((item) => {
        item.classList.remove(this.prefixClass('heightLight'));
      });
    },
  },
  components: {
    'md-editable-table-pro': MdEditableTablePro,
    'biz-yaopindw': BizYaoPinDW,
    'biz-yaopinph': BizYaoPinPH,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.#{$md-prefix}-rushRed {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px;
  background: #f0f2f5;
  overflow: hidden;
  &-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 45px;
    padding: 0 8px;
    box-sizing: border-box;
    // background-color: #edf6fd;
    background-color: getCssVar('color-1');
    flex-shrink: 0;
    &__action {
      display: flex;
      &__radio {
        display: flex;
        justify-content: center;
        width: 128px;
        height: 30px;
        background-color: rgba(255, 255, 255, 0.4);
        border: 1px solid #c3e5fd;
        border-color: getCssVar('color-2');
        border-radius: 4px;
      }
      &__input {
        display: flex;
        align-items: center;
        margin-left: 12px;
        .#{$md-prefix}-danJuHao-title {
          white-space: nowrap;
          margin-right: 8px;
          color: #333333;
          // font-size: 14px;
          font-size: getCssVar('font-2');
          line-height: 20px;
        }
      }
    }
    &__show {
      display: flex;
      align-items: center;
      margin-left: 24px;
      &__item {
        margin-right: 28px;
        // font-size: 14px;
        font-size: getCssVar('font-2');
        line-height: 20px;
        .#{$md-prefix}-item__label {
          color: #666666;
        }
        .#{$md-prefix}-item__value {
          color: #222222;
        }
      }
    }
  }
  &-body {
    flex: 1;
    min-height: 0;
    background: #ffffff;
    padding: 8px;
    ::v-deep .#{$md-prefix}-editable-table {
      height: 100%;
    }
  }
  &-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    &__header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      flex-shrink: 0;
      &__search {
        width: 240px;
      }
      &__action {
        .#{$md-prefix}-button {
          margin-left: 8px;
        }
        .#{$md-prefix}-change-input {
          width: 264px;
          margin-left: 8px;
        }
      }
    }
    &__body {
      flex: 1;
      overflow: hidden;
      ::v-deep .#{$md-prefix}-table.#{$md-prefix}-table--edit {
        td.#{$md-prefix}-cell-color-red {
          .cell {
            color: #f12933;
          }
        }
        .#{$md-prefix}-base-table__row.#{$md-prefix}-heightLight {
          > td {
            background: #e2efff;
          }
        }
      }
      .#{$md-prefix}-row-copy-label {
        cursor: pointer;
        &:hover {
          color: #1e88e5;
          text-decoration: underline;
        }
      }
    }
    &__footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      flex-shrink: 0;
      line-height: 20px;
      font-size: 14px;
      &__info {
        display: flex;
        .#{$md-prefix}-info__name {
          margin-right: 8px;
        }
        .#{$md-prefix}-margin-right-12 {
          margin-right: 12px;
        }
        &.#{$md-prefix}-right {
          span {
            color: #aaa;
          }
        }
      }
      span {
        color: #666666;
        &.#{$md-prefix}-color-222 {
          color: #222222;
        }
        &.#{$md-prefix}-font-bold {
          font-weight: 600;
          color: #222222;
        }
      }
    }
  }
  &-notPage {
    height: 100%;
    background: #ffffff;
    > span {
      color: #aaaaaa;
      font-size: 14px;
      line-height: 20px;
      padding-top: 16px;
    }
  }
}
</style>
