<template>
  <md-dialog
    title="分类信息"
    v-model="visible"
    :append-to-body="false"
    :before-close="handleClose"
  >
    <md-form
      v-loading="dialogLoad"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      use-status-icon
      ref="form"
    >
      <md-form-item label="分类名称" prop="fenLeiMC">
        <md-input v-model="formData.fenLeiMC" />
      </md-form-item>

      <md-form-item label="显示列" prop="columnsCheck">
        <div class="HISYK-TPNYPWH-xianShiLie">
          <md-checkbox-group v-model="formData.columnsCheck" inline>
            <md-checkbox
              v-for="(item, index) in columnsList"
              :label="item.zhiBiaoID"
              :key="item.zhiBiaoID"
            >
              {{ item.zhiBiaoMC }}
            </md-checkbox>
          </md-checkbox-group>
        </div>
      </md-form-item>
    </md-form>
    <template #footer>
      <span class="dialog-footer">
        <md-button
          v-if="formData.id"
          :disabled="saveLoading"
          type="danger"
          plain
          style="float: left"
          @click="handleCancel"
        >
          作废
        </md-button>
        <md-button
          type="primary"
          plain
          :disabled="saveLoading"
          @click="handleClose"
          >取消</md-button
        >
        <md-button type="primary" :loading="saveLoading" @click="handleSave">
          确定
        </md-button>
      </span>
    </template>
  </md-dialog>
</template>

<script>
import {
  GetYingYangFLXX,
  GetZhiBiaoList,
  SaveYingYangFL,
  ZuoFeiYingYangFL,
} from '@/service/yaoPin/yaoPinTPN.js';
import { MdMessageBox } from '@mdfe/medi-ui';
export default {
  name: 'fenLeiXX',
  data() {
    return {
      saveLoading: false,
      dialogLoad: false,
      visible: false,
      formData: {
        id: null,
        fenLeiMC: null,
        fenLeiID: null,
        columnsCheck: [],
      },
      formRules: {
        fenLeiMC: [{ required: true, message: '分类名称', trigger: 'change' }],
        // columnsCheck: [
        //   {
        //     required: true,
        //     type: 'array',
        //     message: '至少选中一个显示列',
        //     trigger: 'change',
        //   },
        // ],
      },
      columnsList: [],
    };
  },
  created() {
    GetZhiBiaoList({ qiYongBZ: 1 }).then((res) => {
      this.columnsList = res;
    });
  },
  methods: {
    async showDialog({ mode, data }) {
      try {
        this.formData.id = null;
        this.visible = true;
        this.dialogLoad = true;
        await this.$nextTick();
        this.$refs.form.resetFields();
        if (mode == 'edit') {
          const curData = await GetYingYangFLXX({ id: data.id });
          let columnsCheck = [];
          Object.assign(this.formData, data);
          curData.yingYangFLZBList.forEach((item) => {
            columnsCheck.push(item.zhiBiaoID);
          });
          this.formData.columnsCheck = columnsCheck;
        }
      } catch (error) {
        throw error;
      } finally {
        this.$refs.form.clearValidate();
        this.dialogLoad = false;
      }
    },

    async handleSave() {
      const result = await this.$refs.form.validate();
      if (!result) return;
      try {
        this.saveLoading = true;
        let yingYangFLZBList = [];
        console.log(this.formData.columnsCheck, 'this.formData.columnsCheck');
        console.log(this.columnsList, 'curData');
        this.formData.columnsCheck.forEach((ele) => {
          const curData = this.columnsList.find(
            (item) => item.zhiBiaoID == ele,
          );
          if (curData) {
            yingYangFLZBList.push({
              zhiBiaoID: ele,
              zhiBiaoMC: curData.zhiBiaoMC,
            });
          }
        });
        let params = {
          ...this.formData,
          yingYangFLZBList,
        };
        await SaveYingYangFL(params);
        this.$emit('refresh');
      } catch (e) {
        throw e;
      } finally {
        this.saveLoading = false;
        this.visible = false;
      }
    },
    handleClose() {
      this.visible = false;
    },
    handleCancel() {
      MdMessageBox.confirm('作废后数据不可恢复，确定作废？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        ZuoFeiYingYangFL({ id: this.formData.id }).then(() => {
          this.visible = false;
          this.$refs.form.resetFields();
          this.$emit('change');
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-TPNYPWH-xianShiLie {
  height: 280px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  padding: getCssVar('spacing-3');
  overflow: auto;

  ::v-deep .#{$namespace}-checkbox-group .#{$namespace}-checkbox {
    width: 48%;
  }
  .#{$namespace}-checkbox {
    margin-right: getCssVar('spacing-4');
  }
}
</style>
