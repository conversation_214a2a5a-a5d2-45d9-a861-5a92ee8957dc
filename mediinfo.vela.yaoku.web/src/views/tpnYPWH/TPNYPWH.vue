<template>
  <div class="HISYK-TPNYPWH">
    <div class="HISYK-TPNYPWH-header">
      <span class="mr-8">药名</span>
      <biz-yaopindw
        v-model="searchObj"
        placeholder="输入关键字搜索药品"
        style="width: 285px"
        yaoPinLXStr="1|2"
        @change="handleSearch"
      >
      </biz-yaopindw>
    </div>
    <div class="HISYK-TPNYPWH-container" v-loading="fenLeiLoading">
      <div class="HISYK-TPNYPWH-Aside">
        <div class="Aside-title">
          <md-title label="TPN分类"></md-title>
          <md-button
            type="primary"
            :icon="nsIcon.b('xinzeng')"
            noneBg
            @click="handleAddType"
            >分类</md-button
          >
        </div>
        <md-scrollbar
          v-if="typeList.length > 0"
          :native="false"
          class="Aside-scrollbar"
        >
          <div
            :class="['type-list', typeActive == item.fenLeiID ? 'active' : '']"
            v-for="item in typeList"
            :key="item.fenLeiID"
            @click="handleTypeClick(item)"
          >
            <span class="name">{{ item.fenLeiMC }}</span>
            <span class="edit">
              <md-button
                type="primary"
                :icon="nsIcon.b('bianji')"
                noneBg
                @click="handleEditType(item)"
              ></md-button
            ></span>
            <md-icon
              v-if="typeActive == item.id"
              class="pos"
              name="youjiantou-s"
            />
          </div>
        </md-scrollbar>
        <md-empty v-else class="Aside-scrollbar empty">暂无数据</md-empty>
      </div>
      <div class="HISYK-TPNYPWH-main">
        <div class="TPNYPWH-main-content">
          <md-editable-table-pro
            v-model="tableData"
            row-key="id"
            height="100%"
            :columns="columns"
            :new-row="newRow"
            :rules="rules"
            autoNew
            auto-fill
            hideAddButton
            :key="key"
            ref="mdEditTable"
            :showDefaultOperate="false"
          >
            <template #yaoPinMCYGG="{ $index, row, cellRef }">
              <biz-yaopindw
                v-model="row.yaoPinMCYGG"
                labelKey="yaoPinZC"
                :class="nsYP.b('select')"
                yaoPinLXStr="1|2"
                :columnsList="columnsList"
                @change="
                  handleTableYaoPinDWChange($event, row, $index, cellRef)
                "
              >
              </biz-yaopindw>
            </template>
            <template #tuiJianJL="{ $index }">
              <md-input
                v-number.float="{ min: 0 }"
                v-model="tableData[$index].tuiJianJL"
              ></md-input>
            </template>
            <template #moErXS="{ $index }">
              <md-input v-model="tableData[$index].sex"></md-input>
            </template>
            <template #gaoNengpttl="{ $index }">
              <md-checkbox
                v-model="tableData[$index].gaoNengpttl"
              ></md-checkbox>
            </template>
            <template #opera="{ $index }">
              <md-button
                type="primary"
                :icon="nsIcon.b('shanchu')"
                noneBg
                @click="handleRemove($index)"
              ></md-button>
            </template>
          </md-editable-table-pro>
        </div>
        <div class="HISYK-TPNYPWH-footer">
          <md-button class="mr-8" @click="handleCancel">取消</md-button>
          <md-button type="primary" @click="handleSave">保存</md-button>
        </div>
      </div>
    </div>
    <fenLeiXX ref="fenLeixx" @refresh="getTypeData"></fenLeiXX>
  </div>
</template>

<script>
//TODO
import BizYaoPinDW from '@/components/YaoKu/BizYaoPinDW';
import {
  GetFenLeiBTList,
  GetFenLeiYPList,
  GetYingYangFLList,
  SaveFenLeiYP,
} from '@/service/yaoPin/yaoPinTPN.js';
import { GetCaiGouJHDGHXX } from '@/service/yaoPinYK/caiGouJH.js';
import { useParams } from '@/system/utils/useParams';
import {
  MdCheckbox,
  MdInput,
  MdMessage,
  MdMessageBox,
  useNamespace,
} from '@mdfe/medi-ui';
import { cloneDeep } from 'lodash';
import fenLeiXX from './components/fenLeiXX.vue';
let columnsDefault = () => {
  return [
    {
      label: '',
      prop: 'yaoPinLXMC',
      width: 35,
      type: 'text',
      fixed: true,
      align: 'center',
      showOverflowTooltip: false,
      formatter(v) {
        return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
      },
    },
    {
      label: '药品名称与规格',
      prop: 'yaoPinMCYGG',
      slot: 'yaoPinMCYGG',
      width: 270,
      endMode: 'custom',
      formatter(v) {
        if (v.yaoPinMC) {
          return v.yaoPinMC + ' ' + v.yaoPinGG;
        } else {
          return '';
        }
      },
      showOverflowTooltip: true,
      autoNewRequired: true,
      fixed: true,
      placeholder: '请输入',
    },
    {
      label: '产地',
      prop: 'chanDiMC',
      type: 'text',
      minWidth: 200,
      showOverflowTooltip: true,
    },
    {
      label: '包装单位',
      prop: 'baoZhuangDW',
      width: 80,
      type: 'text',
      showOverflowTooltip: false,
    },
    {
      label: '剂量单位',
      prop: 'jiLiangDW',
      width: 80,
      type: 'text',
      showOverflowTooltip: false,
    },
    {
      label: '推荐剂量',
      prop: 'tuiJianJL',
      slot: 'tuiJianJL',
      align: 'right',
      placeholder: '请输入',
      width: 90,
      formatter: (row) => row.tuiJianJL || 0,
      showOverflowTooltip: false,
      autoNewRequired: true,
    },
    {
      label: '',
      prop: 'opera',
      slot: 'opera',
      width: 50,
      align: 'center',
      fixed: 'right',
      showOverflowTooltip: false,
    },
  ];
};
export default {
  name: 'TPNYPWH',
  data() {
    return {
      fenLeiLoading: false,
      searchVal: null,
      searchObj: null,
      typeActive: 0,
      typeList: [],
      rules: {
        tuiJianJL: [{ required: true, message: '请输入', trigger: 'change' }],
      },
      columns: columnsDefault(),
      columnsList: [
        {
          label: '',
          prop: 'yaoPinLXMC',
          width: 50,
          align: 'center',
          formatter(v) {
            return v.yaoPinLXMC ? v.yaoPinLXMC.slice(0, 1) : '';
          },
        },
        {
          label: '药品名称',
          prop: 'yaoPinMCYGG',
          width: 400,
          formatter(v) {
            if (v.yaoPinMC) {
              return v.yaoPinMC;
            } else {
              return '';
            }
          },
        },
        {
          label: '规格',
          prop: 'yaoPinGG',
          width: 200,
          formatter(v) {
            if (v.jiaGeID) {
              return v.yaoPinGG;
            } else {
              return '';
            }
          },
          showOverflowTooltip: true,
        },
        {
          label: '产地名称',
          prop: 'chanDiMC',
          width: 140,
        },
        {
          label: '单位',
          prop: 'baoZhuangDW',
          width: 50,
        },
      ],
      tableData: [],
      originData: [],
      dynamicsData: {},
      key: 0,
      tableHeaderData: [],
    };
  },
  setup() {
    const nsIcon = useNamespace('icon');
    const nsYP = useNamespace('yaopin');

    return {
      nsIcon,
      nsYP,
    };
  },
  async created() {
    await this.getTypeData();
  },
  methods: {
    async getTypeData() {
      this.fenLeiLoading = true;
      GetYingYangFLList({ jiaGeID: this.searchVal })
        .then((res) => {
          this.typeList = res;
          if (res.length > 0) {
            this.typeActive = res[0].fenLeiID;
            this.fenLeiMC = res[0].fenLeiMC;
            this.getHeaderData();
          } else {
            this.tableData = [];
          }
        })
        .finally(() => {
          this.fenLeiLoading = false;
        });
    },
    async getHeaderData() {
      // 获取分类表头列表
      this.tableHeaderData = await GetFenLeiBTList({
        fenLeiID: this.typeActive,
      });
      var column = [];
      this.dynamicsData = {};
      this.tableHeaderData.forEach((tab) => {
        let render = (h, { $index }) => {
          return h(MdInput, {
            placeholder: '请输入描述',
            modelValue: this.tableData[$index][tab.zhiBiaoMC],
            'onUpdate:modelValue': (val) =>
              (this.tableData[$index][tab.zhiBiaoMC] = val),
            //TODO自定义指令，业务组件库的内容
            // directives: [
            //   {
            //     name: 'number',
            //     value: { min: 0 },
            //     modifiers: {
            //       float: true
            //     }
            //   }
            // ],
          });
        };
        if (tab.xianShiFSDM == 2) {
          render = (h, { $index }) => {
            return h(MdCheckbox, {
              placeholder: '请输入描述',
              trueLabel: 1,
              falseLabel: 0,
              modelValue: this.tableData[$index][tab.zhiBiaoMC],
              onChange: (val) => (this.tableData[$index][tab.zhiBiaoMC] = val),
            });
          };
        }
        let obj = {
          label: tab.zhiBiaoMC,
          prop: tab.zhiBiaoMC,
          placeholder: '请输入',
          width: 180,
          align: tab.xianShiFSDM == 1 ? 'left' : 'center',
          autoNewRequired: true,
          formatter:
            tab.xianShiFSDM == 1
              ? (row) => {
                  if (row[tab.zhiBiaoMC] === 0) return '0';

                  return row[tab.zhiBiaoMC];
                }
              : null,
          showOverflowTooltip: false,
          render,
        };
        if (!this.dynamicsData.hasOwnProperty.call(tab.zhiBiaoMC)) {
          this.dynamicsData[tab.zhiBiaoMC] = null;
        }
        column.push(obj);
        if (!this.rules[obj.prop]) {
          this.rules[obj.prop] = [
            { required: true, message: '请输入', trigger: 'change' },
          ];
        }
      });
      let defaultData = columnsDefault();
      defaultData.splice(defaultData.length - 1, 0, ...column);
      this.columns = defaultData;
      this.getTableData();
      this.$nextTick(() => {
        this.key++;
      });
    },
    async getTableData() {
      this.tableData = [];
      const res = await GetFenLeiYPList({
        fenLeiID: this.typeActive,
        jiaGeID: this.searchVal,
      });
      this.originData = cloneDeep(res);
      res.forEach((item) => {
        this.tableHeaderData.find((subKey) => {
          if (!Object.keys(item).includes(subKey.zhiBiaoMC)) {
            item[subKey.zhiBiaoMC] = '';
          }
        });
        item.yuanJiaGID = item.jiaGeID;
        item.yaoPinMCYGG = {
          baoZhuangDW: item.baoZhuangDW,
          chanDiMC: item.chanDiMC,
          danJia: item.danJia,
          guiGeID: item.guiGeID,
          jiaGeID: item.jiaGeID,
          jinJia: item.jinJia,
          kuCunSL: item.kuCunSL,
          yaoPinGG: item.yaoPinGG,
          yaoPinLXDM: item.yaoPinLXDM,
          yaoPinLXMC: item.yaoPinLXMC,
          yaoPinMC: item.yaoPinMC,
        };
      });

      this.tableData = res;
      this.tableData.push(this.newRow());
    },
    handleTypeClick(item) {
      this.typeActive = item.fenLeiID;
      this.fenLeiMC = item.fenLeiMC;
      this.getHeaderData();
    },
    handleSearch(data) {
      if (data) {
        this.searchVal = data.jiaGeID;
      } else {
        this.searchVal = '';
      }
      this.getTypeData();
    },
    /**
     * 选择药品change事件
     */
    handleTableYaoPinDWChange(data, row, index, cellRef) {
      if (!data) {
        Object.assign(row, this.newRow());
        cellRef.endEdit();
        return;
      }
      //判断是否有重复的
      const isRepeat = this.tableData.some((item, i) => {
        if (index == i) return false; //排除自身
        return item.jiaGeID == data.jiaGeID;
      });

      if (isRepeat) {
        MdMessage.warning('药品不可以重复添加');
        Object.assign(row, this.newRow());
        // this.$refs.mdEditTable._current()
        return;
      } else {
        this.loading = true;
        GetCaiGouJHDGHXX(data.jiaGeID, data.guiGeID)
          .then((res) => {
            const assingData = {
              baoZhuangDW: data.baoZhuangDW,
              chanDiMC: data.chanDiMC,
              chanDiID: data.chanDiID,
              yaoPinGG: data.yaoPinGG,
              jiaGeID: data.jiaGeID,
              kuCunSL: data.kuCunSL,
              yaoPinMC: data.yaoPinMC,
              yaoPinLXDM: data.yaoPinLXDM,
              yaoPinLXMC: data.yaoPinLXMC,
              jiLiangDW: data.jiLiangDW,
            };
            Object.assign(row, assingData);
            row.yaoPinMCYGG = {
              guiGeID: data.guiGeID,
              yaoPinMC: data.yaoPinMC,
              yaoPinGG: data.yaoPinGG,
              jiaGeID: data.jiaGeID,
              chanDiMC: data.chanDiMC,
              kuCunSL: data.kuCunSL,
              baoZhuangDW: data.baoZhuangDW,
              danJia: data.danJia,
              yaoPinLXDM: data.yaoPinLXDM,
              yaoPinLXMC: data.yaoPinLXMC,
            };
            row.yaoPinZC = data.yaoPinMC + ' ' + data.yaoPinGG;
            this.loading = false;
            //判断是否已存在空行
            const isKongHang = this.tableData.some((item) => !item.jiaGeID);
            if (!isKongHang) {
              this.tableData.push(this.newRow());
            }
          })
          .catch(() => {
            // this.$refs.mdEditTable._current()
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    async handleRemove(index) {
      if (!this.tableData[index].id) {
        this.tableData.splice(index, 1);
        return;
      }
      await MdMessageBox.confirm('作废后数据不可恢复，确定作废？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      let params = {
        fenLeiID: this.typeActive,
        fenLeiMC: this.fenLeiMC,
        addList: [],
        updateList: [],
        delList: [this.tableData[index].jiaGeID],
      };
      await SaveFenLeiYP(params);
      this.getTableData();
      this.$message({
        type: 'success',
        message: '保存成功',
      });
    },
    handleAddType() {
      this.$refs.fenLeixx.showDialog({
        mode: 'add',
      });
    },
    handleEditType(item) {
      this.$refs.fenLeixx.showDialog({
        mode: 'edit',
        data: item,
      });
    },
    newRow() {
      let obj = {
        id: null,
        jiLiangDW: null,
        tuiJianJL: null,
        yaoPinLXDM: null, //药品类型代码
        yaoPinLXMC: null, //药品类型名称
        chanDiMC: null, //产地名称
        yaoPinMC: null, //药品名称
        yaoPinGG: null, // 药品规格
        yaoPinMCYGG: {}, //药品名称与规格
        jiaGeID: null, //价格ID
        baoZhuangDW: null, // 包装单位
      };
      Object.assign(obj, this.dynamicsData);
      return obj;
    },
    handleCancel() {
      this.tableData = cloneDeep(this.originData);
    },
    handleSave() {
      const hasEmpty = this.tableData.some((item) => {
        let isSome = false;
        if (item['jiaGeID']) {
          for (const key in item) {
            if (Object.hasOwnProperty.call(item, key)) {
              if (
                !['id', 'jiLiangDW', 'baoZhuangDW', 'element'].includes(key)
              ) {
                const element = item[key];
                if (
                  (!element && element != 0) ||
                  String(element).trim().length == 0
                ) {
                  isSome = true;
                  break;
                }
              }
            }
          }
        }
        return isSome;
      });

      if (hasEmpty) {
        this.$message({
          type: 'warning',
          message: '数据没有写全！',
        });
        return;
      }
      if (this.tableData.length === 1) {
        return;
      }
      let tableData = cloneDeep(this.tableData);
      if (!tableData[tableData.length - 1].jiaGeID) {
        tableData = tableData.filter((fl) => fl.jiaGeID);
      }
      const { contrast } = useParams(this.originData, {
        primaryKey: 'jiaGeID',
      });
      const { addArr, delArr, putArr } = contrast(tableData);
      let params = {
        fenLeiID: this.typeActive,
        fenLeiMC: this.fenLeiMC,
        addList: addArr,
        updateList: putArr,
        delList: delArr,
      };
      SaveFenLeiYP(params).then((res) => {
        this.getHeaderData();
        this.$message({
          type: 'success',
          message: '保存成功',
        });
      });
    },
  },
  components: {
    fenLeiXX,
    'biz-yaopindw': BizYaoPinDW,
  },
};
</script>

<style lang="scss" scoped>
@use '@mdfe/medi-ui/theme-chalk/src/mixins/mixins.scss' as *;

.HISYK-TPNYPWH {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-header {
    background-color: #fff;
    padding: getCssVar('spacing-3');
  }

  &-container {
    flex: 1;
    display: flex;
    min-height: 0;
    justify-content: space-between;
    background-color: #f0f2f5;
    padding: getCssVar('spacing-3');
  }

  &-Aside {
    display: flex;
    flex-direction: column;
    width: 240px;
    background-color: #fff;
    margin-right: getCssVar('spacing-3');
    overflow: hidden;
    padding: getCssVar('spacing-3');
    box-sizing: border-box;

    .Aside-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .Aside-scrollbar {
      flex: 1;
      min-height: 0;
      margin-top: getCssVar('spacing-3');

      &.empty {
        justify-content: center;
      }

      .type-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin-bottom: getCssVar('spacing-3');
        padding: 0 getCssVar('spacing-3');
        cursor: pointer;

        .name {
          flex: 1;
        }

        .edit {
          display: none;
        }

        &.active {
          background-color: getCssVar('color-2');
        }

        &:hover {
          background-color: getCssVar('color-1');

          .edit {
            display: block;
          }
        }
      }
    }
  }

  &-main {
    flex: 1;
    min-height: 0;
    min-width: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .TPNYPWH-main-content {
      flex: 1;
      min-height: 0;
      padding: getCssVar('spacing-3');
      padding-bottom: 0;

      ::v-deep .#{$namespace}-form-item--status-icon-inline {
        padding-right: unset;

        .#{$namespace}-form-item__content {
          justify-content: center;
        }
      }

      ::v-deep
        .#{$namespace}-table--edit
        .#{$namespace}-base-table__body
        .#{$namespace}-input__inner {
        border: unset;
      }

      ::v-deep .#{$namespace}-select .#{$namespace}-input__wrapper:hover {
        border-color: transparent;
      }
    }
  }

  &-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: getCssVar('spacing-3');
    background-color: getCssVar('color-1');
  }

  .mr-8 {
    margin-right: getCssVar('spacing-3');
  }
}
</style>
