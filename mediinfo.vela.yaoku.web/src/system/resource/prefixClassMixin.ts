//@ts-nocheck
/**
 * 1. prefixClass('body') => 'HISZY-body'
 * 2. prefixClass(['body',{active:true,focus:false}])=> 'HISZY-body HISZY-active'
 * 3. prefixClass(['body',{active: 1 < 0}])=> 'HISZY-body'
 * 4. prefixClass(['body body--wrapper',{active: 1 < 0}])=> 'HISZY-body HISZY-body--wrapper'
 * 5. prefixClass(['body',{'active focus': 1 > 0}])=> 'HISZY-body HISZY-active HISZY-focus'
 */
const prefixClassMixin = {
  methods: {
    prefixClass(c) {
      const cssPrefix = process.env.VUE_APP_NAMESPACE;
      if (typeof c === 'string') {
        return string2Class(c, cssPrefix).trim();
      }

      if (Array.isArray(c)) {
        let str = '';
        c.forEach((item) => {
          if (typeof item === 'string') {
            str += string2Class(item, cssPrefix);
          }

          // 简单判断一下是否是对象，默认开发者不传Date等类型的
          if (typeof item === 'object') {
            str = Object.keys(item).reduce((s, c) => {
              if (item[c]) {
                s += string2Class(c, cssPrefix);
              }
              return s;
            }, str);
          }
        });
        return str.trim();
      }
    },
  },
};

export default prefixClassMixin;

function string2Class(s, prefix) {
  const staticClassArr = s.trim().split(' ');
  return staticClassArr.reduce((str, c) => {
    return (str = str + ' ' + prefix + '-' + c);
  }, '');
}
