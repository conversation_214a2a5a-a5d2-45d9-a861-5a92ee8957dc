import Vue from 'vue';
/**
 * 初始化字典选项
 * @param{list} shuJuYZYList 字典值域列表
 * add by leiweikang
 */
Vue.prototype.formatOptions = function (shuJuYZYList) {
  return shuJuYZYList.map((item) => {
    return {
      label: item.biaoZhunMC,
      value: item.biaoZhunDM,
    };
  });
};

/**
 * 匹配下拉值
 * @param {*} arr options列表
 * @param {*} daiMa 字典代码
 * @param {*} fenGeFu 分隔符，默认|
 */
Vue.prototype.matchLabel = function (arr, daiMa, fenGeFu = '|') {
  let result = '';
  if (Array.isArray(daiMa)) {
    result = daiMa.reduce((result, current, currentIndex) => {
      const item = arr.find((el) => el.value == current);
      if (item) {
        return (
          result +
          item.label +
          (currentIndex == daiMa.length - 1 ? '' : fenGeFu)
        );
      }
      return result;
    }, '');
  } else {
    const item = arr.find((el) => el.value == daiMa);
    if (item) {
      result = item.label;
    }
  }
  return result;
};
