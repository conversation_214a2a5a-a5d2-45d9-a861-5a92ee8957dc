import Vue from 'vue';

import MediUI from '@mdfe/medi-ui';
import MediUIPro, { ConfigForPro } from '@mdfe/medi-ui-pro';

ConfigForPro.value = {
  tableProPageSizes: [10, 20, 30, 40, 50, 100, 500, 1000],
};

import '@mdfe/medi-ui/lib/style/index.scss';
import '@mdfe/medi-ui-pro/es/index.scss';
//TODO
// import '@mdfe/bmis-ui/src/index.scss'
import '@/assets/iconfont/iconfont.css';
import '@/assets/styles/index.scss';

import '@/system/resource/mixin';
import '@/system/resource/directive';
import '@/system/resource/filter';

//打印报表插件
import 'jquery-ui/themes/base/all.css';
import 'devextreme/dist/css/dx.light.css';
import '@devexpress/analytics-core/dist/css/dx-analytics.common.css';
import '@devexpress/analytics-core/dist/css/dx-analytics.light.css';
import 'devexpress-reporting/dist/css/dx-webdocumentviewer.css';

import VueClipboard from 'vue-clipboard2';
VueClipboard.config.autoSetContainer = true;
Vue.use(VueClipboard);

Vue.config.productionTip = false;

Vue.use(MediUI);
Vue.use(MediUIPro);
