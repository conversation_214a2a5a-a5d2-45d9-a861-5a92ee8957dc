// @ts-nocheck
import { defineBEM } from '@mdfe/medi-ui-3-compact';
import { type App } from 'vue';

import { ConfigForPro } from '@mdfe/medi-ui-pro';
declare namespace ConfigForPro {
  interface value {
    tableProPageSizes: [10, 20, 30, 40, 50, 100, 500, 1000];
  }
}

import '@mdfe/medi-ui-pro/theme-chalk/src/index.scss';
import '@mdfe/medi-ui/theme-chalk/src/index.scss';
//TODO
// import '@mdfe/bmis-ui/src/index.scss'
import '@/assets/iconfont/iconfont.css';
import '@/assets/styles/index.scss';

import '@/system/resource/directive';
import '@/system/resource/filter';
import prefixClassMixin from './resource/prefixClassMixin';

//打印报表插件
//TODO
// import 'jquery-ui/themes/base/all.css'
// import 'devextreme/dist/css/dx.light.css'
// import '@devexpress/analytics-core/dist/css/dx-analytics.common.css'
// import '@devexpress/analytics-core/dist/css/dx-analytics.light.css'
// import 'devexpress-reporting/dist/css/dx-webdocumentviewer.css'
// import VueClipboard from 'vue-clipboard3';
import vNumber from '@/components/v-number/v-number';
import TableEnter from '@/components/table-enter/table-enter';
import FormEnter from '@/components/form-enter/form-enter';

function install(app: App) {
  app.mixin(prefixClassMixin);
  app.mixin(defineBEM());
  // VueClipboard.config.autoSetContainer = true;
  // app.use(VueClipboard);
  app.use(vNumber);
  app.use(TableEnter);
  app.use(FormEnter);

  // app.config.productionTip = false

  // app.use(MediUI, { namespace: 'mediinfo-vela-yaoku-web' })
  // app.use(MediUIPro)
}

export default {
  install,
};
