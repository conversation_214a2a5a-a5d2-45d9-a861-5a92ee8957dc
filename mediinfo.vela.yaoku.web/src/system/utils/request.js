import { showMessageBox } from '@/components/showMessageBox';
import { getAuthClient } from '@mdfe/auth';
import lyra from '@mdfe/lyra';
import { getLogger } from '@mdfe/mediinfo.lyra.log';
import { inMicroApp } from '@mdfe/stark-app';
import axios from 'axios';
// 引入 sdk
import { devJiGouXX, devToken } from '@/service/sys/login';
import localCache from './local-cache';
const headers = {};
//微应用代码，需要和未同意配置的值完全一致
const logger = getLogger('mediinfo_vela_yaoku_web');
const http = axios.create({
  baseURL: '/',
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  timeout: 50000,
  withCredentials: true,
});
export function setHttpHeaders(data) {
  Object.assign(headers, data);
  Object.assign(http.defaults.headers, data);
}

// 在可以在这里对处理 config 对象
http.interceptors.request.use(async (config) => {
  let userInfoHeaders = null;
  let Authorization = null;
  //开发环境用本地写死的假token
  if (!inMicroApp) {
    Authorization = devToken;
    userInfoHeaders = devJiGouXX;
    // localCache.setUserInfo(devJiGouXX);
  } else {
    //生产环境拿lyra给的
    const auth = getAuthClient();
    Authorization = await auth.getAuthorizationHeaderValue();
    userInfoHeaders = localCache.getUserInfo([
      'WeiZhiID',
      'WeiZhiMC',
      'KeShiID',
      'KeShiMC',
      'BingQuID',
      'BingQuMC',
      'JiGouID',
      'JiGouMC',
      'ShuRuMLX',
      'CaiDanID',
    ]);
    // console.log(userInfoHeaders, 'userInfoHeaders');
  }
  const { jiGouLX, shangJiYLJGID, gongNengID } = lyra.getShareDataSync();
  // const jiGouLX = localStorage.getItem('jiGouLX') || '';
  // const ShangJiJGID = localStorage.getItem('shangJiYLJGID') || '';
  // const gongNengID = localStorage.getItem('gongNengID') || '';
  config.headers['Authorization'] = Authorization;
  //调用医保接口时 除了token 请求头内容全部去掉
  if (config.url !== 'http://medi/api/YiBao') {
    config.headers = {
      ...config.headers,
      ...userInfoHeaders,
      jiGouLX,
      ShangJiJGID: shangJiYLJGID,
      gongNengID,
    };
  }
  config.params = config.params || {};
  config.data = config.data || {};
  // 必须要返回配置对象
  return config;
});

// 在这里预处理后台响应的数据
http.interceptors.response.use(
  (response) => {
    // 这里的 res 是后台返回的数据
    const res = Object(response.data);
    //目前先加一层判断 因为mock数据没做code
    if (res.code !== undefined) {
      // 如果 code 为 0 表示正常数据，那就直接返回 data
      if (res.code == 0) return res.data;
      if (res.code == -30) {
        showMessageBox(res);
        return;
      }
      loggerErr(response);
      // 非 0 就直接到错误处理
      showMessageBox(res);
      return Promise.reject(
        new Error(
          res.message || `出现未知的系统错误，错误代码: ${res.code || -1}`,
        ),
      );
    } else {
      return res;
    }
  },
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          break;
        case 400:
          return Promise.reject(
            new Error(`系统内部错误：${error.response.data.title}`),
          );
      }
    }
    return Promise.reject(error);
  },
);

function loggerErr(response) {
  const result = response.data || {};
  const config = response.config;
  const xiangYingBT = JSON.stringify(response.headers);
  const qingQiuBT = JSON.stringify(config.headers);
  const params = JSON.stringify(config.params) + JSON.stringify(config.data);
  let content = '';
  content = content + `接口地址：${config.url}` + '\n';
  content = content + `接口类型：${config.method}\n`;
  content = content + `错误代码：${result.code}\n`;
  content = content + `请求参数：${params}\n`;
  content = content + `响应标头：${xiangYingBT}\n`;
  content = content + `请求标头：${qingQiuBT}\n`;
  content = content + `错误内容：${result.message}`;
  logger.error(content);
}

/**
 * post 方法
 * @param url 请求地址
 * @param data  请求参数
 * @returns {*} promise
 */
function post(url, data, config) {
  return http.post(url, data, config);
}

/**
 * get 方法
 * @param url 请求地址
 * @param params 请求参数
 * @returns {*} promise
 */
function get(url, params, config) {
  return http.get(url, {
    params,
    ...config,
  });
}

/**
 * put 请求
 * @param url
 * @param data
 * @returns {Promise<any>}
 */
function put(url, data = {}, config) {
  return http.put(url, data, config);
}

/**
 * patch请求
 * @param url 请求地址
 * @param data 请求参数
 * @returns {Promise<any>}
 */
function patch(url, data = {}, config) {
  return http.patch(url, data, config);
}

/**
 * delete请求
 * @param url 请求地址
 * @param params 请求参数
 * @returns {Promise<any>}
 */
function del(url, params, config) {
  return http.delete(url, {
    params,
    ...config,
  });
}

export { del, get, patch, post, put };

export default http;
