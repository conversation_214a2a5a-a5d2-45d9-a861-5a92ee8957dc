import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';

export function useParams(data, options) {
  if (!options) options = {};
  const oldData = cloneDeep(data);
  const defaultOption = {
    primaryKey: 'id',
    delObj: 'id',
  };

  const toFn = (value, fn) => {
    let f;
    if (typeof value === 'string') {
      f = fn;
    }
    if (typeof value === 'function') {
      f = value;
    }
    return f;
  };
  options = Object.assign(defaultOption, options);

  const { primaryKey, delObj } = options;

  const contrast = (targetData) => {
    const addArr = [];
    const putArr = [];
    let delArr = cloneDeep(oldData);
    const findFn = toFn(
      primaryKey,
      (v1, v2) => v1[primaryKey] === v2[primaryKey],
    );

    targetData.forEach((s) => {
      const index = delArr.findIndex((o) => findFn(o, s));
      if (index === -1) {
        addArr.push(s);
      } else {
        const d = delArr[index];
        // 深度对比，如果不相同，则它是更改的项
        if (!isEqual(s, d)) {
          putArr.push(s);
        }
        // 从删除数组中去除， 因为找到了id相同的，所以它肯定不是删除的项
        delArr.splice(index, 1);
      }
    });

    // 如果有删除项，根据规则格式化删除项结构，默认只输出删除项id
    if (delArr.length) {
      const returnObjFn = toFn(delObj, (v) => v[delObj]);

      delArr = delArr.map((d) => {
        return returnObjFn(d);
      });
    }

    return {
      addArr,
      putArr,
      delArr,
    };
  };

  return {
    contrast,
    oldData,
  };
}
