/**
 * 格式化价格
 * @param {String} num 传入字符串类型价格的 如'1.210100'
 * @return {String} 返回格式化之后的价格 如 1.2101
 *
 */
export default function formatJiaGe(num) {
  if (!num) return '0.00';
  num = +num + '';
  let numLength = num.split('.');
  if (numLength.length === 1) {
    return Number(num).toFixed(3);
  }
  numLength = numLength[1];
  if (numLength.length < 2) {
    return Number(num).toFixed(3);
  }
  return num;
}
