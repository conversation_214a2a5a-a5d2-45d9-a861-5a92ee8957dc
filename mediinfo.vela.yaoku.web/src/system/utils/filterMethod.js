/**
 * 用于根据
 * @param {String} query 过滤搜索的数据
 * @param {Array} originData  原始数据
 * @param {Array} fields 搜索条件
 * @returns
 */
const defaultFields = ['shuRuMa1', 'shuRuMa2', 'shuRuMa3'];
const cache = {};
function cacheFields(fields) {
  let id = 'default';
  if (Array.isArray(fields) && fields.length > 0) {
    id = JSON.stringify(fields);
  }
  if (cache[id]) {
    return cache[id];
  } else {
    const data = fields.reduce((prev, item) => {
      const index = prev.findIndex((i) => i === item);
      if (index === -1) {
        prev.push(item);
      }
      return prev;
    }, defaultFields);
    cache[id] = data;
    return cache[id];
  }
}

export function filterMethod(query, data, field) {
  const queryStr = query.toLowerCase();
  let fields = defaultFields;
  if (Array.isArray(field) && field.length > 0) {
    fields = cacheFields(field);
  }
  const predicate = (data) => {
    let fieldFlag = false;
    for (const item of fields) {
      const v = data[item];
      if (v) {
        fieldFlag = v.toLowerCase().indexOf(queryStr) > -1;
      }
      if (fieldFlag) break;
    }
    return fieldFlag;
  };
  if (!queryStr) {
    data.forEach((item) => {
      item.visible = true;
    });
  } else {
    data.forEach((item) => {
      item.visible = predicate(item);
    });
  }
}
