/**
 * 开立西成药时，根据返回的药品信息组装剂量单位下拉选择项
 * @param {object} yaoPinXX 通过getYiZhuYPXXByJGID接口获取的药品信息
 * @param {string} yaoPinXX.guiGeID 规格id， 判断大小规格
 * @param {string} yaoPinXX.daGuiGID 大规格id， 判断大小规格
 * @param {string} yaoPinXX.baoZhuangDW 包装单位
 * @param {string} yaoPinXX.zuiXiaoDW 最小单位
 * @param {string} yaoPinXX.tiJiDW 体积单位
 * @param {string} yaoPinXX.jiLiangDW 剂量单位
 * @returns {*[]} [{label: 'xxx', value: 'xxx'}]
 */
export function zuZhuangJLDWOptions(yaoPinXX) {
  // 判断单位是否存在，且不在已有数据中。
  const checkDanWeiExist = (item, options) => {
    if (
      yaoPinXX[item.key] &&
      options.findIndex((x) => x.label === yaoPinXX[item.key]) === -1
    ) {
      return {
        value: item.value,
        label: yaoPinXX[item.key],
      };
    }
  };
  const danWeiOptions = [];
  // 大规格：guiGeID等于daGuiGID
  // 小规格： guiGeID等于xiaoGuiGID
  if (yaoPinXX.guiGeID === yaoPinXX.daGuiGID && yaoPinXX.baoZhuangDW) {
    danWeiOptions.push({
      value: '4',
      label: yaoPinXX.baoZhuangDW,
    });
  }
  const danWeiList = [
    {
      key: 'zuiXiaoDW',
      value: '3',
    },
    {
      key: 'tiJiDW',
      value: '2',
    },
    {
      key: 'jiLiangDW',
      value: '1',
    },
  ];
  danWeiList.forEach((item) => {
    const option = checkDanWeiExist(item, danWeiOptions);
    if (option) danWeiOptions.push(option);
  });
  return danWeiOptions;
}
