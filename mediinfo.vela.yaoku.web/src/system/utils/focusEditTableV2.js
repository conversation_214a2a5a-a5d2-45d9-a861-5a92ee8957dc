/**
 * 聚焦可编辑表格的某一个单元格。（只能聚焦可编辑的单元格）
 *
 * 两种调用方式
 *
 * 1. 传入 key, rowIndex, columns,  （利用columns、key计算columnIndex， 保存时校验必填时使用）
 *
 * 2. 传入rowIndex、columnIndex （手动聚焦某个点时使用）
 * @param key 聚焦关键字 名称
 * @param rowIndex 行索引
 * @param columns 列数据
 * @param columnIndex 列索引
 */
export function focusEditTableDomV2({ key, rowIndex, columns, columnIndex }) {
  let isHiddenTimes = 0;
  if (!columnIndex) {
    columnIndex = columns.findIndex(
      (item) => item.prop === key || item.slot === key,
    );
    columns.forEach((el, index) => {
      if (el.hidden && index < columnIndex) {
        isHiddenTimes = isHiddenTimes + 1;
      }
    });
    columnIndex = Number(columnIndex) - isHiddenTimes;
  }
  const rowDom = document.querySelectorAll(
    '.jingping-table .mediinfo-vela-yaoku-web-base-table__body-wrapper tr',
  )[rowIndex];
  const cellDom = rowDom.querySelectorAll('td .cell')[columnIndex];

  const clickDom = cellDom.querySelectorAll(
    '.mediinfo-vela-yaoku-web-editable-table__cell',
  )[0];
  clickDom.firstChild.click();
  setTimeout(() => {
    const childNode = getChildNode(clickDom);
    childNode.focus();
    getFormObj(clickDom).then((res) => {
      //下一个如果为时间选择器 要额外处理
      if (res.type == 'date') {
        const nextVue = res.el.__vue__;
        if (!nextVue.value) {
          nextVue.picker.handleDatePick(new Date());
        } else {
          nextVue.picker.handleDatePick(new Date(nextVue.value));
        }
        nextVue.showPicker();
      }
      // 如果当前为select
      if (res?.type == 'select') {
        const nextVue = res.el.__vue__;
        nextVue.visible = true;
        //如果 默认没有选中 并且没有值  以及没有值得select框 默认选中第一个
        if ((nextVue.hoverIndex == -1 && !nextVue.value) || !nextVue.value) {
          nextVue.hoverIndex = 0;
          nextVue.hoverOption = nextVue.options[0];
          //有值的 找到值 添加hover
        } else {
          const value = nextVue.value;
          const options = nextVue.options;
          const index = options.findIndex((item) => {
            return item.value == value;
          });
          nextVue.hoverIndex = index;
          nextVue.hoverOption = nextVue.options[index];
        }
      }
    });
  }, 100);
}

function getChildNode(el) {
  //如果标签类型不是 input button textarea 找子级
  if (
    el.tagName != 'INPUT' &&
    el.tagName != 'BUTTON' &&
    el.tagName != 'TEXTAREA'
  ) {
    return el.querySelectorAll('input,textarea')[0];
  } else {
    return el;
  }
}

function getFormObj(el) {
  return new Promise((resolve) => {
    const typeArr = ['select', 'date', 'radio', 'checkbox', 'number', 'input'];
    const ele = el?.children[0]?.firstChild;
    const obj = {};
    if (ele) {
      obj.type = typeArr.find((item) => ele.className?.indexOf(item) > -1);
      obj.el = ele;
      obj.parentEl = el;
    }
    resolve(obj);
  });
}
