function getDecimalLength(num) {
  //获取小数位长度
  let length = 0;
  try {
    length = String(num).split('.')[1].length;
  } catch (e) {
    //TODO handle the exception
  }
  return length;
}
function getBeishu(num1, num2) {
  //获取放大倍数
  const num1DecimalLength = getDecimalLength(num1);
  const num2DecimalLength = getDecimalLength(num2);
  const longer = Math.max(num1DecimalLength, num2DecimalLength);
  return Math.pow(10, longer);
}

//加减乘除算法
function add(num1, num2) {
  const beishu = getBeishu(num1, num2);
  return (
    (parseFloat((num1 * beishu).toFixed(2)) +
      parseFloat((num2 * beishu).toFixed(2))) /
    beishu
  );
}

function subtract(num1, num2) {
  const beishu = getBeishu(num1, num2);
  return (num1 * beishu - num2 * beishu) / beishu;
}

function multiply(num1, num2) {
  const num1DecLen = getDecimalLength(num1);
  const num2DecLen = getDecimalLength(num2);
  const num1toStr = String(num1);
  const num2toStr = String(num2);
  return (
    (Number(num1toStr.replace('.', '')) * Number(num2toStr.replace('.', ''))) /
    Math.pow(10, num1DecLen + num2DecLen)
  );
}

function divide(num1, num2) {
  const num1DecLen = getDecimalLength(num1);
  const num2DecLen = getDecimalLength(num2);
  const num1toStr = String(num1);
  const num2toStr = String(num2);
  return (
    Number(num1toStr.replace('.', '')) /
    Number(num2toStr.replace('.', '')) /
    Math.pow(10, num1DecLen - num2DecLen)
  );
}
export { add, subtract, multiply, divide };
