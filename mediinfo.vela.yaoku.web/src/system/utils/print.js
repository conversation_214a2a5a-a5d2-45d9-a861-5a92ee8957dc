// TODO
// import { printReport } from '@mdfe/bmis-print/index.js.'
import { printReport } from '@/components/print/index.js';
import http from '@/system/utils/request';

import { getDaYinPZXXByDaYinLXID } from '@/service/mediter/daYin';

//TODO
const PrintReport = new printReport(http, getDaYinPZXXByDaYinLXID);
export const printByUrl = (...arg) => {
  PrintReport.printByUrl(...arg);
};
export const savePrintFile = (...arg) => {
  PrintReport.savePrintFile(...arg);
};

export const sleep = (delayMs = 1000) => {
  return new Promise(resolve => setTimeout(resolve, delayMs));
}