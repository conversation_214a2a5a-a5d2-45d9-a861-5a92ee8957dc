import { getCanShu, getCanShuList } from '@mdfe/lyra';
import { getGongNengID } from '@/system/utils/local-cache';
/**
 * 获取单个参数
 *
 * @param {Object} obj
 *
 * @return {Promise<*>}
 */
export function GetCanShuZhi(obj) {
  const gongNengID =
    obj.gongNengID && obj.gongNengID == '0' ? '0' : getGongNengID();
  return getCanShu({
    gongNengID,
    canShuMC: obj.canShuMC,
    canShuMRZ: obj.canShuMRZ,
  });
}
// 获取多个个参数值
//
export async function GetCanShuZhiList(canShuZhi, flag) {
  const gongNengID = flag ? '0' : getGongNengID();
  const canShuObj = {};
  const quanJuCSOBJ = {};
  canShuZhi.forEach((item) => {
    if (item.gongNengID === '0') {
      quanJuCSOBJ[item.canShuMC] = item.canShuMRZ;
    } else {
      canShuObj[item.canShuMC] = item.canShuMRZ;
    }
  });
  const params = {
    gongNengID,
    canShuList: canShuObj,
  };
  const quanJuCS = {
    gongNengID: '0',
    canShuList: quanJuCSOBJ,
  };

  const [canShuData, quanJuCSData] = await Promise.all([
    getCanShuList(params),
    Object.keys(quanJuCSOBJ).length > 0 && getCanShuList(quanJuCS),
  ]);

  return { ...(canShuData || {}), ...(quanJuCSData || {}) };
}
