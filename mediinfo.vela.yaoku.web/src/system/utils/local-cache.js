import { devToken } from '@/service/sys/login';
import { getAuthClient } from '@mdfe/auth';
import lyra from '@mdfe/lyra';
import Cookies from 'js-cookie';
const yongHuID = 'yongHuID';
const xingMing = 'yongHuMC';
const token = 'token';

/**
 * 获取token
 * @returns 获取token
 */
export async function getToken() {
  if (process.env.NODE_ENV === 'development') {
    return devToken;
  } else {
    const auth = getAuthClient();
    return await auth.getAuthorizationHeaderValue();
  }
}

/**
 * 设置TOKEN
 * @param {} 设置TOKEN
 */
export function setToken(re) {
  // let exp = dayjs(new Date().getTime() + re.expiresIn * 1000)
  // document.cookie =
  //   'expires=' + exp + '; path=/; expires=' + re.expiresIn * 1000
  // document.cookie='expires=' + exp + '; path=/'
  const exp = new Date().getTime() + re.expiresIn * 1000;
  Cookies.set('expires', exp, { path: '/', expires: exp });
  setValue(token, re.tokenType + ' ' + re.accessToken);
}

/**
 * 获取用户ID
 * @returns 用户ID
 */
export function getYongHuID() {
  const { yongHuID } = lyra.getShareDataSync();
  return yongHuID;
}

/**
 * 设置用户ID
 * @param {*} val
 */
export function setYongHuID(val) {
  setValue(yongHuID, val);
}

/**
 * 获取用户姓名
 * @returns
 */
export function getYongHuXM() {
  return decodeURIComponent(getValue(xingMing));
  // const { xingMing } = lyra.getShareDataSync();
  // return xingMing;
}
/**
 * 获取功能ID
 * @returns 功能ID
 */
export function getGongNengID() {
  const { gongNengID } = lyra.getShareDataSync();
  return gongNengID;
}

/**
 * 设置用户姓名
 * @param {*} val 用户姓名
 */
export function setYongHuXM(val) {
  setValue(xingMing, val);
}

/**
 * 传入一个对象 来设置
 *
 * 例如 {WeiZhiID: '1',WeiZhiMC: encodeURIComponent('位置测试'),KeShiID: '1',KeShiMC: '',BingQuID: '1',BingQuMC: '',JiGouID: '330421',JiGouMC: encodeURIComponent('嘉善县卫生健康局'),}
 *
 */
export function setUserInfo(obj) {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      setValue(key, obj[key]);
    }
  }
}

/**
 * 传入一个数组 表示你想拿哪些数据
 *
 * 例如 ['YongHuID','WeiZhiID']
 */
export function getUserInfo(arr) {
  const obj = {};
  arr.forEach((item) => {
    let value = '';
    if (item.includes('XM') || item.includes('MC')) {
      value = encodeURIComponent(getValue(item));
    } else value = getValue(item);
    if (value && value !== 'null') {
      obj[item] = value;
    }
  });
  return obj;
}
/**
 * 获取科室ID
 * @returns 科室ID
 */
export function getKeShiID() {
  const { KeShiID } = lyra.getShareDataSync();
  return KeShiID;
}

/**
 * 获取科室名称
 * @returns 科室名称
 */
export function getKeShiMC() {
  const { KeShiMC } = lyra.getShareDataSync();
  return KeShiMC;
}

/**
 * 获取病区ID
 * @returns 病区ID
 */
export function getBingQuId() {
  const { BingQuId } = lyra.getShareDataSync();
  return BingQuId;
}

/**
 * 获取病区名称
 * @returns 病区名称
 */
export function getBingQuMC() {
  const { BingQuMC } = lyra.getShareDataSync();
  return BingQuMC;
}

/**
 * 获取机构ID
 * @returns 机构ID
 */
export function getJiGouID() {
  const { JiGouID } = lyra.getShareDataSync();
  return JiGouID;
}

/**
 * 获取机构名称
 * @returns 机构名称
 */
export function getJiGouMC() {
  const { JiGouMC } = lyra.getShareDataSync();
  return JiGouMC;
}

/**
 * 获取位置ID
 * @returns 位置ID
 */
export function getWeiZhiID() {
  const { WeiZhiID } = lyra.getShareDataSync();
  return WeiZhiID;
}

/**
 * 获取位置名称
 * @returns 位置名称
 */
export function getWeiZhiMC() {
  const { WeiZhiMC } = lyra.getShareDataSync();
  return WeiZhiMC;
}

/**
 * 获取输入码类型
 * @returns 输入码类型
 */
export function getShuRuMLX() {
  //暂时写死1
  return '1';
}

/**
 * 设置输入码类型
 */
export function setShuRuMLX() {}

function setData(key, data) {
  const strJson = JSON.stringify(data);
  localStorage.setItem(key, strJson);
}

function setValue(key, val) {
  const obj = {};
  obj[key] = val;
  lyra.setShareDataSync(obj);
  // localStorage.setItem(key, val);
}

function getData(key) {
  const obj = lyra.getShareDataSync();
  const local = obj[key];
  if (local == '') {
    return {};
  }
  return JSON.parse(local);
}

function getValue(key) {
  const ShareData = lyra.getShareDataSync();
  let val = ShareData[key];
  if (val === 'null') return '';
  return val || '';
}

function clearData(key) {
  localStorage.removeItem(key);
}

function clear() {
  const shouYePX = getShouYePX();
  localStorage.clear();
  setShouYePX(shouYePX);
}

export function getShouYePX() {
  return getData('ShouYePX');
}

export function setShouYePX(list) {
  return setData('ShouYePX', list);
}

// export function getKuCunGLLX() {
//   return JSON.parse(getValue('kuCunGLLXs')).join('|');
// }
export function getKuCunGLLX() {
  const { kuCunGLLXs } = lyra.getShareDataSync();
  return typeof kuCunGLLXs == 'string'
    ? JSON.parse(kuCunGLLXs).join('|')
    : kuCunGLLXs.join('|');
}
export default {
  getToken,
  setToken,
  getYongHuID,
  setYongHuID,
  getYongHuXM,
  getJiGouID,
  getJiGouMC,
  clear,
  setUserInfo,
  getUserInfo,
  getKeShiID,
  getKeShiMC,
  getBingQuId,
  getBingQuMC,
  getWeiZhiID,
  getWeiZhiMC,
  getShuRuMLX,
  getKuCunGLLX,
  getGongNengID,
};
