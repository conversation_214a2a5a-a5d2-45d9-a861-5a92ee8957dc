export const formatPrice = (value, minFigures = 2, maxFigures = 6) => {
  let v = Number(value) || 0;
  if (v === 0) {
    return toFixedFun(v, minFigures);
  }
  const strList = String(v).split('.');
  if (strList.length == 1) {
    if (v < 0) {
      v = Math.abs(v);
      return -toFixedFun(v, minFigures);
    } else {
      return toFixedFun(v, minFigures);
    }
  }
  let keepFigures = minFigures;
  if (strList[1].length >= maxFigures) {
    keepFigures = maxFigures;
  } else if (strList[1].length < maxFigures && strList[1].length > minFigures) {
    keepFigures = strList[1].length;
  }
  if (v < 0) {
    v = Math.abs(v);
    return -toFixedFun(v, keepFigures);
  } else {
    return toFixedFun(v, keepFigures);
  }
};

export const formatMoney = (value, keepFigures = 2) => {
  let v = Number(value) || 0;
  if (v < 0) {
    v = Math.abs(v);
    return -toFixedFun(v, keepFigures);
  } else {
    return toFixedFun(v, keepFigures);
  }
};

//重写toFixed方法
function toFixedFun(data, len) {
  const number = Number(data);
  if (isNaN(number) || number >= Math.pow(10, 21)) {
    return number.toString();
  }
  if (typeof len === 'undefined' || len === 0) {
    return Math.round(number).toString();
  }
  let result = number.toString();
  const numberArr = result.split('.');

  if (numberArr.length < 2) {
    // 整数的情况
    return padNum(result);
  }
  const intNum = numberArr[0]; // 整数部分
  const deciNum = numberArr[1]; // 小数部分
  const lastNum = deciNum.substr(len, 1); // 最后一个数字

  if (deciNum.length === len) {
    // 需要截取的长度等于当前长度
    return result;
  }
  if (deciNum.length < len) {
    // 需要截取的长度大于当前长度 1.3.toFixed(2)
    return padNum(result);
  }
  // 需要截取的长度小于当前长度，需要判断最后一位数字
  result = `${intNum}.${deciNum.substr(0, len)}`;
  if (parseInt(lastNum, 10) >= 5) {
    // 最后一位数字大于5，要进位
    const times = Math.pow(10, len); // 需要放大的倍数
    let changedInt = Number(result.replace('.', '')); // 截取后转为整数
    changedInt++; // 整数进位
    changedInt /= times; // 整数转为小数，注：有可能还是整数
    result = padNum(`${changedInt}`);
  }
  return result;
  // 对数字末尾加0
  function padNum(num) {
    const dotPos = num.indexOf('.');
    if (dotPos === -1) {
      // 整数的情况
      num += '.';
      for (let i = 0; i < len; i++) {
        num += '0';
      }
      return num;
    } else {
      // 小数的情况
      const need = len - (num.length - dotPos - 1);
      for (let j = 0; j < need; j++) {
        num += '0';
      }
      return num;
    }
  }
}

// function filter(Vue) {
//   Vue.filter('formatPrice', formatPrice);
//   Vue.filter('formatMoney', formatMoney);
// }

// export default {
//   install: function install(Vue) {
//     Vue.use(filter);
//   },
// };
