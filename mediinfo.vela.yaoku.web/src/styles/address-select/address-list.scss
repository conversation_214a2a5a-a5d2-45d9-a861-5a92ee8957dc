@include md-b('address-list') {
  height: 100%;
  @include md-e('content') {
    height: 220px;
    padding: 8px;
    @include md-e('item') {
      display: inline-block;
      margin-right: 4px;
      margin-bottom: 4px;
      padding: 2px 8px;
      color: #333;
      white-space: nowrap;
      text-decoration: none;
      border-radius: 2px;
      cursor: pointer;
      &:hover {
        background-color: getCssVar('color-1');
      }
      &.is-active {
        color: getCssVar('color-6');
        background-color: getCssVar('color-2');
      }
    }
  }
  @include md-b('scrollbar') {
    height: 100%;
  }
  @include md-b('address-suggestion') {
    height: 100%;
    max-height: 400px;
    ul {
      max-height: 204px;
      margin: 0;
      padding: 0;
      color: #333;
      text-align: center;
      li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        padding: 0 8px;
        text-align: left;
        cursor: pointer;
        span {
          margin-right: 8px;
        }
        &:hover {
          background-color: getCssVar('color-1');
        }
        &.is-hover {
          background-color: getCssVar('color-1');
        }
        &.is-active {
          color: getCssVar('color-6');
          background-color: getCssVar('color-2');
        }
      }
    }
  }
}
