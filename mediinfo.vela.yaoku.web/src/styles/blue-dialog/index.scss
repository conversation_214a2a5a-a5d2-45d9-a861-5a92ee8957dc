@include md-b('blue-dialog') {
  display: flex;
  flex-direction: column;
  z-index: 2;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
  @include md-e('wrapper') {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    visibility: hidden;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    &.is-show {
      visibility: visible;
    }
    @include md-e('mask') {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
    }
  }
  @include md-e('header') {
    position: relative;
    height: 64px;
    flex-shrink: 0;
    background-image: linear-gradient(90deg, #1385f0, #3da0fc, #66b5ff);
    @include md-e(header__background) {
      position: absolute;
      height: 100%;
      width: 100%;
    }
    @include md-e(header__default) {
      text-align: center;
      line-height: 64px;
      font-weight: 500;
      font-size: 18px;
      letter-spacing: 2px;
      color: #fff;
    }
  }
  @include md-e('body') {
    padding: 8px;
    flex: 1;
    min-height: 0;
    min-width: 0;
  }
  @include md-e('footer') {
    padding: 0 16px 8px;
    text-align: right;
    font-size: 0;
    .buttonstyle {
      min-width: 64px;
      margin-left: 8px;
    }
  }
  // @include md-b('button') {
  //   min-width: 64px;
  //   margin-left: 8px;
  // }
}

.dialog-fade-enter-active {
  animation: dialog-fade-in 0.3s;
}

.dialog-fade-leave-active {
  animation: dialog-fade-out 0.3s;
}

.mask-fade-enter-active {
  animation: mask-fade-in 0.3s;
}

.mask-fade-leave-active {
  animation: mask-fade-out 0.3s;
}

@keyframes dialog-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes dialog-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}
@keyframes mask-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes mask-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
