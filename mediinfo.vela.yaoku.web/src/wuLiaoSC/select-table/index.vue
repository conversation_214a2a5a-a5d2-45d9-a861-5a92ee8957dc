<template>
  <div class="wrap">
    <Base></Base>
    <Multiple></Multiple>
    <Remote></Remote>
    <AllowCreate></AllowCreate>
  </div>
</template>
<script>
import Base from './component/base';
import Multiple from './component/multiple';
import Remote from './component/remote';
import AllowCreate from './component/allowCreate.vue';

export default {
  name: 'App',
  components: {
    Base,
    Multiple,
    Remote,
    AllowCreate,
  },
};
</script>
<style lang="scss">
.wrap {
  display: flex;
  justify-content: space-around;
}
</style>
