<template>
  <div>
    <md-select-table
      v-model="data"
      :data="filterTableData"
      :columns="columns"
      labelKey="pinCiMC"
      valueKey="pinCiID"
      :remote="false"
      placeholder="输入搜索"
      filterable
      allowCreate
      :filter-method="filterMethod"
      ref="selectTable"
      @change="selectChange"
    />
  </div>
</template>
<script>
export default {
  name: 'select-table-base',
  data() {
    return {
      data: {},
      filterTableData: [],
      tableData: [],
      columns: [
        {
          prop: 'pinCiMC',
          label: '频次名称',
          minWidth: '100px',
        },
        {
          prop: 'daYinMC',
          label: '描述',
          minWidth: '160px',
        },
      ],
    };
  },
  mounted() {
    //赋值默认值操作
    setTimeout(() => {
      this.data = {
        pinCiID: 'ONCE',
        pinCiMC: 'ONCE',
      };
    }, 444);
    setTimeout(() => {
      //请求回来下拉table的数据操作
      this.filterTableData = this.tableData = [
        {
          pinCiID: 'CXX',
          pinCiMC: 'CXX',
          daYinMC: '持续性',
          yiRiCS: 1,
          shouRiCSMRFSDM: '1',
          zhiXingSJ: '23:30',
          zhiXingRQ: null,
        },
        {
          pinCiID: 'ALWAYS',
          pinCiMC: 'ALWAYS',
          daYinMC: '持续',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'TIW',
          pinCiMC: 'TIW',
          daYinMC: '每周三次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '2',
          zhiXingSJ: '08:00',
          zhiXingRQ: '1010100',
        },
        {
          pinCiID: 'Q2H',
          pinCiMC: 'Q2H',
          daYinMC: '每两小时一次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '2',
          zhiXingSJ:
            '00:00|02:00|04:00|06:00|08:00|10:00|12:00|14:00|16:00|18:00|20:00|22:00|0:00|2:00|4:00|6:00|8:00',
          zhiXingRQ: null,
        },
        {
          pinCiID: 'Q6H',
          pinCiMC: 'Q6H',
          daYinMC: '每六小时一次',
          yiRiCS: 4,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|06:00|12:00|18:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'QQ23',
          pinCiMC: 'QQ23',
          daYinMC: 'QD11',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '1000000',
        },
        {
          pinCiID: 'Q3D',
          pinCiMC: 'Q3D',
          daYinMC: '每3天一次',
          yiRiCS: 2,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|00:30',
          zhiXingRQ: '000',
        },
        {
          pinCiID: 'Q15DS',
          pinCiMC: 'Q15DS',
          daYinMC: '半月2次',
          yiRiCS: 2,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '05:00|05:30',
          zhiXingRQ: '010000000000010',
        },
        {
          pinCiID: 'Q30DS',
          pinCiMC: 'Q30DS',
          daYinMC: 'Q30D',
          yiRiCS: 3,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|10:00|20:00',
          zhiXingRQ: '1111000',
        },
        {
          pinCiID: 'ZCQGY',
          pinCiMC: 'ZCQGY',
          daYinMC: '早餐前服药',
          yiRiCS: 4,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'Q3W',
          pinCiMC: 'Q3W',
          daYinMC: '每三周一次',
          yiRiCS: 4,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|04:00|08:00|12:00',
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'Q12H',
          pinCiMC: 'Q12H',
          daYinMC: '每十二小时一次',
          yiRiCS: 2,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00|20:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'Q15D',
          pinCiMC: 'Q15D',
          daYinMC: '每月二次',
          yiRiCS: 2,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '1100000',
        },
        {
          pinCiID: 'ZCHGY',
          pinCiMC: 'ZCHGY',
          daYinMC: '早餐后服药',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'Q2W',
          pinCiMC: 'Q2W',
          daYinMC: '每两周一次',
          yiRiCS: 0,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '8:00|14:00',
          zhiXingRQ: '00000000000000',
        },
        {
          pinCiID: 'TID',
          pinCiMC: 'TID',
          daYinMC: '1日3次',
          yiRiCS: 3,
          shouRiCSMRFSDM: '2',
          zhiXingSJ: '08:00|12:00|16:00',
          zhiXingRQ: '0',
        },
        {
          pinCiID: 'QM',
          pinCiMC: 'QM',
          daYinMC: '每月一次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '02:30',
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'QD',
          pinCiMC: 'QD',
          daYinMC: '1日1次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'ST',
          pinCiMC: 'ST',
          daYinMC: '立即',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'QOD',
          pinCiMC: 'QOD',
          daYinMC: '隔日一次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '14:00',
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'QN',
          pinCiMC: 'QN',
          daYinMC: '每晚一次',
          yiRiCS: 8,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '11:30|13:00|15:00|19:00|19:30|20:00|23:00|23:30',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'HS',
          pinCiMC: 'HS',
          daYinMC: '临睡给药',
          yiRiCS: null,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'BIW',
          pinCiMC: 'BIW',
          daYinMC: '每周两次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '0101000',
        },
        {
          pinCiID: 'QW',
          pinCiMC: 'QW',
          daYinMC: '每周一次',
          yiRiCS: 24,
          shouRiCSMRFSDM: '0',
          zhiXingSJ:
            '00:00|01:00|02:00|03:00|04:00|05:00|06:00|07:00|08:00|09:00|10:00|11:00|12:00|13:00|14:00|15:00|16:00|17:00|18:00|19:00|20:00|21:00|22:00|23:00',
          zhiXingRQ: '1000000',
        },
        {
          pinCiID: 'PRN',
          pinCiMC: 'PRN',
          daYinMC: '必要时',
          yiRiCS: 24,
          shouRiCSMRFSDM: '0',
          zhiXingSJ:
            '00:00|00:30|01:00|01:30|02:00|02:30|03:00|03:30|04:00|07:30|08:00|11:30|12:00|15:30|16:00|19:30|20:00|20:30|21:00|21:30|22:00|22:30|23:00|23:30',
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'BIW(1,4)',
          pinCiMC: 'BIW(1,4)',
          daYinMC: '每周两次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00',
          zhiXingRQ: '1001000',
        },
        {
          pinCiID: 'ST3',
          pinCiMC: 'ST3',
          daYinMC: '立即',
          yiRiCS: 3,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'SOS',
          pinCiMC: 'SOS',
          daYinMC: 'SOS',
          yiRiCS: null,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'QH',
          pinCiMC: 'QH',
          daYinMC: '每小时一次',
          yiRiCS: 24,
          shouRiCSMRFSDM: '2',
          zhiXingSJ:
            '00:00|01:00|02:00|03:00|04:00|05:00|06:00|07:00|08:00|09:00|10:00|11:00|12:00|13:00|14:00|15:00|16:00|17:00|18:00|19:00|20:00|21:00|22:00|23:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'QD11',
          pinCiMC: 'QD11',
          daYinMC: '1日1次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '11:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: '一周五次',
          pinCiMC: '一周五次',
          daYinMC: '一周五',
          yiRiCS: 0,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '8:00|12:00|16:00',
          zhiXingRQ: '1111100',
        },
        {
          pinCiID: 'Q3H',
          pinCiMC: 'Q3H',
          daYinMC: '每三小时一次',
          yiRiCS: 8,
          shouRiCSMRFSDM: '1',
          zhiXingSJ: '00:00|03:00|06:00|09:00|12:00|15:00|18:00|21:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'Q4H',
          pinCiMC: 'Q4H',
          daYinMC: '每四小时一次',
          yiRiCS: 6,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|04:00|08:00|12:00|16:00|20:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'Q8H',
          pinCiMC: 'Q8H',
          daYinMC: '每八小时一次',
          yiRiCS: 3,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '00:00|08:00|16:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'QID',
          pinCiMC: 'QID',
          daYinMC: '每日四次',
          yiRiCS: 4,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00|12:00|16:00|20:00',
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'ONCE',
          pinCiMC: 'ONCE',
          daYinMC: '一次',
          yiRiCS: 1,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '1111111',
        },
        {
          pinCiID: 'Q30D',
          pinCiMC: 'Q30D',
          daYinMC: '每月一次',
          yiRiCS: null,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: null,
          zhiXingRQ: '0000000',
        },
        {
          pinCiID: 'BID',
          pinCiMC: 'BID',
          daYinMC: '1日2次',
          yiRiCS: 2,
          shouRiCSMRFSDM: '0',
          zhiXingSJ: '08:00|16:00',
          zhiXingRQ: '1111111',
        },
      ];
    }, 500);
  },
  methods: {
    filterMethod(query) {
      this.filterTableData = this.tableData.filter((item) => {
        return item.pinCiMC
          .toLocaleLowerCase()
          .includes(query.toLocaleLowerCase());
      });
    },
    selectChange(v) {
      // console.log(v);
    },
  },
};
</script>
