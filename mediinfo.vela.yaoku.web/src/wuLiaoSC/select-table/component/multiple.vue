<template>
  <div>
    <md-select-table
      v-model="data"
      :data="tableData"
      :columns="columns"
      labelKey="yiShengXM"
      valueKey="yiShengID"
      :remote="false"
      placeholder="输入搜索"
      multiple
      @change="selectChange"
    />
  </div>
</template>
<script>
export default {
  name: 'select-table-base',
  data() {
    return {
      data: [],
      tableData: [],
      columns: [
        {
          prop: 'keShiMC',
          label: '科室',
          width: '100px',
        },
        {
          prop: 'yiShengXM',
          label: '姓名',
          width: '160px',
        },
        {
          prop: 'zhiCheng',
          label: '职称',
        },
      ],
    };
  },
  mounted() {
    //赋值默认值操作
    this.data = [
      {
        keShiMC: '科室43',
        yiShengXM: 'd64',
        yiShengID: 0.34253175473668596,
        zhiCheng: '主任医师54',
      },
    ];
    //请求回来下拉table的数据操作
    this.tableData = [
      {
        keShiMC: '科室43',
        yiShengXM: 'd64',
        yiShengID: 0.34253175473668596,
        zhiCheng: '主任医师54',
      },
      {
        keShiMC: '科室44',
        yiShengXM: 'd642qac',
        yiShengID: 0.3425317546,
        zhiCheng: '主任医师54',
      },
      {
        keShiMC: '科室45',
        yiShengXM: 'd64sd',
        yiShengID: 0.342175473668596,
        zhiCheng: '主任医师54',
      },
      {
        keShiMC: '科室46',
        yiShengXM: 'd64ads',
        yiShengID: 0.342533668596,
        zhiCheng: '主任医师54',
      },
      {
        keShiMC: '科室47',
        yiShengXM: 'd64s',
        yiShengID: 0.34253473668596,
        zhiCheng: '主任医师54',
      },
      {
        keShiMC: '科室48',
        yiShengXM: 'd64的',
        yiShengID: 0.3423668596,
        zhiCheng: '主任医师54',
      },
    ];
  },
  methods: {
    //模拟接口请求
    requestData(params) {
      //模拟滚动到底
      if (params.page === 5) return [];
      return new Promise((resolve) => {
        setTimeout(() => {
          const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
          const data = arr.map(() => {
            return {
              keShiMC: '科室' + Math.ceil(Math.random() * 100),
              yiShengXM: params.inputValue + Math.ceil(Math.random() * 100),
              yiShengID: Math.random(),
              zhiCheng: '主任医师' + Math.ceil(Math.random() * 100),
            };
          });
          resolve(data);
        }, 500);
      });
    },
    async fetchData({ page, pageSize, inputValue }) {
      return await this.requestData({ page, pageSize, inputValue });
    },
    selectChange(data) {
      // console.log(data);
    },
  },
};
</script>
