<template>
  <div>
    <md-select-table
      v-model="data"
      :fetchData="fetchData"
      :columns="columns"
      labelKey="yiShengXM"
      valueKey="yiShengID"
      :immediateLoad="false"
      placeholder="输入搜索"
      @change="selectChange"
      filterable
    />
  </div>
</template>
<script>
export default {
  name: 'select-table-base',
  data() {
    return {
      data: {},
      columns: [
        {
          prop: 'keShiMC',
          label: '科室',
          width: '100px',
        },
        {
          prop: 'yiShengXM',
          label: '姓名',
          width: '160px',
        },
        {
          prop: 'zhiCheng',
          label: '职称',
        },
      ],
    };
  },
  mounted() {
    //赋值默认值操作
    this.data = {
      keShiMC: '科室43',
      yiShengXM: 'd64',
      yiShengID: 0.34253175473668596,
      zhiCheng: '主任医师54',
    };
  },
  methods: {
    //模拟接口请求
    requestData(params) {
      //模拟滚动到底
      if (params.page === 5) return [];
      return new Promise((resolve) => {
        setTimeout(() => {
          const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
          const data = arr.map(() => {
            return {
              keShiMC: '科室' + Math.ceil(Math.random() * 100),
              yiShengXM: params.inputValue + Math.ceil(Math.random() * 100),
              yiShengID: Math.random(),
              zhiCheng: '主任医师' + Math.ceil(Math.random() * 100),
            };
          });
          resolve(data);
        }, 500);
      });
    },
    async fetchData({ page, pageSize, inputValue }) {
      if (!inputValue) return [];
      return await this.requestData({ page, pageSize, inputValue });
    },
    selectChange(data) {
      // console.log(data);
    },
  },
};
</script>
