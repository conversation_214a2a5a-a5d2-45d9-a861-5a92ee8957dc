// @ts-nocheck
import MediUI from '@mdfe/medi-ui';
import MediUIPro from '@mdfe/medi-ui-pro';
import MdSelectTable from '@mdfe/material.select-table';
import MdSelectScroll from '@mdfe/material.select-scroll';
import { type AppParcelProps, inMicroApp } from '@mdfe/stark-app';
import { createApp, ComponentPublicInstance } from 'vue';
import { createRouter, createWebHashHistory } from 'vue-router';
import '@mdfe/medi-ui/theme-chalk/src/reset.scss';
import '@mdfe/medi-ui/theme-chalk/src/index.scss';
import '@mdfe/medi-ui-pro/theme-chalk/src/index.scss';
import '@mdfe/medi-layout/dist/style/index.scss';
import '@mdfe/view-manager/style/index.scss';
import '@mdfe/material.select-scroll/es/index.scss';
import lyra from '@mdfe/lyra';
import { createAccess } from '@mdfe/vue-access';
import './assets/iconfont/iconfont.css';
import App from './App.vue';
import routes from './routes';
import { createStore } from './store';
import boot from './system/bootstrap';
import VXETable from 'vxe-table';
import '@mdfe/vxe-table/index.scss';
import '@/index.scss';
import { logger } from '@/service/log';
// 缓存方法
import { devToken, devJiGouXX } from '@/service/sys/login';

function appCreator({ anNiuLB }) {
  const app = createApp(App);
  const store = createStore();

  const access = createAccess({
    /**
     * 权限判断函数
     *
     * @param {String} id 资源ID
     * @param {Object} args 额外参数，占位参数，后续开放
     * @param {Object} ctx 上下文
     * @param {Object} ctx.route 当前的路由对象
     *
     * @returns {Boolean} 如果返回 true 就显示，否者隐藏
     */
    can: function (id, _, { route }) {
      // 按钮列表
      const list = anNiuLB[route.path] || [];
      // 页面肯定是有权限路由才会激活
      // 而按钮是在页面中的，所以通常不会出现这个问题
      if (list.length === 0) return false;

      // 如果找到
      const result = list.find((b) => b['caiDanURL'] === id);

      return !!result;
    },
  });

  const router = createRouter({
    history: createWebHashHistory(),
    routes,
  });
  if (process.env.NODE_ENV !== 'development') {
    app.config.errorHandler = function (
      err: unknown,
      instance: ComponentPublicInstance | null,
      // `info` 是一个 Vue 特定的错误信息
      // 例如：错误是在哪个生命周期的钩子上抛出的
      info: string,
    ) {
      // 你需要通过特殊 reportVueError 方法上报此错误
      logger.reportVueError(err, instance, info);
    };
    app.config.globalProperties.$logger = logger;
  }
  app.use(boot);
  app.use(MediUI, { namespace: 'mediinfo-vela-yaoku-web' });
  app.use(MediUIPro);
  app.use(MdSelectTable);
  app.use(MdSelectScroll);
  app.use(router);
  app.use(store);
  app.use(VXETable);
  app.use(access);
  app.mixin({
    mounted() {
      this.$el.__md__instance = this;
    },
    beforeUnmount() {
      this.$el.__md__instance = null;
    },
  });

  return { app, store, router };
}

async function main() {
  // await loginWithRedirect();
  //开发环境用本地写死的假token
  if (process.env.NODE_ENV === 'development') {
    lyra.httpClient.setToken(devToken);
    lyra.setShareDataSync({
      JiGouID: devJiGouXX.JiGouID,
      JiGouMC: devJiGouXX.JiGouMC,
      KeShiID: devJiGouXX.KeShiID,
      KeShiMC: devJiGouXX.KeShiMC,
      WeiZhiID: devJiGouXX.WeiZhiID,
      WeiZhiMC: devJiGouXX.WeiZhiMC,
      gongNengID: devJiGouXX.gongNengID,
      caiDanID: devJiGouXX.caiDanID,
      yongHuDLM: devJiGouXX.yongHuDLM,
      yongHuID: devJiGouXX.yongHuID,
      yongHuMC: devJiGouXX.yongHuMC,
      kuCunGLLXs: devJiGouXX.kuCunGLLXs,
      WeiZhiLXDM: devJiGouXX.WeiZhiLXDM,
      gongNengMC: devJiGouXX.gongNengMC,
      yingYongID: devJiGouXX.yingYongID,
      yingYongMC: devJiGouXX.yingYongMC,
      WeiZhiLXDM: devJiGouXX.WeiZhiLXDM,
    });
  }
  await lyra.installCanShuService({
    zuZhiJGID: '331003020',
    yingYongID: '998478025217499136',
    weiZhiID: '0000010543',
  });

  const { app } = appCreator({
    anNiuLB: {},
  });

  app.mount('#app');
}

if (!inMicroApp) main();

export async function bootstrap() { }

export async function mount({
  container,
  jiGou,
  weiZhi,
  user,
  caiDanList,
  yeMianCDList,
  yingYongID,
  onGlobalStateChange,
}: AppParcelProps) {
  const quanXianList = [...caiDanList, ...yeMianCDList];

  const { app, store, router } = appCreator({
    anNiuLB: menuAnNiuLB(quanXianList),
  });

  // 初始化共享状态
  store.commit('user/load', user);

  lyra.installCanShuService({
    zuZhiJGID: jiGou.jiGouID,
    weiZhiID: weiZhi.weiZhiID,
    yingYongID: yingYongID,
  });

  // TODO 需要停止使用
  lyra.setShareDataSync({
    yingYongID: yingYongID,
    yongHuMC: user.name,
    yongHuID: user.yongHuID,
    yongHuDLM: user.unique_name,
    JiGouMC: jiGou.jiGouMC,
    JiGouID: jiGou.jiGouID,
    jiGouLX: jiGou.jiGouLX,
    weiZhiID: weiZhi.weiZhiID,
    WeiZhiMC: weiZhi.weiZhiMC,
    WeiZhiID: weiZhi.weiZhiID,
    KeShiID: weiZhi.keShiID,
    KeShiMC: weiZhi.keShiMC,
    BingQuMC: weiZhi.bingQuMC,
    BingQuID: weiZhi.bingQuID,
    kuCunGLLXs: JSON.stringify(weiZhi.kuCunGLLXs),
    WeiZhiLXMC: weiZhi.weiZhiLXMC,
  });

  onGlobalStateChange(({ themeData }) => {
    const { filterCode } = themeData;

    let ShuRuMLX = '1';
    if (filterCode == 'pinyin') {
      ShuRuMLX = '1';
    } else if (filterCode == 'wubi') {
      ShuRuMLX = '2';
    } else if (filterCode == 'customize') {
      ShuRuMLX = '';
    }

    store.commit('app/setConfigSetting', { ...themeData, ShuRuMLX });

    lyra.setShareDataSync({
      ShuRuMLX: ShuRuMLX,
    });
  }, true);

  //获取当前页面功能id
  router.beforeResolve((to, from, next) => {
    const path = to.path?.toLowerCase();
    const currentYeMian = quanXianList.find(
      (item) => item.caiDanURL?.toLowerCase() === path,
    );
    if (currentYeMian) {
      // currentJiGou.caiDanID = currentYeMian.caiDanID
      // currentJiGou.gongNengID = currentYeMian.gongNengID
      //存储共享数据
      lyra.setShareDataSync({
        caiDanID: currentYeMian.caiDanID,
        gongNengID: currentYeMian.gongNengID,
      });
    }
    // setHttpHeaders(currentJiGou)
    return next();
  });

  app.mount(container.querySelector('#app')!);
}

export async function unmount({ container }: AppParcelProps) {
  const elem = container.querySelector('#app');
  const app = elem ? elem.__vue_app__ : null;

  if (app) app.unmount();
}

if (typeof window !== 'undefined' && window.__POWERED_BY_QIANKUN__) {
  // 修改变量，以修复构建资源加载问题
  // https://qiankun.umijs.org/zh/faq#%E4%B8%BA%E4%BB%80%E4%B9%88%E5%BE%AE%E5%BA%94%E7%94%A8%E5%8A%A0%E8%BD%BD%E7%9A%84%E8%B5%84%E6%BA%90%E4%BC%9A-404
  __webpack_public_path__ = new URL(
    process.env.BASE_URL,
    window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__,
  ).toString();
}

// 获取按钮列表数据
function menuAnNiuLB(caiDanList) {
  const menu = {};

  caiDanList.forEach((item) => {
    if (item.fuJiGNID && item.leiXingDM === '5') {
      const find = caiDanList.find((c) => item.fuJiGNID === c.gongNengID);
      if (find) {
        const anNiuLB = menu[find.caiDanURL] || [];
        anNiuLB.push(item);
        menu[find.caiDanURL] = anNiuLB;
      }
    }
  });

  return menu;
};
