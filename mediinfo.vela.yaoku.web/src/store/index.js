import { createStore as storeCreator } from 'vuex';

import app from './modules/app';
import user from './modules/user';
import jiezhen from './modules/jiezhen';
import guahaosf from './modules/guahaosf';
import yaokupc from './modules/yaokupc';
import yaoku from './modules/yaofang';

export function createStore() {
  return storeCreator({
    modules: { app, user, jiezhen, guahaosf, yaokupc, yaoku },
  });
}

// export function createStore() {
//   const plugins = []
//
//   if (process.env.NODE_ENV === 'development') {
//     plugins.push(createLogger())
//   }
//
//   const store = new Store({
//     strict: process.env.NODE_ENV === 'development',
//     modules: { app, user, jiezhen, guahaosf, yaokupc, yaoku },
//     plugins: plugins
//   })
//
//   // 仅允许调试使用
//   if (process.env.NODE_ENV === 'development') {
//     window.store = store
//   }
//   return store
// }
