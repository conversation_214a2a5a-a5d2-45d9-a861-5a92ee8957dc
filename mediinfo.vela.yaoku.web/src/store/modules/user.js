import { Base64 } from 'js-base64';
import Cookies from 'js-cookie';
const data = initialUser();

export default {
  namespaced: true,
  state() {
    return {
      data,
      weiZhiID: '',
    };
  },
  mutations: {
    load(state, payload) {
      state.data = payload;
    },
    setOldWeiZhiID(state, weiZhiID) {
      state.weiZhiID = weiZhiID;
    },
  },
};

function initialUser() {
  try {
    const token = Cookies.get('Authorization');
    if (token) {
      const value = token.split('.')[1];
      return JSON.parse(Base64.decode(value));
    }
  } catch (error) {
    console.error('加载用户信息失败', error.message);
  }

  return { name: '匿名用户' };
}
