import axios from 'axios';

export default {
  namespaced: true,
  state() {
    return {
      jieZhenKS: null, //选择的科室
      huanZheCurrent: null,
      zhenJianLayout: 0, //诊间布局模式 默认0 0-竖版 1-横板
    };
  },
  mutations: {
    SET_KESHI(state, payload) {
      state.jieZhenKS = payload;
    },
    SET_HUANZHE(state, payload) {
      state.huanZheCurrent = payload;
    },
    CLEAR_JIEZHENHZ(state) {
      state.huanZheCurrent = null;
    },
    SET_ZHENJIANLAYOUT(state, payload) {
      state.zhenJianLayout = payload;
    },
  },
  actions: {
    setKeShi({ commit }, value) {
      commit('SET_KESHI', value);
    },
    async setHuanZheCurrent({ commit }, value) {
      const res = await axios.get('/api/yishengzj/huanzheinfo');
      commit('SET_HUANZHE', res.data);
    },
    clearJieZhenHZ({ commit }) {
      commit('CLEAR_JIEZHENHZ');
    },
    setZhenJianLayout({ commit }, value) {
      commit('SET_ZHENJIANLAYOUT', value);
    },
  },
  getters: {
    getKeShiID(state) {
      return state.jieZhenKS;
    },
    getHuanZheCurrent(state) {
      return state.huanZheCurrent;
    },
    getZhenJianLayout(state) {
      return state.zhenJianLayout;
    },
  },
};
