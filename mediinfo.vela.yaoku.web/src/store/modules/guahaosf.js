import axios from 'axios';

export default {
  namespaced: true,
  state() {
    return {
      huanZheCurrent: null,
    };
  },
  mutations: {
    SET_HUANZHE(state, payload) {
      state.huanZheCurrent = payload;
    },
    CLEAR_GUAHAOSFHZ(state) {
      state.huanZheCurrent = null;
    },
  },
  actions: {
    async setHuanZheCurrent({ commit }, value) {
      const res = await axios.get('/api/guahaosfhzxx/huanzheinfo');
      commit('SET_HUANZHE', res.data);
    },
    clearJieZhenHZ({ commit }) {
      commit('CLEAR_GUAHAOSFHZ');
    },
  },
  getters: {
    getHuanZheCurrent(state) {
      return state.huanZheCurrent;
    },
  },
};
