body,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

input,
select,
textarea {
  font-size: 100%;
}

a img {
  border: 0;
}

a {
  text-decoration: none;
}

ol,
ul {
  list-style: none;
}

img {
  max-width: 100%;
}

body {
  font-size: 12px;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100% !important;
  /** cSpell: disable-next-line */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-size: 12px;
  text-decoration: none;
  color: #2e82ff;
}

html,
body,
#app {
  position: relative;
  // z-index: 1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
