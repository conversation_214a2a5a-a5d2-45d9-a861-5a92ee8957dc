@import '@/assets/styles/variables.scss';
//浮动
.#{$md-prefix}-f-left {
  float: left;
}
.#{$md-prefix}-f-right {
  float: right;
}

// flex布局
.#{$md-prefix}-flex--center {
  display: flex;
  align-items: center;
}

.#{$md-prefix}-flex--between {
  display: flex;
  justify-content: space-between;
}

//颜色
.#{$md-prefix}-color-red {
  color: $red;
}

.#{$md-prefix}-color-blue {
  color: $primary;
}

.#{$md-prefix}-color-gray-6 {
  color: $gray-666;
}

//字号
.#{$md-prefix}-t-16 {
  font-size: 16px;
}

.#{$md-prefix}-t-18 {
  font-size: 18px;
}

// 文本对齐方式
.#{$md-prefix}-text-left {
  text-align: left;
}

.#{$md-prefix}-text-right {
  text-align: right;
}

//margin
.#{$md-prefix}-margin-left-5 {
  margin-left: 5px;
}

.#{$md-prefix}-margin-left-20 {
  margin-left: 20px;
}

.#{$md-prefix}-margin-top-5 {
  margin-top: 5px;
}

.#{$md-prefix}-margin-right-5 {
  margin-right: 5px;
}

.#{$md-prefix}-margin-right-20 {
  margin-right: 20px;
}

//padding
.#{$md-prefix}-padding-10 {
  padding: 10px;
}

.#{$md-prefix}-padding-top-5 {
  padding-top: 5px;
}

.#{$md-prefix}-padding-right-5 {
  padding-right: 5px;
}

.#{$md-prefix}-padding-right-10 {
  padding-right: 10px;
}

.#{$md-prefix}-padding-left-5 {
  padding-left: 5px;
}

.#{$md-prefix}-padding-bottom-5 {
  padding-bottom: 5px;
}

//鼠标样式
.#{$md-prefix}-cursor-pointer {
  cursor: pointer;
}

/*清除 #{$md-prefix}-scrollbar 横向滚动条*/

.#{$md-prefix}-clear-scroll-x {
  .#{$md-prefix}-scrollbar__wrap {
    overflow-x: hidden;
  }
  .#{$md-prefix}-select-dropdown
    .#{$md-prefix}-scrollbar
    .#{$md-prefix}-select-dropdown__wrap {
    margin-bottom: 0 !important;
  }
}

//覆盖#{$md-prefix}-button样式
.#{$md-prefix}-button {
  &.#{$md-prefix}-button--medium {
    padding: 7px 12px;
  }
  &.#{$md-prefix}-button--default {
    &.is-plain {
      border-color: $light-blue-2;
      color: $primary;
    }
  }
}

.#{$md-prefix}-select {
  width: 100%;
}

.#{$md-prefix}-select-dropdown.is-multiple
  .#{$md-prefix}-select-dropdown__item.selected {
  color: $primary;
}

.#{$md-prefix}-select-dropdown__item.height-auto {
  height: auto;
  &.hover {
    background-color: transparent;
  }
}

// Pagination 分页
.#{$md-prefix}-pagination {
  padding: 10px 0;
  text-align: right;
}

.#{$md-prefix}-border-box {
  border: 1px solid $gray-e5;
}

.#{$md-prefix}-border-table {
  .#{$md-prefix}-pagination {
    padding: 10px;
  }
}

.#{$md-prefix}-clearfix {
  clear: both;
}

//搜索栏
.filter-box {
  padding: 5px;
  position: relative;
  &.filter-box-layout {
    &.left {
      .filter-form {
        padding-right: 55px;
      }
    }
    .filter-btn {
      height: 30px;
      line-height: 30px;
      position: absolute;
      right: 5px;
      top: 5px;
      .#{$md-prefix}-button {
        padding: 7px 0;
      }
      .#{$md-prefix}-button {
        margin-right: 8px;
      }
    }
  }
  .filter-item {
    margin-right: 15px;
    width: 200px;
    &.filter-item-md {
      width: 135px;
    }
    &.filter-item-lg {
      width: 240px;
    }
  }
  .filter-item-letter {
    margin-right: 8px;
  }
  &.padding-0 {
    padding: 0;
    .filter-btn {
      top: 0;
    }
  }
}

.dialog-slg {
  .#{$md-prefix}-dialog {
    width: 1000px;
  }
}

.dialog-lg {
  .#{$md-prefix}-dialog {
    width: 1000px;
  }
}

//中框:margin-top:15vh;
.dialog-md {
  .#{$md-prefix}-dialog {
    width: 800px;
  }
}

//小框:margin-top:20vh;
.dialog-sm {
  .#{$md-prefix}-dialog {
    width: 560px;
    .#{$md-prefix}-dialog__body {
      height: 227px; //dialog整体高度是300
    }
  }
}

.#{$md-prefix}-tabs__header {
  margin-bottom: 5px;
}

.#{$md-prefix}-tabs__extra {
  height: 39px;
  line-height: 39px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    height: 1px;
    background-color: #e4e7ed;
    z-index: 1;
  }
}

//展开/收起的查询面板
.search-conditions {
  height: 68px;
  position: relative;
  .operate-wrap {
    width: 146px;
    box-sizing: border-box;
    padding-left: 10px;
    .#{$md-prefix}-button {
      padding: 7px 12px;
    }
  }
  .form-wrap {
    margin-right: 146px;
  }
  .form-more {
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    top: 68px;
    padding-top: 8px;
    background-color: $white;
    border: 0px solid rgba(0, 0, 0, 0.15);
    border-top: 0;
    border-bottom-width: 1px;
    z-index: 2;
  }
  .search-conditions-result {
    .label {
      width: 80px;
      line-height: 30px;
      text-align: right;
    }
    .content {
      margin-left: 80px;
      .condition-item {
        display: inline-block;
        line-height: 28px;
        padding: 0 8px;
        border-radius: 4px;
        border: 1px solid $gray-ddd;
        margin-left: 5px;
        margin-bottom: 5px;
        .#{$md-prefix}-icon {
          margin-left: 8px;
          font-size: 12px;
          color: $gray-999;
          cursor: pointer;
        }
      }
    }
  }
  &.not-more {
    .operate-wrap {
      width: 64px;
    }
    .form-wrap {
      margin-right: 64px;
    }
  }
}

//右滑面板样式覆盖
.#{$md-prefix}-drawer__header {
  color: #333333;
  height: 37px;
  line-height: 37px;
  background: #e6f3ff;
  padding: 0 0 0 20px;
  font-size: 16px;
  & > span {
    outline: none;
  }
}

.popover-full {
  padding: 0;
}
//禁止选中
.user-select-none {
  user-select: none;
}

.pointer {
  cursor: pointer;
}

/* width */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */

::-webkit-scrollbar-track {
  background: rgb(255, 255, 255);
  border-radius: 4px;
  width: 8px;
}

/* Handle */

::-webkit-scrollbar-thumb {
  background: rgba(102, 102, 102, 0.3);
  border-radius: 4px;
}

/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 102, 102, 0.3);
}

.mediinfo-vela-yaoku-web-scrollbar__bar.is-vertical {
  width: 12px !important;
}
.mediinfo-vela-yaoku-web-scrollbar__bar.is-vertical:hover {
  width: 12px !important;
}

.mediinfo-vela-yaoku-web-scrollbar__bar.is-horizontal {
  height: 12px;
}
// dialog里面的tabs去掉padding
.#{$md-prefix}-dialog-tabs-full
  .#{$md-prefix}-dialog__basic
  .#{$md-prefix}-dialog__body
  .#{$md-prefix}-scrollbar__view {
  padding: 0;
}

// 覆盖tab组件extra高度
.#{$md-prefix}-tabs__extra {
  height: 32px;
  line-height: 32px;
}

.collapse-transition {
  transition:
    height 0.3s ease-in-out,
    padding-top 0.3s ease-in-out,
    padding-bottom 0.3s ease-in-out;
}

.#{$md-prefix}-overflow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.#{$md-prefix}-container,
.#{$md-prefix}-tabs-view__content-tabs {
  min-height: 0;
}

.#{$md-prefix}-nodata {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  img {
    width: 120px;
  }
  span {
    padding: 32px 0;
    font-size: 18px;
    color: #999999;
  }
}

.#{$md-prefix}-table {
  .#{$md-prefix}-base-table__header-wrapper {
    th.gutter {
      display: table-cell !important;
    }
  }
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  min-width: 0;
}
.#{$md-prefix}-data-table__pagination {
  flex-shrink: 0;
}

.#{$md-prefix}-drawer__body {
  min-height: 0;
}

.#{$md-prefix}-editable-table {
  .biz-input {
    border: none !important;
    border-radius: 0 !important;
  }
}
