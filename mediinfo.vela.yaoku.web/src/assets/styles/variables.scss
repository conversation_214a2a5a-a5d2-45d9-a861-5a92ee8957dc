// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// sidebar
$menuText: #fff;
$menuActiveText: #fff;
$menuActiveBg: #29b6f6;
$subMenuActiveText: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #3c516f;
$menuHover: #263445;

$subMenuBg: #2c384a;
$subMenuHover: #001528;

$sideBarWidth: 180px;

// table
$tableHeaderBg: #f0f5fb;

// dialog
$dialogHeaderBg: #f0f5fb;

// button
$btnBg: #1e88e5;
$btnHoverBg: #29b6f6;

$btnDefaultHover: #29b6f6;
$btnDefaultHoverBg: #fff;

// mediinfo-color
$primary: #1e88e5;
$deep-blue-1: #027abb;
$deep-blue-2: #001e4a;
$light-blue: #29b6f6;
$light-blue-1: #94d4ff;
$light-blue-2: #c3e5fd;
$light-blue-3: #eaf3ff;
$light-blue-4: #f0f5fb;
$light-blue-5: #c8e5fd;
$light-blue-6: #e3f5fd;
$light-blue-7: #c3e2fe;
$light-blue-8: #e3f3ff;
$light-blue-9: #6abaff;
$light-blue-10: #e2efff;
$light-blue-11: #c3e5fd;
$red: #f12933;
$red-1: #ff6b58;
$red-2: #e02020;
$light-red-1: #f56c6c;
$light-red-2: #fbe9e9;
$light-red-3: #ffd2cc;
$rose: #e956b6;
$green: #52c41a;
$light-green: #5ad8a6;
$light-green-2: #e0f4f2;
$orange: #ff9900;
$light-orange: #f6bd16;
$white: #fff;
$gray-form-border: #ddd;
$gray-f8: #f8f8f8;
$gray-e5: #e5e5e5;
$gray-ddd: #ddd;
$gray-999: #999;
$gray-666: #666;
$gray-555: #555;
$gray-333: #333;
$gray-eee: #eee;
$gray-dot: #e3ecf4;
$gray-ccc: #ccc;
$gray-f5: #f5f5f5;
$gray-f0: #f0f0f0;
$gray-f7: #f7f7f7;
$light-purple-1: #ddd7ff;
$purple: #6236ff;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
