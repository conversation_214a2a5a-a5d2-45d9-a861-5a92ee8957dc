@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}

//用户信息标题横条风格
@mixin titleStripesStyle(
  $height: 40px,
  $backgroundColor: #f8f8f8,
  $padding: 0 15px
) {
  display: flex;
  width: 100%;
  height: $height;
  box-sizing: border-box;
  align-items: center;
  padding: $padding;
  font-size: 14px;
  background-color: $backgroundColor;
  .#{$md-prefix}-title-icon {
    font-size: 16px;
  }
  .#{$md-prefix}-username {
    margin-left: 6px;
    font-size: 16px;
    font-weight: bold;
  }
  .#{$md-prefix}-userage {
    margin: 0 10px 0 4px;
    color: #333;
  }
  .#{$md-prefix}-descriptions {
    display: flex;
    .#{$md-prefix}-description-item {
      margin-right: 15px;
      flex-basis: auto !important;
      .#{$md-prefix}-description-item__content {
        color: #222222;
      }
    }
  }
  @content;
}

//title装饰物  nicknack 拆开俩
@mixin titleTrim(
  $type: line,
  $left: 0,
  $position: rightTop,
  $width: 3px,
  $height: 16px,
  $backgroundColor: #1e88e5
) {
  &::before {
    content: '';
    position: absolute;
    display: inline-block;
    //左侧线型
    @if $type == line {
      background-color: $backgroundColor;
      left: $left;
      top: 50%;
      transform: translateY(-50%);
      width: $width;
      height: $height;
    }

    //右侧三角形
    @if $type == triangle {
      width: 0;
      height: 0;
      border-width: $width / 2;
      border-color: $backgroundColor;
      border-style: solid;
      @if $position==rightTop {
        right: 0;
        top: 0;
        border-left-color: transparent;
        border-bottom-color: transparent;
      } @else if $position==leftTop {
        left: 0;
        top: 0;
        border-right-color: transparent;
        border-bottom-color: transparent;
      } @else if $position==leftBottom {
        left: 0;
        bottom: 0;
        border-right-color: transparent;
        border-top-color: transparent;
      } @else if $position==rightBottom {
        right: 0;
        bottom: 0;
        border-left-color: transparent;
        border-top-color: transparent;
      }
    }
  }
  @content;
}

@mixin price($unit, $fontSize: 14px, $color: '#222222') {
  font-size: $fontSize;
  color: $color;
  @if $unit {
    position: relative;
    &::before {
      content: '¥';
      display: inline;
      position: absolute;
      left: 0;
    }
  }
}

@mixin slideInDown($timer: 1s, $delay: 0s, $timing: linear) {
  transition: $timer $delay $timing;
}

@mixin borderShadow($color: $primary) {
  border: 1px solid $color;
  box-shadow: 0 0 4px 0 $color;
}
@mixin xuXianKuang($width: 1px, $color: #dddddd, $radius: 4px) {
  border: $width dashed $color;
  border-radius: $radius;
}

@mixin toggleDownUpTransition($duration: 0.3s, $timing-function: ease) {
  &-enter,
  &-leave-to {
    opacity: 0.5;
    transform: translateY(-10%);
  }
  &-enter-to,
  &-leave {
    opacity: 1;
    transform: translateY(0);
  }
  &-enter-active,
  &-leave-active {
    transition: all $duration $timing-function;
  }
}
