<script lang="ts" setup>
import { MdConfigProvider } from '@mdfe/medi-ui';
import { inMicroApp, SyncViewsToAppHost } from '@mdfe/stark-app';
import { MdViewManager } from '@mdfe/view-manager';

import Logo from './assets/images/logo.png';
import Layout from './components/Layout.vue';
import sideData from './configs/sidebar';

const prefixCls = process.env.VUE_APP_NAMESPACE;
</script>

<template>
  <MdConfigProvider
    :namespace="prefixCls"
    :table-page-sizes="[10, 20, 30, 40, 50, 100, 500, 1000]"
  >
    <MdViewManager
      v-if="inMicroApp"
      hidden-tabs
      :sync-title-to-document="false"
    >
      <SyncViewsToAppHost></SyncViewsToAppHost>
    </MdViewManager>
    <Layout v-else :logo="Logo" :sidebar="sideData">
      <MdViewManager :sync-title-to-document="false"></MdViewManager>
    </Layout>
  </MdConfigProvider>
</template>

<style lang="scss">
#app {
  /* height: 100vh;
  width: 100vw; */
  overflow: hidden;
}
</style>
