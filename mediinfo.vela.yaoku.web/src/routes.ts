import { withDynamicView } from '@mdfe/view-manager';
import { type RouteRecordRaw } from 'vue-router';

import WelcomeView from './views/portals/WelcomeView.vue';

export default [
  {
    path: '/',
    component: WelcomeView,
    meta: {
      title: '欢迎页',
    },
  },
  {
    name: 'GongYingSGL',
    path: '/GongYingSGL',
    component: () => import('@/views/haoCaiGL/gongYingSGL/index.vue'),
    meta: {
      title: '供应商管理',
    },
  },
  {
    name: 'YaoKuFSZ',
    path: '/YaoKuFSZ',
    component: () => import('@/views/yaoKuGL/yaoKuFSZ/index.vue'),
    meta: {
      cache: true,
      title: '药库房设置',
    },
  },
  // 采购计划
  {
    name: 'CaiGouJH',
    path: '/CaiGouJH',
    component: () => import('@/views/yaoKuGL/caiGouJH/CaiGouJH.vue'),
    meta: {
      cache: true,
      title: '采购计划',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YaoPinRK',
    path: '/YaoPinRK',
    component: () => import('@/views/yaoKuGL/yaoPinRK/YaoPinRK.vue'),
    meta: {
      cache: true,
      title: '药品入库',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengRKD',
    path: '/XinZengRKD',
    component: () => import('@/views/yaoKuGL/yaoPinRK/XinZengRKD.vue'),
    meta: {
      cache: true,
      title: '新增入库',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XiuGaiRKD',
    path: '/XiuGaiRKD',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yaoPinRK/XinZengRKD.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '修改入库单',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengCGA',
    path: '/XinZengCGA',
    component: () => import('@/views/yaoKuGL/caiGouJH/XinZengCGA.vue'),
    meta: {
      cache: true,
      title: '新增采购计划单',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengCG',
    path: '/XinZengCG',
    component: () => import('@/views/yaoKuGL/caiGouJH/XinZengCG.vue'),
    meta: {
      cache: true,
      title: '新增采购计划单',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XiuGai',
    path: '/XiuGai',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/caiGouJH/XinZengCGA.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '新增采购',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'ShouLiQL',
    path: '/ShouLiQL',
    component: () => import('@/views/yaoKuGL/shouLiQL/ShouLiQL.vue'),
    meta: {
      cache: true,
      title: '受理请领',
      weiZhiLX: '3',
    },
  },
  {
    name: 'ShouLiQL_V2',
    path: '/ShouLiQL_V2',
    component: () => import('@/views/yaoKuGL/shouLiQL_V2/ShouLiQL.vue'),
    meta: {
      cache: true,
      title: '受理请领_V2',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XiuGaiCKD',
    path: '/XiuGaiCKD',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yaoPinCK/XinZengCKD.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '修改出库单',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'YaoPinCK',
    path: '/YaoPinCK',
    component: () => import('@/views/yaoKuGL/yaoPinCK/YaoPinCK.vue'),
    meta: {
      cache: true,
      title: '药品出库',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengCKD',
    path: '/XinZengCKD',
    component: () => import('@/views/yaoKuGL/yaoPinCK/XinZengCKD.vue'),
    meta: {
      cache: true,
      title: '新增出库',
      weiZhiLX: '3',
    },
  },
  {
    name: 'ShuangKongYPXL',
    path: '/ShuangKongYPXL',
    component: () =>
      import('@/views/yaoKuGL/shuangKongYPXL/ShuangKongYPXL.vue'),
    meta: {
      cache: true,
      title: '双控药品限量',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YaoKuFPGL',
    path: '/YaoKuFPGL',
    component: () => import('@/views/yaoKuGL/faPiaoGL/FaPiaoGL.vue'),
    meta: {
      cache: true,
      title: '发票管理',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YingFuKuan',
    path: '/YingFuKuan',
    component: () => import('@/views/yaoKuGL/yingFuKuan/YingFuKuan.vue'),
    meta: {
      cache: true,
      title: '应付款',
      weiZhiLX: '3',
    },
  },
  {
    name: 'TPNZBWH',
    path: '/TPNZBWH',
    component: () => import('@/views/tpnZBWH/TPNZBWH.vue'),
    meta: {
      title: 'tpn指标维护',
    },
  },
  {
    name: 'CanKaoXWH',
    path: '/CanKaoXWH',
    component: () => import('@/views/canKaoXWH/CanKaoXWH.vue'),
    meta: {
      title: '参考项维护',
    },
  },
  {
    name: 'TPNYPWH',
    path: '/TPNYPWH',
    component: () => import('@/views/tpnYPWH/TPNYPWH.vue'),
    meta: {
      title: 'TPN药品维护',
    },
  },
  {
    name: 'CaiJiGL',
    path: '/CaiJiGL',
    component: () => import('@/views/yaoKuGL/caiJiGL/CaiJiGL.vue'),
    meta: {
      title: '采集管理',
    },
  },
  {
    name: 'XinZengCJ',
    path: '/XinZengCJ',
    component: () => import('@/views/yaoKuGL/caiJiGL/XinZengCJ.vue'),
    meta: {
      title: '新增采集项目',
    },
  },
  {
    name: 'caiJi',
    path: '/caiJi',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/caiJiGL/XinZengCJ.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '新增采集项目',
    },
  },
  {
    name: 'YangHuJL',
    path: '/YangHuJL',
    component: () => import('@/views/yaoKuGL/yangHuJL/YangHuJL.vue'),
    meta: {
      cache: true,
      title: '养护记录',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YangHuDXZ',
    path: '/YangHuDXZ',
    component: () =>
      import('@/views/yaoKuGL/yangHuJL/components/YangHuDXZ.vue'),
    meta: {
      cache: true,
      title: '养护记录单',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YangHuDBJ',
    path: '/YangHuDBJ',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yangHuJL/components/YangHuDXZ.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '养护记录单编辑',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'XiaoQiGL',
    path: '/XiaoQiGL',
    component: () => import('@/views/yaoKuGL/xiaoQiGL/XiaoQiGL.vue'),
    meta: {
      cache: true,
      title: '效期管理',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengRKD',
    path: '/XinZengRKD',
    component: () => import('@/views/yaoKuGL/yaoPinRK/XinZengRKD.vue'),
    meta: {
      cache: true,
      title: '新增入库',
    },
  },
  {
    name: 'YaoKuTJ',
    path: '/YaoKuTJ',
    component: () => import('@/views/yaoKuGL/yaoKuTJ/YaoKuTJ.vue'),
    meta: {
      cache: true,
      title: '药库调价',
      weiZhiLX: '3',
    },
  },
  {
    name: 'JiYaGL',
    path: '/JiYaGL',
    component: () => import('@/views/yaoKuGL/jiYaGL/JiYaGL.vue'),
    meta: {
      cache: true,
      title: '积压管理',
    },
  },
  {
    name: 'BianJiTJD',
    path: '/BianJiTJD',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yaoKuTJ/XinZengTJD.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '调价单',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengTJD',
    path: '/XinZengTJD',
    component: () => import('@/views/yaoKuGL/yaoKuTJ/XinZengTJD.vue'),
    meta: {
      cache: true,
      title: '新增调价单',
      weiZhiLX: '3',
    },
  },
  {
    name: 'QiMoJZ',
    path: '/QiMoJZ',
    component: () => import('@/views/yaoKuGL/qiMoJZ/QiMoJZ.vue'),
    meta: {
      cache: true,
      title: '期末结转',
    },
  },
  //新增
  {
    name: 'YaoKuBS',
    path: '/YaoKuBS',
    component: () => import('@/views//yaoKuGL/yaoKuBS/YaoKuBS.vue'),
    meta: {
      cache: true,
      title: '药库报损',
      weiZhiLX: '3',
    },
  },

  {
    name: 'YaoPinBaiFWZ',
    path: '/YaoPinBaiFWZ',
    component: () => import('@/views/yaoKuGL/yaoPinBaiFWZ/YaoPinBaiFWZ.vue'),
    meta: {
      cache: true,
      title: '药品摆放位置',
    },
  },
  {
    name: 'KuCunSXXSZ',
    path: '/KuCunSXXSZ',
    component: () => import('@/views/yaoKuGL/kuCunSXXSZ/KuCunSXXSZ.vue'),
    meta: {
      cache: true,
      title: '库存上下限设置',
      //weiZhiLX: '3'    //上下限改成与药品字典一致 任何地方都能进来
    },
  },
  {
    name: 'KuCunZK',
    path: '/KuCunZK',
    component: () => import('@/views/yaoKuGL/kuCunZK/index.vue'),
    meta: {
      cache: true,
      title: '库存暂控',
    },
  },
  {
    name: 'ZhuanKeZYYPGL',
    path: '/ZhuanKeZYYPGL',
    component: () => import('@/views/yaoKuGL/zhuanKeZYYPGL/index.vue'),
    meta: {
      cache: true,
      title: '专科专用药品管理',
    },
  },
  {
    name: 'XinZengBSD',
    path: '/XinZengBSD',
    component: () => import('@/views/yaoKuGL/yaoKuBS/XinZengBSD.vue'),
    meta: {
      cache: true,
      title: '新增报损单',
      weiZhiLX: '3',
    },
  },
  {
    name: 'BianJiBSD',
    path: '/BianJiBSD/:id',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yaoKuBS/XinZengBSD.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '报损单',
      viewManager: false,
      weiZhiLX: '3',
    },
  },
  {
    name: 'YaoKuPC',
    path: '/YaoKuPC',
    component: () => import('@/views/yaoKuGL/yaoKuPC/YaoKuPC.vue'),
    meta: {
      cache: true,
      title: '药库盘存',
      weiZhiLX: '3',
    },
  },
  {
    name: 'XinZengPC',
    path: '/XinZengPC',
    component: () => import('@/views/yaoKuGL/yaoKuPC/components/XinZengPC.vue'),
    meta: {
      cache: true,
      title: '新增盘存',
      weiZhiLX: '3',
    },
  },
  {
    name: 'BianJiPCD',
    path: '/BianJiPCD',
    component: withDynamicView(
      () => import('@/views/yaoKuGL/yaoKuPC/components/XinZengPC.vue'),
      {
        name: 'id',
      },
    ),
    meta: {
      title: '编辑盘存单',
      viewManager: false,
    },
  },
  {
    name: 'DanJuCH',
    path: '/DanJuCH',
    component: () => import('@/views/yaoKuGL/danJuCH/DanJuCH.vue'),
    meta: {
      cache: true,
      title: '单据冲红',
      weiZhiLX: '3',
    },
  },
  {
    name: 'KuCunGL',
    path: '/KuCunGL',
    component: () => import('@/views/yaoKuGL/kuCunGL/KuCunGL.vue'),
    meta: {
      cache: true,
      title: '库存管理',
      weiZhiLX: '3',
    },
  },

  {
    name: 'ChuRuKFS',
    path: '/ChuRuKFS',
    component: () => import('@/views/yaoKuGL/chuRuKFS/ChuRuKFS.vue'),
    meta: {
      cache: true,
      title: '出入库方式',
    },
  },
  {
    name: 'CaoYaoXDF',
    path: '/CaoYaoXDF',
    component: () => import('@/views/caoYaoXDF/index.vue'),
    meta: {
      title: '草药协定方',
    },
  },
  {
    name: 'GongHuoDW',
    path: '/GongHuoDW',
    component: () => import('@/views/yaoKuGL/gongHuoDW/GongHuoDW.vue'),
    meta: {
      cache: true,
      title: '供货单位',
      weiZhiLX: '3',
    },
  },
  {
    name: 'GongHuoDWDZ',
    path: '/GongHuoDWDZ',
    component: () => import('@/views/yaoKuGL/gongHuoDWDZ/index.vue'),
    meta: {
      cache: true,
      title: '供货单位对照',
      weiZhiLX: '3',
    },
  },
  // 采购计划审核
  {
    name: 'CaiGouJHSH',
    path: '/CaiGouJHSH',
    component: () => import('@/views/yaoKuGL/caiGouJHSH/index.vue'),
    meta: {
      cache: true,
      title: '采购计划审核',
      weiZhiLX: '3',
    },
  },
  {
    name: 'LinShiYYGL',
    path: '/LinShiYYGL',
    component: () => import('@/views/linShiYYGL/index.vue'),
    meta: {
      cache: true,
      title: '临时专药管理',
    },
  },
  {
    name: 'PeiWuJJ',
    path: '/PeiWuJJ',
    component: () => import('@/views/peiWuJJ/index.vue'),
    meta: {
      title: '配伍禁忌',
    },
  },
  {
    name: 'GuoTanYPSQKS',
    path: '/GuoTanYPSQKS',
    component: () => import('@/views/guoTanYPSQKS/index.vue'),
    meta: {
      title: '国谈药品申请科室',
    },
  },
  {
    name: 'XiaoHaoZLXZ',
    path: '/XiaoHaoZLXZ',
    component: () => import('@/views/xiaoHaoZLXZ/index.vue'),
    meta: {
      cache: true,
      title: '消耗总量限制',
      weiZhiLX: '3',
    },
  },
  {
    name: 'YaoPinBFWZ',
    path: '/YaoPinBFWZ',
    component: () => import('@/views/yaoKuGL/yaoPinBFWZ/index.vue'),
    meta: {
      cache: true,
      title: '药品摆放位置',
      weiZhiLX: '3',
    },
  },
  {
    name: 'KuaiDiYPCX',
    path: '/KuaiDiYPCX',
    component: () => import('@/views/yaoKuGL/kuaiDiYPCX/index.vue'),
    meta: {
      cache: true,
      title: '快递药品查询',
      weiZhiLX: '3',
    },
  },
  {
    name: 'KeShiQLYPKZ',
    path: '/KeShiQLYPKZ',
    component: () => import('@/views/keShiQLYPKZ/KeShiQLYPKZ.vue'),
    meta: {
      cache: true,
      title: '科室请领药品控制',
    },
  },
  {
    name: 'CaiJiYPGZ',
    path: '/CaiJiYPGZ',
    component: () => import('@/views/yaoKuGL/caiJiYPGZ/index.vue'),
    meta: {
      cache: true,
      title: '集采药品跟踪',
    },
  },
  {
    name: 'BiaoZhunGLSZ',
    path: '/BiaoZhunGLSZ',
    component: () => import('@/views/yaoKuGL/biaoZhunGLSZ/index.vue'),
    meta: {
      cache: true,
      title: '标准规格设置',
    },
  },
] as RouteRecordRaw[];
