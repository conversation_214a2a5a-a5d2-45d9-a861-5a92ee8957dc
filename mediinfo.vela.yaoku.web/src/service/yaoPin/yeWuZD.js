import { get, post, del, put } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/` + actionUrl;
}

/**
 * 获取数据源类别列表
 * @param {*} param
 */
export const getShuJuYLBList = (param, config) => {
  const url = getBaseurl('GetShuJuYLBList');
  return get(url, param, config);
};

/**
 * 获取数据源类别总数
 * @param {*} param
 */
export const getShuJuYLBCount = (param, config) => {
  const url = getBaseurl('GetShuJuYLBCount');
  return get(url, param, config);
};

/**
 * 获取数据源值域列表
 * @param {*} param
 */
export const getShuJuYZYList = (param, config) => {
  const url = getBaseurl('GetShuJuYZYList');
  return get(url, param, config);
};

/**
 * 获取数据源值域总数
 * @param {*} param
 */
export const getShuJuYZYCount = (param, config) => {
  const url = getBaseurl('GetShuJuYZYCount');
  return get(url, param, config);
};

/**
 *新增数据源类别
 * @param {*} obj
 */
export function addShuJuYLB(obj) {
  return post(getBaseurl('AddShuJuYLB'), obj);
}

/**
 *更新数据源类别
 * @param {*} obj
 */
export function updateShuJuYLB(obj) {
  return post(getBaseurl('UpdateShuJuYLB'), obj);
}

/**
 * 删除数据源类别
 * @param {string}主键
 * @returns
 */
export function zuoFeiShuJuYLB(id) {
  return del(getBaseurl(`ZuoFeiShuJuYLB?id=${id}`));
}

/**
 *新增数据源值域
 * @param {*} obj
 */
export function addShuJuYZY(obj) {
  return post(getBaseurl('AddShuJuYZY'), obj);
}

/**
 *更新数据源值域
 * @param {*} obj
 */
export function updateShuJuYZY(obj) {
  return post(getBaseurl('UpdateShuJuYZY'), obj);
}

/**
 * 删除数据源值域
 * @param {string}主键
 * @returns
 */
export function zuoFeiShuJuYZY(id) {
  return del(getBaseurl(`ZuoFeiShuJuYZY?id=${id}`));
}

/**
 *根据数据源类别IDList查询值域
 * @param {Array<string>} ids [""]
 * @returns [{shuJuYLBID:"",zhiYuList:[{shuJuYLBID:"",biaoZhunDM:"",biaoZhunMC:""}]}]
 */
export function getYaoPinShuJuYZYList(ids) {
  return post(getBaseurl('GetShuJuYZYListByLBList'), ids);
}
