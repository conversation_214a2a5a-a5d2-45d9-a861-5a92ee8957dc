import { get, post, del, put } from '@/system/utils/request';
import GL<PERSON><PERSON>L from '../configSettings';

/**
 * 获取列表
 * @param {*} param
 */
export const getLinShiYYList = (param, config) => {
  const url = getBaseurl('GetLinShiYYList');
  return post(url, param);
};

/**
 * 获取列表Count
 * @param {*} param
 */
export const getLinShiYYCount = (param, config) => {
  const url = getBaseurl('GetLinShiYYCount');
  return post(url, param);
};
export const addLinShiYY = (param, config) => {
  const url = getBaseurl('AddLinShiYY');
  return post(url, param);
};
export const updateLinShiYY = (param, config) => {
  const url = getBaseurl('UpdateLinShiYY');
  return post(url, param);
};
export const getLinShiYYXQ = (param, config) => {
  const url = getBaseurl('GetLinShiYYXQ');
  return get(url, param, config);
};
export const zuoFeiLinShiYY = (param) => {
  const url = getBaseurl('zuoFeiLinShiYY');
  return del(url, param);
};
function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/LinShiYYGL/` + actionUrl;
}
