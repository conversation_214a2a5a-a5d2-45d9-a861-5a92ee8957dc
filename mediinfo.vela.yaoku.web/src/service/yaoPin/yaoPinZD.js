import { del, get, post } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1/YaoPinZD/` + actionUrl;
}

function getYaoPin(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/` + actionUrl;
}
function getGongYongurl(url) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/ZuZhiXX/${url}`;
}

/**
 * 获取药品分类
 * @param {*} param
 */
export const GetYaoPinBFWZSelect = (param, config) => {
  const url = getBaseurl('GetYaoPinBFWZSelect');
  return get(url, param, config);
};
export const getBaiFangWZTreeForPC = (param, config) => {
  const url = getBaseurl('getBaiFangWZTreeForPC')
  return get(url, param, config)
}
/**
 *  获取配伍禁忌药列表
 */
export function GetPeiWuJJYPList(obj, config) {
  const uri = getBaseurl('GetPeiWuJJYPList');
  return get(uri, obj, config);
}

/**
 *  获取国谈药品申请科室列表
 */
export function GetGuoTanYPSQKSList(obj, config) {
  const uri = getBaseurl('GetGuoTanYPSQKSList');
  return get(uri, obj, config);
}

/**
 *  获取国谈药品申请科室总数
 */
export function GetGuoTanYPSQKSCount(obj, config) {
  const uri = getBaseurl('GetGuoTanYPSQKSCount');
  return get(uri, obj, config);
}
/**
 *  保存
 */
export function SaveGuoTanYPSQKS(data, config) {
  const uri = getBaseurl('SaveGuoTanYPSQKS');
  return post(uri, data, config);
}

export function MoJiKSList(obj, config) {
  const uri = getGongYongurl('KeShi/MoJiKSList');
  return get(uri, obj, config);
}
// ----------
export function GetBaiFangWZTree(obj, config) {
  const uri = getYaoPin('GetBaiFangWZTree');
  return get(uri, obj, config);
}

export function GetBaiFangWZSelect(obj, config) {
  const uri = getYaoPin('GetBaiFangWZSelect');
  return get(uri, obj, config);
}

export function GetYiYuanMLByJiaGeID(obj, config) {
  const uri = getBaseurl('GetYiYuanMLByJiaGeID');
  return get(uri, obj, config);
}

export function YiYuanMLUpdate(data, config) {
  const uri = getYaoPin('YiYuanMLUpdate');
  return post(uri, data, config);
}

export function GetBaiFangWZById(obj, config) {
  const uri = getYaoPin('GetBaiFangWZById');
  return get(uri, obj, config);
}
export function DeleteBaiFangWZ(obj, config) {
  const uri = getYaoPin('DeleteBaiFangWZ');
  return del(uri, obj, config);
}
export function GetYaoPinBaiFangWZList(obj, config) {
  const uri = getYaoPin('GetYaoPinBaiFangWZList');
  return get(uri, obj, config);
}
export function GetYaoPinBaiFangWZCount(obj, config) {
  const uri = getYaoPin('GetYaoPinBaiFangWZCount');
  return get(uri, obj, config);
}

export function SaveBaiFangWZ(data, config) {
  const uri = getYaoPin('SaveBaiFangWZ');
  return post(uri, data, config);
}
export function GetYaoPinCDJGListForAddYP(obj, config) {
  const uri = getYaoPin('GetYaoPinCDJGListForAddYP');
  return get(uri, obj, config);
}
export function GetYaoPinCDJGCountForAddYP(obj, config) {
  const uri = getYaoPin('GetYaoPinCDJGCountForAddYP');
  return get(uri, obj, config);
}
export function DeleteYaoPinBaiFangWZByIDs(obj, config) {
  const uri = getYaoPin('DeleteYaoPinBaiFangWZByIDs');
  return del(uri, obj, config);
}
export function SaveYaoPinBaiFangWZPL(data, config) {
  const uri = getYaoPin('SaveYaoPinBaiFangWZPL');
  return post(uri, data, config);
}

//获取摆放位置顺序哈
export function GetBaiFangWZSXHMax(obj, config) {
  const uri = getYaoPin('GetBaiFangWZSXHMax');
  return get(uri, obj, config);
}
//获取科室请领药品控制列表
export function getKeShiQLYPXXList(obj, config) {
  const uri = getYaoPin('GetKeShiQLYPXXList');
  return get(uri, obj, config);
}
//保存科室请领药品控制
export function saveKeShiQLYPXX(obj, config) {
  const uri = getYaoPin('SaveKeShiQLYPXX');
  return post(uri, obj, config);
}

//获取药房
export function GetYaoFangListByJGID(obj, config) {
  const uri = getYaoPin('GetYaoFangListByJGID');
  return get(uri, obj, config);
}
