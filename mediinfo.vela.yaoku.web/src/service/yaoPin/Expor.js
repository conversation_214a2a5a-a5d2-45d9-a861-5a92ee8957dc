import { get, post, put, del } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/Expor/` + actionUrl;
}

//获取导出字段
export function GetExportableModelInfo(obj, config) {
  const uri = getBaseUri('GetExportableModelInfo');
  return get(uri, obj, config);
}

//导出批次库存
export function ExporYaoKuPCKCList(config) {
  const uri = getBaseUri('ExporYaoKuPCKCList');
  return post(uri, config);
}

//导出药库报损明细
export function ExporYaoKuBSList(config) {
  const uri = getBaseUri('ExporYaoKuBSList');
  return post(uri, config);
}

//导出药库盘存明细
export function ExporYaoKuPCList(config) {
  const uri = getBaseUri('ExporYaoKuPCList');
  return post(uri, config);
}
