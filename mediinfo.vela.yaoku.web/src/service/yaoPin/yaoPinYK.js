import { del, get, post } from '@/system/utils/request';
import GLO<PERSON><PERSON> from '../configSettings';

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1/YaoPinYK/` + actionUrl;
}

export function saveZhongYaoCFKZ(obj, config) {
  const uri = getBaseurl('SaveZhongYaoCFKZ');
  return post(uri, obj, config);
}
export function getZhongYaoCFKZCount(obj, config) {
  const uri = getBaseurl('GetZhongYaoCFKZCount');
  return get(uri, obj, config);
}
export function getZhongYaoCFKZList(obj, config) {
  const uri = getBaseurl('GetZhongYaoCFKZList');
  return get(uri, obj, config);
}
export function zuoFeiZhongYaoCFKZ(obj, config) {
  const uri = getBaseurl('ZuoFeiZhongYaoCFKZ');
  return del(uri, obj, config);
}
