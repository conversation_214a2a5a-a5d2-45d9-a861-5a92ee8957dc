import { get, post, del, put } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';
function getBaseurl(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinTPN/${url}`;
}
function getGongYongurl(url) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/ZuZhiXX/${url}`;
}

/**
 * 获取营养分类列表
 * @param {*} param
 */
export const GetYingYangFLList = (param) => {
  const url = getBaseurl('GetYingYangFLList');
  return get(url, param);
};
/**
 * 获取营养分类信息
 * @param {*} param
 */
export const GetYingYangFLXX = (param) => {
  const url = getBaseurl('GetYingYangFLXX');
  return get(url, param);
};
/**
 * 获取指标下拉列表
 * @param {*} param
 */
export const GetZhiBiaoSelectList = (param) => {
  const url = getBaseurl('GetZhiBiaoSelectList');
  return get(url, param);
};

/**
 * 作废营养分类
 * @param {*} param
 */
export const ZuoFeiYingYangFL = (param) => {
  const url = getBaseurl('ZuoFeiYingYangFL');
  return del(url, param);
};

/**
 * 保存营养分类
 * @param {*} param
 */
export const SaveYingYangFL = (param) => {
  const url = getBaseurl('SaveYingYangFL');
  return post(url, param);
};
/**
 * 获取分类表头列表
 * @param {*} param
 */
export const GetFenLeiBTList = (param) => {
  const url = getBaseurl('GetFenLeiBTList');
  return get(url, param);
};
/**
 * 获取药品分类列表
 * @param {*} param
 */
export const GetFenLeiYPList = (param) => {
  const url = getBaseurl('GetFenLeiYPList');
  return get(url, param);
};
/**
 * 保存分类药品
 * @param {*} param
 */
export const SaveFenLeiYP = (param) => {
  const url = getBaseurl('SaveFenLeiYP');
  return post(url, param);
};

/**
 * 获取参考项列表
 * @param {*} param
 */
export const GetCanKaoXiangList = (param) => {
  const url = getBaseurl('GetCanKaoXiangList');
  return get(url, param);
};
/**
 * 获取参考项Count
 * @param {*} param
 */
export const GetCanKaoXiangCount = (param) => {
  const url = getBaseurl('GetCanKaoXiangCount');
  return get(url, param);
};
/**
 * 启停用参考项
 * @param {*} param
 */
export const QiTingYCKX = (param) => {
  const url = getBaseurl('QiTingYCKX');
  return get(url, param);
};
/**
 * 更新参考项
 * @param {*} param
 */
export const GengXinCKX = (param) => {
  const url = getBaseurl('GengXinCKX');
  return get(url, param);
};
/**
 * 初始化参考项
 * @param {*} param
 */
export const ChuShiHCKX = (param) => {
  const url = getBaseurl('ChuShiHCKX');
  return get(url, param);
};
/**
 * 获取参考项信息
 * @param {*} param
 */
export const GetCanKaoXXX = (param) => {
  const url = getBaseurl('GetCanKaoXXX');
  return get(url, param);
};
/**
 * 获取指标list
 * @param {*} param
 */
export const GetZhiBiaoList = (param) => {
  const url = getBaseurl('GetZhiBiaoList');
  return get(url, param);
};
/**
 * 保存营养指标
 * @param {*} param
 */
export const SaveZhiBiao = (param) => {
  const url = getBaseurl('SaveZhiBiao');
  return post(url, param);
};

/**
 * 作废参考项
 * @param {*} param
 */
export const ZuoFeiCanKaoXiang = (param) => {
  const url = getBaseurl('ZuoFeiCanKaoXiang');
  return del(url, param);
};
/**
 * 保存参考项
 * @param {*} param
 */
export const SaveCanKaoXiang = (param) => {
  const url = getBaseurl('SaveCanKaoXiang');
  return post(url, param);
};

/**
 * 获取全院的病区和科室
 * @param {*} param
 * {
 *    zuZhiJGID:'',
 *    youWuTY:true
 * }
 */
export const GetQuanYuanAndBQKS = (param) => {
  const url = getGongYongurl('GetQuanYuanAndBQKS');
  return get(url, param);
};
