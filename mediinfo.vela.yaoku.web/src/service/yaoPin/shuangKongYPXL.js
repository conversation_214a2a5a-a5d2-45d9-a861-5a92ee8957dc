import { del, get, post } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/XiaoHaoXL/` + actionUrl;
}

export const getSortList = (param, config) => {
  const url = getBaseurl('GetXianLiangFL');
  return get(url, param, config);
};

// 获取列表数据
export function GetYaoPinXHLXZPZList(obj, config) {
  var uri = getBaseurl('GetYaoPinXHLXZPZList');
  return get(uri, obj, config);
}

// 获取列表消耗总量
export function GetYaoPinXHLXZPZCount(obj, config) {
  var uri = getBaseurl('GetYaoPinXHLXZPZCount');
  return get(uri, obj, config);
}

//启用
export function QiTingYXHLXZPZ(obj, config) {
  var uri = getBaseurl('QiTingYXHLXZPZ');
  return post(uri + '?id=' + obj.id + '&qiYongLX=' + obj.qiYongLX, config);
  // return post(uri, {params:obj} , config)
}

//换算比例
export function GengXinHSBL(data, config) {
  const url = getBaseurl('GengXinHSBL');
  return post(url, data, config);
}
