import { get, post } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1/YaoPinZDJCSJ/` + actionUrl;
}
function getYaoPin(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/` + actionUrl;
}
function getGongYongurl(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/weizhixx/` + actionUrl;
}
/**
 * @description
获取可管理机构列表
 * @return {Promise<any>}
 */
export function getChuRuKFSDZListByZuZhiJGIDAndFangShiID(params) {
  const url = getBaseUri('getChuRuKFSDZListByZuZhiJGIDAndFangShiID');
  return get(url, params);
}

export function saveChuRuKFSDZ(data, config) {
  const url = getBaseUri('saveChuRuKFSDZ');
  return post(url, data, config);
}

//获取药房设置树
export function GetYaoKuFSZWZList(params) {
  const url = getGongYongurl('WeiZhi/GetYaoKuFSZWZList');
  return get(url, params);
}

// 保存药房设置
export function SaveKuFangSZ(data, config) {
  const url = getBaseUri('SaveKuFangSZ');
  return post(url, data, config);
}

//获取药品设置
export function getKuFangSZ(obj, config) {
  const uri = getBaseUri('getKuFangSZ');
  return get(uri, obj, config);
}

export function getYaoKuFangListByJGID(params) {
  const url = getYaoPin('getYaoKuFangListByJGID');
  return get(url, params);
}

export function getYaoPinSXFJList(params) {
  const url = getYaoPin('GetYaoPinSXFJList');
  return get(url, params);
}

//获取账簿类别
export function GetZhangBuLBSelectList(obj, config) {
  const uri = getBaseUri('GetZhangBuLBSelectList');
  return get(uri, obj, config);
}

//h获取中药库饮品规则
export function getZhongYaoYPRKJGXZ(obj, config) {
  const uri = getBaseUri('getZhongYaoYPRKJGXZ');
  return get(uri, obj, config);
}

//根据属性名获取值
export function getKuFangSZList(obj, config) {
  const uri = getBaseUri('getKuFangSZList');
  return post(uri, obj, config);
}

/**
 * 获取批次药品下拉数据
 */
export function GetJingPeiPCGZSelectList(id) {
  return get(
    `${GLOBAL.ypBaseUri}/api/v1/JingPei/GetJingPeiPCSelectList?weiZhiID=${id}`,
  );
}

export function GetCaoYaoTieGYFSList(obj, config) {
  const uri = getBaseUri('GetCaoYaoTieGYFSList');
  return get(uri, obj, config);
}