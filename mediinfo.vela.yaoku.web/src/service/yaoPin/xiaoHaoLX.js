import { get, post, del } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';

function getYaoPin(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/XiaoHaoXL/` + actionUrl;
}
/**
 * 保存/编辑药品限量分类
 */
export function saveXianLiangFL(data, config) {
  const url = getYaoPin('saveXianLiangFL');
  return post(url, data, config);
}

/**
 * 作废药品限量分类
 */
export function zuoFeiXianLiangFL(obj, config) {
  const url = getYaoPin('ZuoFeiXianLiangFL');
  return del(url, obj, config);
}

/**
 查询限量设置
 fenLeiID
 */
export function getXiangLSZ(params) {
  const url = getYaoPin('GetXiangLSZ');
  return get(url, params);
}

/**
 保存获取限量设置
 */
export function xianLiangSZ(data, config) {
  const url = getYaoPin('XianLiangSZ');
  return post(url, data, config);
}

/**
 * 新增药品总量限制 /SaveYaoPinXHLXZPZ
 */
export function saveYaoPinXHLXZPZ(data, config) {
  const url = getYaoPin('SaveYaoPinXHLXZPZ');
  return post(url, data, config);
}
/**
 * 作废药品总量限制
 */
export function zuoFeiYaoPinXHLXZPZ(obj, config) {
  const url = getYaoPin('ZuoFeiYaoPinXHLXZPZ');
  return del(url, obj, config);
}

/**
 * 获取限制范围 GetXianZhiFW
 */
export function getXianZhiFW(params) {
  const url = getYaoPin('GetXianZhiFW');
  return get(url, params);
}

/**
 * 新增限制范围 AddXianZhiFW
 */
export function addXianZhiFW(data, config) {
  const url = getYaoPin('AddXianZhiFW');
  return post(url, data, config);
}

/**
 * 修改限制范围 UpdateXianZhiFW
 */
export function updateXianZhiFW(data, config) {
  const url = getYaoPin('UpdateXianZhiFW');
  return post(url, data, config);
}
/**
 * 作废限制范围 ZuoFeiXianZhiFW
 */
export function zuoFeiXianZhiFW(obj, config) {
  const url = getYaoPin('ZuoFeiXianZhiFW');
  return del(url, obj, config);
}

// 获取免控详情
export function getMianKongSZ(params) {
  const url = getYaoPin('getMianKongSZ');
  return get(url, params);
}
// 新增免控
export function saveMianKongSZ(data, config) {
  const url = getYaoPin('saveMianKongSZ');
  return post(url, data, config);
}
// 编辑免控
export function updateMianKongSZ(data, config) {
  const url = getYaoPin('updateMianKongSZ');
  return post(url, data, config);
}
// 作废免控
export function zuoFeiMianKongSZ(obj, config) {
  const url = getYaoPin('zuoFeiMianKongSZ');
  return del(url, obj, config);
}
