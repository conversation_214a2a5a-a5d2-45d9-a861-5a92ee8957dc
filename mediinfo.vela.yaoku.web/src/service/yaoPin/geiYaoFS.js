import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

/**
 * 获取优惠类别列表
 * @param {*} param
 */
export const getGeiYaoFSList = (param, config) => {
  const url = getBaseurl('GetGeiYaoFSList');
  return get(url, param, config);
};

/**
 * 获取给药方式列表Count
 * @param {*} param
 */
export const getGeiYaoFSCount = (param, config) => {
  const url = getBaseurl('GetGeiYaoFSCount');
  return get(url, param, config);
};

/**
 * 获取给药方式附加收费列表
 * @param {*} param
 */
export const getGeiYaoFSFJSFList = (param) => {
  const url = getBaseurl('GetGeiYaoFSFJSFList');
  return get(url, param);
};

/**
 *作废给药方式
 * @param {string} id
 */
export function zuoFeiGeiYaoFS(id) {
  const url = getBaseurl('ZuoFeiGeiYaoFS?id=' + id);
  return del(url);
}

/**
 *作废给药方式附加收费项目
 * @param {string} id
 */
export function zuoFeiGeiYaoFSFJSF(id) {
  const url = getBaseurl('ZuoFeiGeiYaoFSFJSF?id=' + id);
  return del(url);
}

//新增给药方式
export const addGeiYaoFS = (param) => {
  //mock测试
  const url = getBaseurl('AddGeiYaoFS');
  return post(url, param);
};

//编辑给药方式
export const updateGeiYaoFS = (id, param) => {
  const url = getBaseurl('UpdateGeiYaoFS?id=' + id);
  return put(url, param);
};
//根据Id获取给药方式
export const getGeiYaoFSAllBYID = (id) => {
  const url = getBaseurl('GetGeiYaoFSAllBYID?geiYaoFSID=' + id);
  return get(url);
};

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/` + actionUrl;
}

/**
 * 获取给药方式下拉框
 * @param {object} query
 * @returns
 */
export function getGeiYaoFSSel(query) {
  return get(getBaseurl('GetGeiYaoFSSel'), query);
}

//获取用药频次选择框列表
export function getPinCiSelectList(obj) {
  const uri = getBaseurl(`GetPinCiSelectList`);
  return get(uri, obj);
}

/**
 *根据数据源类别IDList查询值域
 * @param {Array<string>} ids [""]
 * @returns [{shuJuYLBID:"",zhiYuList:[{shuJuYLBID:"",biaoZhunDM:"",biaoZhunMC:""}]}]
 */
export function getYaoPinShuJuYZYList(ids) {
  return post(getBaseurl('GetShuJuYZYListByLBList'), ids);
}
