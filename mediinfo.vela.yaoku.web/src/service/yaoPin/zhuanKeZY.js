import { del, get, post } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseurl(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1/ZhuanKeZY/` + actionUrl;
}

function getZuanKeZY(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/ZhuanKeZY/` + actionUrl;
}
function getGongYongurl(url) {
  return `${GLOBAL.lyragyBaseUri}/api/v1/ZuZhiXX/${url}`;
}
function getGongYongYHXXurl(url) {
  return `${GLOBAL.lyragyBaseUri}/api/v1/YongHuXX/${url}`;
}
function getJiBenXXurl(url) {
  return `${GLOBAL.brBaseUri}/api/v1/JiBenXX/${url}`;
}
/**
 *  获取专科专用药品管理列表
 */
export function GetZhuanKeZYList(obj, config) {
  const uri = getBaseurl('GetZhuanKeZYList');
  return get(uri, obj, config);
}

/**
 *  获取专科专用药品管理总数
 */
export function GetZhuanKeZYCount(obj, config) {
  const uri = getBaseurl('GetZhuanKeZYListCount');
  return get(uri, obj, config);
}
/**
 *  启用专科专用药品管理数据项
 */
export function UpdateZhuanKeZYQYBZ(obj, config) {
  const uri = getZuanKeZY('UpdateZhuanKeZYQYBZ');
  return get(uri, obj, config);
}
/**
 * 选择药品后判断该药品是否已存在
 */
export function getPanDuanZKZYXX(obj, config) {
  const uri = getZuanKeZY('PanDuanZKZYXX');
  return get(uri, obj, config);
}
/**
 * 新增修改界面---科室
 */
export function getKeShiList(obj, config) {
  const uri = getGongYongurl('KeShi/MoJiKSList');
  return get(uri, obj, config);
}
/**
 * 新增修改界面---患者
 */
export function getHuanZheList(obj, config) {
  const uri = getJiBenXXurl('GetBingRenListByXMHZHHM');
  return get(uri, obj, config);
}
/**
 * 新增修改界面---医生
 */
export function getYiShengList(obj, config) {
  const uri = getGongYongYHXXurl('GetYongHuXXByYHLB');
  return get(uri, obj, config);
}
/**
 *  保存
 */
export function SaveZhuanKeZY(data, config) {
  const uri = getZuanKeZY('AddZhuanKeZY');
  return post(uri, data, config);
}
/**
 *  修改
 */
export function UpdateZhuanKeZY(data, config) {
  const uri = getZuanKeZY('UpdateZhuanKeZY');
  return post(uri, data, config);
}
/**
 * 专科专用明细查询
 */
export function GetZhuanKeZYXX(obj, config) {
  const uri = getZuanKeZY('GetZhuanKeZYXX');
  return get(uri, obj, config);
}
/**
 * 作废
 */
export function ZuoFeiZhuanKeZY(obj, config) {
  const uri = getZuanKeZY('ZuoFeiZhuanKeZY');
  return del(uri, obj, config);
}
