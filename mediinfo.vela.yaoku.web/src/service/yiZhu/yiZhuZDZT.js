import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

//获取草药协定方组套数据
export function getXieDingFList(obj) {
  const uri = getBaseUri('GetXieDingFList');
  return get(uri, obj);
}
//获取协定方信息
export function getXieDingFXX(obj) {
  const uri = getBaseUri('GetXieDingFXX');
  return get(uri, obj);
}
//保存组套
export function saveZuTao(obj) {
  const uri = getBaseUri('SaveZuTao');
  return post(uri, obj);
}
//作废组套和组套模板
export function zuoFeiZuTao(obj) {
  const uri = getBaseUri('ZuoFeiZuTao');
  return del(uri, obj);
}
//获取位置信息
export function getWeiZhiData(id) {
  const uri = `/mediinfo-lyra-gongyong/api/v1/WeiZhiXX/weizhi/weizhiid/${id}`;
  return get(uri);
}

function getBaseUri(actionUrl) {
  return `${GLOBAL.yzBaseUri}/api/v1.0/YiZhuZDZT/` + actionUrl;
}
