import { get, post, del, put } from '@/system/utils/request';
import G<PERSON><PERSON><PERSON><PERSON> from '../configSettings';

/**
 * 根据数据源类别IDList查询值域
 * @param {Array<string>} ids [""]
 * @returns [{shuJuYLBID:"",zhiYuList:[{shuJuYLBID:"",biaoZhunDM:"",biaoZhunMC:""}]}]
 */
export function getYiZhuSJYZYListByLBList(ids) {
  return post(getBaseurl('GetShuJuYZYListByLBList'), ids);
}

/**
 * 根据数据源类别id查询值域
 * @param {string} id ""
 * @returns [{shuJuYLBID:"",biaoZhunDM:"",biaoZhunMC:""}]
 */
export function getYiZhuSJYZYListByLBID(id) {
  return get(getBaseurl(`GetShuJuYZYListByLBID?shuJuYLBID=${id}`));
}

function getBaseurl(actionUrl) {
  return `${GLOBAL.yzBaseUri}/api/v1.0/YiZhuYWZD/` + actionUrl;
}
