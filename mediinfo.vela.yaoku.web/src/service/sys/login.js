/*
 * @Author: wuxiaoling <EMAIL>
 * @Date: 2024-12-25 10:23:56
 * @LastEditors: wuxiaoling <EMAIL>
 * @LastEditTime: 2024-12-28 10:53:32
 * @FilePath: \yaoku-药库管理\mediinfo.vela.yaoku.web\src\service\sys\login.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
//开发环境时 使用写死的假token

const tokenType = 'Bearer';
const accessToken =
  '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
export const devToken = tokenType + ' ' + accessToken;

export const devJiGouXX = {
  // WeiZhiID: '0000000009',
  WeiZhiLXDM: '3',
  WeiZhiID: '401',
  WeiZhiMC: encodeURIComponent('西药库'),
  weiZhiLXDM: '3',
  // KeShiID: '0000000159',
  KeShiID: '401',
  KeShiMC: encodeURIComponent('普通内科门诊'),
  BingQuID: '',
  BingQuMC: '',
  yongHuID: '1311929197781848064',
  yongHuMC: encodeURIComponent('普通内科医生'),
  yongHuDLM: 'jsdyrmyy',
  JiGouID: '47142000133068311A1001',
  JiGouMC: encodeURIComponent('嘉善第一人民医院'),
  kuCunGLLXs: JSON.stringify([1, 2, 3, 4]),
  gongNengID: '1010095857315307520',
  gongNengMC: encodeURIComponent('药品入库'),
  yingYongID: '1010091260773359616',
  yingYongMC: encodeURIComponent('药库管理'),
};
