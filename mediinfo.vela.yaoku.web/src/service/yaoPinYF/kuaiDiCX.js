import { get, post, del, put } from '@/system/utils/request';
import GL<PERSON>BA<PERSON> from '../configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/${url}`;
}
export function UpdateKuaiDiDYZT(obj, config) {
  const uri = getBaseUri('UpdateKuaiDiDYZT');
  return post(uri, obj, config);
}

export function GetKuaiDiYPList(obj, config) {
  const uri = getBaseUri('GetKuaiDiYPList');
  return get(uri, obj, config);
}
export function GetKuaiDiYPCount(obj, config) {
  const uri = getBaseUri('GetKuaiDiYPCount');
  return get(uri, obj, config);
}
export function GetKuaiDiCFXQ(obj, config) {
  const uri = getBaseUri('GetKuaiDiCFXQ');
  return get(uri, obj, config);
}
