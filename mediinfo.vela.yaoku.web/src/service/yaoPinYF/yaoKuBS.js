import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/` + actionUrl;
}
//获取未记账报损单开始时间
export function GetWeiJiZBSDKSRQ(obj, config) {
  const uri = getBaseUri('GetWeiJiZBSDKSRQ');
  return get(uri, obj, config);
}
//获取未记账报损单列表
export function GetWeiJiZBSDList(obj, config) {
  const uri = getBaseUri('GetWeiJiZBSDList');
  return get(uri, obj, config);
}

//获取未记账报损单列表数
export function GetWeiJiZBSDCount(obj, config) {
  const uri = getBaseUri('GetWeiJiZBSDCount');
  return get(uri, obj, config);
}

//新增报损单
export function AddBaoSunDan(obj, config) {
  const uri = getBaseUri('AddBaoSunDan');
  return post(uri, obj, config);
}
export function ZuoFeiBaoSunDan(id, config) {
  const uri = getBaseUri('ZuoFeiBaoSunDan?baoSunDID=' + id);
  return post(uri, {}, config);
}

//获取报损单详情信息
export function GetBaoSunDXQXX(obj, config) {
  const uri = getBaseUri('GetBaoSunDXQXX');
  return get(uri, obj, config);
}

//更新报损单
export function UpdateBaoSunDan(obj, config) {
  const uri = getBaseUri('UpdateBaoSunDan');
  return post(uri, obj, config);
}

//记账报损单
export function JiZhangBaoSunDan(obj, config) {
  const uri = getBaseUri('JiZhangBaoSunDan');
  return get(uri, obj, config);
}

// 根据报损单id获取报损单明细信息
export function GetBaoSunDMXList(obj, config) {
  const uri = getBaseUri('GetBaoSunDMXList');
  return get(uri, obj, config);
}

//获取已记账报损单列表
export function GetYiJiZBSDList(obj, config) {
  const uri = getBaseUri('GetYiJiZBSDList');
  return get(uri, obj, config);
}

//获取已记账报损单列表数
export function GetYiJiZBSDCount(obj, config) {
  const uri = getBaseUri('GetYiJiZBSDCount');
  return get(uri, obj, config);
}

//根据价格ID获取出库药品批次库存
export function GetBaoSunYPPCListYF(obj, config) {
  const uri = getBaseUri('GetBaoSunYPPCList');
  return get(uri, obj, config);
}
