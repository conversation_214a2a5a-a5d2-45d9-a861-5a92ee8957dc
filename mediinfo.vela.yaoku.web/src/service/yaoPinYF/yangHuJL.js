import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

//养护记录列表
export function GetYangHuDanList(obj, config) {
  const uri = getBaseUri('GetYangHuDanList');
  return get(uri, obj, config);
}

//养护记录列表count
export function GetYangHuDanCount(obj, config) {
  const uri = getBaseUri('GetYangHuDanCount');
  return get(uri, obj, config);
}

//养护记录编辑初始化数据接口、详情接口
export function GetYangHuDanXX(obj, config) {
  const uri = getBaseUri('GetYangHuDanXX');
  return get(uri, obj, config);
}

//养护记录单保存
export function SaveYangHuDan(obj, config) {
  const uri = getBaseUri('SaveYangHuDan');
  return post(uri, obj, config);
}

//根据价格id获取药品详细信息接口
export function GetYKYaoPinCDJGByJGID(obj, config) {
  const uri = getBaseUri('GetYKYaoPinCDJGByJGID');
  return get(uri, obj, config);
}

//作废养护记录
export function ZuoFeiYangHuDan(obj, config) {
  const uri = getBaseUri('ZuoFeiYangHuDan');
  return del(uri, obj, config);
}

//库存>0
export function ShengChengDaYuLYP(obj, config) {
  const uri = getBaseUri('ShengChengDaYuLYP');
  return post(uri, obj, config);
}
