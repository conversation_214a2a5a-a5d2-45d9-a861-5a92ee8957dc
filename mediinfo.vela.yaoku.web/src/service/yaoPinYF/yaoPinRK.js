import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

/**
 * 新增药品入库单(同时可保存明细)
 */
export function AddYaoPinRKD(obj, config) {
  const uri = getBaseUri('AddYaoPinRKD');
  return post(uri, obj, config);
}
/**
 * 修改入库单
 */
export function UpdateYaoPinRKD(obj, config) {
  const uri = getBaseUri('UpdateYaoPinRKD');
  return put(uri + '?id=' + obj.id, obj, config);
}
/**
 * 作废入库单
 */
export function ZuoFeiYaoPinRKD(id, config) {
  const uri = getBaseUri('ZuoFeiYaoPinRKD');
  return del(uri + '?id=' + id, config);
}
/**
 * 根据价格ID获取药品产地价格信息
 */
export function GetYaoPinCDJGByJGID(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGByJGID');
  return get(uri, obj, config);
}
/**
 * 获取记账入库单
 */
export function JiZhangYaoPinRKD(obj, config) {
  const uri = getBaseUri('JiZhangYaoPinRKD');
  return post(uri + '?id=' + obj.id, obj, config);
}
// export function GetCaiGouJHDCount(obj, config) {
//   var uri = getBaseUri('GetCaiGouJHDCount')
//   return get(uri, obj, config)
// }
/**
 * 获取药品入库单列表
 */
export function GetYaoPinRKDList(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDList');
  return get(uri, obj, config);
}
export function GetYaoPinRKDCount(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDCount');
  return get(uri, obj, config);
}
/**
 * 获取药品入库单详情
 *
 */
export function GetYaoPinRKDXQ(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDXQ');
  return get(uri, obj, config);
}

/**
 * 获取未结账日期
 */
export function GetYaoPinRKDWJZRQ(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDWJZRQ');
  return get(uri, obj, config);
}

function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/${url}`;
}
