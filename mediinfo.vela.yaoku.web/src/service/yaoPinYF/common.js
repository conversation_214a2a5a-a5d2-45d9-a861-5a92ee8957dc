import { get, post } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/` + actionUrl;
}
function getBaseUriYK(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}
function getGongYongUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/GongYong/` + actionUrl;
}
function getYaoPinZDUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/` + actionUrl;
}

// 药房-药品批号
export function GetChuKuYPPCListYF(obj, config) {
  const uri = getBaseUri('GetChuKuYPPCList');
  return get(uri, obj, config);
}
// 库存上下限设置用
export function GetYaoPinList(obj, config) {
  const uri = getGongYongUri('GetYaoPinList');
  return get(uri, obj, config);
}
// 药房-药品定位
export function GetGongYongYPForYFList(obj, config) {
  const uri = getBaseUri('GetGongYongYPForYFList');
  return get(uri, obj, config);
}

// 西药库不需要按照批号出库
export function GetChuKuYPSLByPCList(obj, config) {
  const uri = getBaseUriYK('GetChuKuYPSLByPCList');
  return post(uri, obj, config);
}
/**
 * 药房-根据价格id获取库存数量，导入历史时使用GetYaoPinKCByJGID
 */
export function GetYaoPinKCByJGID(obj, config = {}) {
  const uri = getBaseUri('GetYaoPinKCByJGID');
  return post(uri, obj, config);
}
//获取药品显示信息
export function GetYaoPinTSSX(obj, config = {}) {
  const uri = getYaoPinZDUri('GetYaoPinTSSX');
  return post(uri, obj, config);
}
