import { get } from '@/system/utils/request';
import GLO<PERSON><PERSON> from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/${url}`;
}

/**
 *  获取调价查询记录数量
 *
 */
export function GetTiaoJiaDCount(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDCount');
  return get(uri, obj, config);
}
/**
 * @description: 获取调价查询数据list
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetTiaoJiaDList(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDList');
  return get(uri, obj, config);
}
