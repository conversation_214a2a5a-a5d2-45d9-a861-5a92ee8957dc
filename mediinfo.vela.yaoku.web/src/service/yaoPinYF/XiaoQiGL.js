import { get } from '@/system/utils/request';
import GLOBAL from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYF/${url}`;
}

/**
 *  获取效期管理数据list
 *
 */
export function GetShiXiaoYPList(obj, config) {
  const uri = getBaseUri('GetShiXiaoYPList');
  return get(uri, obj, config);
}

/**
 *  查询药房效期信息记录数
 *
 */
export function GetShiXiaoYPCount(obj, config) {
  const uri = getBaseUri('GetShiXiaoYPCount');
  return get(uri, obj, config);
}
/**
 *  查询药房效期信息零售总额
 *
 */
export function GetShiXiaoYPLSZE(obj, config) {
  const uri = getBaseUri('GetShiXiaoYPLSZE');
  return get(uri, obj, config);
}

/**
 *  根据价格ID获取最近入库药品的批号和效期
 *
 */
export function GetJinRuKYPXQPHListByJGID(obj, config) {
  const uri = getBaseUri('GetJinRuKYPXQPHListByJGID');
  return get(uri, obj, config);
}
