import { get, post, put, del } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/` + actionUrl;
}

//获取账簿类别列表
export function getZhangBuLBList(params, config) {
  const uri = getBaseUri(`GetZhangBuLBList`);
  return get(uri, params, config);
}

//获取账簿类别数量
export function getZhangBuLBCount(params, config) {
  const uri = getBaseUri(`GetZhangBuLBCount`);
  return get(uri, params, config);
}

//获取账簿类别
export function getZhangBuLB(id) {
  const uri = getBaseUri(`GetZhangBuLB?id=` + id);
  return get(uri);
}

//新增账簿类别
export function addZhangBuLB(data) {
  const uri = getBaseUri(`AddZhangBuLB`);
  return post(uri, data);
}

//更新账簿类别
export function updateZhangBuLB(data) {
  const uri = getBaseUri(`UpdateZhangBuLB`);
  return put(uri, data);
}

//作废账簿类别
export function zuoFeiZhangBuLB(id) {
  const uri = getBaseUri(`ZuoFeiZhangBuLB`);
  return del(uri, { id: id });
}

//获取账簿类别选择框列表
export function getZhangBuLBSelectList(obj) {
  const uri = getBaseUri(`GetZhangBuLBSelectList`);
  return get(uri, obj);
}

function getBaseUriYPZD(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/${url}`;
}

// 获取账簿类别根据权限获取类别
export function getZhangBuQXXQ(obj, config) {
  var uri = getBaseUriYPZD('GetZhangBuQXXQ');
  return get(uri, obj, config);
}
