import { get, post, put, del } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

/**
 *获取链接
 * @param {string} action
 * @returns
 */
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/` + actionUrl;
}

//获取剂型数据集合
export function getJiXingList(params, config) {
  const uri = getBaseUri('GetJiXingList');
  return get(uri, params, config);
}

//获取剂型数据数量
export function getJiXingCount(params, config) {
  const uri = getBaseUri(`GetJiXingCount`);
  return get(uri, params, config);
}

//根据Id获取剂型数据
export function getJiXing(id) {
  const uri = getBaseUri(`GetJiXing?id=` + id);
  return get(uri);
}

//新增剂型
export function addJiXing(data) {
  const uri = getBaseUri(`AddJiXing`);
  return post(uri, data);
}

//更新
export function updateJiXing(data) {
  const uri = getBaseUri(`UpdateJiXing`);
  return put(uri, data);
}

//作废剂型数据
export function zuoFeiJiXing(id) {
  const uri = getBaseUri(`ZuoFeiJiXing`);
  return del(uri, { id: id });
}

//获取剂型选择框列表
export function getJiXingSelectList(obj) {
  const uri = getBaseUri(`GetJiXingSelectList`);
  return get(uri, obj);
}
