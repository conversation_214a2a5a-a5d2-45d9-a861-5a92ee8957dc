import { get } from '@/system/utils/request';
import GLOBA<PERSON> from '@/service/configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/YongHuXX/` + actionUrl;
}
function getBaseUri2(actionUrl) {
  return `${GLOBAL.dzBaseUri}/api/v1.0/` + actionUrl;
}

/**
 * 获取医生列表, 根据输入值模糊查询，输入值匹配科室名称和用户名称
 * @param {*} params
 */
export function getYongHuXXByYHLB(params) {
  const url = getBaseUri('GetYongHuXXByYHLB');
  return get(`${url}`, params);
}
export function getYongHuXX(params) {
  const url = getBaseUri2('YongHuXX');
  return get(`${url}`, params);
}

/**
 * 根据位置ID 获取用户
 */
export function pageByWeiZhi(params) {
  const url = getBaseUri('GetYaoKuFangYongHuXX');
  return get(`${url}`, params);
}
