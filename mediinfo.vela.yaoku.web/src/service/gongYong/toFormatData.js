// 转换成货币格式
// number为数字类型，是需要转格式的数字
// places为数字类型，小数点保留的位数，不传默认为2
export function formatJiaGe(number, places) {
  number = number || 0;
  places = !isNaN((places = Math.abs(places))) ? places : 2;

  var negative = number < 0 ? '-' : '',
    i = parseInt((number = Math.abs(+number || 0).toFixed(places)), 10) + '',
    j = (j = i.length) > 3 ? j % 3 : 0;
  return (
    negative +
    (j ? i.substr(0, j) + ',' : '') +
    i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + ',') +
    (places
      ? '.' +
        Math.abs(number - i)
          .toFixed(places)
          .slice(2)
      : '')
  );
}
