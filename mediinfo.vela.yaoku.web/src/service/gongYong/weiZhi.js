import { get, post } from '@/system/utils/request';
import G<PERSON><PERSON><PERSON><PERSON> from '@/service/configSettings';
import qs from 'qs';

function getBaseUri(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/WeiZhiXX/` + actionUrl;
}
function getBaseUri2(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/ZuZhiXX/` + actionUrl;
}
/**
 * 根据组织机构ID获取位置列表
 * @param {*} params
 */
export function getHouZhenWZList(params) {
  const url = getBaseUri('WeiZhi/List');
  return get(`${url}`, params);
}

/**
 * 根据组织机构ID获取位置列表(批量)
 * @param {*} params
 */
export function getWeiZhiPLList(params) {
  const url = getBaseUri('WeiZhi/JiGouWZ');
  return get(`${url}?` + qs.stringify(params));
}
export function getMoJiKSList(params) {
  const url = getBaseUri2('KeShi/MoJiKSList');
  return get(`${url}`, params);
}
