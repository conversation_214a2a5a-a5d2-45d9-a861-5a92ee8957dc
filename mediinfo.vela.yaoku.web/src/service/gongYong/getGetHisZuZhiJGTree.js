import http from '@/system/utils/request';
import { toURL } from '@/system/utils/toURL';
import { getApiVersion } from '@/system/utils/apiVersion';

/**
 * 获取HIS医疗机构树
 *
 * @param {Object} params
 * @param {string} params.shiFouQB 是否加载全部医疗机构
 * @param {string} params.youWuQYQ 是否加载有签约权医疗机构
 * @param {string} params.youWuMJ 是否加载末级
 * @param {string} params.youWuTY 是否显示通用
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function request(params, config) {
  params = params || {};
  params.version = getApiVersion('yewukzt');
  const url = toURL(
    '/mediinfo-lyra-gongyong/api/v1/ZuZhiXX/GetHisZuZhiJGTree',
    params,
  );
  return http.request({ url, method: 'GET', params, ...config });
}
