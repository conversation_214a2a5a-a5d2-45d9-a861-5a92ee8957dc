import { post, get } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1/zuzhixx/` + actionUrl;
}

/**
 * @description 获取可管理机构列表
 * @return {Promise<any>}
 */
export function getKeGuanLiJGList(params) {
  const url = getBaseUri('GetKeGuanLiAndShangJiJGList');
  return get(url, params);
}

/**
 * @description 获取项目管理列表
 * @return {Promise<any>}
 */
export function getJiGouGLPZListByYeWuLXDM(params) {
  const url = getBaseUri('getJiGouGLPZListByYeWuLXDM');
  return get(url, params);
}

/**
 * @description 保存项目管理模式
 * @param {Object} data
 * @return {Promise<any>}
 */
export function saveJiGouGLPZ(data) {
  const url = getBaseUri('saveJiGouGLPZ');
  return post(url, data);
}
