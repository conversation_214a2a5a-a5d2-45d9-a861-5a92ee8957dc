import http from '@/system/utils/request';
import { toURL } from '@/system/utils/toURL';
import { getApiVersion } from '@/system/utils/apiVersion';

/**
 * @return {Promise<*>}
 */
export function request(params, config) {
  params = params || {};
  params.version = getApiVersion('gongyong');
  const url = toURL(
    '/mediinfo-lyra-gongyong/api/v1.0/yewu/peizhism/GetPeiZhiSMXQ',
    params,
  );
  return http.request({ url, method: 'GET', params, ...config });
}
