import http from '@/system/utils/request';
import { toURL, urlParams } from '@/system/utils/toURL';
import { getApiVersion } from '@/system/utils/apiVersion';

/**
 * 根据应用ID获取功能列表
 *
 * @param {Object} params
 * @param {string} params.yingYongID
 * @param {string} params.yeMianBZ
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function request(params, config) {
  params = params || {};
  params.version = getApiVersion('dizuoJava');
  let url = toURL(
    '/mediinfo-lyra-dizuo/api/v1.0/CaiDan/mojicdbycdymbb',
    params,
  );
  return http.request({ url, method: 'GET', params, ...config });
}
