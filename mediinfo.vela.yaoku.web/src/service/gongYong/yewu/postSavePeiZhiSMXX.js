import http from '@/system/utils/request';
import { toURL } from '@/system/utils/toURL';
import { getApiVersion } from '@/system/utils/apiVersion';

/**
 * 保存配置说明信息
 *
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function request(data, config) {
  data = data || {};
  data.version = getApiVersion('gongyong');
  const url = toURL(
    '/mediinfo-lyra-gongyong/api/v1.0/yewu/peizhism/SavePeiZhiSMXX',
    data,
  );
  return http.request({ url, method: 'POST', data, ...config });
}
