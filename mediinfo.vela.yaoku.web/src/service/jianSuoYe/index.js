/*
 * @Author: yangyang2 <EMAIL>
 * @Date: 2024-11-14 10:32:20
 * @LastEditors: yangyang2 <EMAIL>
 * @LastEditTime: 2024-12-03 15:48:56
 * @FilePath: \mediinfo.vela.yaoku.web\src\service\jianSuoYe\index.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import GLOBAL from '@/service/configSettings';
import { get, post, del, put } from '@/system/utils/request';

/**
 * 获取字段列表
 *
 * @param {Object} params
 * @param {string} params.jianSuoYDM 检索页代码
 * @param {string} params.kongZhiJBDM 控制级别代码
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function getJiansuoyexxGetziduanlistByJianSuoYDM(obj) {
  const uri = getBaseUri(`getziduanlist/${obj.jianSuoYDM}`);
  return get(uri, obj);
}

/**
 * 添加个人档案检索页信息
 * @param {Object} data
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 * @return {Promise<*>}
 */
export function postJiansuoyexxAddgerendajsyxx(obj) {
  const uri = getBaseUri('addgerendajsyxx');
  return post(uri, obj);
}

/**
 * 恢复初始值
 *
 * @param {Object} params
 * @param {string} params.jiBenXXID 基本信息ID
 * @param {string} params.kongZhiJBDM 控制级别代码
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function deleteJiansuoyexxHuifucszByJiBenXXID(obj) {
  const uri = getBaseUri(`huifucsz/${obj.jiBenXXID}`);
  return del(uri, obj);
}
/**
 * 恢复初始值
 *
 * @param {Object} params
 * @param {string} params.jiBenXXID 基本信息ID
 * @param {string} params.kongZhiJBDM 控制级别代码
 * @param {import('axios').AxiosRequestConfig} config axios 可选配置
 *
 * @return {Promise<*>}
 */
export function getYongHuLCQX(obj) {
  const uri = getGYBaseUri('YongHuLCQX');
  return get(uri, obj);
}

//lyra公用
function getGYBaseUri(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/` + actionUrl;
}
//lyra公用
function getBaseUri(actionUrl) {
  return `${GLOBAL.mediterUrl}/api/v1.0/JianSuoYe/jiansuoyexx/` + actionUrl;
}
