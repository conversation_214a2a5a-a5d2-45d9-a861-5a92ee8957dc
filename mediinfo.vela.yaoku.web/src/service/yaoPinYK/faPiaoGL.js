import { get, post } from '@/system/utils/request';
import GLOBAL from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 * 获取未登记发票供货单位列表
 */
export function getBuDengFPList(obj, config) {
  const uri = getBaseUri('GetBuDengFPList');
  return get(uri, obj, config);
}

/**
 * 获取未登记发票供货单位列表总数
 */
export function getBuDengFPCount(obj, config) {
  const uri = getBaseUri('GetBuDengFPCount');
  return get(uri, obj, config);
}

/**
 * 根据供货单位获取补登药品明细列表
 */
export function GetBuDengPMXListByGHDW(obj, config) {
  const uri = getBaseUri('GetBuDengPMXListByGHDW');
  return get(uri, obj, config);
}
/**
 * 根据供货单位获取补登药品明细列表数量
 */
export function GetBuDengPMXCountByGHDW(obj, config) {
  const uri = getBaseUri('GetBuDengPMXCountByGHDW');
  return get(uri, obj, config);
}
/**
 * 更新批量发票信息
 */
export function updateBuDengFP(obj, config) {
  const uri = getBaseUri('UpdateBuDengFP');
  return post(uri, obj, config);
}

/**
 * 更新批量发票信息(删除)
 */
export function deleteBuDengFP(obj, config) {
  const uri = getBaseUri('DeleteBuDengFP');
  return post(uri, obj, config);
}

/**
 * 根据发票获取药品明细
 */
export function getWeiJieSYPMXListByFP(obj, config) {
  const uri = getBaseUri('GetWeiJieSYPMXListByFP');
  return get(uri, obj, config);
}

/**
 * 删除更新发票号
 */
export function updatePiLiangFPXX(obj, config) {
  const uri = getBaseUri('UpdatePiLiangFPXX');
  return post(uri, obj, config);
}

// 发票查询-模糊查询
export function GetFaPiaoListByFPHMLike(obj, config) {
  const uri = getBaseUri('GetFaPiaoListByFPHMLike');
  return get(uri, obj, config);
}
