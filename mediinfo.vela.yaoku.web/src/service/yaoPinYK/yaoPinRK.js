import { del, get, post, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

/**
 * 新增药品入库单(同时可保存明细)
 */
export function AddYaoPinRKD(obj, config) {
  const uri = getBaseUri('AddYaoPinRKD');
  return post(uri, obj, config);
}
/**
 * 修改入库单
 */
export function UpdateYaoPinRKD(obj, config) {
  const uri = getBaseUri('UpdateYaoPinRKD');
  return put(uri + '?id=' + obj.id, obj, config);
}
/**
 * 作废入库单
 */
export function ZuoFeiYaoPinRKD(id, config) {
  const uri = getBaseUri('ZuoFeiYaoPinRKD');
  return del(uri + '?id=' + id, config);
}
/**
 * 根据价格ID获取药品产地价格信息
 */
export function GetYaoPinCDJGByJGID(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGByJGID');
  return get(uri, obj, config);
}
/**
 * 根据价格ID获取药品产地价格信息
 */
export function GetYaoPinCDJGListByJGIDList(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGListByJGIDList');
  return post(uri, obj, config);
}

/**
 * 获取采购计划单列表
 */
export function GetCaiGouJHDList(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDListForRKD');
  return get(uri, obj, config);
}
export function GetCaiGouJHDCount(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDListForRKDCount');
  return get(uri, obj, config);
}
/**
 * 获取采购计划单列表
 */
export function GetCaiGouJHDMXForRKD(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDMXForRKD');
  return get(uri, obj, config);
}
/**
 * 获取记账入库单
 */
export function JiZhangYaoPinRKD(obj, config) {
  const uri = getBaseUri('JiZhangYaoPinRKD');
  return post(uri + '?id=' + obj.id, obj, config);
}
// export function GetCaiGouJHDCount(obj, config) {
//   var uri = getBaseUri('GetCaiGouJHDCount')
//   return get(uri, obj, config)
// }
/**
 * 获取药品入库单列表
 */
export function GetYaoPinRKDList(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDList');
  return get(uri, obj, config);
}
export function GetYaoPinRKDCount(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDCount');
  return get(uri, obj, config);
}
/**
 * 获取药品入库单详情
 *
 */
export function GetYaoPinRKDXQ(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDXQ');
  return get(uri, obj, config);
}

export function CheckFaPiaoSFCF(obj, config) {
  const uri = getBaseUri('CheckFaPiaoSFCF');
  return get(uri, obj, config);
}

/**
 * 获取未结账日期
 */
export function GetYaoPinRKDWJZRQ(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDWJZRQ');
  return get(uri, obj, config);
}
export function UpdateCaiGouDanZT(obj, config) {
  const uri = getBaseUri('UpdateCaiGouDanZT');
  return post(uri, obj, config);
}
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

//获取产地
export function GetChanDiXXSelectList() {
  const uri = `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/GetChanDiSelectList`;
  return get(uri);
}

export function GetYaoPinPHXQByJGID(obj, config) {
  const uri = getBaseUri('GetYaoPinPHXQByJGID');
  return get(uri, obj, config);
}
