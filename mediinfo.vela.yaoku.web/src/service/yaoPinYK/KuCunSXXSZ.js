import { get, post } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 * 保存、更新库存上下限设置
 */
export function AddYaoPinCLKZ(obj, config) {
  const uri = getBaseUri('AddYaoPinCLKZ');
  return post(uri, obj, config);
}
/**
 * 药品产地上下线设置数量
 */
export function GetYaoPinCDJGSXSZCount(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGSXSZCount');
  return get(uri, obj, config);
}
/**
 * 药品产地上下线设置列表
 */
export function GetYaoPinCDJGSXSZList(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGSXSZList');
  return get(uri, obj, config);
}

/**
 * 药品规格上下线设置列表数量
 */
export function GetYaoPinGGSXSZCount(obj, config) {
  const uri = getBaseUri('GetYaoPinGGSXSZCount');
  return get(uri, obj, config);
}
/**
 * 药品规格上下线设置列表
 */
export function GetYaoPinGGSXSZList(obj, config) {
  const uri = getBaseUri('GetYaoPinGGSXSZList');
  return get(uri, obj, config);
}

//全部应用和保存接口
export function UpdateYaoPinXXBYXHL(obj, config) {
  const uri = getBaseUri('UpdateYaoPinXXBYXHL');
  return post(uri, obj, config);
}
