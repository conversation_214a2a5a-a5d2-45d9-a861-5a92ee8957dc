import { get, put, post } from '@/system/utils/request';
import GLOBAL from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}
//采购计划列表
export function GetCaiGouJHDSHXXList(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDSHXXList');
  return get(uri, obj, config);
}

//列表数量
export function GetCaiGouJHDSHXXCount(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDSHXXCount');
  return get(uri, obj, config);
}
//采购详情
export function GetCaiGouJHDSHXQList(caiGouJHDID) {
  const uri = getBaseUri('GetCaiGouJHDSHXQXX?id=' + caiGouJHDID);
  return get(uri);
}

//拒绝
export function JuJueCGJHDSH(obj) {
  const uri = getBaseUri('JuJueCGJHDSH');
  return put(uri, obj);
}
//同意
export function TongYiCGJHDSH(caiGouJHDID) {
  const uri = getBaseUri('TongYiCGJHDSH?id=' + caiGouJHDID);
  return put(uri);
}

// 入库拒收
export function juShouCGJHDSH(caiGouJHDID) {
  const uri = getBaseUri('juShouCGJHDSH');
  return put(uri, caiGouJHDID);
}
// 更新待入库列表数据
export function UpdateCaiGouJHDListForRKD(obj, config) {
  const uri = getBaseUri('UpdateCaiGouJHDListForRKD');
  return get(uri, obj, config);
}

// 获取发票号码
export function getCaiGouJHDListByFPHM(obj, config) {
  const uri = getBaseUri('getCaiGouJHDListByFPHM');
  return get(uri, obj, config);
}
// 获取发票号码
export function GetCaiGouJHDListByFPHMCount(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDListByFPHMCount');
  return get(uri, obj, config);
}

// 根据发票号获取明细
export function GetCaiGouJHDMXByFPHM(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDMXByFPHM');
  return get(uri, obj, config);
}
