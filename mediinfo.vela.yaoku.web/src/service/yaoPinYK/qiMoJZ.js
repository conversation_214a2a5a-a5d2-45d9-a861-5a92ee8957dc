import { get, post, put, del } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

/**
 * 结转
 * @param {*} config
 * @returns
 */
export function JieZhuan(query, config) {
  return get(getBaseUri('JieZhuan'), query, config);
}

/**
 * 取消结转
 * @param {*} config
 * @returns
 */
export function QuXiaoJZ(config) {
  return get(getBaseUri('QuXiaoJZ'), config);
}

/**
 * 尾差调整
 * @param {*} config
 * @returns
 */
export function WeiChaTZ(query, config) {
  return get(getBaseUri('WeiChaTZ'), query, config);
}

/**
 * 核对账本
 * @param {*} config
 * @returns
 */
export function HeDuiZB(query, config) {
  return get(getBaseUri('HeDuiZB'), query, config);
}

/**
 * 获取最近一次结转的结束日期
 * @param {*} config
 * @returns
 */
export function GetZuiJinJZRQ(query, config) {
  return get(getBaseUri('GetZuiJinJZRQ'), query, config);
}
/**
 * 获取结转单记录
 * @param {*} config
 * @returns
 */
export function GetJieZhuanDanList(config) {
  return get(getBaseUri('GetJieZhuanDanList'), config);
}
