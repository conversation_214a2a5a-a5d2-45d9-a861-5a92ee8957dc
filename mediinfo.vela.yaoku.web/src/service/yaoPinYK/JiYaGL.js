import { get } from '@/system/utils/request';
import GLOBA<PERSON> from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 *  获取积压药品数据list
 *
 */
export function GetJiYaYPList(obj, config) {
  const uri = getBaseUri('GetJiYaYPList');
  return get(uri, obj, config);
}
/**
 * @description: 获取总量库存记录数量
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetJiYaYPCount(obj, config) {
  const uri = getBaseUri('GetJiYaYPCount');
  return get(uri, obj, config);
}

/**
 * @description: 根据价格ID获取批次库存记录
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetPiCiKCListByJGID(obj, config) {
  const uri = getBaseUri('GetPiCiKCListByJGID');
  return get(uri, obj, config);
}
