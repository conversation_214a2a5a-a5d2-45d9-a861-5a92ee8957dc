import { get, post, del } from '@/system/utils/request';
import GLOBAL from '@/service/configSettings';

/**
 * 获取待出库请领单list
 */
export function GetDaiChuKQLDCount(obj, config) {
  const uri = getBaseUri('GetDaiChuKQLDCount');
  return get(uri, obj, config);
}
export function GetDaiChuKQLDList(obj, config) {
  const uri = getBaseUri('GetDaiChuKQLDList');
  return get(uri, obj, config);
}
/**
 * 获取待出库请领单明细list
 */
export function GetDaiChuKQLDMXCount(obj, config) {
  const uri = getBaseUri('GetDaiChuKQLDMXCount');
  return get(uri, obj, config);
}
export function GetDaiChuKQLDMXList(obj, config) {
  const uri = getBaseUri('GetDaiChuKQLDMXList');
  return get(uri, obj, config);
}
/**
 * 获取待出库入库单明细list
 */
export function GetDaiChuKRKDMXCount(obj, config) {
  const uri = getBaseUri('GetDaiChuKRKDMXCount');
  return get(uri, obj, config);
}
export function GetDaiChuKRKDMXList(obj, config) {
  const uri = getBaseUri('GetDaiChuKRKDMXList');
  return get(uri, obj, config);
}
/**
 * 获取待出库入库单list
 */
export function GetDaiChuKRKDCount(obj, config) {
  const uri = getBaseUri('GetDaiChuKRKDCount');
  return get(uri, obj, config);
}
export function GetDaiChuKRKDList(obj, config) {
  const uri = getBaseUri('GetDaiChuKRKDList');
  return get(uri, obj, config);
}
/**
 * 获取未记账出库单list
 */
export function GetWeiJiZCKDCount(obj, config) {
  const uri = getBaseUri('GetWeiJiZCKDCount');
  return get(uri, obj, config);
}
export function GetWeiJiZCKDList(obj, config) {
  const uri = getBaseUri('GetWeiJiZCKDList');
  return get(uri, obj, config);
}
/**
 * 获取已记账出库单list
 */
export function GetYiJiZCKDCount(obj, config) {
  const uri = getBaseUri('GetYiJiZCKDCount');
  return get(uri, obj, config);
}
export function GetYiJiZCKDList(obj, config) {
  const uri = getBaseUri('GetYiJiZCKDList');
  return get(uri, obj, config);
}

/**
 *  根据出库单id获取出库单明细列表
 */
export function GetChuKuDMXList(obj, config) {
  const uri = getBaseUri('GetChuKuDMXList');
  return get(uri, obj, config);
}
/**
 * 获取出库单详情信息——编辑用
 */
export function GetChuKuDXQXX(obj, config) {
  const uri = getBaseUri('GetChuKuDXQXX');
  return get(uri, obj, config);
}
/**
 *  记账出库单
 */
export function JiZhangChuKuDan(obj, config) {
  const uri = getBaseUri('JiZhangChuKuDan');
  return get(uri, obj, config);
}
/**
 *  未记账开始日期
 */
export function GetWeiJiZCKDKSRQ(obj, config) {
  const uri = getBaseUri('GetWeiJiZCKDKSRQ');
  return get(uri, obj, config);
}
/**
 *  编辑保存出库单及明细
 */
export function UpdateChuKuDan(obj, config) {
  const uri = getBaseUri('UpdateChuKuDan');
  return post(uri, obj, config);
}
/**
 *  保存出库单及明细
 */
export function AddChuKuDan(obj, config) {
  const uri = getBaseUri('AddChuKuDan');
  return post(uri, obj, config);
}
/**
 *  保存出库单及明细
 */
export function ZuoFeiChuKuDan(id, config) {
  const uri = getBaseUri('ZuoFeiChuKuDan?chuKuDID=' + id);
  return del(uri, {}, config);
}
/**
 *  请领单出库制单
 */
export function QingLingCKZD(obj, config) {
  const uri = getBaseUri('QingLingCKZD');
  return get(uri, obj, config);
}
/**
 *  保存出库单及明细
 */
export function GetChuKuYPPCList(obj, config) {
  const uri = getBaseUri('GetChuKuYPPCList');
  return get(uri, obj, config);
}
export function UpdateQingLingDanZT(obj, config) {
  const uri = getBaseUri('UpdateQingLingDanZT');
  return post(uri, obj, config);
}
export function UpdateRuKuDanZT(obj, config) {
  const uri = getBaseUri('UpdateRuKuDanZT');
  return post(uri, obj, config);
}
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}
