import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

//获取请领位置
export function GetQingLingWZList(obj, config) {
  const uri = getBaseUri('GetQingLingWZList');
  return get(uri, obj, config);
}

//获取请领单列表
export function GetQingLingDanList(obj, config) {
  const uri = getBaseUri('GetQingLingDanList');
  return get(uri, obj, config);
}

export function GetShouLiQLDList(obj, config) {
  const uri = getBaseUri('GetShouLiQLDList');
  return get(uri, obj, config);
}

//请领单数量
export function GetQingLingDanCount(obj, config) {
  const uri = getBaseUri('GetQingLingDanCount');
  return get(uri, obj, config);
}

export function GetShouLiQLDCount(obj, config) {
  const uri = getBaseUri('GetShouLiQLDCount');
  return get(uri, obj, config);
}

//获取指定请领单的明细
export function GetQingLingDMXList(obj, config) {
  const uri = getBaseUri('GetQingLingDMXList');
  return get(uri, obj, config);
}

//获取请领单闭环
export function GetQingLingBHList(obj, config) {
  const uri = getBaseUri('GetQingLingBHList');
  return get(uri, obj, config);
}

// // 获取待出库请领单列表
// export function GetDaiChuKQLDList(obj, config) {
//   var uri = getBaseUri('GetDaiChuKQLDList')
//   return get(uri, obj, config)
// }

// // 获取待出库请领单列表数
// export function GetDaiChuKQLDCount(obj, config) {
//   var uri = getBaseUri('GetDaiChuKQLDList')
//   return get(uri, obj, config)
// }

// //获取待出库请领单列表
// export function GetDaiChuKQLDMXList(obj, config) {
//   var uri = getBaseUri('GetDaiChuKQLDMXCount')
//   return get(uri, obj, config)
// }

// //获取待出库请领单明细列表数
// export function GetDaiChuKQLDMXCount(obj, config) {
//   var uri = getBaseUri('GetDaiChuKQLDMXCount')
//   return get(uri, obj, config)
// }

//受理请领
export function ShouLiQL(obj, config) {
  const uri = getBaseUri('ShouLiQL');
  return post(uri, obj, config);
}
export function ShouLiQLPL(obj, config) {
  const uri = getBaseUri('ShouLiQLPL');
  return post(uri, obj, config);
}

//药库---受理批量
export function shouLiQLPLByCRKFS(obj, config) {
  var uri = getBaseUri('ShouLiQLPLByCRKFS');
  return post(uri, obj, config);
}

//药库---单个受理批量
export function shouLiQLByCRKFS(obj, config) {
  var uri = getBaseUri('ShouLiQLByCRKFS');
  return post(uri, obj, config);
}
//拒绝请领
export function JuJueQL(obj, config) {
  const uri = getBaseUri('JuJueQL');
  return post(uri, obj, config);
}

/**
 *  新增药品请领单
 */
export function AddQingLingDan(obj, config) {
  const uri = getBaseUri('AddQingLingDan');
  return post(uri, obj, config);
}
/**
 *  更新药品请领单
 */
export function UpdateQingLingDan(obj, config) {
  const uri = getBaseUri('UpdateQingLingDan');
  return post(uri, obj, config);
}

/**
 *  获取请领类型
 */
export function GetQingLingLXList(obj, config) {
  const uri = getBaseUri('GetQingLingLXList');
  return get(uri, obj, config);
}

/**
 *  获取目标位置药库药品库存
 */
export function GetKuCunXXByJGIDWZID(obj, config) {
  const uri = getBaseUri('GetKuCunXXByJGIDWZID');
  return get(uri, obj, config);
}

/**
 *  通过计算指定日期内的消耗量，自动生成请领单明细
 */
export function GetQingLingMXByXHL(obj, config) {
  const uri = getBaseUri('GetQingLingMXByXHL');
  return get(uri, obj, config);
}

/**
 *  通过库存上下限自动生成请领单明细
 */
export function GetQingLingMXByKCSXX(obj, config) {
  const uri = getBaseUri('GetQingLingMXByKCSXX');
  return get(uri, obj, config);
}

/**
 *  作废请领单
 */
export function ZuoFeiQLD(obj, config) {
  const uri = getBaseUri('ZuoFeiQLD');
  return del(uri, obj, config);
}

/**
 *  获取目标位置list
 */
export function GetMuBiaoWZList(obj, config) {
  const uri = getBaseUri('GetMuBiaoWZList');
  return get(uri, obj, config);
}

//根据加个id,日期区间查询库存分布
export function GetKuCunFBByJGIDAndRQ(obj, config) {
  const uri = getBaseUri('getKuCunFBByJGIDAndRQ');
  return get(uri, obj, config);
}
