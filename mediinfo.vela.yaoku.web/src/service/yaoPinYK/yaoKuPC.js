import { get, post, put, del } from '@/system/utils/request';
import G<PERSON><PERSON><PERSON><PERSON> from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

/**
 * 盘存开始
 * @param {*} config
 * @returns
 */
export function PanCunKS(config) {
  return post(getBaseUri('PanCunKS'), config);
}

/**
 * 盘存结束
 * @param {*} config
 * @returns
 */
export function PanCunJS(config) {
  return post(getBaseUri('PanCunJS'), config);
}

/**
 * 新增盘存单
 * @param {*} config
 * @returns
 */
export function AddPanCunDan(params, config) {
  return post(getBaseUri('AddPanCunDan'), params, config);
}

/**
 * 查询盘存单列表
 * @param {*} config
 * @returns
 */
export function GetPanCunDList(params, config) {
  return get(getBaseUri('GetPanCunDList'), params, config);
}

/**
 * 查询盘存单数量
 * @param {*} config
 * @returns
 */
export function GetPanCunDCount(params, config) {
  return get(getBaseUri('GetPanCunDCount'), params, config);
}
/**
 * 根据id获取盘存单信息
 * @param {*} config
 * @returns
 */
export function GetPanCunDXXById(panCunDID, config) {
  return get(getBaseUri('GetPanCunDXXById?panCunDID=' + panCunDID), config);
}
/**
 * 记账盘存单
 * @param {*} config
 * @returns
 */
export function JiZhangPCD(params, config) {
  return put(getBaseUri('JiZhangPCD?panCunDID=' + params.panCunDID), config);
}

/**
 * 修改盘存单
 * @param {*} config
 * @returns
 */
export function UpdatePanCunDan(params, config) {
  return put(getBaseUri('UpdatePanCunDan'), params, config);
}

/**
 * 作废盘存单
 * @param {*} config
 * @returns
 */
export function ZuoFeiPCD(params, config) {
  return del(getBaseUri('ZuoFeiPCD'), params, config);
}
/**
 * 获取未记账单据
 * @param {*} config
 * @returns
 */
export function GetWeiJiZDJ(params, config) {
  return get(getBaseUri('GetWeiJiZDJ'), params, config);
}

/**
 * 获取盘存状态
 * @param {*} config
 * @returns
 */
export function GetPanCunZT() {
  return post(getBaseUri('GetPanCunZT'));
}

/**
 * 批量生成盘存单
 * @param {*} config
 * @returns
 */
export function PiLiangSC(params) {
  return post(getBaseUri('PiLiangSC'), params);
}

/**
 * 根据价格id获取药品批次
 * @param {*} query
 * @returns
 */
export function GetPanCunYPPCListYK(query) {
  return get(getBaseUri('GetPanCunYPPCList'), query);
}
