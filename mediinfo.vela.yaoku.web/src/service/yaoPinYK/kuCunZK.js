import { del, get, post } from '@/system/utils/request';
import GLOBAL from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 * 获取库存暂控列表信息
 */
export function getkuCunKZXXList(obj, config) {
  const uri = getBaseUri('getkuCunKZXXList');
  return get(uri, obj, config);
}

/**
 * 获取库存暂控列表数量
 */
export function getkuCunKZXXCount(obj, config) {
  const uri = getBaseUri('getkuCunKZXXCount');
  return get(uri, obj, config);
}

/**
 * 新增
 */
export function addKuCunKZXX(obj, config) {
  const uri = getBaseUri('addKuCunKZXX');
  return post(uri, obj, config);
}
/**
 * 获取库存暂控详情信息
 */
export function getKuCunKZXQXX(obj, config) {
  const uri = getBaseUri('getKuCunKZXQXX');
  return get(uri, obj, config);
}
/**
 * 编辑库存暂控
 */
export function updateKuCunKZXX(obj, config) {
  const uri = getBaseUri('updateKuCunKZXX');
  return post(uri, obj, config);
}
/**
 * 作废库存暂控
 */
export function zuoFeiKuCunKZXX(obj, config) {
  const uri = getBaseUri('zuoFeiKuCunKZXX');
  return del(uri, obj, config);
}
/**
 * 库存暂控启用标志变更
 */
export function kuCunKZQYBZBG(obj, config) {
  const uri = getBaseUri('kuCunKZQYBZBG');
  return get(uri, obj, config);
}

/**
 * 获取总量库存记录数量
 */
export function getZongLiangKCCount(obj, config) {
  const uri = getBaseUri('GetZongLiangKCCount');
  return post(uri, obj, config);
}

/**
 * 根据价格id查询库存分布
 */
export function getKuCunFBByJGID(obj, config) {
  const uri = getBaseUri('GetKuCunFBByJGID');
  return get(uri, obj, config);
}

/**
 * 获取每日结余库存
 */
export function getMeiRiJYKC(obj, config) {
  const uri = getBaseUri('GetMeiRiJYKC');
  return get(uri, obj, config);
}

/**
 * 根据价格ID获取每日结余库存
 */
export function getMeiRiJYKCByJGID(obj, config) {
  const uri = getBaseUri('GetMeiRiJYKCByJGID');
  return get(uri, obj, config);
}

/**
 * 获取当时库存
 */
export function getDangShiKC(obj, config) {
  const uri = getBaseUri('GetDangShiKC');
  return get(uri, obj, config);
}
