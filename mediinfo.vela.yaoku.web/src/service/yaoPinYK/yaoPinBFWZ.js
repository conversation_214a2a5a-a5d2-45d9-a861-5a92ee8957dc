import { get, post, del, put } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/${url}`;
}

function getSelectUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZDJCSJ/${url}`;
}
/**
 * 保存药品摆放位置
 */
export function BaoCunYPBFWZ(obj, config) {
  const uri = getBaseUri('BaoCunYPBFWZ');
  return post(uri, obj, config);
}
/**
 * @description: 获取药品摆放位置列表数——按产地
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetYaoPinBFWZCountByCD(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZCountByCD');
  return get(uri, obj, config);
}
//获取药品摆放位置列表数——按规格
export function GetYaoPinBFWZCountByGG(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZCountByGG');
  return get(uri, obj, config);
}
//获取药品摆放位置列表数——按主名
export function GetYaoPinBFWZCountByZM(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZCountByZM');
  return get(uri, obj, config);
}

/**
 * @description: 获取药品摆放位置列表——按产地
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetYaoPinBFWZListByCD(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByCD');
  return get(uri, obj, config);
}
//获取药品摆放位置列表——按规格
export function GetYaoPinBFWZListByGG(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByGG');
  return get(uri, obj, config);
}
//获取药品摆放位置列表——按主名
export function GetYaoPinBFWZListByZM(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByZM');
  return get(uri, obj, config);
}

/**
 * @description: 根据规格ID获取药品摆放位置列表——编辑
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetYaoPinBFWZListByGGID(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByGGID');
  return get(uri, obj, config);
}
//根据价格ID获取药品摆放位置列表——编辑(产地)
export function GetYaoPinBFWZListByJGID(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByJGID');
  return get(uri, obj, config);
}
//根据药品ID获取药品摆放位置列表——编辑(主名)
export function GetYaoPinBFWZListByYPID(obj, config) {
  const uri = getBaseUri('GetYaoPinBFWZListByYPID');
  return get(uri, obj, config);
}
/**
 * @description: 根据规格ID清空药品摆放位置列表
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function QingKongYPBFWZListByGGID(obj, config) {
  const uri = getBaseUri('QingKongYPBFWZListByGGID');
  return del(uri, obj, config);
}
// 根据价格ID清空药品摆放位置列表(产地)
export function QingKongYPBFWZListByJGID(obj, config) {
  const uri = getBaseUri('QingKongYPBFWZListByJGID');
  return del(uri, obj, config);
}
// 根据药品ID清空药品摆放位置列表(主名)
export function QingKongYPBFWZListByYPID(obj, config) {
  const uri = getBaseUri('QingKongYPBFWZListByYPID');
  return del(uri, obj, config);
}
/**
 * @description:获取账簿类别选择框列表
 * @param {*} obj
 * @param {*} config
 * @return {*}
 */
export function GetZhangBuLBSelectList(obj, config) {
  const uri = getSelectUri('GetZhangBuLBSelectList');
  return get(uri, obj, config);
}

/**
 *  获取医嘱费用信息
 * @param {*} params
 * @returns
 */
export function getYiZhuFYXX(params, config) {
  const uri = getBaseUri('GetYiZhuFYXX');
  return post(uri, params, config);
}

/**
 *  获取给药方式费用信息
 * @param {*} params
 * @returns
 */
export function getGeiYaoFSFYXX(params, config) {
  const uri = getBaseUri('GetGeiYaoFSFYXX');
  return post(uri, params, config);
}

/**
 *  根据输入内容查询药品
 * @param {*} params
 * @returns
 */
export function getYiZhuYPList(params, config) {
  const uri = getBaseUri('GetYiZhuYPList');
  return post(uri, params, config);
}

/**
 *  根据价格id获取药品信息
 * @param {*} params
 * @returns
 */
export function getYiZhuYPXXByJGID(params) {
  const uri = getBaseUri('GetYiZhuYPXXByJGID');
  return get(uri, params);
}

/**
 *  获取药品摆放位置下拉
 * @param {*} params
 * @returns
 */
export function getBaiFangWZSelect(params) {
  const uri = getBaseUri('GetBaiFangWZSelect');
  return get(uri, params);
}
