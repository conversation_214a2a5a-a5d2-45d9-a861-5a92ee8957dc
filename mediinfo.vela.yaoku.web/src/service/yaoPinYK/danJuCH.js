import { get, post, put, del } from '@/system/utils/request';
import GLOBA<PERSON> from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

/**
 * 出库单冲红
 * @param {*} config
 * @returns
 */
export function ChongHongChuKuDan(params, config) {
  return post(getBaseUri('ChongHongChuKuDan'), params, config);
}

/**
 * 入库单冲红
 * @param {*} config
 * @returns
 */
export function ChongHongRuKuDan(params, config) {
  return post(getBaseUri('ChongHongRuKuDan'), params, config);
}

/**
 * 出库单记账（冲红）
 * @param {*} config
 * @returns
 */
export function ChongHongJZChuKuDan(params, config) {
  return post(
    getBaseUri('ChongHongJZChuKuDan?chuKuDID=' + params.chuKuDID),
    {},
    config,
  );
}

/**
 * 入库单记账（冲红）
 * @param {*} config
 * @returns
 */
export function ChongHongJZRuKuDan(params, config) {
  return post(
    getBaseUri('ChongHongJZRuKuDan?ruKuDID=' + params.ruKuDID),
    {},
    config,
  );
}

/**
 * 获取药品出库单（冲红）
 * @param {*} config
 * @returns
 */
export function GetYaoPinCKDForCH(query, config) {
  return get(getBaseUri('GetYaoPinCKDForCH'), query, config);
}

/**
 * 获取药品入库单（冲红）
 * @param {*} config
 * @returns
 */
export function GetYaoPinRKDForCH(query, config) {
  return get(getBaseUri('GetYaoPinRKDForCH'), query, config);
}
