import { del, get, post } from '@/system/utils/request';
import GLOBAL from '../configSettings';

//添加集采计划单
export function AddJiCaiJHD(obj, config) {
  const uri = getBaseUri('AddJiCaiJHD');
  return post(uri, obj, config);
}

export function UpdateJiCaiJHD(obj, config) {
  const uri = getBaseUri('UpdateJiCaiJHD');
  return post(uri, obj, config);
}

export function GetJiCaiJHDList(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDList');
  return get(uri, obj, config);
}

export function GetJiCaiJHDCount(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDCount');
  return get(uri, obj, config);
}

export function GetJiCaiJHDXQ(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDXQ');
  return get(uri, obj, config);
}

export function GetYaoPinCDJGXX(obj, config) {
  const uri = getBaseUri('GetYaoPinCDJGXX');
  return get(uri, obj, config);
}
export function ZuoFeiJiCaiJHD(obj, config) {
  const uri = getBaseUri('ZuoFeiJiCaiJHD');
  return del(uri, obj, config);
}
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

export function GetJiCaiYPBZGGXX(obj, config) {
  const uri = getBaseUri('GetJiCaiYPBZGGXX');
  return get(uri, obj, config);
}

// 获取集采左侧项目
export function getJiCaiXMList(obj, config) {
  const uri = getBaseUri('GetJiCaiXMList');
  return get(uri, obj, config);
}

// 根据左侧集采项目id获取详情
export function getJiCaiJHDListByJCXMID(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDListByJCXMID');
  return get(uri, obj, config);
}
// 根据左侧集采项目id获取详情count
export function getJiCaiJHDCountByJCXMID(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDCountByJCXMID');
  return get(uri, obj, config);
}

// 保存集采项目弹窗
export function SaveJiCaiXM(obj, config) {
  const uri = getBaseUri('SaveJiCaiXM');
  return post(uri, obj, config);
}

// 作废集采项目
export function ZuoFeiJiCaiXM(obj, config) {
  const uri = getBaseUri('ZuoFeiJiCaiXM');
  return del(uri, obj, config);
}

// 导入集采项目
export function GetJiCaiXMDRList(obj, config) {
  const uri = getBaseUri('GetJiCaiXMDRList');
  return get(uri, obj, config);
}

// 新增集采的导入
export function ImportJiCaiXMJHDXQ(obj, config) {
  const uri = getBaseUri('ImportJiCaiXMJHDXQ');
  return get(uri, obj, config);
}

// 获取标准规格设置左侧内容
export function getJiCaiYPFZList(obj, config) {
  const uri = getBaseUri('GetJiCaiYPFZList');
  return get(uri, obj, config);
}

// 获取标准规格设置左侧内容count
export function GetJiCaiYPFZCount(obj, config) {
  const uri = getBaseUri('GetJiCaiYPFZCount');
  return get(uri, obj, config);
}

// 获取右侧详情
export function GetJiCaiYPGGList(obj, config) {
  const uri = getBaseUri('GetJiCaiYPGGList');
  return get(uri, obj, config);
}

// 保存标准规格
export function SaveJiCaiYPGGList(obj, config) {
  const uri = getBaseUri('SaveJiCaiYPGGList ');
  return post(uri, obj, config);
}

// 获取药品跟踪左侧list
export function GetJiCaiXMList(obj, config) {
  const uri = getBaseUri('GetJiCaiXMList');
  return get(uri, obj, config);
}

// 获取药品跟踪右侧详情
export function GetJiCaiJHDXHXQ(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDXHXQ');
  return get(uri, obj, config);
}

// 获取药品跟踪右侧详情底部表格
export function GetJiCaiJHDKSXHXQ(obj, config) {
  const uri = getBaseUri('GetJiCaiJHDKSXHXQ');
  return get(uri, obj, config);
}

function getBaseUriJP(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/jingPei/` + actionUrl;
}

// 保存竞品限量设置弹窗
export function SaveJiCaiXLKZSZ(obj, config) {
  const uri = getBaseUriJP('SaveJiCaiXLKZSZ ');
  return post(uri, obj, config);
}

export function GetJiCaiXLKZSZ(obj, config) {
  const uri = getBaseUriJP('GetJiCaiXLKZSZ');
  return get(uri, obj, config);
}
