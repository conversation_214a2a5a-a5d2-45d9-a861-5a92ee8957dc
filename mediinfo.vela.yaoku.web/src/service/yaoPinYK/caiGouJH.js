import { del, get, post, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}
//采购计划列表
export function GetCaiGouJHDList(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDList');
  return get(uri, obj, config);
}

//列表数量
export function GetCaiGouJHDCount(obj, config) {
  const uri = getBaseUri('GetCaiGouJHDCount');
  return get(uri, obj, config);
}
//采购详情
export function GetCaiGouJHDMX(caiGouJHDID) {
  const uri = getBaseUri('GetCaiGouJHDMX?id=' + caiGouJHDID);
  return get(uri);
}
//新增采购计划
export function AddCaiGouJHD(obj) {
  const uri = getBaseUri('AddCaiGouJHD');
  return post(uri, obj);
}
//编辑采购单
export function UpdateCaiGouJHD(obj) {
  const uri = getBaseUri('UpdateCaiGouJHD');
  return put(uri, obj);
}
//作废采购计划
export function ZuoFeiCaiGouJHD(id) {
  const uri = getBaseUri(`ZuoFeiCaiGouJHD?id=${id}`);
  return del(uri);
}
//根据库存下限批量生成
export function GetKuCunXXYPList() {
  const uri = getBaseUri('GetKuCunXXYPList');
  return get(uri);
}

//根据库存下限批量生成
export function GetKuCunXXYPByYLList(obj, config) {
  const uri = getBaseUri('GetKuCunXXYPByYLList');
  return get(uri, obj, config);
}

export function GetCaiGouJHDGHXX(jiaGeID, guiGeID, kaiShiSJ, jieShuSJ) {
  const uri = getBaseUri(
    `GetCaiGouJHDGHXX?jiaGeID=${jiaGeID}&guiGeID=${guiGeID}&kaiShiSJ=${kaiShiSJ}&jieShuSJ=${jieShuSJ}`,
  );
  return get(uri);
}
/*let aj ={
  LikeQuery: "ffffff",
  PageIndex: 1,
  PageSize: 10,
}*/
//供货单位
export function GetGongHuoDWList(LikeQuery, PageIndex, PageSize) {
  const uri = getBaseUri(
    `GetGongHuoDWList?LikeQuery=${LikeQuery}&PageIndex=${PageIndex}&PageSize=${PageSize}`,
  );
  return get(uri);
}

export function GetYaoPinRKDWJZRQ(obj, config) {
  const uri = getBaseUri('GetYaoPinRKDWJZRQ');
  return get(uri, obj, config);
}

//综合生成
export function GetZongHeSCCGJHList() {
  const uri = getBaseUri('GetZongHeSCCGJHList');
  return get(uri);
}

//获取行内消耗量
export function GetYaoPinXHLMXList(obj, config) {
  const uri = getBaseUri('GetYaoPinXHLMXList');
  return get(uri, obj, config);
}

//撤回
export function CheHuiCGJHDXX(id) {
  const uri = getBaseUri('CheHuiCGJHDXX?id=' + id);
  return put(uri);
}
// 提交
export function TiJiaoCGJHDXX(id) {
  const uri = getBaseUri('TiJiaoCGJHDXX?id=' + id);
  return put(uri);
}

//保存并提交--新增保存
export function BaoCunBTJCGJHD(obj, config) {
  const uri = getBaseUri('BaoCunBTJCGJHD');
  return post(uri, obj, config);
}

//保存并提交--编辑保存
export function BaoCunBTJBJCGJHD(obj, config) {
  const uri = getBaseUri('BaoCunBTJBJCGJHD');
  return post(uri, obj, config);
}


// 明细账消耗  
export function GetKuCunXXYPByMXZList(obj, config) {
  const uri = getBaseUri('GetKuCunXXYPByMXZList');
  return get(uri, obj, config);
}