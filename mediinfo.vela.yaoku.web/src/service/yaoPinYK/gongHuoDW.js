import { get, post, put, del } from '@/system/utils/request';
import GL<PERSON><PERSON><PERSON> from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}
function getBaseUrL(actionUrl) {
  return `${GLOBAL.dzBaseUri}/api/v1.0/GongYong/` + actionUrl;
}
function getDanWeiXXBaseUrL(actionUrl) {
  return `${GLOBAL.lyragyBaseUri}/api/v1.0/DanWeiXX/` + actionUrl;
}
//新增省份信息列表
export function GetShengFenXXList(obj, config) {
  const url = getBaseUrL('GetShengFenXXList');
  return get(url, obj, config);
}
//新增单位信息（同时处理新增许可证）
export function AddDanWeiXX(obj, config) {
  const url = getBaseUri('AddDanWeiXX');
  return post(url, obj, config);
}
//获取单位信息列表数
export function GetDanWeiXXCount(obj, config) {
  const url = getBaseUri('GetDanWeiXXCount');
  return get(url, obj, config);
}
//获取单位信息列表
export function GetDanWeiXXList(obj, config) {
  const url = getBaseUri('GetDanWeiXXList');
  return get(url, obj, config);
}
//更新单位信息（同时处理更新许可证）
export function UpdateDanWeiXX(obj, config) {
  const url = getBaseUri('UpdateDanWeiXX');
  return put(url, obj, config);
}
//作废单位信息
export function ZuoFeiDanWeiXX(obj, config) {
  const url = getBaseUri('ZuoFeiDanWeiXX');
  return del(url, obj, config);
}

//根据单位ID获取许可证列表
export function GetXuKeZhengListByDWID(obj, config) {
  const url = getBaseUri('GetXuKeZhengListByDWID');
  return get(url, obj, config);
}

//更改供货单位状态
export function getUpdateGongHuoDWZT(obj, config) {
  const url = getBaseUri('UpdateGongHuoDWZT');
  return get(url, obj, config);
}
//作废许可证信息
export function ZuoFeiXuKeZhengXX(obj, config) {
  const url = getBaseUri('ZuoFeiXuKeZhengXX');
  return del(url, obj, config);
}
//城市选择list
export function getShengShiQList() {
  const url = getBaseUrL('GetXingZhengQHTree');
  return get(url);
}

export function getGongYingShangList(obj, config) {
  const url = getDanWeiXXBaseUrL('GetGongYingShangList');
  return get(url, obj, config);
}

// 获取供应商信息
export function getGongYingShangXX(obj, config) {
  const url = getDanWeiXXBaseUrL(`getGongYingShangXX`);
  return get(url, obj, config);
}
// 判断单位是否重复
export function getCheckDanWeiZT(obj, config) {
  const url = getBaseUri(`CheckDanWeiZT`);
  return get(url, obj, config);
}
