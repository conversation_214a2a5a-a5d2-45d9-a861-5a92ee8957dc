import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

//查询调价单主表记录
export function GetTiaoJiaDList(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDList');
  return get(uri, obj, config);
}

//查询调价单主表数量
export function GetTiaoJiaDCount(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDCount');
  return get(uri, obj, config);
}

//根据id获取调价单信息
export function GetTiaoJiaDById(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDById');
  return get(uri, obj, config);
}

export function GetTiaoJiaDByIds(obj, config) {
  const uri = getBaseUri('GetTiaoJiaDByIds');
  return get(uri, obj, config);
}

//记账调价单
export function JiZhangTJD(obj, config) {
  const uri = getBaseUri('JiZhangTJD?tiaoJiaDID=' + obj.tiaoJiaDID);
  return put(uri, {}, config);
}

//修改调价单
export function UpdateTiaoJiaDan(obj, config) {
  const uri = getBaseUri('UpdateTiaoJiaDan');
  return post(uri, obj, config);
}

//作废调价单
export function ZuoFeiTJD(obj, config) {
  const uri = getBaseUri('ZuoFeiTJD?tiaoJiaDID=' + obj.tiaoJiaDID);
  return del(uri, obj, config);
}

// 新增调价单
export function AddTiaoJiaDan(obj, config) {
  const uri = getBaseUri('AddTiaoJiaDan');
  return post(uri, obj, config);
}

//药品库存分布
export function GetTiaoJiaYPKCFB(obj, config) {
  const uri = getBaseUri('GetTiaoJiaYPKCFB');
  return get(uri, obj, config);
}
//获取调价药品批次库存
export function GetTiaoJiaYPPCKCList(obj, config) {
  const uri = getBaseUri('GetTiaoJiaYPPCKCList');
  return get(uri, obj, config);
}

//按库存下限生产调价单
export function AnKuCunXXSC(obj, config) {
  const uri = getBaseUri('AnKuCunXXSC');
  return get(uri, obj, config);
}
