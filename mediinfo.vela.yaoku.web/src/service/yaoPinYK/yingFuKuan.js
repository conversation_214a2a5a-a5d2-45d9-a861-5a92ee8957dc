import GLOBAL from '@/service/configSettings';
import { get, post } from '@/system/utils/request';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 * 获取未结算发票列表
 */
export function getWeiJieSFPList(obj, config) {
  const uri = getBaseUri('GetWeiJieSFPList');
  return get(uri, obj, config);
}

/**
 * 获取未结算发票列表数量
 */
export function getWeiJieSFPCount(obj, config) {
  const uri = getBaseUri('getWeiJieSFPCount');
  return get(uri, obj, config);
}

/**
 * 获取未结算的供货单位列表
 */
export function getWeiJieSGHDWList(obj, config) {
  const uri = getBaseUri('GetWeiJieSGHDWList');
  return get(uri, obj, config);
}

/**
 * 获取未结算的供货单位总数
 */
export function getWeiJieSGHDWCount(obj, config) {
  const uri = getBaseUri('GetWeiJieSGHDWCount');
  return get(uri, obj, config);
}

/**
 * 根据发票获取未结算的药品明细
 */
export function getWeiJieSYPMXListByFP(obj, config) {
  const uri = getBaseUri('GetWeiJieSYPMXListByFP');
  return get(uri, obj, config);
}

/**
 * 根据供货单位获取未结算的药品明细
 */
export function getWeiJieSYPMXListByGHDW(obj, config) {
  const uri = getBaseUri('GetWeiJieSYFKMXListByGHDW');
  return get(uri, obj, config);
}

/**
 * 根据供货单位获取未结算的药品明细数量
 */
export function getWeiJieSYPMXCountByGHDW(obj, config) {
  const uri = getBaseUri('GetWeiJieSYFKMXCountByGHDW');
  return get(uri, obj, config);
}

/**
 * 应付款结算--按发票号
 */
export function addYingFuKJS(obj, config) {
  const uri = getBaseUri('addYingFuKJS');
  return post(uri, obj, config);
}
/**
 * 应付款结算
 */
export function addYingFuKJSByRuKuDH(obj, config) {
  const uri = getBaseUri('addYingFuKJSByRuKuDH');
  return post(uri, obj, config);
}

/**
 * 获取结算单列表
 */
export function getJieSuanDanList(obj, config) {
  const uri = getBaseUri('GetJieSuanDanList');
  return get(uri, obj, config);
}

/**
 * 获取结算单数量
 */
export function getJieSuanDanCount(obj, config) {
  const uri = getBaseUri('GetJieSuanDanCount');
  return get(uri, obj, config);
}

/**
 * 销账结算单
 */
export function xiaoZhangJieSuanDan(obj, config) {
  const uri = getBaseUri('XiaoZhangJieSuanDan');
  return get(uri, obj, config);
}

/**
 * 取消结算单
 */
export function quXiaoJieSuanDan(obj, config) {
  const uri = getBaseUri('QuXiaoJieSuanDan');
  return get(uri, obj, config);
}

/**
 * 获取结算单详情
 */
export function getJieSuanDXX(obj, config) {
  const uri = getBaseUri('GetJieSuanDXX');
  return get(uri, obj, config);
}

/**
 * 打印结算单
 */
export function daYingJieSuanDXX(obj, config) {
  const uri = getBaseUri('DaYingJieSuanDXX');
  return post(uri, obj, config);
}

export function getGongHuoDWXX(obj, config) {
  const uri = getBaseUri('GetGongHuoDWXX');
  return get(uri, obj, config);
}

export function getWeiJieSDXXByRuKuDH(obj, config) {
  const uri = getBaseUri('getWeiJieSDXXByRuKuDH');
  return get(uri, obj, config);
}

export function getWeiJieSDXX(obj, config) {
  const uri = getBaseUri('getWeiJieSDXX');
  return get(uri, obj, config);
}
