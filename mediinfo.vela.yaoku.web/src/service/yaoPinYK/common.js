import { get, post, del, put } from '@/system/utils/request';
import GLOBAL from '../configSettings';

function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

//药库——药品公用查询弹框
export function GetGongYongYPForYKList(obj, config) {
  const uri = getBaseUri('GetGongYongYPForYKList');
  return get(uri, obj, config);
}

// 药库-药品批号
export function GetChuKuYPPCList(obj, config) {
  const uri = getBaseUri('GetChuKuYPPCList');
  return get(uri, obj, config);
}

//药库-药品历史进价 GetChuKuYPLCJJList?jiaGeID=284
export function GetChuKuYPLCJJList(obj, config) {
  const uri = getBaseUri('GetChuKuYPLCJJList');
  return get(uri, obj, config);
}

// 根据出入库方式ID获取单位部门信息
export function GetDanWeiBMXXByCRKFS(obj, config) {
  const uri = getBaseUri('GetDanWeiBMXXByCRKFS');
  return get(uri, obj, config);
}
//供货单位
export function GetGongHuoDWList(params, config) {
  const uri = getBaseUri(`GetGongHuoDWList`);
  return get(uri, params, config);
}
export function GetGongHuoDWCount(params, config) {
  const uri = getBaseUri(`GetGongHuoDWCount`);
  return get(uri, params, config);
}
