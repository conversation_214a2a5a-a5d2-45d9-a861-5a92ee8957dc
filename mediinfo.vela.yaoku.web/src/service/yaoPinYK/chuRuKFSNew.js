import { get, post, put, del } from '@/system/utils/request';
import GLOBAL from '../configSettings';
function getBaseUri(actionUrl) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/` + actionUrl;
}

//获取出入库方式列表
export function GetChuRuKFSList(obj, config) {
  const uri = getBaseUri('GetChuRuKFSListNew');
  return get(uri, obj, config);
}
//获取出入库方式列表数
export function GetChuRuKFSCount(obj, config) {
  const uri = getBaseUri('GetChuRuKFSCountNew');
  return get(uri, obj, config);
}
//添加出入库方式
export function AddChuRuKFS(obj, config) {
  const uri = getBaseUri('AddChuRuKFSNew');
  return post(uri, obj, config);
}
//修改出入库方式
export function UpdateChuRuKFS(obj, config) {
  const uri = getBaseUri('UpdateChuRuKFSNew');
  return put(uri, obj, config);
}

//作废出入库方式
export function ZuoFeiChuRuKFS(obj, config) {
  const uri = getBaseUri('ZuoFeiChuRuKFSNew');
  return del(uri, obj, config);
}
// 通过获取
export function GetChuRuKFSByFXDM(chuRuKFXDM) {
  const uri = getBaseUri('GetChuRuKFSByFXDMNew');
  return get(uri, { chuRuKFXDM: chuRuKFXDM });
}
//获取方式id
export function GetFangShiID(obj, config) {
  const uri = getBaseUri('GetFangShiID');
  return get(uri, obj, config);
}
