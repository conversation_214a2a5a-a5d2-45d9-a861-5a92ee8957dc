import { get, post } from '@/system/utils/request';
import GLOBAL from '@/service/configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}

/**
 * 获取未登记发票供货单位列表
 */
export function getPiCiKCList(obj, config) {
  const uri = getBaseUri('GetPiCiKCList');
  return post(uri, obj, config);
}

/**
 * 获取批次库存记录数量
 */
export function getPiCiKCCount(obj, config) {
  const uri = getBaseUri('GetPiCiKCCount');
  return post(uri, obj, config);
}

/**
 * 获取总量库存记录
 */
export function getZongLiangKCList(obj, config) {
  const uri = getBaseUri('GetZongLiangKCList');
  return post(uri, obj, config);
}

/**
 * 获取总量库存记录数量
 */
export function getZongLiangKCCount(obj, config) {
  const uri = getBaseUri('GetZongLiangKCCount');
  return post(uri, obj, config);
}

/**
 * 根据价格id查询库存分布
 */
export function getKuCunFBByJGID(obj, config) {
  const uri = getBaseUri('GetKuCunFBByJGID');
  return get(uri, obj, config);
}

/**
 * 获取每日结余库存
 */
export function getMeiRiJYKC(obj, config) {
  const uri = getBaseUri('GetMeiRiJYKC');
  return get(uri, obj, config);
}

/**
 * 根据价格ID获取每日结余库存
 */
export function getMeiRiJYKCByJGID(obj, config) {
  const uri = getBaseUri('GetMeiRiJYKCByJGID');
  return get(uri, obj, config);
}

/**
 * 获取当时库存
 */
export function getDangShiKC(obj, config) {
  const uri = getBaseUri('GetDangShiKC');
  return get(uri, obj, config);
}

// 零库存保存方法
export function XinZengLKC(obj, config) {
  var uri = getBaseUri('XinZengLKC');
  return post(uri, obj, config);
}
