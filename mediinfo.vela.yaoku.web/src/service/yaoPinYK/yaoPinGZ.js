import { get, post, del } from '@/system/utils/request';
import GLOBA<PERSON> from '@/service/configSettings';

/**
 *  获取配伍禁忌列表数量
 */
export function GetPeiWuJJCount(obj, config) {
  const uri = getBaseUri('GetPeiWuJJCount');
  return get(uri, obj, config);
}
/**
 *  获取配伍禁忌列表
 */
export function GetPeiWuJJList(obj, config) {
  const uri = getBaseUri('GetPeiWuJJList');
  return get(uri, obj, config);
}

/**
 *  保存配伍禁忌
 */
export function SavePeiWuJJList(obj, config) {
  const uri = getBaseUri('SavePeiWuJJList');
  return post(uri, obj, config);
}

/**
 *  删除配伍禁忌
 */
export function DeletePeiWuJJList(id, config) {
  const uri = getBaseUri('DeletePeiWuJJList?id=' + id);
  return del(uri, {}, config);
}
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinYK/${url}`;
}
