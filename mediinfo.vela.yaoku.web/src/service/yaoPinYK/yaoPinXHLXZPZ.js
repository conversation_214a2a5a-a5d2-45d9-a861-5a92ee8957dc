import { get, post } from '@/system/utils/request';
import GLOBAL from '../configSettings';
function getBaseUri(url) {
  return `${GLOBAL.ypBaseUri}/api/v1.0/YaoPinZD/${url}`;
}

/**
 * 保存药品消耗量限制配置
 * SaveYaoPinXHLXZPZ
 * 请求方式：Post
 * 入参：如下、需要注意新增和修改都是调改接口、没有id就是新增，有id就是修改
 * 出参：true或false
 */
export function SaveYaoPinXHLXZPZ(obj, config) {
  var uri = getBaseUri('SaveYaoPinXHLXZPZ');
  return post(uri, obj, config);
}
/**
 * 获取药品消耗量限制配置总数
 * GetYaoPinXHLXZPZCount
 * 请求方式：Get
 * 入参：jiaGeID、qiYongBZ、tiShiBL
 * 出参：药品消耗总量限制总数
 */

export function GetYaoPinXHLXZPZCount(obj, config) {
  var uri = getBaseUri('GetYaoPinXHLXZPZCount');
  return get(uri, obj, config);
}
/**
 * 获取药品消耗量限制配置列表
 * GetYaoPinXHLXZPZList
 * 请求方式：Get
 * 入参：jiaGeID、qiYongBZ、tiShiBL、pageSize、pageIndex
 * 出参：如下
 */

export function GetYaoPinXHLXZPZList(obj, config) {
  var uri = getBaseUri('GetYaoPinXHLXZPZList');
  return get(uri, obj, config);
}

//获取限量消耗详情
export function getXiaoHaoZLXZCZJLList(obj, config) {
  var uri = getBaseUri('getXiaoHaoZLXZCZJLList');
  return get(uri, obj, config);
}
//获取限量消耗详情
export function GetXiaoHaoZLXZCZJLCount(obj, config) {
  var uri = getBaseUri('GetXiaoHaoZLXZCZJLCount');
  return get(uri, obj, config);
}
/**
 * 启停用消耗量限制配置
 * QiTingYXHLXZPZ
 * 请求方式：Post
 * 入参：id
 * 出参：true或false
 */

export function QiTingYXHLXZPZ(obj, config) {
  var uri = getBaseUri('QiTingYXHLXZPZ');
  return post(uri + '?id=' + obj.id + '&qiYongLX=' + obj.qiYongLX, config);
  // return post(uri, {params:obj} , config)
}
/**
 * 删除药品消耗量限制配置
 * DeleteYaoPinXHLXZPZ
 * 请求方式：Post
 * 入参：id
 * 出参：true或false
 */
export function DeleteYaoPinXHLXZPZ(obj, config) {
  var uri = getBaseUri('DeleteYaoPinXHLXZPZ');
  return post(uri + '?id=' + obj, config);
}
/**
 * 更新提示比例
 * UpdateTiShiBL
 * 请求方式：Post
 * 入参：如下
 * 出参：true或false
 */
export function UpdateTiShiBL(obj, config) {
  var uri = getBaseUri('UpdateTiShiBL');
  return post(uri, obj, config);
}
