# ================================== #
#             构建静态资源             #
# ================================== #

FROM 172.19.30.186:8000/pub/node:16.19.1 AS build-stage

# 创建并进入工作目录
WORKDIR /app

# 拷贝依赖与配置文件
COPY package.json ./
COPY .npmrc ./

# 使用 yarn 构建
RUN yarn

# 拷贝源码
COPY . .

# 构建静态资源
RUN yarn build

# ================================== #
#           启动 Nginx 服务           #
# ================================== #

FROM 172.19.30.186:8000/pub/nginx:latest

# 从构建镜像中拷贝文件
COPY --from=build-stage /app/dist/  /usr/share/nginx/html/

# 拷贝 nginx 配置文件
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
