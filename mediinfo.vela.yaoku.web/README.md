<div align="center">
  <a href="https://mdesign.mediinfo.cn/" target="_blank" rel="noopener noreferrer">
    <img width="300" src="./media/logo.png" alt="青鸟 Logo">
  </a>
</div>
<br />
<div align="center">
  <h1>通用中后台微应用模板</h1>
</div>
<div align="center">遵循 M.design 设计，通用 <b>标准</b> 的应用模板。</div>
<br />
<div align="center">
  <a href="https://cn.vuejs.org/">
    <img src="https://img.shields.io/badge/Vue-3+-42b983.svg?style=flat-square" alt="Vue 版本" />
  </a>
  <a href="https://mdesign.mediinfo.cn/#/web/medi-ui">
    <img src="https://img.shields.io/badge/Medi%20UI-next-1385f0.svg?style=flat-square" alt="Medi UI 版本" />
  </a>
  <a href="#版权声明">
    <img src="https://img.shields.io/badge/%E7%89%88%E6%9D%83%E6%89%80%E6%9C%89-%E8%81%94%E4%BC%97%E6%99%BA%E6%85%A7%E7%A7%91%E6%8A%80%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-1385f0?style=flat-square" alt="版权声明" />
  </a>
</div>
<br />
<div align="center">
  🔥 <a href="https://mdesign.mediinfo.cn/#/web/medi-ui">Medi UI (文档)</a>
  &nbsp;
  🌈 <a href="https://mdesign.mediinfo.cn/developer/">Quark (夸克)</a>
</div>
<br />

---

<br />

基于 **Vue.js** & **Medi UI** & **QianKun** 开发，由 **Quark (夸克)** 提供工程化开发能力。

## 特性

- 🚀 标准化的应用创建流程
- 💪 升级到 Medi Ui
- 📖 提供丰富的技术文档（持续改进中）
- 🍭 支持主题定制，内置 100+ 个主题变量

## 启动项目

```sh
# 安装依赖
$ yarn install

# 启动开发服务
$ yarn serve
```

## 浏览器兼容

| Chrome | IE  | Edge | Firefox | Safari | Opera |
| :----: | :-: | :--: | :-----: | :----: | :---: |
|  Yes   | No  | Yes  |   Yes   |  Yes   |  Yes  |

## 核心团队

以下是模板的核心贡献者们：

|        姓名         | 职责               |
| :-----------------: | ------------------ |
| @郑贤森(体验设计部) | 框架升级、技术支持 |
|  @胡昊(体验设计部)  | Medi UI 项目负责人 |
|  @李龙(体验设计部)  | Medi UI 项目成员   |

## 贡献指南

贡献代码请阅读我们的[贡献指南](./CONTRIBUTING.md)。

使用过程中发现任何问题都可以提 [Issue][issues] 给我们，当然，我们也非常欢迎你给我们发 [MR][mr]。

## 版权声明

Copyright © 2018-present 联众智慧科技股份有限公司.

[issues]: https://git1.mediinfo.cn/mdfe/templates/template-admin-app-next/-/issues/
[mr]: https://git1.mediinfo.cn/mdfe/templates/template-admin-app-next/-/merge_requests/
