/*
 * @Author: wuxiaoling <EMAIL>
 * @Date: 2024-12-25 10:23:56
 * @LastEditors: wuxiaoling <EMAIL>
 * @LastEditTime: 2024-12-27 10:46:16
 * @FilePath: \yaoku-药库管理\mediinfo.vela.yaoku.web\config\proxy.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const proxyOptions = {
  // target: 'http://************:46081/',//后端地址
  target: 'http://************:31003/', //测试环境服务地址
  changeOrigin: true,
};
// const proxyOptions1 = {
//   target: 'http://************:46081/',
//   changeOrigin: true,
// };
// const proxyOptions2 = {
//   target: 'http://**************:46082/',
//   changeOrigin: true,
// };
module.exports = {
  '^/mediinfo-': proxyOptions,
};
